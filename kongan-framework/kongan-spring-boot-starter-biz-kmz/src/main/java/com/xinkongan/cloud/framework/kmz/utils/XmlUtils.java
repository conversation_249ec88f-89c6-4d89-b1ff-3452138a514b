package com.xinkongan.cloud.framework.kmz.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.xinkongan.cloud.framework.kmz.annotation.*;
import com.xinkongan.cloud.framework.kmz.constant.RouteDocumentConstant;
import com.xinkongan.cloud.framework.kmz.dto.*;
import com.xinkongan.cloud.framework.kmz.exception.RouteXmlFormatException;
import com.xinkongan.cloud.module.system.enums.route.RouteTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.dom4j.*;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;

import java.io.StringWriter;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class XmlUtils {

    public void convertWayLineXml(Element parentElement, Object obj) {
        Element rootElement = parentElement.addElement(getLabelName(obj.getClass()));
        if (isIgnoreChild(obj)) {
            if (obj instanceof Point) {
                Element coordinatesElement = rootElement.addElement("coordinates");
                coordinatesElement.addText(obj.toString());
                return;
            }
            rootElement.setText(obj.toString());
            return;
        }

        List<Field> fields = getAllFields(obj);
        for (Field field : fields) {
            try {
                // 获取字段的注解,判断是否要忽略字段转化为xml
                RouteLabelAnnotation fieldRouteAnnotation = field.getAnnotation(RouteLabelAnnotation.class);
                if (fieldRouteAnnotation != null && !fieldRouteAnnotation.include()) {
                    continue;
                }
                // 查询是否有WayLinesField 注解,若存在 WayLinesField 注解则直接忽略该字段
                TemplateField wayLinesFieldAnn = field.getAnnotation(TemplateField.class);
                if (wayLinesFieldAnn != null) {
                    continue;
                }
                field.setAccessible(Boolean.TRUE);
                Object value = field.get(obj);
                if (value == null) {
                    continue;
                }
                // 如果字段是一个简单类型（如String, Integer等），直接设置其文本
                if (value instanceof String || value instanceof Integer || value instanceof Long ||
                        value instanceof Double || value instanceof Float || value instanceof Short) {
                    String fieldLabelName = getLabelName(field);
                    fieldLabelName = !fieldLabelName.contains(RouteDocumentConstant.ROUTE_SEPARATOR_STR) ? RouteDocumentConstant.ROUTE_DOCUMENT_WPML_LABEL + RouteDocumentConstant.ROUTE_SEPARATOR_STR + fieldLabelName : fieldLabelName;
                    Element fieldElement = rootElement.addElement(fieldLabelName);
                    fieldElement.setText(value.toString());
                    continue;
                }
                if (value instanceof List<?> list) {
                    list.forEach(o -> convertWayLineXml(rootElement, o));
                    continue;
                }
                convertWayLineXml(rootElement, value);
            } catch (Exception e) {
                log.error("访问对象字段出现异常，异常信息为：{}", e.getMessage());
                log.error(ExceptionUtils.getStackTrace(e));
            }
        }
    }
    public void convertWayLineXmlModel(Element parentElement, Object obj) {
        Element rootElement = parentElement.addElement(getLabelName(obj.getClass()));
        if (isIgnoreChild(obj)) {
            if (obj instanceof Point) {
                Element coordinatesElement = rootElement.addElement("coordinates");
                coordinatesElement.addText(obj.toString());
                return;
            }
            rootElement.setText(obj.toString());
            return;
        }

        List<Field> fields = getAllFields(obj);
        for (Field field : fields) {
            try {
                // 获取字段的注解,判断是否要忽略字段转化为xml
                RouteLabelAnnotation fieldRouteAnnotation = field.getAnnotation(RouteLabelAnnotation.class);
                if (fieldRouteAnnotation != null && !fieldRouteAnnotation.include()) {
                    continue;
                }
                // 查询是否有TemplateModelField 注解,若存在 TemplateModelField注解则直接忽略该字段
                TemplateModelField templateModelField = field.getAnnotation(TemplateModelField.class);
                if (templateModelField != null) {
                    continue;
                }
                // 忽略@NoModelField注解的字段
                NoModelField noModelFieldAnn = field.getAnnotation(NoModelField.class);
                if (noModelFieldAnn != null) {
                    continue;
                }
                field.setAccessible(Boolean.TRUE);
                Object value = field.get(obj);
                if (value == null) {
                    continue;
                }
                // 如果字段是一个简单类型（如String, Integer等），直接设置其文本
                if (value instanceof String || value instanceof Integer || value instanceof Long ||
                        value instanceof Double || value instanceof Float || value instanceof Short) {
                    String fieldLabelName = getLabelName(field);
                    fieldLabelName = !fieldLabelName.contains(RouteDocumentConstant.ROUTE_SEPARATOR_STR) ? RouteDocumentConstant.ROUTE_DOCUMENT_WPML_LABEL + RouteDocumentConstant.ROUTE_SEPARATOR_STR + fieldLabelName : fieldLabelName;
                    Element fieldElement = rootElement.addElement(fieldLabelName);
                    fieldElement.setText(value.toString());
                    continue;
                }
                if (value instanceof List<?> list) {
                    list.forEach(o -> convertWayLineXmlModel(rootElement, o));
                    continue;
                }
                convertWayLineXmlModel(rootElement, value);
            } catch (Exception e) {
                log.error("访问对象字段出现异常，异常信息为：{}", e.getMessage());
                log.error(ExceptionUtils.getStackTrace(e));
            }
        }
    }

    public void convertTemplateXml(Element parentElement, Object obj) {
        Element rootElement = parentElement.addElement(getLabelName(obj.getClass()));

        if (isIgnoreChild(obj)) {
            if (obj instanceof Polygon) {
                Element coordinatesElement = rootElement.addElement("coordinates");
                coordinatesElement.addText(obj.toString());
                return;
            }
            rootElement.setText(obj.toString());
            return;
        }

        List<Field> fields = getAllFields(obj);
        for (Field field : fields) {
            try {
                // 获取字段的注解,判断是否要忽略字段转化为xml
                RouteLabelAnnotation fieldRouteAnnotation = field.getAnnotation(RouteLabelAnnotation.class);
                if (fieldRouteAnnotation != null && !fieldRouteAnnotation.include()) {
                    continue;
                }
                // 查询是否有WayLinesField 注解,若存在 WayLinesField 注解则直接忽略该字段
                WayLinesField wayLinesFieldAnn = field.getAnnotation(WayLinesField.class);
                if (wayLinesFieldAnn != null) {
                    continue;
                }
                // 忽略序列化的静态字段
                if (Modifier.isStatic(field.getModifiers()) || Modifier.isTransient(field.getModifiers())) {
                    continue;
                }
                field.setAccessible(Boolean.TRUE);
                Object value = field.get(obj);
                if (value == null) {
                    continue;
                }
                // 如果字段是一个简单类型（如String, Integer等），直接设置其文本
                if (value instanceof String || value instanceof Integer || value instanceof Long ||
                        value instanceof Double || value instanceof Float || value instanceof Short) {
                    String fieldLabelName = getLabelName(field);
                    fieldLabelName = !fieldLabelName.contains(RouteDocumentConstant.ROUTE_SEPARATOR_STR) ? RouteDocumentConstant.ROUTE_DOCUMENT_WPML_LABEL + RouteDocumentConstant.ROUTE_SEPARATOR_STR + fieldLabelName : fieldLabelName;
                    Element fieldElement = rootElement.addElement(fieldLabelName);
                    fieldElement.setText(value.toString());
                    continue;
                }
                if (value instanceof List<?> list) {
                    list.forEach(o -> convertTemplateXml(rootElement, o));
                    continue;
                }
                convertTemplateXml(rootElement, value);
            } catch (Exception e) {
                log.error("访问对象字段出现异常，异常信息为：{}", e.getMessage());
                log.error(ExceptionUtils.getStackTrace(e));
            }
        }
    }
    public void convertTemplateXmlModel(Element parentElement, Object obj) {
        Element rootElement = parentElement.addElement(getLabelName(obj.getClass()));

        if (isIgnoreChild(obj)) {
            if (obj instanceof Polygon) {
                Element coordinatesElement = rootElement.addElement("outerBoundaryIs");
                Element linearRingElement = coordinatesElement.addElement("LinearRing");
                Element coordinates = linearRingElement.addElement("coordinates");
               coordinates.addText(obj.toString());
                return;
            }
            rootElement.setText(obj.toString());
            return;
        }

        List<Field> fields = getAllFields(obj);
        for (Field field : fields) {
            try {
                // 忽略@NoModelField注解的字段
                NoModelField noModelFieldAnn = field.getAnnotation(NoModelField.class);
                if (noModelFieldAnn != null) {
                    continue;
                }

                // 忽略@WayLinesModelField注解的字段
                WayLinesModelField wayLinesModelFieldAnn = field.getAnnotation(WayLinesModelField.class);
                if (wayLinesModelFieldAnn != null) {
                    continue;
                }

                // 获取字段的注解,判断是否要忽略字段转化为xml
                RouteLabelAnnotation fieldRouteAnnotation = field.getAnnotation(RouteLabelAnnotation.class);
                if (fieldRouteAnnotation != null && !fieldRouteAnnotation.include()) {
                    continue;
                }
                field.setAccessible(Boolean.TRUE);
                Object value = field.get(obj);
                if (value == null) {
                    continue;
                }
                // 如果字段是一个简单类型（如String, Integer等），直接设置其文本
                if (value instanceof String || value instanceof Integer || value instanceof Long ||
                        value instanceof Double || value instanceof Float || value instanceof Short) {
                    String fieldLabelName = getLabelName(field);
                    fieldLabelName = !fieldLabelName.contains(RouteDocumentConstant.ROUTE_SEPARATOR_STR) ? RouteDocumentConstant.ROUTE_DOCUMENT_WPML_LABEL + RouteDocumentConstant.ROUTE_SEPARATOR_STR + fieldLabelName : fieldLabelName;
                    Element fieldElement = rootElement.addElement(fieldLabelName);
                    fieldElement.setText(value.toString());
                    continue;
                }
                if (value instanceof List<?> list) {
                    list.forEach(o -> convertTemplateXmlModel(rootElement, o));
                    continue;
                }
                convertTemplateXmlModel(rootElement, value);
            } catch (Exception e) {
                log.error("访问对象字段出现异常，异常信息为：{}", e.getMessage());
                log.error(ExceptionUtils.getStackTrace(e));
            }
        }
    }

    private boolean isIgnoreChild(Object obj) {
        Class<?> clazz = obj.getClass();
        IgnoreChildAnnotation ignoreAnnotation = clazz.getAnnotation(IgnoreChildAnnotation.class);
        return ignoreAnnotation != null;
    }


    private List<Field> getAllFields(Object obj) {
        List<Field> fields = new ArrayList<>();
        Class<?> clazz = obj.getClass();
        while (clazz != null) {
            Field[] declaredFields = clazz.getDeclaredFields();
            fields.addAll(Arrays.asList(declaredFields));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }


    private String getLabelName(Field field) {
        RouteLabelAnnotation fieldRouteAnnotation = field.getAnnotation(RouteLabelAnnotation.class);
        String labelName = fieldRouteAnnotation != null && !StringUtils.isEmpty(fieldRouteAnnotation.label()) ? fieldRouteAnnotation.label() : field.getName();
        return fieldRouteAnnotation != null && !StringUtils.isEmpty(fieldRouteAnnotation.prefix()) ? fieldRouteAnnotation.prefix() + RouteDocumentConstant.ROUTE_SEPARATOR_STR + labelName : labelName;
    }

    private String getLabelName(Class<?> clazz) {
        RouteLabelAnnotation clazzRouteAnnotation = clazz.getAnnotation(RouteLabelAnnotation.class);
        String labelName = clazzRouteAnnotation != null && !StringUtils.isEmpty(clazzRouteAnnotation.label()) ? clazzRouteAnnotation.label() : clazz.getName();
        return clazzRouteAnnotation != null && !StringUtils.isEmpty(clazzRouteAnnotation.prefix()) ? clazzRouteAnnotation.prefix() + RouteDocumentConstant.ROUTE_SEPARATOR_STR + labelName : labelName;
    }

    private String getBasicLabelPrefix(Class<?> clazz) {
        RouteLabelAnnotation clazzRouteAnnotation = clazz.getAnnotation(RouteLabelAnnotation.class);
        if (clazzRouteAnnotation == null) {
            return null;
        }
        return clazzRouteAnnotation.prefix();
    }

    /**
     * 进行xml格式化并且返回格式化的xml
     *
     * @param rootElement
     * @return
     */
    public String formatXml(Document rootElement) {
        try {
            OutputFormat format = OutputFormat.createPrettyPrint();
            format.setIndent(true);   // 启用缩进
            format.setNewlines(true); // 启用换行

            // 使用 StringWriter 将格式化后的 XML 输出为字符串
            StringWriter stringWriter = new StringWriter();
            XMLWriter writer = new XMLWriter(stringWriter, format);
            writer.write(rootElement);
            writer.close();
            String xml = stringWriter.toString().replaceAll("<Document\\s+xmlns=\"\"", "<Document");
            return cleanUpXml(xml);
        } catch (Exception e) {
            log.error("[格式化xml异常],异常信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
            throw new RouteXmlFormatException(e);
        }
    }

    public String cleanUpXml(String xml) {
        if (xml == null || xml.isEmpty()) {
            return xml;
        }
        // 移除XML声明后的空行
        String[] lines = xml.split("\n");
        StringBuilder cleanedXml = new StringBuilder();

        boolean afterDeclaration = false;
        for (String line : lines) {
            if (line.trim().startsWith("<?xml")) {
                cleanedXml.append(line).append("\n");
                afterDeclaration = true;
            } else if (afterDeclaration && line.trim().isEmpty()) {
                // 跳过XML声明后的第一个空行
                afterDeclaration = false;
            } else {
                cleanedXml.append(line).append("\n");
            }
        }

        return cleanedXml.toString();
    }

    public Element addKmlNameSpace(Document document) {
        Element kmlElement = document.addElement(RouteDocumentConstant.ROUTE_DOCUMENT_ROOT_NODE_LABEL);
        kmlElement.addNamespace(RouteDocumentConstant.ROUTE_EMPTY_STR, RouteDocumentConstant.ROUTE_DOCUMENT_KML_LABEL_NAMESPACE);
        kmlElement.addNamespace(RouteDocumentConstant.ROUTE_DOCUMENT_WPML_LABEL, RouteDocumentConstant.ROUTE_DOCUMENT_WPML_LABEL_DJI_NAMESPACE);
        return kmlElement;
    }


    public String createRouteTemplateXml(WpmlRouteInfo wpmlRouteInfo,Integer routeType) {

        Document document = DocumentHelper.createDocument();
        Element kmlElement = this.addKmlNameSpace(document);

        // 将对象转化为xml并格式化
        if (RouteTypeEnum.isModelingRoute(routeType)){
            // template模板文件只填充一个folder标签和一个placemark标签
            List<Folder> folders = wpmlRouteInfo.getFolders();
            List<Folder> newFolders = new ArrayList<>();
            if (!CollectionUtil.isEmpty(folders)) {
                Folder folder = folders.get(0);
                List<Placemark> placemarks = new ArrayList<>();
                placemarks.add(folders.get(0).getPlacemarks().get(0));
                folder.setPlacemarks(placemarks);
                newFolders.add(folder);
                wpmlRouteInfo.setFolders(newFolders);
            }
            this.convertTemplateXmlModel(kmlElement, wpmlRouteInfo);
        }else {
            this.convertTemplateXml(kmlElement, wpmlRouteInfo);
        }
        return this.formatXml(document);
    }

    public String createRouteWayLinesXml(WpmlRouteInfo wpmlRouteInfo,Integer routeType) {

        Document document = DocumentHelper.createDocument();
        Element kmlElement = this.addKmlNameSpace(document);

        // 将对象转化为xml并格式化
        if (RouteTypeEnum.isModelingRoute(routeType)){
            this.convertWayLineXmlModel(kmlElement, wpmlRouteInfo);
        }else {
            this.convertWayLineXml(kmlElement, wpmlRouteInfo);
        }
        return this.formatXml(document);
    }


    public <T> T xmlToBean(String xml, Class<T> clazz) {
        try {
            ObjectMapper xmlMapper = new XmlMapper();
            xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return clazz.cast(xmlMapper.readValue(xml, clazz));
        } catch (Exception e) {
            log.error("[xml 转化 bean出现异常]，异常信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
        }
        return null;
    }
}
