package com.xinkongan.cloud.framework.kmz.utils;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import com.xinkongan.cloud.framework.kmz.constant.RouteDocumentConstant;
import com.xinkongan.cloud.framework.kmz.dto.ImportRouteInfo;
import com.xinkongan.cloud.framework.kmz.dto.WpmlRouteInfo;
import com.xinkongan.cloud.framework.kmz.exception.RouteGenerateException;
import com.xinkongan.cloud.framework.kmz.exception.RouteLoadException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;


/**
 * <AUTHOR>
 * @date: 2024-11-04
 */

@Slf4j
public class KmzUtils {

    public static final XmlUtils xmlUtils = new XmlUtils();


    /**
     * 生成航线数据到指定路径
     *
     * @param wpmlRouteInfo 航线信息
     * @param path          指定路径，例如：/data/route/tempRoute.kmz
     */
    public static void buildWpmlRouteInfoToFile(WpmlRouteInfo wpmlRouteInfo, String path) {
        byte[] kmzBytes = KmzUtils.buildWpmlRouteInfoToBytes(wpmlRouteInfo, 3);
        FileUtil.writeBytes(kmzBytes, path);
    }


    public static byte[] buildWpmlRouteInfoToBytes(WpmlRouteInfo wpmlRouteInfo,Integer routeType) {

        // 构建 waylines.wpml 文件内容
        String wayLinesXml = xmlUtils.createRouteWayLinesXml(wpmlRouteInfo,routeType);
        // 构建template.kml 文件内容
        String templateXml = xmlUtils.createRouteTemplateXml(wpmlRouteInfo,routeType);

        // 创建临时目录
        File tempDir = RouteFileUtils.createTempDir();
        try {
            // 在临时文件夹下创建wpmz文件夹
            File wpmzFile = FileUtil.mkdir(tempDir.getPath() + "/" + RouteDocumentConstant.ROUTE_ZIP_FILE_DIR);

            FileUtil.writeUtf8String(templateXml, wpmzFile.getPath() + "/" + RouteDocumentConstant.ROUTE_TEMPLATE_NAME);
            FileUtil.writeUtf8String(wayLinesXml, wpmzFile.getPath() + "/" + RouteDocumentConstant.ROUTE_WAYLINES_NAME);

            // 压缩为zip文件并读取字节流
            File routeZipFile = ZipUtil.zip(tempDir);
            byte[] routeBytes = FileUtil.readBytes(routeZipFile);

            // 删除压缩后的文件
            FileUtil.del(routeZipFile);
            return routeBytes;
        } catch (Exception e) {
            log.error("[航线文件构建异常]，异常信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
            throw new RouteGenerateException(e);
        } finally {
            FileUtil.del(tempDir);
        }
    }


    /**
     * 导入归档的kmz文档信息生成wpml信息
     *
     * @param path     路径或者url
     * @param fileName 读取xml的文件名
     * @return wpml航线信息
     */
    public static ImportRouteInfo loadKmzToWpmlRouteInfo(String path, String fileName) {
        // 将url读取为字节流
        try {
            byte[] kmzBytes = RouteFileUtils.readFile(path);
            // 解析为kmz的xml文件
            ImportRouteInfo importRouteInfo = null;
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(kmzBytes);
            ZipInputStream zipInputStream = new ZipInputStream(byteArrayInputStream);
            ZipEntry entry;
            while ((entry = zipInputStream.getNextEntry()) != null) {
                String entryName = entry.getName();
                if (entryName.contains(fileName)) {
                    byte[] templateBytes = RouteFileUtils.readZipFile(zipInputStream);
                    importRouteInfo = xmlUtils.xmlToBean(new String(templateBytes), ImportRouteInfo.class);
                    zipInputStream.closeEntry();
                    break;
                }
                zipInputStream.closeEntry();
            }
            return importRouteInfo;
        } catch (Exception e) {
            log.error("[读取kmz文件流错误]，错误信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
            throw new RouteLoadException(e);
        }
    }

    /**
     * 完整解析KMZ文件，合并template.kml和waylines.wpml的数据
     *
     * @param path KMZ文件路径或URL
     * @return 完整的WpmlRouteInfo对象
     */
    public static WpmlRouteInfo loadCompleteKmzToWpmlRouteInfo(String path) {
        try {
            // 分别解析template.kml和waylines.wpml文件
            ImportRouteInfo templateInfo = loadKmzToWpmlRouteInfo(path, RouteDocumentConstant.ROUTE_TEMPLATE_NAME);
            ImportRouteInfo waylinesInfo = loadKmzToWpmlRouteInfo(path, RouteDocumentConstant.ROUTE_WAYLINES_NAME);

            if (templateInfo == null || templateInfo.getWpmlRouteInfo() == null) {
                throw new RouteLoadException("无法解析template.kml文件");
            }

            if (waylinesInfo == null || waylinesInfo.getWpmlRouteInfo() == null) {
                throw new RouteLoadException("无法解析waylines.wpml文件");
            }

            // 合并两个WpmlRouteInfo对象的数据
            return mergeWpmlRouteInfo(templateInfo.getWpmlRouteInfo(), waylinesInfo.getWpmlRouteInfo());

        } catch (Exception e) {
            log.error("[完整解析kmz文件错误]，错误信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
            throw new RouteLoadException(e);
        }
    }

    /**
     * 合并template.kml和waylines.wpml解析出的WpmlRouteInfo数据
     * template.kml包含：@TemplateField 和 @TemplateModelField 注解的字段
     * waylines.wpml包含：@WayLinesModelField 注解的字段
     *
     * @param templateInfo template.kml解析的数据
     * @param waylinesInfo waylines.wpml解析的数据
     * @return 合并后的完整WpmlRouteInfo对象
     */
    private static WpmlRouteInfo mergeWpmlRouteInfo(WpmlRouteInfo templateInfo, WpmlRouteInfo waylinesInfo) {
        // 以templateInfo为基础，补充waylinesInfo中的数据
        WpmlRouteInfo mergedInfo = WpmlRouteInfo.builder()
                .author(templateInfo.getAuthor())
                .createTime(templateInfo.getCreateTime())
                .updateTime(templateInfo.getUpdateTime())
                .missionConfig(templateInfo.getMissionConfig())
                .build();

        // 合并Folders数据
        List<Folder> mergedFolders = mergeFolders(templateInfo.getFolders(), waylinesInfo.getFolders());
        mergedInfo.setFolders(mergedFolders);

        return mergedInfo;
    }
}
