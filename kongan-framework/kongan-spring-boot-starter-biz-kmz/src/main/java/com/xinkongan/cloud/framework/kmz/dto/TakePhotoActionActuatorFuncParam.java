package com.xinkongan.cloud.framework.kmz.dto;

import com.xinkongan.cloud.framework.kmz.annotation.RouteLabelAnnotation;
import com.xinkongan.cloud.framework.kmz.constant.RouteDocumentConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * takePhoto 动作
 */
@Data
@RouteLabelAnnotation(label = "actionActuatorFuncParam",prefix = RouteDocumentConstant.ROUTE_DOCUMENT_WPML_LABEL)
public class TakePhotoActionActuatorFuncParam extends ActionActuatorFuncParam {

    @Schema(description = "负载挂载位置")
    private Integer payloadPositionIndex = 0;

    /**
     * 为生成媒体文件命名时将额外附带该后缀。
     */
    @Schema(description = "拍摄照片文件后缀")
    private String fileSuffix;

    /**
     * 0：不使用全局设置
     * 1：使用全局设置
     */
    @Schema(description = "是否使用全局存储类型")
    private Integer useGlobalPayloadLensIndex = 0;

    /**
     * <p>zoom: 存储变焦镜头拍摄照片</p>
     * <p>wide: 存储广角镜头拍摄照片</p>
     * <p> ir: 存储红外镜头拍摄照片</p>
     * <p>narrow_band: 存储窄带镜头拍摄照片</p>
     * <p>visable：可见光照片</p>
     * <p>存储多个镜头照片，格式如“<wpml:payloadLensIndex>wide,ir,narrow_band</wpml:payloadLensIndex>”表示同时使用广角、红外和窄带镜头</p>
     */
    @Schema(description = "拍摄照片存储类型")
    private String payloadLensIndex;

}
