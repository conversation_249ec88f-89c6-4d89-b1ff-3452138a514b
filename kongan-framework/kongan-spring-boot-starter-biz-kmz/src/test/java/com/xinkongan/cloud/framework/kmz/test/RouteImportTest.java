package com.xinkongan.cloud.framework.kmz.test;

import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.framework.kmz.constant.RouteDocumentConstant;
import com.xinkongan.cloud.framework.kmz.dto.ImportRouteInfo;
import com.xinkongan.cloud.framework.kmz.dto.WpmlRouteInfo;
import com.xinkongan.cloud.framework.kmz.utils.KmzUtils;
import org.junit.jupiter.api.Test;

/**
 * 航线导入功能测试
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-03
 */
public class RouteImportTest {

    /**
     * 测试完整解析KMZ文件功能
     */
    @Test
    public void testLoadCompleteKmzToWpmlRouteInfo() {
        String kmzUrl = "https://xka-ronghe.oss-cn-shanghai.aliyuncs.com/fly_route/1392536385093632/2024/11/7/66a970ea492c4ac28ca1b714969a96be.kmz";
        
        try {
            // 测试完整解析功能
            WpmlRouteInfo completeWpmlInfo = KmzUtils.loadCompleteKmzToWpmlRouteInfo(kmzUrl);
            
            System.out.println("=== 完整解析结果 ===");
            System.out.println(JSONObject.toJSONString(completeWpmlInfo, true));
            
            // 验证数据完整性
            if (completeWpmlInfo != null) {
                System.out.println("\n=== 数据验证 ===");
                System.out.println("作者: " + completeWpmlInfo.getAuthor());
                System.out.println("创建时间: " + completeWpmlInfo.getCreateTime());
                System.out.println("更新时间: " + completeWpmlInfo.getUpdateTime());
                
                if (completeWpmlInfo.getMissionConfig() != null) {
                    System.out.println("任务配置存在: ✓");
                    System.out.println("起飞点: " + completeWpmlInfo.getMissionConfig().getTakeOffRefPoint());
                    System.out.println("飞行器信息: " + completeWpmlInfo.getMissionConfig().getDroneInfo());
                }
                
                if (completeWpmlInfo.getFolders() != null && !completeWpmlInfo.getFolders().isEmpty()) {
                    System.out.println("Folders数量: " + completeWpmlInfo.getFolders().size());
                    completeWpmlInfo.getFolders().forEach(folder -> {
                        System.out.println("  - 模板类型: " + folder.getTemplateType());
                        System.out.println("  - 模板ID: " + folder.getTemplateId());
                        System.out.println("  - 航线ID: " + folder.getWaylineId());
                        System.out.println("  - 执行高度模式: " + folder.getExecuteHeightMode());
                        System.out.println("  - 距离: " + folder.getDistance());
                        System.out.println("  - 时长: " + folder.getDuration());
                        if (folder.getPlacemarks() != null) {
                            System.out.println("  - 航点数量: " + folder.getPlacemarks().size());
                        }
                    });
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试分别解析template.kml和waylines.wpml
     */
    @Test
    public void testSeparateParseKmzFiles() {
        String kmzUrl = "https://xka-ronghe.oss-cn-shanghai.aliyuncs.com/fly_route/1392536385093632/2024/11/7/66a970ea492c4ac28ca1b714969a96be.kmz";
        
        try {
            // 分别解析两个文件
            ImportRouteInfo templateInfo = KmzUtils.loadKmzToWpmlRouteInfo(kmzUrl, RouteDocumentConstant.ROUTE_TEMPLATE_NAME);
            ImportRouteInfo waylinesInfo = KmzUtils.loadKmzToWpmlRouteInfo(kmzUrl, RouteDocumentConstant.ROUTE_WAYLINES_NAME);
            
            System.out.println("=== Template.kml 解析结果 ===");
            if (templateInfo != null && templateInfo.getWpmlRouteInfo() != null) {
                System.out.println(JSONObject.toJSONString(templateInfo.getWpmlRouteInfo(), true));
            }
            
            System.out.println("\n=== Waylines.wpml 解析结果 ===");
            if (waylinesInfo != null && waylinesInfo.getWpmlRouteInfo() != null) {
                System.out.println(JSONObject.toJSONString(waylinesInfo.getWpmlRouteInfo(), true));
            }
            
            // 对比分析
            System.out.println("\n=== 数据分析 ===");
            if (templateInfo != null && templateInfo.getWpmlRouteInfo() != null) {
                WpmlRouteInfo templateWpml = templateInfo.getWpmlRouteInfo();
                System.out.println("Template文件包含:");
                System.out.println("  - 作者: " + templateWpml.getAuthor());
                System.out.println("  - 任务配置: " + (templateWpml.getMissionConfig() != null ? "✓" : "✗"));
                System.out.println("  - Folders数量: " + (templateWpml.getFolders() != null ? templateWpml.getFolders().size() : 0));
            }
            
            if (waylinesInfo != null && waylinesInfo.getWpmlRouteInfo() != null) {
                WpmlRouteInfo waylinesWpml = waylinesInfo.getWpmlRouteInfo();
                System.out.println("Waylines文件包含:");
                System.out.println("  - 作者: " + waylinesWpml.getAuthor());
                System.out.println("  - 任务配置: " + (waylinesWpml.getMissionConfig() != null ? "✓" : "✗"));
                System.out.println("  - Folders数量: " + (waylinesWpml.getFolders() != null ? waylinesWpml.getFolders().size() : 0));
                if (waylinesWpml.getFolders() != null && !waylinesWpml.getFolders().isEmpty()) {
                    waylinesWpml.getFolders().forEach(folder -> {
                        System.out.println("    - 航线ID: " + folder.getWaylineId());
                        System.out.println("    - 执行高度模式: " + folder.getExecuteHeightMode());
                        if (folder.getPlacemarks() != null) {
                            System.out.println("    - 航点数量: " + folder.getPlacemarks().size());
                        }
                    });
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        RouteImportTest test = new RouteImportTest();
        System.out.println("开始测试航线导入功能...\n");
        
        test.testLoadCompleteKmzToWpmlRouteInfo();
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        test.testSeparateParseKmzFiles();
        
        System.out.println("\n测试完成！");
    }
}
