package com.xinkongan.cloud.framework.job.core.powerjob.handler;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.job.core.exceptions.JobErrorCodeConstants;
import com.xinkongan.cloud.framework.job.core.powerjob.event.JobFailEvent;
import com.xinkongan.cloud.framework.job.core.powerjob.event.JobSuccessEvent;
import com.xinkongan.cloud.framework.job.core.powerjob.scheduler.SchedulerManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import tech.powerjob.common.response.JobInfoDTO;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.util.HashMap;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 定时任务统一调度执行器
 * 基础 Job 调用者，负责调用 {@link JobHandler#executeHandler(String)} 执行任务<br/>
 * ① 系统中所有定时任务都实现 {@link JobHandler}接口，由统一调度执行器调用<br/>
 * ② api创建的定时任务使用 {@link JobParam} 注入参数<br/>
 * ③ 控制面板创建的 按照 {“id”: xxx, "handler":"Xxxxx", "data": Xxxx } 的格式创建参数 其中参数对应{@link JobParam}<br/>
 *
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnProperty(prefix = "powerjob.worker", name = "enabled", havingValue = "true")
public class PowerJobHandlerInvoker implements BasicProcessor {

    @Resource
    private SchedulerManager schedulerManager;

    @Resource
    private ApplicationContext applicationContext;


    /**
     * 定时任务统一调度执行器
     *
     * @param taskContext 任务上下文，可通过 jobParams 和 instanceParams 分别获取控制台参数和OpenAPI传递的任务实例参数
     */
    @Override
    public ProcessResult process(TaskContext taskContext) {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        Long jobId = taskContext.getJobId();
        try {
            // 将日志注入上下文
            PowerJobLoggerContext.saveOmsLogger(omsLogger);
            String jobParams = taskContext.getJobParams();

            // 手动触发定时任务时需要将定时任务的参数查询出来注入上下文
            if (taskContext.getJobParams() == null) {
                JobInfoDTO jobInfoDTO = schedulerManager.fetchJob(jobId);
                if (jobInfoDTO == null) {
                    // 任务不存在
                    log.error("任务不存在:{}", JSONUtil.toJsonStr(taskContext));
                }
                jobParams = jobInfoDTO.getJobParams();
            }

            JobParam jobParam = JSONUtil.toBean(jobParams, JobParam.class);
            String handler = jobParam.getHandler();
            jobParams = JSONUtil.toJsonStr(jobParam);
            taskContext.setJobParams(jobParams);
            omsLogger.info("开始执行定时任务:{}", jobId);
            // 执行任务
            ExecuteJobResult executeJobResult = this.executeInternal(handler, taskContext);
            if (!executeJobResult.getSuccess()) {
                // 更新任务状态为失败
                HashMap<String, Object> failDataMap = new HashMap<>();
                failDataMap.put(JobFailEvent.ID, jobId);
                failDataMap.put(JobFailEvent.MESSAGE, executeJobResult.getMessage());
                // 更新任务状态为失败
                applicationContext.publishEvent(new JobFailEvent(failDataMap));
                throw exception(JobErrorCodeConstants.EXECUTE_JOB_ERROR);
            }
            omsLogger.info("定时任务执行完成!");
            // 更新任务状态为成功
            HashMap<String, Object> successDataMap = new HashMap<>();
            successDataMap.put(JobSuccessEvent.ID, jobId);
            applicationContext.publishEvent(new JobSuccessEvent(successDataMap));
            // 结束任务
            return new ProcessResult(true, executeJobResult.getMessage());
        } catch (Exception e) {
            HashMap<String, Object> failDataMap = new HashMap<>();
            failDataMap.put(JobFailEvent.ID, jobId);
            failDataMap.put(JobFailEvent.MESSAGE, e.getMessage());
            // 更新任务状态为失败
            applicationContext.publishEvent(new JobFailEvent(failDataMap));
            //记录错误日志
            omsLogger.error("定时任务执行失败!,{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } finally {
            // 清除上下文中的日志
            PowerJobLoggerContext.clearOmsLogger();
        }
    }

    private ExecuteJobResult executeInternal(String handler, TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        String jobParams = taskContext.getJobParams();
        omsLogger.info("执行器:[{}],执行参数:[{}]", handler, jobParams);
        // 获得 JobHandler 对象
        JobHandler jobHandler = null;
        try {
            jobHandler = applicationContext.getBean(handler, JobHandler.class);
        } catch (BeansException e) {
            log.info("【定时任务】执行器未找到beanName:[{}], e:[{}]", handler, e.getMessage());
            e.printStackTrace();
            return ExecuteJobResult.builder().success(Boolean.FALSE).message(e.getMessage()).build();
        }
        Assert.notNull(jobHandler, "JobHandler 不会为空");
        // 执行任务
        return jobHandler.executeHandler(jobParams);
    }
}
