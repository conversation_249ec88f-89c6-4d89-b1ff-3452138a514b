<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xinkongan.cloud</groupId>
        <artifactId>kongan-module-system</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>kongan-module-system-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，放整个业务系统。
    </description>

    <repositories>
        <repository>
            <id>osgeo</id>
            <name>OSGeo</name>
            <url>https://repo.osgeo.org/repository/release/</url>
        </repository>
    </repositories>

    <dependencies>
        <!-- Spring Cloud 基础 -->

        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-protection</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 业务组件 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- sdk相关 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-dock-sdk</artifactId>
        </dependency>

        <!-- gis相关 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-gis-sdk</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- 导入bootstrap -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-mq</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>com.xingyuv</groupId>
            <artifactId>spring-boot-starter-justauth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId> <!-- 微信登录（公众号） -->
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>  <!-- 微信登录（小程序） -->
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId> <!-- 文件客户端：解决 ftp 连接 -->
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId> <!-- 文件客户端：解决 sftp 连接 -->
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId> <!-- 文件客户端：解决阿里云、腾讯云、minio 等 S3 连接 -->
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId> <!-- 文件客户端：文件类型的识别 -->
        </dependency>

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId> <!-- 邮件 -->
        </dependency>

        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-spring-boot-starter-biz-kmz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-module-websocket-api</artifactId>
            <version>2.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-module-algorithm-api</artifactId>
            <version>2.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xinkongan.cloud</groupId>
            <artifactId>kongan-geo-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>io.agora</groupId>
            <artifactId>authentication</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>io.agora</groupId>
            <artifactId>agora-rest-client-core</artifactId>
            <version>0.2.0</version>
        </dependency>
        <!--geoserver-manager依赖-->
        <dependency>
            <groupId>nl.pdok</groupId>
            <artifactId>geoserver-manager</artifactId>
            <version>1.7.0-pdok2</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jcl-over-slf4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- GeoTools 依赖 -->
        <!-- https://mvnrepository.com/artifact/org.geotools/gt-main -->
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-main</artifactId>
            <version>29.5</version>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-coverage</artifactId>
            <version>29.5</version><!-- 替换 VERSION 为你希望使用的 GeoTools 版本 -->
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-geotiff</artifactId>
            <version>29.5</version><!-- 替换 VERSION 为你希望使用的 GeoTools 版本 -->
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-epsg-hsql</artifactId>
            <version>29.5</version>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
                <configuration>
                    <fork>true</fork>
                    <outputDirectory>${project.basedir}/../../jars</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 删除Jar包-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>custom-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <configuration>
                            <filesets>
                                <fileset>
                                    <directory>${project.basedir}/../../jars</directory>
                                    <includes>
                                        <include>${project.artifactId}.jar</include>
                                    </includes>
                                </fileset>
                            </filesets>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>
