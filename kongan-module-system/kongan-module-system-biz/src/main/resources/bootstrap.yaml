--- #################### 注册中心 + 配置中心相关配置 ####################
spring:
  application:
    name: system-server
  profiles:
    active: @profile.name@
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务

  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      server-addr: @nacos.server-addr@
      discovery:
        namespace: @nacos.namespace@
        group: @nacos.group@
      config:
        namespace: @nacos.namespace@
        group: @nacos.group@
        shared-configs:
          - dataId: common.yaml #加载公用配置
            refresh: true
          - dataId: database-sql.yaml #加载公用配置
            refresh: true
          - dataId: redis.yaml #加载公用配置
            refresh: true
          - dataId: ${spring.application.name}.yaml # 加载【Nacos】的配置
            refresh: true
          - data-id: powerjob.yaml # 加载 PowerJob 的配置
            refresh: true
          - data-id: rocketmq.yaml # 加载rocketmq的配置
            refresh: true
# 日志文件配置。注意，如果 logging.file.name 不放在 bootstrap.yaml 配置文件，而是放在 application.yaml 中，会导致出现 LOG_FILE_IS_UNDEFINED 文件
logging:
  file:
    name: @log.path@/logs/${spring.application.name}/root.log  # 日志文件名，全路径