<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.measure.MeasureTargetMapper">

<!--        /**
     * 查询最大排序
     */
    Integer selectMaxSortByJobId(@Param("jobId") Long jobId);-->
    <select id="selectMaxSortByJobId" resultType="java.lang.Integer">
        SELECT IF(MAX(sort), MAX(sort), 0)
        FROM system_measure_target
        WHERE job_id = #{jobId}
    </select>
</mapper>