<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.dept.DeptMapper">

    <select id="selectDeptNoDefaultAlarmConfig"
            resultType="com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO">
        SELECT *
        FROM system_dept
        WHERE deleted = 0 AND id NOT IN (SELECT dept_id FROM system_alarm_config WHERE deleted = 0 AND default_flag = 1)
    </select>
</mapper>