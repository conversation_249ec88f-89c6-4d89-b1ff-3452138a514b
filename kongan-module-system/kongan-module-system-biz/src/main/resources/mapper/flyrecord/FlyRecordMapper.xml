<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.flyrecord.FlyRecordMapper">

    <select id="detailById"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordDetailRespVO">
        SELECT fr.*, d.name deptName, dock.device_name dockName, dock.height
        FROM system_fly_record fr
                 LEFT JOIN system_dept d ON fr.dept_id = d.id AND d.deleted = 0
                 LEFT JOIN system_dock_device dock ON fr.dock_sn = dock.device_sn AND dock.deleted = 0
        WHERE fr.id = #{id}
          AND fr.deleted = 0
    </select>
    <select id="selectPageWithShare"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordPageRespVO">
        SELECT fr.id,fr.scene,fr.name,fr.take_off_time,fr.dept_id, d.name deptName,fr.job_id jobId,j.name jobName
        FROM system_fly_record fr
        LEFT JOIN system_dept d ON fr.dept_id = d.id AND d.deleted = 0
        LEFT JOIN system_job j ON fr.job_id = j.id AND j.deleted = 0
        WHERE fr.deleted = 0 AND fr.end_flag = 1
        <if test="reqVO.dockSn!=null and reqVO.dockSn.size() > 0">
            AND fr.dock_sn in
            <foreach collection="reqVO.dockSn" item="sn" open="(" separator="," close=")">
                #{sn}
            </foreach>
        </if>
        <if test="reqVO.importantLevel != null and reqVO.importantLevel.size() > 0">
            AND fr.important_level in
            <foreach collection="reqVO.importantLevel" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="reqVO.beginTime!=null">
            AND fr.take_off_time &gt;= #{reqVO.beginTime}
        </if>
        <if test="reqVO.endTime!=null">
            AND fr.take_off_time &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.scene!=null and reqVO.scene.size() > 0">
            AND fr.scene in
            <foreach collection="reqVO.scene" item="scene" open="(" separator="," close=")">
                #{scene}
            </foreach>
        </if>
        <if test="reqVO.searchKey!=null and reqVO.searchKey!=''">
            AND (fr.name LIKE CONCAT('%',#{reqVO.searchKey},'%') OR d.name LIKE
            CONCAT('%',#{reqVO.searchKey},'%'))
        </if>
        <if test="reqVO.excludeJobId!=null">
            AND fr.job_id != #{reqVO.excludeJobId}
        </if>
        ORDER BY fr.take_off_time DESC
    </select>
    <select id="getFlyRecordCount"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordCountRespVO">
        -- 场景类型说明:  0=巡检任务, 1=警情任务, 2=联合行动, 3=建模任务, 4=临时任务
        SELECT COUNT(*)                      AS totalCount,
               SUM(IF(scene_type = 0, 1, 0)) AS inspectionCount,
               SUM(IF(scene_type = 2, 1, 0)) AS alarmCount,
               SUM(IF(scene_type = 1, 1, 0)) AS modelingCount
        FROM system_fly_record
        WHERE deleted = 0;
    </select>
    <select id="getFlyRecordCountByDays"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordCountByTimeRespVO">
        SELECT
        DATE_FORMAT( create_time, '%Y-%m-%d' ) AS date,
        IFNULL( SUM( CASE scene_type WHEN 0 THEN 1 ELSE 0 END ), 0 ) AS inspectionCount,
        IFNULL( SUM( CASE scene_type WHEN 2 THEN 1 ELSE 0 END ), 0 ) AS alarmCount,
        IFNULL( SUM( CASE scene_type WHEN 1 THEN 1 ELSE 0 END ), 0 ) AS modelingCount
        FROM
        system_fly_record WHERE deleted = 0
        <if test="startTime!=null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY date
        ORDER BY date
    </select>
    <select id="getFlyRecordCountByMonths"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordCountByTimeRespVO">
        SELECT
        DATE_FORMAT( create_time, '%Y-%m-01' ) AS date,
        IFNULL( SUM( CASE scene_type WHEN 0 THEN 1 ELSE 0 END ), 0 ) AS inspectionCount,
        IFNULL( SUM( CASE scene_type WHEN 2 THEN 1 ELSE 0 END ), 0 ) AS alarmCount,
        IFNULL( SUM( CASE scene_type WHEN 1 THEN 1 ELSE 0 END ), 0 ) AS modelingCount
        FROM
        system_fly_record WHERE deleted = 0
        <if test="startTime!=null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY date
        ORDER BY date
    </select>
    <select id="selectAfterCountWithShare_mpCount" resultType="java.lang.Integer">
        SELECT COUNT(fr.id)
        FROM system_fly_record fr
                 LEFT JOIN system_dept d ON fr.dept_id = d.id AND d.deleted = 0
        WHERE fr.deleted = 0
          AND fr.end_flag = 1
          AND fr.take_off_time &gt;= #{takeOffTime}
        ORDER BY fr.take_off_time DESC
    </select>
    <select id="relateFlyRecordPage"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.RelateFlyRecordPageRespVO">
        SELECT *, 0 as bindFlag
        FROM system_fly_record fr
        WHERE deleted = 0
          AND end_flag = 1
          AND job_id = #{reqVO.jobId}
        UNION ALL
        SELECT *, 1 as bindFlag
        FROM system_fly_record
        WHERE deleted = 0
          AND end_flag = 1
          AND id in
              (SELECT fly_record_id FROM system_data_bind WHERE job_id = #{reqVO.jobId})
    </select>


    <!--    Double getFlyRecordTotalMaileByDeptId(Long deptId);-->
    <select id="getFlyRecordTotalMaileByDeptId" resultType="java.lang.Double">
        select sum(sfr.flight_mileage) as totalMaile
        from system_fly_record sfr
        where sfr.deleted = 0
          and sfr.dept_id = #{deptId}
    </select>

    <!--    Double getFlyRecordTotalFlyTimeByDeptId(Long deptId);-->
    <select id="getFlyRecordTotalFlyTimeByDeptId" resultType="java.lang.Double">
        select sum(sfr.flight_duration) as totalFlyTime
        from system_fly_record sfr
        where sfr.deleted = 0
          and sfr.dept_id = #{deptId}
    </select>

    <!--        List<FlyRecordRespVO> getFlyRecordPage(@Param("page") Page<FlyRecordRespVO> page,@Param("searchParams") FlyRecordSearchDTO searchParams);-->
    <select id="getFlyRecordPage"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordRespVO">
        SELECT
        fr.id as id,
        fr.name as name,
        fr.take_off_time AS takeOffTime,
        fr.land_time AS landTime,
        fr.task_id AS taskId,
        fr.job_id AS jobId,
        fr.fly_id AS flyId,
        fr.dock_sn AS dockSn,
        fr.drone_sn AS droneSn,
        fr.route_id AS routeId,
        fr.uploaded_file_count AS uploadedFileCount,
        fr.expected_file_count AS expectedFileCount,
        fr.important_level AS importantLevel,
        fr.flight_mileage AS flightMileage,
        fr.flight_duration AS flightDuration,
        fr.take_off_lat AS takeOffLat,
        fr.take_off_lon AS takeOffLon,
        fr.last_lat AS lastLat,
        fr.last_lon AS lastLon,
        fr.scene_type AS sceneType,
        fr.flight_type AS flightType,
        fr.user_id AS userId,
        fr.end_flag AS endFlag,
        fr.file_id AS fileId,
        fr.osd_url AS osdUrl,
        fr.scene as scene
        FROM system_fly_record fr
        WHERE fr.deleted = 0
        and fr.dept_id = #{searchParams.deptId}
        <if test="searchParams.searchKey != null and searchParams.searchKey != ''">
            AND fr.name LIKE CONCAT('%',#{searchParams.searchKey},'%')
        </if>
        <if test="searchParams.startTime!= null">
            AND fr.take_off_time &gt;= #{searchParams.startTime}
        </if>
        <if test="searchParams.endTime!= null">
            AND fr.take_off_time &lt;= #{searchParams.endTime}
        </if>
        ORDER BY fr.take_off_time DESC
    </select>

<!--            List<HistogramRespVO> getHistogramData(@Param("deptId") Long deptId,
                                           @Param("timeFormat") String timeFormat,
                                           @Param("timeRange") LocalDateTime[] timeRange);-->
    <select id="getHistogramData" resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.HistogramRespVO">
        select  sum(case when sfr.scene_type = 0 then 1 else 0 end)       as inspectCount,
                sum(case when sfr.scene_type = 2 then 1 else 0 end)       as alarmCount,
                sum(case when sfr.scene_type !=0 and sfr.scene_type!= 1 then 1 else 0 end)       as otherCount,
                date_format(sfr.create_time,#{timeFormat})           as timePeriod
        from system_fly_record sfr
        where sfr.deleted = 0
          and sfr.dept_id = #{deptId}
          and sfr.create_time >= #{timeRange[0]}
          and sfr.create_time &lt;= #{timeRange[1]}
        group by date_format(sfr.create_time,#{timeFormat});
    </select>
</mapper>