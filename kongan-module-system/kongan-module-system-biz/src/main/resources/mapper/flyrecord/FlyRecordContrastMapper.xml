<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.flyrecord.FlyRecordContrastMapper">

    <select id="page" resultType="com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordContrastDO">
        SELECT frc.*, d.name deptName
        FROM system_fly_record_contrast frc LEFT JOIN system_dept d ON d.id = frc.dept_id AND d.deleted = 0
        WHERE frc.deleted = 0 AND frc.left_fly_record_id = #{reqVO.flyRecordId}
        <if test="isShare!=null and !isShare">
            AND frc.dept_id IN
            <foreach collection="deptList" item="deptId" index="index" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="reqVO.searchKey!=null and reqVO.searchKey!=''">
            AND (frc.name LIKE CONCAT('%', #{reqVO.searchKey}, '%') OR d.name LIKE CONCAT('%', #{reqVO.searchKey}, '%'))
        </if>
        <if test="reqVO.beginTime!=null">
            AND frc.create_time &gt;= #{reqVO.beginTime}
        </if>
        <if test="reqVO.endTime!=null">
            AND frc.create_time &lt;= #{reqVO.endTime}
        </if>
        ORDER BY frc.create_time DESC
    </select>
</mapper>



