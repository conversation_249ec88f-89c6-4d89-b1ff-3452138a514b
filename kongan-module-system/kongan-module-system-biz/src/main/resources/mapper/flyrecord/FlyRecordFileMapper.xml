<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.flyrecord.FlyRecordFileMapper">
    <select id="selectUnImportantFile"
            resultType="com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO">
        SELECT * FROM system_fly_record_file frf INNER JOIN system_fly_record fr ON fr.id = frf.fly_record_id AND
        fr.deleted = 0
        WHERE frf.deleted = 0 AND fr.important_level = 0 AND fr.dept_id = #{deptId}
        -- 删除图片
        <if test="type==1">
            AND (frf.file_type = 1 OR frf.file_type = 2)
        </if>
        -- 删除视频
        <if test="type==2">
            AND frf.file_type = 0
        </if>
        -- 删除图片和视频
        <if test="type==3">
            AND (frf.file_type = 1 OR frf.file_type = 0 OR frf.file_type = 2)
        </if>
          -- 创建时间小于等于最终时间 就删除
        AND fr.create_time &lt;= #{endTime}
    </select>
    <select id="countByFlyIdWithoutException" resultType="java.lang.Integer">
       SELECT COUNT(1) FROM system_fly_record_file WHERE deleted = 0  AND exception_id IS NULL AND fly_record_id IN ( SELECT id FROM system_fly_record WHERE deleted = 0 AND fly_id IN
        <foreach collection="flyId" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>)
    </select>
    <select id="flyRecordMaterialCountMap" resultType="java.util.HashMap">
        SELECT fly_record_id as `key`, SUM(material_count) AS `value`
        FROM (
            -- 统计飞行记录文件表中的素材数量
            SELECT fly_record_id, COUNT(1) as material_count
            FROM system_fly_record_file
            WHERE deleted = 0 AND fly_record_id IN
            <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            GROUP BY fly_record_id

            UNION ALL

            -- 统计异常表中的异常记录数量
            SELECT fly_record_id, COUNT(1) as material_count
            FROM algorithm_exception
            WHERE deleted = 0 AND fly_record_id IN
            <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            GROUP BY fly_record_id
        ) combined_materials
        GROUP BY fly_record_id
    </select>

<!--        Long countExceptionMediaByFlyRecordId(@Param("flyRecordId") Long flyRecordId);-->
    <select id="countExceptionMediaByFlyRecordId" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM algorithm_exception
        WHERE deleted = 0
          and fly_record_id = #{flyRecordId}
    </select>

    <!-- 根据任务ID统计拍摄的照片数量（一个SQL查询优化版本） -->
    <select id="countPhotographByJobIdOptimized" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM system_fly_record_file frf
        INNER JOIN system_fly_record fr ON frf.fly_record_id = fr.id AND fr.deleted = 0
        WHERE frf.deleted = 0
          AND fr.job_id = #{jobId}
          AND frf.file_source = 0  -- 拍摄来源 FileSourceEnum.PHOTOGRAPH
          AND frf.file_type = 1    -- 图片类型 FileTypeEnum.IMAGE
    </select>
</mapper>



