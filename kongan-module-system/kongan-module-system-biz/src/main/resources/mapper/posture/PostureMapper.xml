<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.posture.PostureMapper">

    <select id="mixedJobPage"
            resultType="com.xinkongan.cloud.module.system.controller.admin.posture.dto.MixedJobPageResp">
        SELECT job.`id` AS jobId,
        job.`name` AS jobName,
        job.`description` AS description,
        job.`dept_id` AS deptId,
        job.`exec_time` AS executeTime,
        job.`status` AS STATUS,
        job.`scene` AS scene,
        job.dock_sn AS dockSn,
        dept.`name` AS deptName
        FROM `system_job` AS job
        LEFT JOIN system_dept AS dept ON job.dept_id = dept.id
        WHERE job.deleted = 0
        AND job.scene in (0,1,2,3)
        AND job.dept_id IN
        <foreach item="deptId" collection="param.deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        ORDER BY
        CASE
        WHEN job.status = 2 THEN 0 -- 执行中（2）排在最前面
        ELSE 1 -- 其他状态按正常顺序排列
        END,
        job.exec_time DESC
    </select>
    <select id="jobSceneNumber"
            resultType="com.xinkongan.cloud.module.system.controller.admin.posture.dto.JobSceneNumberVO">
        SELECT
        SUM(CASE WHEN scene = 0 THEN 1 ELSE 0 END) AS inspection,
        SUM(CASE WHEN scene = 1 THEN 1 ELSE 0 END) AS modeling,
        SUM(CASE WHEN scene = 2 THEN 1 ELSE 0 END) AS alarmResponse
        FROM `system_job`
        WHERE deleted = 0
        AND dept_id IN
        <foreach item="deptId" collection="param.deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>
    <select id="jobNumberTrend"
            resultType="com.xinkongan.cloud.module.system.controller.admin.posture.dto.JobNumberTrendVO">
        SELECT
        DATE_FORMAT(create_time, '%Y-%m') AS MONTH,
        SUM(CASE WHEN scene = 0 THEN 1 ELSE 0 END) AS inspectionJobNumber,
        SUM(CASE WHEN scene = 1 THEN 1 ELSE 0 END) AS modelingJobNumber,
        SUM(CASE WHEN scene = 2 THEN 1 ELSE 0 END) AS alarmResponseJobNumber
        FROM
        `system_job`
        WHERE deleted = 0
        AND dept_id IN
        <foreach item="deptId" collection="param.deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        GROUP BY
        DATE_FORMAT(create_time, '%Y-%m')
        ORDER BY
        MONTH;
    </select>
    <select id="deviceRank"
            resultType="com.xinkongan.cloud.module.system.controller.admin.posture.dto.DeviceRankVO">
        SELECT f.dock_sn as deviceSn,
        <if test="param.type != null">
            <if test="param.type == 1">
                SUM(f.flight_duration) as flightTime,
            </if>
            <if test="param.type == 2">
                SUM(f.flight_mileage) as mileage,
            </if>
            <if test="param.type == 3">
                COUNT(f.id) as number,
            </if>
        </if>
        dd.device_name
        FROM `system_fly_record` f
        INNER JOIN `system_dock_device` dd ON f.dock_sn = dd.device_sn
        INNER JOIN `system_dept` d ON dd.dept_id = d.id
        WHERE f.deleted = 0 AND
        dd.deleted = 0 AND
        d.deleted = 0 AND
        f.flight_duration is not null AND
        f.flight_mileage is not null AND
        f.create_time BETWEEN #{param.startTime} AND #{param.endTime} AND
        f.dept_id in
        <foreach item="deptId" collection="param.deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        GROUP BY f.dock_sn
        <if test="param.type != null">
            <if test="param.type == 1">
                ORDER BY flightTime DESC
            </if>
            <if test="param.type == 2">
                ORDER BY mileage DESC
            </if>
            <if test="param.type == 3">
                ORDER BY number DESC
            </if>
        </if>
    </select>
    <select id="deviceFlyStatistics"
            resultType="com.xinkongan.cloud.module.system.controller.admin.posture.dto.DeviceRankVO">
        SELECT
        SUM(f.flight_duration) as flightTime,
        SUM(f.flight_mileage) as mileage,
        COUNT(f.id) as number
        FROM `system_fly_record` f
        WHERE f.deleted = 0 AND
        f.flight_duration is not null AND
        f.flight_mileage is not null AND
        f.create_time BETWEEN #{param.startTime} AND #{param.endTime} AND
        f.dept_id in
        <foreach item="deptId" collection="param.deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>
    <select id="alertListPage"
            resultType="com.xinkongan.cloud.module.system.controller.admin.posture.dto.PostureAlarmListVO">
        select
        a.id as alarmId,
        a.description as description,
        a.name as alarmName,
        a.create_time as createTime,
        c.alarm_scene as alarmSceneName,
        d.name as deptName
        from
        `system_alarm` a
        INNER JOIN `system_dept` d ON a.dept_id = d.id
        left join system_alarm_config c on a.alarm_scene_id = c.id
        where
        a.deleted = 0
        AND d.deleted = 0
        AND a.dept_id in
        <foreach item="deptId" collection="param.deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        order by
        a.create_time desc
    </select>
    <select id="noticeListPage"
            resultType="com.xinkongan.cloud.module.system.controller.admin.posture.dto.PostureNoticeListVO">
        SELECT n.id          as id,
               n.content     as content,
               n.module      as module,
               n.type        as type,
               n.type_desc   as typeDesc,
               n.create_time as createTime
        FROM system_notice_read nr
                 INNER JOIN system_notice n ON nr.notice_id = n.id AND n.deleted = 0
        WHERE nr.deleted = 0
          AND nr.user_id = #{userId}
        order by n.create_time desc
    </select>
</mapper>