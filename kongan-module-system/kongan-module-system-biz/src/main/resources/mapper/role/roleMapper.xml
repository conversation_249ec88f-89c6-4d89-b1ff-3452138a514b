<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.permission.RoleMapper">
<!--        /**
     * 获取数据权限范围内的角色统计数据
     *
     * @return 角色统计数据
     */
    List<RoleStatisticVO> getRoleStatisticInfoByDataPermission();-->
    <select id="getRoleStatisticInfoByDataPermission" resultType="com.xinkongan.cloud.module.system.controller.admin.report.vo.RoleStatisticVO">
        select sr.name      as roleName,
               count(sr.id) as roleCount
        from system_role sr
        where sr.deleted = 0
        group by sr.name
        order by roleCount desc
    </select>
</mapper>