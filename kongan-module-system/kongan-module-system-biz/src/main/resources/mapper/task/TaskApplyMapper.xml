<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.task.TaskApplyMapper">

    <resultMap id="applyMap" type="com.xinkongan.cloud.module.system.dal.dataobject.task.TaskApplyDO">
        <id property="id" column="id" />
        <result property="dockSn" column="dock_sn"/>
        <result property="status" column="status"/>
        <result property="userId" column="user_id"/>
        <result property="userNickname" column="user_nickname"/>
        <result property="userPhoneNumber" column="user_phone_number"/>
        <result property="userDeptId" column="user_dept_id"/>
        <result property="userDeptName" column="user_dept_name"/>
        <result property="reason" column="reason"/>
        <result property="taskId" column="task_id"/>
        <result property="taskExecMode" column="task_exec_mode"/>
        <result property="jobId" column="job_id"/>
        <result property="taskUrgency" column="task_urgency"/>
        <result property="taskName" column="task_name"/>
        <result property="scene" column="scene"/>
        <result property="taskFlyTime" column="task_fly_time"/>
        <result property="taskRouteId" column="task_route_id"/>
        <result property="routeThumbnail" column="route_thumbnail"/>
        <result property="fileIds" column="fileIds" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="taskRouteName" column="task_route_name"/>
        <result property="taskRoutePointInfo" column="task_route_point_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="taskExpectedExecutionTime" column="task_expected_execution_time"/>
        <result property="taskExpectedExecutionMileage" column="task_expected_execution_mileage"/>
        <result property="taskCount" column="task_count"/>
        <result property="taskStartTime" column="task_start_time"/>
        <result property="taskEndTime" column="task_end_time"/>
        <result property="approveId" column="approve_id"/>
        <result property="approveNickname" column="approve_nickname"/>
        <result property="approveTime" column="approve_time"/>
        <result property="approveDeptId" column="approve_dept_id"/>
        <result property="approveDeptName" column="approve_dept_name"/>
        <result property="approvePhoneNumber" column="approve_phone_number"/>
        <result property="approveComments" column="approve_comments"/>
        <result property="scheduleId" column="schedule_id"/>
        <result property="autoApprove" column="auto_approve"/>
        <result property="taskFlyTimeList" column="task_fly_time_list" typeHandler="com.xinkongan.cloud.framework.mybatis.core.type.LocalDateTimeListTypeHandler"/>

    </resultMap>

    <select id="approvalPage" resultMap="applyMap">
        SELECT * FROM system_task_apply t LEFT JOIN system_dept d ON d.deleted = 0 AND t.user_dept_id = d.id
        WHERE t.deleted = 0 AND t.dock_sn = #{pageReqVO.dockSn}
        <if test="pageReqVO!=null">
            <if test="pageReqVO.status!=null">
                AND t.status = #{pageReqVO.status}
            </if>
            <if test="pageReqVO.taskOrDeptName!=null and pageReqVO.taskOrDeptName!=''">
                AND (t.task_name like CONCAT('%',#{pageReqVO.taskOrDeptName},'%') OR d.name like CONCAT('%',#{pageReqVO.taskOrDeptName},'%'))
            </if>
            ORDER BY t.status ASC,t.create_time DESC
        </if>
    </select>
    <select id="applyPage" resultMap="applyMap">
        SELECT * FROM system_task_apply t LEFT JOIN system_dept d ON d.deleted = 0 AND t.user_dept_id = d.id
        WHERE t.deleted = 0 AND t.dock_sn = #{pageReqVO.dockSn}
        <if test="pageReqVO.status!=null">
            AND t.status = #{pageReqVO.status}
        </if>
        <if test="pageReqVO.taskOrDeptName!=null and pageReqVO.taskOrDeptName!=''">
            AND (t.task_name like CONCAT('%',#{pageReqVO.taskOrDeptName},'%') OR d.name like
            CONCAT('%',#{pageReqVO.taskOrDeptName},'%'))
        </if>
        AND t.user_dept_id IN
        <foreach collection="deptList" item="item" open="(" separator="," close=")">#{item}</foreach>
        ORDER BY t.status ASC,t.create_time DESC
    </select>
    <select id="selectAfterCount" resultType="java.lang.Long">
        SELECT t.id FROM system_task_apply t LEFT JOIN system_dept d ON d.deleted = 0 AND t.user_dept_id = d.id
        WHERE t.deleted = 0 AND t.dock_sn = #{dockSn}
        <if test="deptList!=null and deptList.size()>0">
            AND t.user_dept_id IN
            <foreach collection="deptList" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
        ORDER BY t.status,t.task_urgency,t.create_time
    </select>
</mapper>