<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.task.DemandMapper">

    <select id="page"
            resultType="com.xinkongan.cloud.module.system.controller.admin.task.vo.demand.FlyDemandPageRespVO">
        SELECT d.demand_name demandName,d.id id,d.dept_id deptId,sd.name deptName,d.status status,d.inspection_content inspectionContent,
               d.start_time startTime,d.end_time endTime,d.cycle_mode cycleMode,d.cycle_interval cycleInterval,
               d.execute_time executeTime,d.execute_time_list executeTimeList,d.submit_time submitTime,d.description description,d.create_time createTime,
               d.update_time updateTime,d.tenant_id tenantId,d.creator userId,d.updater updater,d.deleted deleted
        FROM system_demand d LEFT JOIN system_dept sd ON d.dept_id = sd.id AND sd.deleted = 0
        WHERE d.deleted = 0
        <if test="reqVO !=null">
            <if test="reqVO.beginTime!=null">
                AND d.create_time &gt;= #{reqVO.beginTime}
            </if>
            <if test="reqVO.endTime!=null">
                AND d.create_time &lt;= #{reqVO.endTime}
            </if>
            <if test="reqVO.demandOrDeptName!=null and reqVO.demandOrDeptName!=''">
                AND (d.demand_name LIKE CONCAT('%',#{reqVO.demandOrDeptName},'%') OR sd.name LIKE
                CONCAT('%',#{reqVO.demandOrDeptName},'%'))
            </if>
            <if test="reqVO.status!=null">
                AND d.status = #{reqVO.status}
            </if>
        </if>
        ORDER BY d.status asc, d.create_time DESC;
    </select>

    <select id="selectByJobId"
            resultType="com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobDetailRespVO">
        SELECT j.task_id taskId,j.id jobId,j.dept_id deptId,d.name deptName,
               j.creator userId,u.nickname, j.name jobName,j.description,j.status,j.exec_mode execMode,j.wayline_precision_type waylinePrecisionType,
               j.auto_break_point autoBreakPoint,j.predict_time predictTime,
               j.dock_sn dockSn,dock.device_name dockName,j.route_id routeId,r.route_name routeName,j.tenant_id tenantId,j.reason,j.create_time
        FROM system_job j
            LEFT JOIN system_dept d ON j.dept_id = d.id AND d.deleted = 0
            LEFT JOIN system_users u ON j.creator = u.id AND u.deleted = 0
            LEFT JOIN system_route r ON j.route_id = r.id AND r.deleted = 0
            LEFT JOIN system_dock_device dock ON j.dock_sn = dock.device_sn AND dock.deleted = 0
        WHERE j.deleted = 0 AND j.id = #{jobId}
    </select>

<!--        /**
     * 查询数据权限范围内任务数目排行榜
     *
     * @param timeRange 时间范围
     * @return top5的结果
     */
    List<TaskTopStatisticVO> getTaskTopStatisticInfo(@Param("timeRange") LocalDate[] timeRange);-->
    <select id="getTaskTopStatisticInfo" resultType="com.xinkongan.cloud.module.system.controller.admin.report.vo.TaskTopStatisticVO">
        select sj.dept_id   as deptId,
               sd.name      as deptName,
               count(sj.id) as taskCount
        from system_job sj
                 left join system_dept sd
                           on sj.dept_id = sd.id and sd.deleted = 0
        where sj.deleted = 0
            and (sj.status = 3 or sj.status = 5)
            <if test="timeRange!=null">
                AND sj.exec_time &gt;= #{timeRange[0]}
            </if>
            <if test="timeRange!=null">
                AND sj.exec_time &lt;= #{timeRange[1]}
            </if>
        group by sj.dept_id
        limit 5
    </select>
    <select id="selectScheduleList"
            resultType="com.xinkongan.cloud.module.system.controller.admin.task.vo.job.ScheduleFlyInfo">
        SELECT jf.id flyId,
        jf.predict_end_time predictEndTime,
        jf.scene,
        fr.id flyRecordId,
        fr.name flyRecordName,
        fr.take_off_time takeOffTime,
        fr.land_time landTime,
        j.id jobId,
        j.name jobName,
        d.name deptName,
        j.status,
        j.dept_id deptId,
        IF(j.scene != 2, j.exec_time, jf.create_time)  execTime,
        j.predict_time predictTime,
        j.wayline_precision_type waylinePrecisionType,
        j.auto_break_point autoBreakPoint,
        j.description,
        j.route_id routeId
        FROM system_job_fly jf
        LEFT JOIN system_fly_record fr ON jf.id = fr.fly_id AND fr.deleted = 0
        LEFT JOIN system_job j ON jf.job_id = j.id AND j.deleted = 0
        LEFT JOIN system_dept d ON j.dept_id = d.id AND d.deleted = 0
        WHERE jf.deleted = 0
        AND jf.dock_sn = #{reqVO.dockSn}
        <if test="reqVO.status!=null and reqVO.status.size()>0">
            AND j.status in
            <foreach collection="reqVO.status" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.startTime!=null">
            AND jf.exec_time &gt;= #{reqVO.startTime}
        </if>
        <if test="reqVO.endTime!=null">
            AND jf.exec_time &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.scene!=null and reqVO.scene.size()>0">
            AND jf.scene in
            <foreach collection="reqVO.scene" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
        AND j.status not in (4,6,7)
        ORDER BY jf.exec_time DESC;

    </select>
    <select id="selectAfterCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM system_job j LEFT JOIN system_dept d ON j.dept_id = d.id AND d.deleted = 0
        WHERE j.deleted = 0
        AND(
        <if test="statusSet!=null and statusSet.size()>0">
            j.status in
            <foreach collection="statusSet" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            or
        </if>
        (j.status = #{status} and j.exec_time &gt;= #{execTime}))
        <if test="scene!=null">
            AND j.scene = #{scene}
        </if>
    </select>
    <select id="listAlarmJob"
            resultType="com.xinkongan.cloud.module.system.controller.admin.alarm.vo.AlarmListRespVO">
        SELECT
        j.alarm_id, j.id, j.name, a.longitude, a.latitude, ac.alarm_scene AS alarmScene, j.status, d.id AS deptId,d.name
        AS deptName, j.create_time
        FROM system_job j
        LEFT JOIN system_alarm a ON j.alarm_id = a.id AND a.deleted = 0
        LEFT JOIN system_alarm_config ac ON a.alarm_scene_id = ac.id AND ac.deleted = 0
        LEFT JOIN system_dept d ON j.dept_id = d.id AND d.deleted = 0
        LEFT JOIN (
        -- 获取每个 job_id 的最后一次飞行时间
        SELECT job_id, MAX(create_time) AS last_fly_time FROM system_job_fly WHERE deleted = 0 GROUP BY job_id
        ) sjf ON j.id = sjf.job_id
        WHERE j.deleted = 0
        AND j.scene = 2
        <if test="reqVO != null and reqVO.status != null and reqVO.status.size() > 0">
            AND j.status IN
            <foreach collection="reqVO.status" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND (
        -- 正在执行中的任务直接返回
        j.status = 2
        -- 创建时间+过期时间 >= 当前时间
        OR DATE_ADD(j.create_time, INTERVAL ac.expire_time HOUR) >= #{reqVO.endTime}
        -- 最后一次飞行的时间+过期时间 >= 当前时间
        OR (sjf.last_fly_time IS NOT NULL AND DATE_ADD(sjf.last_fly_time, INTERVAL ac.expire_time HOUR) >= #{reqVO.endTime})
        )
        ORDER BY j.create_time DESC
    </select>
    <select id="statisticCount"
            resultType="com.xinkongan.cloud.module.system.controller.admin.task.vo.demand.DemandStatisticCountVO">
        SELECT COUNT(*) AS count,           -- 任务总数
               SUM(
                       IF(status = 1 AND ((scene != 2 AND DATE(exec_time) = CURDATE()) OR
                                          (scene = 2 AND DATE(create_time) = CURDATE())), 1, 0)
               )        AS todayWaitExecuted, -- 今日待执行任务数（根据类型分别判断）
               SUM(
                       IF(status = 5 AND DATE(exec_end_time) = CURDATE(), 1, 0)
               )        AS todayExecuted    -- 今日已完成任务数
        FROM system_job
        WHERE deleted = 0;

    </select>
    <select id="statisticCountByType"
            resultType="com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobSceneStatisticCountVO">
        SELECT
        SUM(IF(scene = 0, 1, 0)) AS inspectionCount,
        SUM(IF(scene = 1, 1, 0)) AS modelingCount,
        SUM(IF(scene = 2, 1, 0)) AS alarmCount
        FROM system_job
        WHERE deleted = 0
        <if test="reqVO != null">
            <if test="reqVO.beginTime != null">
                AND (
                (scene != 2 AND exec_time &gt;= #{reqVO.beginTime}) OR
                (scene = 2 AND create_time &gt;= #{reqVO.beginTime})
                )
            </if>
            <if test="reqVO.endTime != null">
                AND (
                (scene != 2 AND exec_time &lt;= #{reqVO.endTime}) OR
                (scene = 2 AND create_time &lt;= #{reqVO.endTime})
                )
            </if>
        </if>
    </select>


<!--            Long computeHistogramData(@Param("histogramSearchInfo") HistogramSearchDTO histogramSearchInfo,
                                       @Param("timeRange") LocalDateTime[] timeRange,
                                       @Param("scene") Integer scene);-->
    <select id="computeHistogramData" resultType="com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.HistogramVO">
        select  sum(case when sj.scene = 0 then 1 else 0 end)       as inspectionCount,
                sum(case when sj.scene = 1 then 1 else 0 end)       as modelingCount,
                sum(case when sj.scene = 2 then 1 else 0 end)       as alarmCount,
                date_format(sj.create_time,#{timeFormat})           as timePeriod
        from system_job sj
            where sj.deleted = 0
            <if test="deptId != null">
                  and sj.dept_id = #{deptId}
            </if>
            <if test="userId != null">
                and sj.creator = #{userId}
            </if>
            and sj.create_time >= #{timeRange[0]}
            and sj.create_time &lt;= #{timeRange[1]}
        group by date_format(sj.create_time,#{timeFormat});
    </select>
    <select id="selectAfterCountForAlarmScene" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM system_job j LEFT JOIN system_dept d ON j.dept_id = d.id AND d.deleted = 0
        WHERE j.deleted = 0 AND j.scene = 2
        AND(
        <if test="statusSet!=null and statusSet.size()>0">
            j.status in
            <foreach collection="statusSet" open="(" separator="," close=")" index="index" item="item">
                #{item}
            </foreach>
            or
        </if>
        (j.status = #{status} and j.create_time &gt;= #{createTime}))
    </select>
    <select id="selectJobIdByalarmId" resultType="java.lang.String">
        select id from system_job sj where sj.deleted = 0 and sj.alarm_id = #{alarmId}
    </select>
</mapper>