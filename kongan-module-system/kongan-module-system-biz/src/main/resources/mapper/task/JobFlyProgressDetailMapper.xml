<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.task.JobFlyProgressDetailMapper">

    <select id="selectDetailByFlyId"
            resultType="com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyProgressDetailDO">
        SELECT *
        FROM system_job_fly_progress_detail pd
                 INNER JOIN system_job_fly_progress p ON pd.fly_progress_id = p.id AND p.deleted = 0
        WHERE pd.deleted = 0 AND p.fly_id = #{flyId}
    </select>
</mapper>



