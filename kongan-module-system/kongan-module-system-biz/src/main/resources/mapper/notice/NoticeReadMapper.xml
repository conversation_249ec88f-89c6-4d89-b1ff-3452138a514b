<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.notice.NoticeReadMapper">

    <select id="noticeStatistics"
            resultType="com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeStatisticsVO">
        SELECT SUM(IF(nr.read_flag = 0, 1, 0)) AS unreadCount,
               SUM(IF(n.module = 1, 1, 0))     AS taskModuleCount,
               SUM(IF(n.module = 2, 1, 0))     AS dockModuleCount,
               SUM(IF(n.module = 3, 1, 0))     AS systemModuleCount,
               SUM(IF(n.type = 1, 1, 0))       AS jobExecFailCount,
               SUM(IF(n.type = 2, 1, 0))       AS jobApproveCount,
               SUM(IF(n.type = 3, 1, 0))       AS dockShareCount,
               SUM(IF(n.type = 4, 1, 0))       AS dockAccessCount,
               SUM(IF(n.type = 5, 1, 0))       AS dockSafeAlarm,
               SUM(IF(n.type = 6, 1, 0))       AS dockHmsInfo,
               SUM(IF(n.type = 7, 1, 0))       AS dockHmsWarn,
               SUM(IF(n.type = 8, 1, 0))       AS dockHmsError,
               SUM(IF(n.type = 9, 1, 0))       AS systemAnnouncement,
               SUM(IF(n.type = 10, 1, 0))      AS systemPackageLow
        FROM system_notice_read nr
                 INNER JOIN system_notice n ON n.id = nr.notice_id AND n.deleted = 0
        WHERE nr.deleted = 0
          AND nr.user_id = #{userId}
    </select>
</mapper>