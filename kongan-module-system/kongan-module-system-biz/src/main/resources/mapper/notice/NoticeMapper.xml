<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.notice.NoticeMapper">

    <select id="page" resultType="com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeVO">
        SELECT n.*, nr.read_flag
        FROM system_notice_read nr
        INNER JOIN system_notice n ON nr.notice_id = n.id AND n.deleted = 0
        WHERE nr.deleted = 0 AND nr.user_id = #{userId}
        <if test="reqVO != null">
            <if test="reqVO.startTime !=null">
                AND n.create_time &gt;= #{reqVO.startTime}
            </if>
            <if test="reqVO.endTime!=null">
                AND n.create_time &lt;= #{reqVO.endTime}
            </if>
            <if test="reqVO.types != null and reqVO.types.size() > 0">
                AND n.type IN
                <foreach collection="reqVO.types" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="reqVO.readFlags != null and reqVO.readFlags.size() > 0">
                AND nr.read_flag IN
                <foreach collection="reqVO.readFlags" item="readFlag" open="(" close=")" separator=",">
                    #{readFlag}
                </foreach>
            </if>
            <if test="reqVO.searchKey!=null and reqVO.searchKey!=''">
                AND n.name LIKE CONCAT('%',#{reqVO.searchKey},'%')
            </if>
        </if>
        ORDER BY n.create_time DESC
    </select>
    <select id="selectNewestNotice"
            resultType="com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeVO">
        SELECT n.*, nr.read_flag
        FROM system_notice_read nr
                 INNER JOIN system_notice n ON nr.notice_id = n.id AND n.deleted = 0
        WHERE nr.deleted = 0
          AND nr.read_flag = 0
          AND nr.user_id = #{userId}
        ORDER BY n.create_time DESC
        LIMIT 1
    </select>
    <select id="selectPopNotice"
            resultType="com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeVO">
        SELECT n.*, nr.read_flag
        FROM system_notice_read nr
                 INNER JOIN system_notice n ON nr.notice_id = n.id AND n.deleted = 0
        WHERE nr.deleted = 0
          AND nr.read_flag = 0
          AND nr.user_id = #{userId}
          AND n.need_pop = 1
        ORDER BY n.create_time DESC
    </select>
    <select id="selectCountBefore" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM system_notice_read nr
                 INNER JOIN system_notice n ON nr.notice_id = n.id AND n.deleted = 0
        WHERE nr.deleted = 0
          AND nr.user_id = #{userId}
          AND n.create_time &gt; #{createTime}
        ORDER BY n.create_time DESC
    </select>
</mapper>