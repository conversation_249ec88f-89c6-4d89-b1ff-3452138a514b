<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.alarm.AlarmConfigMapper">
    <update id="updateDefaultById">
        UPDATE system_alarm_config
        SET default_flag = CASE
                               -- 本组织的数据修改成0
                               WHEN default_flag = 1 THEN 0
                               -- 当前要修改的数据修改成1
                               WHEN id = #{id} THEN 1
                           END
        -- 查询本组织的默认配置 或 id为当前要修改成默认的id的数据
        WHERE deleted = 0 AND default_flag = 1 AND dept_id = #{deptId}
        OR id = #{id}
    </update>
    <select id="selectByAlarmId"
            resultType="com.xinkongan.cloud.module.system.controller.admin.alarm.vo.AlarmConfigRespVO">
        SELECT * FROM system_alarm_config WHERE id = ( SELECT alarm_scene_id FROM system_alarm WHERE id = #{alarmId})
    </select>
</mapper>