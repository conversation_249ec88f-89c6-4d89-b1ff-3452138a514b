<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.alarm.AlarmMapper">
    <select id="selectByAlarmJobId"
            resultType="com.xinkongan.cloud.module.system.controller.admin.task.vo.job.AlarmJobDetailRespVO">
        SELECT j.*, a.address, ac.alarm_scene, a.source,a.latitude,a.longitude,u.nickname,d.name deptName
        FROM system_job j
                 LEFT JOIN system_alarm a ON j.alarm_id = a.id AND a.deleted = 0
                 LEFT JOIN system_alarm_config ac ON a.alarm_scene_id = ac.id AND ac.deleted = 0
                 LEFT JOIN system_users u ON j.creator = u.id AND u.deleted = 0
                 LEFT JOIN system_dept d ON j.dept_id = d.id AND d.deleted = 0
        WHERE j.deleted = 0 AND j.id = #{jobId}
    </select>
    <select id="selectJobSceneMap"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.AlarmJobSceneVO">
        SELECT j.id jobId,ac.alarm_scene alarmScene FROM system_job j LEFT JOIN system_alarm a ON j.alarm_id = a.id AND a.deleted = 0
        LEFT JOIN system_alarm_config ac ON a.alarm_scene_id = ac.id AND ac.deleted = 0
        WHERE j.deleted = 0 AND j.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </select>
    <select id="getAlarmPointInfo"
            resultType="com.xinkongan.cloud.module.system.controller.admin.alarm.vo.AlarmPointInfoRespVO">
        SELECT a.id      as alarmId,
               j.id      as jobId,
               j.status      as status,
               a.address as address,
               d.name    as deptName
        from `system_alarm` a
                 LEFT JOIN `system_job` j ON a.id = j.alarm_id
                 LEFT JOIN `system_dept` d ON j.dept_id = d.id
        WHERE a.id = #{alarmId}
    </select>
</mapper>