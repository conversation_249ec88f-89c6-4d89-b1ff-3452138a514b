<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.label.LabelReferenceMapper">

<!--        List<RouteRespVO> selectRouteByLabelIdWithShare(@Param(value = "page") Page<RouteRespVO> page,
                                                    @Param(value = "sharePluginParam") SharePluginParam sharePluginParam,
                                                    @Param(value = "search") LabelReferSearchDTO searchDTO);-->
    <select id="selectRouteByLabelIdWithShare" resultType="com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteRespVO">
        select  sr.id              as id,
                sr.route_name      as routeName,
                sr.way_point_count as wayPointCount,
                sr.distance        as distance,
                sr.duration        as duration,
                sr.algorithm_count as algorithmCount,
                sr.creator         as creator,
                sr.updater         as updater,
                sr.create_time     as createTime,
                sr.update_time     as updateTime,
                sr.tenant_id       as tenantId,
                sr.dept_id         as deptId,
                sr.lock_flag       as lockFlag,
                su.nickname        as createName,
                sr.route_url       as routeUrl,
                sr.copy_count      as copyCount,
                sr.dept_id         as deptId,
                sr.route_type      as routeType,
                sr.dock_sn         as dockSn,
                sr.drone_sn        as droneSn,
                sd.name            as deptName
        from system_route sr
                left join system_users su
                        on sr.creator = su.id and su.deleted = 0
                left join system_dept sd
                        on sr.dept_id = sd.id and sd.deleted = 0
                left join system_label_reference slr
                        on slr.reference_key = sr.id and slr.deleted = 0 and slr.type = 'route_reference'
            where sr.deleted = 0
                and sr.visible = 1
            <if test="search.labelId != null">
                and slr.label_id = #{search.labelId}
            </if>
            order by sr.update_time desc
    </select>

<!--        /**
     * 查询标注引用的航线数量
     * @return 数量
     */
    Long getLabelReferenceRouteCount();-->
    <select id="getLabelReferenceRouteCount" resultType="java.lang.Long">
        select count(distinct sl.id) as referenceCount
        from system_label sl
                 left join system_label_reference slr
                           on sl.id = slr.label_id and slr.deleted = 0
        where sl.deleted = 0
          and slr.reference_key is not null
    </select>

<!--        List<LabelTopVO> getLabelReferenceTop();-->
    <select id="getLabelReferenceTop" resultType="com.xinkongan.cloud.module.system.controller.admin.report.vo.LabelTopVO">
        select count(slr.reference_key) as count,
               sl.name                  as labelName
        from system_label_reference slr,
             system_label sl
        where slr.deleted = 0
          and sl.deleted = 0
          and slr.label_id = sl.id
        group by slr.label_id
        order by count desc
        limit 5
    </select>
</mapper>