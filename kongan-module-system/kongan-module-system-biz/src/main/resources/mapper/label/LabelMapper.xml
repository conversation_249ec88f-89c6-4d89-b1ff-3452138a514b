<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.label.LabelMapper">
<!--        List<LabelRespVO> getLabelInfosByPageWithShare(@Param(value = "page") Page<LabelRespVO> page,
                                                   @Param(value = "sharePluginParam") SharePluginParam sharePluginParam,
                                                   @Param(value = "search") LabelSearchDTO searchDTO);-->
    <select id="getLabelInfosByPageWithShare" resultType="com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelRespVO" >
        select sl.id            as id,
               sl.name          as name,
               sl.latitude      as latitude,
               sl.longitude     as longitude,
               sl.height        as height,
               sl.thumbnail_url as thumbnailUrl,
               sl.address       as address,
               sl.creator       as creator,
               sl.create_time   as createTime,
               sl.update_time   as updateTime,
               sd.name          as deptName,
               su.nickname      as createName,
               sl.dept_id       as deptId,
               sl.lock_flag     as lockFlag
        from system_label as sl
                 left join system_dept sd
                           on sd.id = sl.dept_id and sd.deleted = 0
                 left join system_users su
                           on sl.creator = su.id and su.deleted = 0
        where sl.deleted = 0
        <if test="search.searchKey != null and search.searchKey != ''">
            and ( sl.name like concat('%',#{search.searchKey},'%')
            or sd.name like concat('%',#{search.searchKey},'%')
            or su.nickname like concat('%',#{search.searchKey},'%')
            )
        </if>
        <if test="search.startTime!=null">
            AND sl.create_time &gt;= #{search.startTime}
        </if>
        <if test="search.endTime!=null">
            AND sl.create_time &lt;= #{search.endTime}
        </if>
        order by sl.create_time desc
    </select>

<!--        /**
     * 获取标注统计
     * @return 标注统计
     */
    LabelStatisticVO getLabelStatisticWithShare();-->
    <select id="getLabelStatisticWithShare" resultType="com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelRespVO">
        select sl.id            as id,
               sl.name          as name,
               sl.latitude      as latitude,
               sl.longitude     as longitude,
               sl.height        as height,
               sl.thumbnail_url as thumbnailUrl,
               sl.address       as address,
               sl.creator       as creator,
               sl.create_time   as createTime,
               sl.update_time   as updateTime,
               sl.dept_id       as deptId,
               sl.lock_flag     as lockFlag
        from system_label as sl
        where sl.deleted = 0
    </select>
    <select id="labelByTimeWithShare_mpCount" resultType="java.lang.Integer">
        select count(1)
        from system_label as sl
        left join system_dept sd
        on sd.id = sl.dept_id and sd.deleted = 0
        left join system_users su
        on sl.creator = su.id and su.deleted = 0
        where sl.deleted = 0
        and sl.create_time &gt;= #{search.startTime}
        <if test="search.searchKey != null and search.searchKey != ''">
            and ( sl.name like concat('%',#{search.searchKey},'%')
            or sd.name like concat('%',#{search.searchKey},'%')
            or su.nickname like concat('%',#{search.searchKey},'%')
            )
        </if>
        <if test="search.startTime!=null">
            AND sl.create_time &gt;= #{search.startTime}
        </if>
        <if test="search.endTime!=null">
            AND sl.create_time &lt;= #{search.endTime}
        </if>
    </select>
    <select id="getLabelInfosByPageDeptId"
            resultType="com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelRespVO">
        select sl.id as id,
                sl.name as name,
                sl.latitude as latitude,
                sl.longitude as longitude,
                sl.height as height,
                sl.thumbnail_url as thumbnailUrl,
                sl.address as address,
                sl.creator as creator,
                sl.create_time as createTime,
                sl.update_time as updateTime,
                sd.name as deptName,
                su.nickname as createName,
                sl.dept_id as deptId,
                sl.lock_flag as lockFlag
        from
            system_label as sl
            left join system_dept sd on sd.id = sl.dept_id
            and sd.deleted = 0
            left join system_users su on sl.creator = su.id
            and su.deleted = 0
        where
            sl.deleted = 0
                and (
                    sl.dept_id in
                        <foreach collection="deptIds" item="deptId" index="index" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                    or sl.id in (
                        select
                            rs.resource_id
                        from
                            system_resource_share rs
                        where
                            rs.deleted = 0
                            and rs.type = 4
                            and rs.share_dept_id = #{search.deptId}
                    )
                )
                <if test="search.searchKey != null and search.searchKey != ''">
                    and ( sl.name like concat('%',#{search.searchKey},'%')
                    or sd.name like concat('%',#{search.searchKey},'%')
                    or su.nickname like concat('%',#{search.searchKey},'%')
                    )
                </if>
                <if test="search.startTime!=null">
                    AND sl.create_time &gt;= #{search.startTime}
                </if>
                <if test="search.endTime!=null">
                    AND sl.create_time &lt;= #{search.endTime}
                </if>
            order by sl.create_time desc
    </select>
</mapper>