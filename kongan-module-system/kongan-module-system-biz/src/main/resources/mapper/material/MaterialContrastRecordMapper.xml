<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.material.MaterialContrastRecordMapper">

<!--    /**
     * 根据id查询素材对比记录
     *
     * @param materialId 素材id
     * @return 对比记录
     */
    List<MaterialContrastRecordVO> getMaterialContrastRecord(@Param(value = "materialContrastRecordSearch") MaterialContrastSearchDTO materialContrastRecordSearch);
-->
    <select id="getMaterialContrastRecord" resultType="com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialContrastRecordVO">
        select  smcr.id          as id,
                smcr.org_id      as orgId,
                sm.name          as orgMaterialName,
                smcr.contrast_id as contrastId,
                sm2.name         as contrastMaterialName,
                smcr.`name`      as contrastName,
                smcr.thumbnail   as thumbnail,
                smcr.create_time as createTime,
                smcr.update_time as updateTime,
                sd.name          as deptName,
                sd.id            as deptId
        from system_material_contrast_record smcr
                left join system_material sm
                    on sm.deleted = 0 and sm.id = smcr.org_id
                left join system_material sm2
                    on sm2.deleted = 0 and sm2.id = smcr.contrast_id
                left join system_dept sd
                    on sd.deleted = 0 and sd.id = smcr.dept_id
        where smcr.deleted = 0
          and smcr.org_id = #{search.materialId}
        <if test="search.searchKey != null and search.searchKey != ''">
            and  (smcr.name like concat('%',#{search.searchKey},'%') or sd.name like concat('%',#{search.searchKey},'%'))
        </if>
        <if test="search.startTime!=null">
            AND smcr.create_time &gt;= #{search.startTime}
        </if>
        <if test="search.endTime!=null">
            AND smcr.create_time &lt;= #{search.endTime}
        </if>
        order by smcr.create_time desc
    </select>
</mapper>