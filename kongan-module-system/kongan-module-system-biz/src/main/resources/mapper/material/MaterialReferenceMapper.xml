<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.material.MaterialReferenceMapper">

<!--                /**
     * 查询素材引用排行榜
     *
     * @return
     */
    List<MaterialTopVO> getMaterialReferenceTop(@Param("materialType") Integer materialType);-->
    <select id="getMaterialReferenceTop" resultType="com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialTopVO">
        select smr.material_id as id,
               count(smr.id)   as count,
               sm.name         as name
        from system_material_reference smr
                 left join system_material sm
                           on smr.material_id = sm.id and sm.deleted = 0
        where smr.deleted = 0
          and sm.type = #{materialType}
        group by smr.material_id
        order by count desc
        limit 10
    </select>


</mapper>