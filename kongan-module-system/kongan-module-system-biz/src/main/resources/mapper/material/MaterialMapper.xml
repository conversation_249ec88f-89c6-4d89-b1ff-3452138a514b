<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.material.MaterialMapper">

<!--        List<MaterialInfoVO> selectMaterialByListWithShare(@Param(value = "sharePluginParam") SharePluginParam sharePluginParam,
                                                       @Param(value = "search") MaterialSearchDTO searchDTO);
    -->
    <select id="selectMaterialByListWithShare" resultType="com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO">
        select  sm.`id`                as id,
                sm.`name`              as name,
                sm.`url`               as url,
                sm.`object_key`        as objectKey,
                sm.`jpg_url`           as jpgUrl,
                sm.`type`              as type,
                sm.`status`            as status,
                sm.`left_up_lon`       as leftUpLon,
                sm.`left_up_lat`       as leftUpLat,
                sm.`right_down_lon`    as rightDownLon,
                sm.`right_down_lat`    as rightDownLat,
                sm.`center_lon`        as centerLon,
                sm.`center_lat`        as centerLat,
                sm.`extend`            as extend,
                sm.`address`           as address,
                sm.`creator_name`      as creatorName,
                sm.`height`            as height,
                sm.`gimbal_yam_degree` as gimbalYamDegree,
                sm.`is_loaded`         as isLoaded,
                sm.`clarity_level`     as clarityLevel,
                sm.`store_name`        as storeName,
                sm.`workspace_name`    as workspaceName,
                sm.`layer_name`        as layerName,
                sm.`format`            as format,
                sm.`minimum_level`     as minimumLevel,
                sm.`maximum_level`     as maximumLevel,
                sm.`gwc_size`          as gwcSize,
                sm.`is_model`          as isModel,
                sm.`tile_url`          as tileUrl,
                sd.name                as deptName,
                su.nickname            as createName,
                sm.create_time         as createTime,
                sm.update_time         as updateTime,
                sm.process             as process,
                sm.size                as size
        from system_material as sm
                left join system_dept sd
                    on sd.id = sm.dept_id and sd.deleted = 0
                left join system_users su
                    on sm.creator = su.id and su.deleted = 0
        where sm.deleted = 0
        <if test="search.type != null">
            and sm.type = #{search.type}
        </if>
        <if test="search.searchKey != null and search.searchKey != ''">
            and ( sm.name like concat('%',#{search.searchKey},'%')
            or sd.name like concat('%',#{search.searchKey},'%')
            or su.nickname like concat('%',#{search.searchKey},'%')
            )
        </if>
        <if test="search.parseStatus!= null and search.parseStatus != ''">
            and sm.status = #{search.parseStatus}
        </if>
        <if test="search.startTime!=null">
            AND sm.create_time &gt;= #{search.startTime}
        </if>
        <if test="search.endTime!=null">
            AND sm.create_time &lt;= #{search.endTime}
        </if>
        <if test="search.deptIds != null and search.deptIds.size() > 0">
            and sm.dept_id in
            <foreach collection="search.deptIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by sm.update_time desc
    </select>



<!--        /**
     * 分页查询素材信息
     *
     * @param page      分页信息
     * @param searchDTO 查询条件
     * @return 素材记录
     */
    List<MaterialInfoVO> selectMaterialByPageWithShare(@Param(value = "page") Page<MaterialInfoVO> page,
                                                       @Param(value = "sharePluginParam") SharePluginParam sharePluginParam,
                                                       @Param(value = "search") MaterialSearchDTO searchDTO);-->

    <select id="selectMaterialByPageWithShare" resultType="com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO">
        select sm.`id`                as id,
               sm.`name`              as name,
               sm.`url`               as url,
               sm.`object_key`        as objectKey,
               sm.`jpg_url`           as jpgUrl,
               sm.`type`              as type,
               sm.`status`            as status,
               sm.`left_up_lon`       as leftUpLon,
               sm.`left_up_lat`       as leftUpLat,
               sm.`right_down_lon`    as rightDownLon,
               sm.`right_down_lat`    as rightDownLat,
               sm.`center_lon`        as centerLon,
               sm.`center_lat`        as centerLat,
               sm.`extend`            as extend,
               sm.`address`           as address,
               sm.`creator_name`      as creatorName,
               sm.`height`            as height,
               sm.`gimbal_yam_degree` as gimbalYamDegree,
               sm.`is_loaded`         as isLoaded,
               sm.`clarity_level`     as clarityLevel,
               sm.`store_name`        as storeName,
               sm.`workspace_name`    as workspaceName,
               sm.`layer_name`        as layerName,
               sm.`format`            as format,
               sm.`minimum_level`     as minimumLevel,
               sm.`maximum_level`     as maximumLevel,
               sm.`gwc_size`          as gwcSize,
               sm.`is_model`          as isModel,
               sm.`tile_url`          as tileUrl,
               sd.name                as deptName,
               su.nickname            as createName,
               sm.create_time         as createTime,
               sm.update_time         as updateTime,
               sm.process             as process,
               sm.size                as size
        from system_material as sm
                left join system_dept sd
                        on sd.id = sm.dept_id and sd.deleted = 0
                left join system_users su
                        on sm.creator = su.id and su.deleted = 0
        where sm.deleted = 0
           <if test="search.type != null">
                  and sm.type = #{search.type}
           </if>
           <if test="search.searchKey != null and search.searchKey != ''">
               and ( sm.name like concat('%',#{search.searchKey},'%')
               or sd.name like concat('%',#{search.searchKey},'%')
               or su.nickname like concat('%',#{search.searchKey},'%')
               )
           </if>
          <if test="search.parseStatus!= null and search.parseStatus != ''">
               and sm.status = #{search.parseStatus}
          </if>
           <if test="search.startTime!=null">
                AND sm.create_time &gt;= #{search.startTime}
           </if>
           <if test="search.endTime!=null">
                AND sm.create_time &lt;= #{search.endTime}
           </if>
           <if test="search.deptIds != null and search.deptIds.size() > 0">
                  and sm.dept_id in
                  <foreach collection="search.deptIds" item="id" open="(" close=")" separator=",">
                         #{id}
                  </foreach>
           </if>
        order by sm.update_time desc
    </select>

<!--        /**
     * 根据类型进行素材统计
     * @return 统计结果
     */
    MaterialSumVO selectMaterialSumByType();-->
    <select id="selectMaterialSumByType" resultType="com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialSumVO">
        select sm.type as type,
               count(sm.id) as count,
               sum(sm.size)  as storageSize
        from system_material as sm
        where sm.deleted = 0
        group by sm.type
    </select>

<!--        /**
     * 可对比数据栏数据查询
     * @param sharePluginParam 分享插件参数
     * @param searchDTO 查询条件
     * @return 查询结果
     */
    List<MaterialInfoVO> selectMaterialContrastByPageWithShare(@Param(value = "sharePluginParam") SharePluginParam sharePluginParam,
                                                               @Param(value = "search") MaterialCompareSearchDTO searchDTO);-->

    <select id="selectMaterialContrastByPageWithShare"
            resultType="com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO">
        select  sm.`id`                as id,
                sm.`name`              as name,
                sm.`url`               as url,
                sm.`object_key`        as objectKey,
                sm.`jpg_url`           as jpgUrl,
                sm.`type`              as type,
                sm.`status`            as status,
                sm.`left_up_lon`       as leftUpLon,
                sm.`left_up_lat`       as leftUpLat,
                sm.`right_down_lon`    as rightDownLon,
                sm.`right_down_lat`    as rightDownLat,
                sm.`center_lon`        as centerLon,
                sm.`center_lat`        as centerLat,
                sm.`extend`            as extend,
                sm.`address`           as address,
                sm.`creator_name`      as creatorName,
                sm.`height`            as height,
                sm.`gimbal_yam_degree` as gimbalYamDegree,
                sm.`is_loaded`         as isLoaded,
                sm.`clarity_level`     as clarityLevel,
                sm.`store_name`        as storeName,
                sm.`workspace_name`    as workspaceName,
                sm.`layer_name`        as layerName,
                sm.`format`            as format,
                sm.`minimum_level`     as minimumLevel,
                sm.`maximum_level`     as maximumLevel,
                sm.`gwc_size`          as gwcSize,
                sm.`is_model`          as isModel,
                sm.`tile_url`          as tileUrl,
                sd.name                as deptName,
                su.nickname            as createName,
                sm.create_time         as createTime,
                sm.update_time         as updateTime,
                sm.process             as process,
                sm.size                as size,
                sd.name                as deptName
        from system_material as sm
                left join system_dept sd
                    on sd.id = sm.dept_id and sd.deleted = 0
                left join system_users su
                    on sm.creator = su.id and su.deleted = 0
        where sm.deleted = 0
        <if test="search.searchKey != null and search.searchKey != ''">
            and (
                    sm.name like concat('%',#{search.searchKey},'%') or
                    sd.name like concat('%',#{search.searchKey},'%') or
                    su.nickname like concat('%',#{search.searchKey},'%')
                )
        </if>
        <if test="search.materialId != null">
            and sm.id !=#{search.materialId}
        </if>
        <if test="search.replaceId != null">
            and sm.id !=#{search.replaceId}
        </if>
        and sm.status ='success'
        <if test="search.type != null and search.type ==2">
            and sm.left_up_lon between -180 and 180
            and sm.right_down_lon between -180 and 180
            and sm.left_up_lat between -90 and 90
            and sm.right_down_lat between -90 and 90
            and #{search.leftUpLon} &lt;= sm.right_down_lon
            and #{search.rightDownLon} >=  sm.left_up_lon
            and #{search.leftUpLat} >=  sm.right_down_lat
            and #{search.rightDownLat} &lt;=  sm.left_up_lat
            and sm.type = #{search.type}
        </if>
        <if test="search.type != null and search.type ==3">
            and sm.type = #{search.type}
            and sm.center_lon between -180 and 180
            and sm.center_lat between -90 and 90
            and 6371000 * 2 * ATAN2(SQRT(sin(((sm.center_lat % 360 * 3.14159265358979323846)/180-(#{search.centerLat} %
            360 * 3.14159265358979323846)/180)/2) *
            sin(((sm.center_lat % 360 * 3.14159265358979323846)/180-(#{search.centerLat} % 360 *
            3.14159265358979323846)/180)/2) +
            COS((#{search.centerLat} % 360 * 3.14159265358979323846)/180) * COS((sm.center_lat % 360 *
            3.14159265358979323846)/180) *
            sin(((sm.center_lon % 360 * 3.14159265358979323846)/180-(#{search.centerLon} % 360 *
            3.14159265358979323846)/180)/2) *
            sin(((sm.center_lon % 360 * 3.14159265358979323846)/180-(#{search.centerLon} % 360 *
            3.14159265358979323846)/180)/2)),
            SQRT(1-(sin(((sm.center_lat % 360 * 3.14159265358979323846)/180-(#{search.centerLat} % 360 *
            3.14159265358979323846)/180)/2) *
            sin(((sm.center_lat % 360 * 3.14159265358979323846)/180-(#{search.centerLat} % 360 *
            3.14159265358979323846)/180)/2) +
            COS((#{search.centerLat} % 360 * 3.14159265358979323846)/180) * COS((sm.center_lat % 360 *
            3.14159265358979323846)/180) *
            sin(((sm.center_lon % 360 * 3.14159265358979323846)/180-(#{search.centerLon} % 360 *
            3.14159265358979323846)/180)/2) *
            sin(((sm.center_lon % 360 * 3.14159265358979323846)/180-(#{search.centerLon} % 360 *
            3.14159265358979323846)/180)/2)))) &lt;= #{search.radius}
        </if>
        <if test="search.deptIds != null and search.deptIds.size() != 0">
            and sm.dept_id in
            <foreach collection="search.deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="search.startTime != null and search.startTime != ''">
            and date_format(sm.create_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{search.startTime},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="search.endTime != null and search.endTime != ''">
            and date_format(sm.create_time,'%Y-%m-%d %H:%i:%s') <![CDATA[<=]]> date_format(#{search.endTime},'%Y-%m-%d %H:%i:%s')
        </if>
        order by sm.update_time desc
    </select>
    <select id="selectMaterialByPageDeptId"
            resultType="com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO">
        select sm.`id`                as id,
        sm.`name`              as name,
        sm.`url`               as url,
        sm.`object_key`        as objectKey,
        sm.`jpg_url`           as jpgUrl,
        sm.`type`              as type,
        sm.`status`            as status,
        sm.`left_up_lon`       as leftUpLon,
        sm.`left_up_lat`       as leftUpLat,
        sm.`right_down_lon`    as rightDownLon,
        sm.`right_down_lat`    as rightDownLat,
        sm.`center_lon`        as centerLon,
        sm.`center_lat`        as centerLat,
        sm.`extend`            as extend,
        sm.`address`           as address,
        sm.`creator_name`      as creatorName,
        sm.`height`            as height,
        sm.`gimbal_yam_degree` as gimbalYamDegree,
        sm.`is_loaded`         as isLoaded,
        sm.`clarity_level`     as clarityLevel,
        sm.`store_name`        as storeName,
        sm.`workspace_name`    as workspaceName,
        sm.`layer_name`        as layerName,
        sm.`format`            as format,
        sm.`minimum_level`     as minimumLevel,
        sm.`maximum_level`     as maximumLevel,
        sm.`gwc_size`          as gwcSize,
        sm.`is_model`          as isModel,
        sm.`tile_url`          as tileUrl,
        sd.name                as deptName,
        su.nickname            as createName,
        sm.create_time         as createTime,
        sm.update_time         as updateTime,
        sm.process             as process,
        sm.size                as size
        from system_material as sm
        left join system_dept sd
        on sd.id = sm.dept_id and sd.deleted = 0
        left join system_users su
        on sm.creator = su.id and su.deleted = 0
        where sm.deleted = 0
          and (
              sm.dept_id in
              <foreach collection="search.deptIds" item="deptId" open="(" separator="," close=")">
                  #{deptId}
              </foreach>
              or sm.id in (
                  select rs.resource_id
                  from system_resource_share rs
                  where rs.deleted = 0
                  and rs.share_dept_id = #{search.deptId}
              )
          )

        <if test="search.type != null">
            and sm.type = #{search.type}
        </if>
        <if test="search.searchKey != null and search.searchKey != ''">
            and ( sm.name like concat('%',#{search.searchKey},'%')
            or sd.name like concat('%',#{search.searchKey},'%')
            or su.nickname like concat('%',#{search.searchKey},'%')
            )
        </if>
        <if test="search.parseStatus!= null and search.parseStatus != ''">
            and sm.status = #{search.parseStatus}
        </if>
        order by sm.update_time desc
    </select>
</mapper>