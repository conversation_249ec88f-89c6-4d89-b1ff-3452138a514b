<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.psdk.VoiceMapper">

<!--        List<VoiceRespVO> getVoiceByPage(@Param(value = "page") Page<RouteRespVO> page,
                                     @Param(value = "search") VoiceSearchDTO searchDTO);-->
    <select id="getVoiceByPage" resultType="com.xinkongan.cloud.module.system.controller.admin.psdk.vo.VoiceRespVO">
        select `id`          as id,
               `name`        as name,
               `text`        as text,
               `url`         as url,
               `type`        as type,
               `mp3_url`     as mp3Url,
               `fingerprint` as fingerprint,
               `duration`    as duration,
               `file_ids`    as fileIds,
               `creator`     as creator,
               `create_time` as createTime,
               `dept_id`     as deptId,
               `tenant_id`   as tenantId,
               `updater`     as updater,
               `update_time` as updateTime
        from system_voice sv
        where sv.deleted = 0
            <if test="search.searchKey!= null and search.searchKey!= ''">
                and sv.name like concat('%', #{search.searchKey}, '%')
            </if>
            <if test="search.type!= null">
                and sv.type = #{search.type}
            </if>
        order by sv.update_time desc
    </select>
</mapper>
