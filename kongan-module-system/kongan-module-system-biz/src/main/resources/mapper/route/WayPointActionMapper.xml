<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.route.WayPointActionMapper">

    <!--        List<WayPointActionRespVO> getWayPointActionByRouteIdAndPointIndex(@Param(value = "routeId") Long routeId,
                                                                           @Param(value = "pointIndex") Integer pointIndex);-->
    <select id="getWayPointActionByRouteIdAndPointIndex"
            resultType="com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointActionRespVO">

        select swpa.id          as id,
               swpa.point_id    as pointId,
               swp.route_id     as routeId,
               swpa.action_type as actionType,
               swpa.type        as type,
               swp.point_index  as pointIndex
        from system_way_point swp,
             system_way_point_action swpa
        where swp.deleted = 0
          and swpa.deleted = 0
          and swp.id = swpa.point_id
          and swp.route_id = #{routeId}
          and swp.point_index <![CDATA[<=]]> #{pointIndex}
    </select>
</mapper>