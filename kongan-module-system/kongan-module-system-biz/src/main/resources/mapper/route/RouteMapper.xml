<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.route.RouteMapper">
        <!--        /**
             * 分页查询航线信息
             * @param page 分页信息
             * @param searchDTO 查询条件
             * @return 航线记录
             */
    List<RouteRespVO> selectRouteByPageWithShare(@Param(value = "page") Page<RouteRespVO> page,
                                        @Param(value = "search") RouteSearchDTO searchDTO);-->
    <select id="selectRouteByPageWithShare" resultType="com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteRespVO">
        select sr.id              as id,
               sr.route_name      as routeName,
               sr.way_point_count as wayPointCount,
               sr.distance        as distance,
               sr.duration        as duration,
               sr.algorithm_count as algorithmCount,
               sr.creator         as creator,
               sr.updater         as updater,
               sr.create_time     as createTime,
               sr.update_time     as updateTime,
               sr.tenant_id       as tenantId,
               sr.dept_id         as deptId,
               sr.lock_flag       as lockFlag,
               su.nickname        as createName,
               sr.route_url       as routeUrl,
               sr.copy_count      as copyCount,
               sr.dept_id         as deptId,
               sr.route_type      as routeType,
               sr.dock_sn         as dockSn,
               sr.drone_sn        as droneSn,
               sd.name            as deptName
        from system_route sr
                left join system_users su
                    on sr.creator = su.id and su.deleted = 0
                left join system_dept sd
                    on sr.dept_id = sd.id and sd.deleted = 0
        where sr.deleted = 0
        and sr.visible = 1
          <if test="search.routeType != null">
              and sr.route_type = #{search.routeType}
          </if>
          <if test="search.searchKey != null and search.searchKey != ''">
              and (sr.route_name like concat('%',#{search.searchKey},'%') or sd.name like concat('%',#{search.searchKey},'%'))
          </if>
          <if test="search.deptIds != null and search.deptIds.size() > 0">
              and sr.dept_id in
              <foreach collection="search.deptIds" item="id" open="(" close=")" separator=",">
                  #{id}
              </foreach>
          </if>
          <if test="search.dockSn != null and search.dockSn != ''">
              and sr.dock_sn = #{search.dockSn}
          </if>
        order by sr.update_time desc
    </select>


<!--    Long selectRouteRowNumberByPageWithShare_mpCount(@Param(value = "sharePluginParam") SharePluginParam sharePluginParam,
                                             @Param(value = "search") RouteSearchDTO searchDTO,
                                             @Param(value = "createTime") LocalDateTime createTime);-->
    <select id="selectRouteRowNumberByPageWithShare_mpCount" resultType="java.lang.Long">
        select count(sr.id)
        from system_route sr
            left join system_users su
                on sr.creator = su.id and su.deleted = 0
            left join system_dept sd
                on sr.dept_id = sd.id and sd.deleted = 0
        where sr.deleted = 0
            and sr.visible = 1
        <if test="search.routeType != null">
            and sr.route_type = #{search.routeType}
        </if>
        <if test="search.searchKey != null and search.searchKey != ''">
            and (sr.route_name like concat('%',#{search.searchKey},'%') or sd.name like concat('%',#{search.searchKey},'%'))
        </if>
        <if test="search.deptIds != null and search.deptIds.size() > 0">
            and sr.dept_id in
            <foreach collection="search.deptIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="search.dockSn != null and search.dockSn != ''">
            and sr.dock_sn = #{search.dockSn}
        </if>
        AND sr.update_time &gt;= #{createTime}
        order by sr.update_time desc
    </select>

<!--        List<RouteStatisticVO> selectRouteStatistic();-->
    <select id="selectRouteStatistic" resultType="com.xinkongan.cloud.module.system.controller.admin.report.vo.RouteStatisticVO">
        select sr.route_load_type as type,
               count(sr.id)       as num
        from system_route sr
        where sr.deleted = 0
          and sr.visible = 1
        group by sr.route_load_type
    </select>

<!--        List<RouteTopStatisticVO> getRouteTopStatisticInfo(LocalDateTime[] timeRange);-->
    <select id="getRouteTopStatisticInfo" resultType="com.xinkongan.cloud.module.system.controller.admin.report.vo.RouteTopStatisticVO">
        select
            sj.route_id as routeId,
            sr.route_name as routeName,
            count(sj.id)  as taskCount
        from system_job sj
                 left join system_route sr
                           on sj.route_id = sr.id and sr.deleted = 0
        where sj.deleted = 0
          and sr.visible = 1
        group by sj.route_id
        order by taskCount desc
        limit 5
    </select>
    <select id="getExecRouteListByDockSn"
            resultType="com.xinkongan.cloud.module.system.controller.admin.route.vo.ExecRouteVO">
        select r.id          as id,
               r.route_name  as routeName,
               r.route_type  as routeType,
               j.dock_sn     as dockSn,
               r.create_time as createTime,
               d.name        as deptName
        from system_route r
                 inner join system_job j on r.id = j.route_id
                 left join system_dept d on r.dept_id = d.id
        where j.dock_sn = #{dockSn}
          and r.visible = 1
        order by r.create_time desc
    </select>
</mapper>