<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.route.RouteAlgorithmMapper">


    <resultMap id="routeAlgorithmVOMap" type="com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteAlgorithmVO">
        <id column="id" property="id"/>
        <result column="pointId" property="pointId"/>
        <result column="routeId" property="routeId"/>
        <result column="algorithmId" property="algorithmId"/>
        <result column="exampleId" property="exampleId"/>
        <result column="type" property="type"/>
        <result column="number" property="number"/>
        <result column="algorithmName" property="algorithmName"/>
        <result column="exampleName" property="exampleName"/>
        <result column="startIndex" property="startIndex"/>
        <result column="endIndex" property="endIndex"/>
        <result column="batchId" property="batchId"/>
        <result column="areaInfo" property="areaInfo" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="mapLabelIds" property="mapLabelIds" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

<!--        List<RouteAlgorithmVO> getRouteAlgorithmInfoByRouteId(Long routeId);-->
    <select id="getRouteAlgorithmInfoByRouteId" resultMap="routeAlgorithmVOMap">
        select swpa.id            as id,
               swpa.point_id      as pointId,
               swpa.route_id      as routeId,
               swpa.algorithm_id  as algorithmId,
               swpa.example_id    as exampleId,
               swpa.type          as type,
               swpa.number        as number,
               swpa.map_label_ids as mapLabelIds,
               swpa.area_info     as areaInfo,
               swpa.start_index   as startIndex,
               swpa.end_index     as endIndex,
               swpa.batch_id      as batchId,
               sa.name            AS algorithmName,
               sad.name           as exampleName
        from system_way_point_algorithm swpa
                 left join system_algorithm sa
                           on sa.id = swpa.algorithm_id and sa.deleted = 0
                 LEFT JOIN system_algorithm_disposal sad
                           ON sad.id = swpa.example_id AND sad.deleted = 0
        where swpa.deleted = 0
          and swpa.route_id = #{routeId}
    </select>

<!--        List<RouteAlgorithmVO> getRouteAlgorithmInfoByPointId(Long pointId);-->
    <select id="getRouteAlgorithmInfoByPointId" resultMap="routeAlgorithmVOMap">
        select swpa.id            as id,
               swpa.point_id      as pointId,
               swpa.route_id      as routeId,
               swpa.algorithm_id  as algorithmId,
               swpa.example_id    as exampleId,
               swpa.type          as type,
               swpa.number        as number,
               swpa.map_label_ids as mapLabelIds,
               swpa.area_info     as areaInfo,
               swpa.start_index   as startIndex,
               swpa.end_index     as endIndex,
               swpa.batch_id      as batchId,
               sa.name            AS algorithmName,
               sad.name           as exampleName
        from system_way_point_algorithm swpa
                 left join system_algorithm sa
                           on sa.id = swpa.algorithm_id and sa.deleted = 0
                 LEFT JOIN system_algorithm_disposal sad
                           ON sad.id = swpa.example_id AND sad.deleted = 0
        where swpa.deleted = 0
          and swpa.point_id = #{pointId}
    </select>


<!--      RouteAlgorithmVO getRouteAlgorithmInfoById(Long id);-->
    <select id="getRouteAlgorithmInfoById" resultMap="routeAlgorithmVOMap">
        select swpa.id            as id,
               swpa.point_id      as pointId,
               swpa.route_id      as routeId,
               swpa.algorithm_id  as algorithmId,
               swpa.example_id    as exampleId,
               swpa.type          as type,
               swpa.number        as number,
               swpa.map_label_ids as mapLabelIds,
               swpa.area_info     as areaInfo,
               swpa.start_index   as startIndex,
               swpa.end_index     as endIndex,
               swpa.batch_id      as batchId,
               sa.name            AS algorithmName,
               sad.name           as exampleName
        from system_way_point_algorithm swpa
                 left join system_algorithm sa
                           on sa.id = swpa.algorithm_id and sa.deleted = 0
                 LEFT JOIN system_algorithm_disposal sad
                           ON sad.id = swpa.example_id AND sad.deleted = 0
        where swpa.deleted = 0
          and swpa.id = #{id}
    </select>
</mapper>