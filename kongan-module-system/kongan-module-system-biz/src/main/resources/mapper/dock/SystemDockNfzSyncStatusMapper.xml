<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.dock.SystemDockNfzSyncStatusMapper">

    <!-- 根据租户ID查询所有机场的同步状态 -->
    <select id="selectByTenantId" resultType="com.xinkongan.cloud.module.system.dal.dataobject.dock.SystemDockNfzSyncStatusDO">
        SELECT id, tenant_id, dock_sn, sync_status, last_sync_time, fail_reason,
               creator, create_time, updater, update_time, deleted
        FROM system_dock_nfz_sync_status
        WHERE tenant_id = #{tenantId}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据租户ID和机场SN查询同步状态 -->
    <select id="selectByTenantIdAndDockSn" resultType="com.xinkongan.cloud.module.system.dal.dataobject.dock.SystemDockNfzSyncStatusDO">
        SELECT id, tenant_id, dock_sn, sync_status, last_sync_time, fail_reason,
               creator, create_time, updater, update_time, deleted
        FROM system_dock_nfz_sync_status
        WHERE tenant_id = #{tenantId}
          AND dock_sn = #{dockSn}
          AND deleted = 0
        LIMIT 1
    </select>
    <select id="selectDockSyncStatusList"
            resultType="com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz.DockNfzSyncStatusVO">
        SELECT dock.*,dock.device_name AS dockName,s.*,dept.name AS deptName
        FROM system_dock_device dock
        LEFT JOIN system_dock_nfz_sync_status s ON dock.device_sn = s.dock_sn AND s.deleted = 0
        LEFT JOIN system_dept dept ON dock.dept_id = dept.id AND dept.deleted = 0
        WHERE dock.deleted = 0 AND dock.device_sn IN
        <foreach collection="sns" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 批量插入或更新机场同步状态 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO system_dock_nfz_sync_status
        (id, tenant_id, dock_sn, sync_status, last_sync_time, fail_reason,
         creator, create_time, updater, update_time, deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.tenantId}, #{item.dockSn}, #{item.syncStatus}, #{item.lastSyncTime},
             #{item.failReason}, #{item.creator}, #{item.createTime}, #{item.updater}, #{item.updateTime}, #{item.deleted})
        </foreach>
        ON DUPLICATE KEY UPDATE
        sync_status = VALUES(sync_status),
        last_sync_time = VALUES(last_sync_time),
        fail_reason = VALUES(fail_reason),
        updater = VALUES(updater),
        update_time = VALUES(update_time)
    </insert>

    <!-- 根据租户ID批量更新机场同步状态 -->
    <update id="updateSyncStatusByTenantId">
        UPDATE system_dock_nfz_sync_status
        SET sync_status = #{syncStatus},
            last_sync_time = NOW(),
            update_time = NOW()
        WHERE tenant_id = #{tenantId}
          AND deleted = 0
    </update>

    <!-- 根据租户ID和机场SN更新同步状态 -->
    <update id="updateSyncStatusByTenantIdAndDockSn">
        UPDATE system_dock_nfz_sync_status
        SET sync_status = #{syncStatus},
            last_sync_time = NOW(),
            update_time = NOW()
            <if test="failReason != null">
            , fail_reason = #{failReason}
            </if>
        WHERE tenant_id = #{tenantId}
          AND dock_sn = #{dockSn}
          AND deleted = 0
    </update>

</mapper>
