<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.dock.DeviceLogMapper">

    <select id="page" resultType="com.xinkongan.cloud.module.system.dal.dataobject.dock.DeviceLogDO">
        SELECT * FROM system_device_log
        WHERE deleted = 0
        <if test="sns!=null and sns.size() > 0">
            AND device_sn IN
            <foreach collection="sns" item="sn" open="(" separator="," close=")">
                #{sn}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </select>
</mapper>