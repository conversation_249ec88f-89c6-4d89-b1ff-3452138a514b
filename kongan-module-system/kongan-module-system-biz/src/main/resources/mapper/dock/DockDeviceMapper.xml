<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.dock.DockDeviceMapper">


    <select id="getDockTreeInfoList"
            resultType="com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceTreeInfoVO">
        SELECT dock.id                      AS id,
               dock.device_sn               AS deviceSn,
               dock.device_name             AS nodeName,
               dock.dept_id                 AS parentId,
               dock.dept_id                 AS deptId,
               dock.child_sn                AS childSn,
               'dock_device'                AS nodeType,
               COALESCE(dc.share_status, 0) AS shareStatus
        FROM system_dock_device AS dock
                 LEFT JOIN
             system_device_config AS dc
             ON
                 dock.device_sn = dc.dock_sn
        WHERE dock.domain = 3
          AND dock.deleted = 0
          AND dc.deleted = 0
        ORDER BY dock.bound_time ASC
    </select>
    <select id="getShareByTenantId"
            resultType="com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceTreeInfoVO">
        SELECT
        dock.id AS id,
        dock.device_sn AS deviceSn,
        dock.device_name AS nodeName,
        dock.dept_id AS parentId,
        dock.dept_id AS deptId,
        dock.child_sn AS childSn,
        'dock_device' AS nodeType, -- 字符串需要用单引号表示
        1 AS shareStatus
        FROM
        system_device_config AS dc
        LEFT JOIN
        system_dock_device AS dock ON dc.dock_sn = dock.device_sn
        WHERE
        dc.share_status = 1
        AND dock.domain = 3
        AND dock.deleted = 0
        AND dc.deleted = 0
        <if test="deptIds != null and deptIds.size() > 0">
            AND dock.dept_id NOT IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        ORDER BY dock.bound_time ASC
    </select>
    <select id="getShareDockListByTenantId"
            resultType="com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceVO">
        SELECT
        dock.*,
        dc.share_status AS shareStatus,
        dept.name AS deptName,
        1 as without
        FROM
        system_device_config AS dc
        LEFT JOIN
        system_dock_device AS dock ON dc.dock_sn = dock.device_sn
        LEFT JOIN system_dept AS dept ON dock.dept_id = dept.id
        WHERE
        dc.share_status = 1
        AND dock.domain = 3
        AND dock.deleted = 0
        AND dc.deleted = 0
        <if test="deptIds != null and deptIds.size() > 0">
            AND dock.dept_id NOT IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        ORDER BY dock.bound_time ASC
    </select>

    <select id="getDeptAndShareDockListForRoute"
            resultType="com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceVO">
        -- 查询指定组织及其子组织的机场
        SELECT
            dock.*,
            COALESCE(dc.share_status, 0) AS shareStatus,
            dept.name AS deptName,
            0 AS without
        FROM
            system_dock_device AS dock
        LEFT JOIN system_device_config AS dc ON dock.device_sn = dc.dock_sn AND dc.deleted = 0
        LEFT JOIN system_dept AS dept ON dock.dept_id = dept.id AND dept.deleted = 0
        WHERE
            dock.domain = 3
            AND dock.deleted = 0
            <if test="childDeptIds != null and childDeptIds.size() > 0">
                AND dock.dept_id IN
                <foreach collection="childDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="childDeptIds == null or childDeptIds.size() == 0">
                AND 1 = 0
            </if>

        UNION

        -- 查询共享的机场（排除当前用户数据权限内的组织）
        SELECT
            dock.*,
            dc.share_status AS shareStatus,
            dept.name AS deptName,
            1 AS without
        FROM
            system_device_config AS dc
        LEFT JOIN system_dock_device AS dock ON dc.dock_sn = dock.device_sn
        LEFT JOIN system_dept AS dept ON dock.dept_id = dept.id
        WHERE
            dc.share_status = 1
            AND dock.domain = 3
            AND dock.deleted = 0
            AND dc.deleted = 0

        ORDER BY bound_time ASC
    </select>

    <select id="getDeviceListByDeptIds"
            resultType="com.xinkongan.cloud.module.system.controller.admin.posture.dto.PostureDeviceVO">
        SELECT
        dock.id AS deviceId,
        dock.device_sn AS deviceSn,
        dock.device_name AS deviceName,
        dock.device_sn AS deviceSn,
        dock.child_sn AS childSn,
        dock.device_type AS deviceType,
        dock.tenant_id AS tenantId,
        dock.bound_time AS boundTime,
        dock.longitude AS longitude,
        dock.latitude AS latitude,
        dock.height AS height,
        dept.NAME AS deptName,
        c.share_status AS shareStatus,
        0 AS without
        FROM
        system_dock_device dock
        LEFT JOIN system_dept dept ON dock.dept_id = dept.id
        LEFT JOIN system_device_config c ON dock.device_sn = c.dock_sn
        WHERE
        dock.domain = 3
        AND dock.deleted = 0
        AND c.deleted = 0
        AND dept.deleted = 0
        <if test="deptIds!=null and deptIds.size() > 0">
            AND dock.dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
    </select>
    <select id="selectAlarmDockDevice"
            resultType="com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm.AlarmDockDeviceVO">
        SELECT dock.*,dept.name AS deptName
        FROM system_dock_device dock
        LEFT JOIN system_device_config dc ON dock.device_sn = dc.dock_sn AND dc.deleted = 0
        LEFT JOIN system_dept dept ON dock.dept_id = dept.id AND dept.deleted = 0
        WHERE dock.deleted = 0
        AND dc.deleted = 0
        AND dept.deleted = 0
        AND dock.domain = 3
        AND (
        dept_id IN
        <foreach collection="deptIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        OR dc.share_status = 1
        )
        <if test="searchKey!=null and searchKey!=''">
            AND dock.device_name LIKE CONCAT('%', #{searchKey}, '%')
        </if>
    </select>

<!--        List<DockDeviceVO> getDeptPageInfo(@Param(value = "page") Page<DockDeviceVO> page,
                                       @Param("deptDeviceSearchInfo") DeptDeviceSearchDTO deptDeviceSearchInfo);-->
    <select id="getDeptPageInfo" resultType="com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceVO">
        SELECT
            dock.*
        FROM
            system_dock_device dock
        where
            dock.deleted = 0
          and dock.dept_id = #{deptDeviceSearchInfo.deptId}
          and dock.domain = 3
        order by dock.create_time;
    </select>
    <select id="getDockDeviceExecJobCount"
            resultType="com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockExecJobRanking">
        select j.dock_sn        as dockSn,
               d.device_name    as dockName,
               count(j.dock_sn) as jobCount
        from system_job j
                 inner join system_dock_device d on j.dock_sn = d.device_sn
        where j.dock_sn is not null
          and j.deleted = 0
          and j.create_time BETWEEN #{param.startTime} AND #{param.endTime}
        group by j.dock_sn
        order by jobCount desc
    </select>
    <select id="getByDeviceSnIgnoreDel" resultType="com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO">
        select *
        from system_dock_device
        where device_sn = #{deviceSn}
    </select>

    <!-- 根据设备SN更新设备信息，忽略逻辑删除状态 -->
    <update id="updateByDeviceSnIgnoreDel">
        UPDATE system_dock_device
        SET device_name = #{dockDevice.deviceName},
        dept_id = #{dockDevice.deptId},
        tenant_id = #{dockDevice.tenantId},
        bound_time = #{dockDevice.boundTime},
        auto_execute = #{dockDevice.autoExecute},
        fly_height = #{dockDevice.flyHeight},
        fly_speed = #{dockDevice.flySpeed},
        return_height = #{dockDevice.returnHeight},
        deleted = #{dockDevice.deleted}
        WHERE device_sn = #{dockDevice.deviceSn}
        <!-- 注意：这里不添加 AND deleted = 0 条件，以便能够更新已逻辑删除的记录 -->
    </update>
</mapper>