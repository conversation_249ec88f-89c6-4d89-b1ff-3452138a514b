<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.permission.UserRoleMapper">


<!--        List<UserRoleVO> selectListByUserIds(Collection<Long> roleIds);-->
    <select id="selectListByUserIds" resultType="com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserRoleVO">
        select sur.user_id as userId,
               sur.role_id as roleId,
               sr.name     as roleName
        from system_users su,
             system_role sr,
             system_user_role sur
        where su.deleted = 0
          and sr.deleted = 0
          and sur.deleted = 0
          and su.id = sur.user_id
          and sr.id = sur.role_id
          <if test="userIds != null and userIds.size() > 0">
              and su.id in
              <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                  #{userId}
              </foreach>
          </if>
    </select>

<!--        List<UserRoleVO> selectUserRoleInfosListByRoleIds(Collection<Long> roleIds);-->
    <select id="selectUserRoleInfosListByRoleIds" resultType="com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserRoleVO">
        select  sur.user_id as userId,
                sur.role_id as roleId,
                sr.name     as roleName,
                su.nickname as nickName,
                su.username as username,
                sr.sort     as sort
                from system_users su,
                system_role sr,
                system_user_role sur
        where su.deleted = 0
        and sr.deleted = 0
        and sur.deleted = 0
        and su.id = sur.user_id
        and sr.id = sur.role_id
        and sr.id in
        <foreach collection="roleIds" item="roleId" separator="," open="(" close=")">
            #{roleId}
        </foreach>
    </select>
</mapper>