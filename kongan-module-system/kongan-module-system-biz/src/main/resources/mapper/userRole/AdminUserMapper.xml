<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.user.AdminUserMapper">


    <select id="selectDeptAndChildPermissionUserIds" resultType="java.lang.Long">
        SELECT u.id FROM system_users u INNER JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
        INNER JOIN system_role r ON r.id = ur.role_id AND r.deleted = 0
        WHERE u.deleted = 0 AND r.data_scope = 4
        <if test="deptIds != null and deptIds.size() > 0">
            AND u.dept_id in
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
    </select>

    <resultMap id="userRespMap" type="com.xinkongan.cloud.module.system.controller.admin.permission.vo.role.RoleUserVO">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="nickname" column="nickname"/>
        <result property="remark" column="remark"/>
        <result property="deptId" column="deptId"/>
        <result property="postIds" column="postIds"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="certificates" column="certificates"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="tags" column="tags"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="email" column="email"/>
        <result property="mobile" column="mobile"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="status" column="status"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="roleId" column="roleId"/>
        <result property="roleName" column="roleName"/>
    </resultMap>

    <!--        List<UserRespVO> getUserListByRoleId(@Param(value = "page") Page<RouteRespVO> page, @Param(value = "roleId") Long roleId);-->
    <select id="getUserListByRoleId" resultMap="userRespMap">
        SELECT u.id           as id,
               u.username     as username,
               u.nickname     as nickname,
               u.remark       as remark,
               u.dept_id      as deptId,
               u.post_ids     as postIds,
               u.certificates as certificates,
               u.tags         as tags,
               u.email        as email,
               u.mobile       as mobile,
               u.sex          as sex,
               u.avatar       as avatar,
               u.status       as status,
               u.create_time  as createTime,
               u.update_time  as updateTime,
               r.id           as roleId,
               r.name         as roleName
        FROM system_users u
                 INNER JOIN system_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
                 INNER JOIN system_role r ON r.id = ur.role_id AND r.deleted = 0
        WHERE u.deleted = 0
          AND r.id = #{userInfoSearchInfo.roleId}
          <if test = "userInfoSearchInfo.searchKey != null and userInfoSearchInfo.searchKey != ''">
              AND u.nickname LIKE CONCAT('%', #{userInfoSearchInfo.searchKey}, '%')
          </if>
        order by u.create_time desc
    </select>

    <resultMap id="deptUserRespMap" type="com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.DeptUserVO">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="nickname" column="nickname"/>
        <result property="remark" column="remark"/>
        <result property="deptId" column="deptId"/>
        <result property="postIds" column="postIds"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="certificates" column="certificates"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="tags" column="tags"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="email" column="email"/>
        <result property="mobile" column="mobile"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="status" column="status"/>
        <result property="createTime" column="createTime"/>
        <result property="roleId" column="roleId"/>
        <result property="roleName" column="roleName"/>
    </resultMap>

<!--        List<DeptUserVO> getUserListByDeptId(@Param(value = "page") Page<DeptUserVO> page,
                                         @Param(value = "deptUserSearchInfo") DeptUserSearchDTO deptUserSearchInfo);-->
    <select id="getUserListByDeptId" resultMap="deptUserRespMap">
        SELECT  u.id           as id,
                u.username     as username,
                u.nickname     as nickname,
                u.remark       as remark,
                u.dept_id      as deptId,
                u.post_ids     as postIds,
                u.certificates as certificates,
                u.tags         as tags,
                u.email        as email,
                u.mobile       as mobile,
                u.sex          as sex,
                u.avatar       as avatar,
                u.status       as status,
                u.create_time  as createTime,
                u.update_time  as updateTime,
                r.id           as roleId,
                r.name         as roleName
        FROM system_users u
            left join system_user_role ur
                on u.id = ur.user_id and ur.deleted = 0
            left join system_role r
                on r.id = ur.role_id and r.deleted = 0
        WHERE u.deleted = 0
        AND u.dept_id = #{deptUserSearchInfo.deptId}
        <if test = "deptUserSearchInfo.searchKey != null and deptUserSearchInfo.searchKey != ''">
            AND u.nickname LIKE CONCAT('%', #{deptUserSearchInfo.searchKey}, '%')
        </if>
        order by u.create_time desc
    </select>
</mapper>