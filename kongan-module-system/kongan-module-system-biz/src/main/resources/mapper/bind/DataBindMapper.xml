<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.system.dal.mysql.bind.DataBindMapper">
    <select id="selectBindJobByFlyRecordId"
            resultType="com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.BindJobVO">
        SELECT id jobId,name jobName
        FROM system_job
        WHERE deleted = 0 AND id IN (SELECT job_id
                     FROM system_data_bind
                     WHERE fly_record_id = #{flyRecordId})
    </select>
</mapper>