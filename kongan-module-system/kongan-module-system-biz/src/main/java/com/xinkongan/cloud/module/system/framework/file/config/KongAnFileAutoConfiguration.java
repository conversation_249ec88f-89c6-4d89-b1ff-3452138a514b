package com.xinkongan.cloud.module.system.framework.file.config;

import com.xinkongan.cloud.module.system.framework.file.core.client.FileClientFactory;
import com.xinkongan.cloud.module.system.framework.file.core.client.FileClientFactoryImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 文件配置类
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class KongAnFileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }

}
