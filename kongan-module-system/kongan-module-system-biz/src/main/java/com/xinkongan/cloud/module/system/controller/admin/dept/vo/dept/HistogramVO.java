package com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class HistogramVO {

    @Schema(description = "巡检任务数目")
    private Long inspectionCount = 0L;

    @Schema(description = "告警任务数目")
    private Long alarmCount = 0L;

    @Schema(description = "建模任务数目")
    private Long modelingCount = 0L;

    @Schema(description = "横轴的值")
    private String timePeriod;
}
