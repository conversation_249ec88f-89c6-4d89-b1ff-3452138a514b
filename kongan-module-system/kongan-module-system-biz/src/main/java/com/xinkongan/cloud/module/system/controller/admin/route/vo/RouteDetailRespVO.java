package com.xinkongan.cloud.module.system.controller.admin.route.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinkongan.cloud.framework.kmz.enums.TemplateTypeEnum;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelDetailRespVO;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.TabRespVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialDetailVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.ModelRouteDTO;
import com.xinkongan.cloud.module.system.enums.route.RouteTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class RouteDetailRespVO {

    @Schema(description = "航线id", example = "xx巡检航线")
    private Long id;

    @Schema(description = "航线名称", example = "xx巡检航线")
    private String routeName;

    @Schema(description = "缩略图")
    private String thumbnail;

    @Schema(description = "航点个数", example = "10")
    private Integer wayPointCount;

    @Schema(description = "预计飞行里程，单位 千米（km）", example = "0.88")
    private Float distance;

    @Schema(description = "预计飞行时间，单位 秒（s）", example = "500")
    private Integer duration;

    @Schema(description = "配置的算法个数", example = "12")
    private Integer algorithmCount;

    @Schema(description = "配置的算法", example = "人员检测、烟火识别")
    private String algorithmConf;

    @Schema(description = "任务引用次数", example = "12")
    private Integer taskRefCount;

    @Schema(description = "航线锁定状态", example = "1：锁定，0：解锁")
    private Integer lockFlag;

    @Schema(description = "分享标志", example = "1：分享，0：不是分享")
    private Integer shareFlag;

    @Schema(description = "创建人", example = "张三")
    private String createName;

    @Schema(description = "创建人id", example = "张三")
    private String creator;

    @Schema(description = "机场SN", example = "1")
    private String dockSn;

    @Schema(description = "机场名称", example = "1")
    private String dockName;

    @Schema(description = "无人机sn", example = "1")
    private String droneSn;

    @Schema(description = "无人机名称", example = "1")
    private String droneName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2024-12-31 14:50:00")
    private Date createTime;

    /**
     * 参考：{@link RouteTypeEnum}
     */
    @Schema(description = "航线类型", example = "1")
    private Integer routeType;

    /**
     * {@link TemplateTypeEnum}
     */
    @Schema(description = "航线模板类型")
    private String templateType;

    // 参考起飞点相关字段

    @Schema(description = "参考起飞点经度", example = "117.31")
    private Double takeOffRefPointLongitude;

    @Schema(description = "参考起飞点纬度", example = "31.111")
    private Double takeOffRefPointLatitude;

    @Schema(description = "参考起飞点高度", example = "50")
    private Double takeOffRefPointHeight;

    @Schema(description = "飞行器机型主类型", example = "91")
    private Integer droneEnumValue;

    @Schema(description = "飞行器机型子类型", example = "1")
    private Integer droneSubEnumValue;

    @Schema(description = "负载机型主类型")
    private Integer payloadEnumValue;

    @Schema(description = "负载机型子类型")
    private Integer payloadSubEnumValue;

    @Schema(description = "高度模式", example = "EGM96")
    private String heightMode;

    @Schema(description = "全局航线飞行速度 m/s")
    private Integer autoFlightSpeed;

    @Schema(description = "失控动作 0返航 1继续执行")
    private Integer lostAction;

    @Schema(description = "飞行器偏航角模式")
    private String globalWaypointHeadingParam;

    /**
     * 安全起飞高度 单位:m
     * [1.5, 1500] （高度模式：相对起飞点高度）
     * 注：飞行器起飞后，先爬升至该高度，再根据“飞向首航点模式”的设置飞至首航点。该元素仅在飞行器未起飞时生效。
     */
    @Schema(description = "安全起飞高度 单位:m")
    private Integer takeOffSecurityHeight;

    /**
     * 全局返航高度
     * 飞行器返航时，先爬升至该高度，再进行返航 20 - 500 (m)
     */
    @Schema(description = "全局返航高度")
    private Integer globalRTHHeight;

    @Schema(description = "是否使用最后一个点的返航高度（1 是，0 否）", example = "1")
    private Integer useLastWayPointRthHeight;

    @Schema(description = "航线结束动作")
    private String finishAction;

    @Schema(description = "航线信息", example = "1：分享，0：不是分享")
    private List<RouteFolderRespVO> folders;

    @Schema(description = "照片存储类型")
    private List<String> imageFormat;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "组织名称")
    private String deptName;

    @Schema(description = "航线高度", example = "100")
    private Integer airlineHeight;

    @Schema(description = "全局航线过渡速度(起飞速度)", example = "3")
    private Integer globalTransitionalSpeed;

    @Schema(description = "全局航点转弯模式")
    private String globalWaypointTurnMode;

    @Schema(description = "航线复制次数", example = "0")
    private Integer copyCount;

    @Schema(description = "航线是否可见（1 可见，0 不可见）")
    private Integer visible;

    @Schema(description = "白模状态（1 开启，0 关闭）")
    private Integer whiteMold;

    @Schema(description = "加载的素材信息")
    private List<MaterialInfoVO> materials;

    @Schema(description = "标注信息")
    private List<LabelDetailRespVO> labels;

    @Schema(description = "标记信息")
    private List<TabRespVO> tabInfos;

    @Schema(description = "建模航线参数信息")
    private ModelRouteDTO modelRouteDTO;

    public List<RouteFolderRespVO> getFolders() {
        if (folders == null) {
            folders = new ArrayList<>();
        }
        return folders;
    }

    public List<MaterialInfoVO> getMaterials() {
        if (materials == null) {
            materials = new ArrayList<>();
        }
        return materials;
    }
    public List<LabelDetailRespVO> getLabels() {
        if (labels == null) {
            labels = new ArrayList<>();
        }
        return labels;
    }

    public List<TabRespVO> getTabInfos() {
        if(tabInfos == null){
            tabInfos = new ArrayList<>();
        }
        return tabInfos;
    }
}
