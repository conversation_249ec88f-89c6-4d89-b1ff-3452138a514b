package com.xinkongan.cloud.module.system.controller.admin.flyrecord;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.dto.FlyRecordSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.dto.HistogramSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordRespVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordStatisticDataVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.HistogramRespVO;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "飞控平台 - 飞行数据")
@RestController
@RequestMapping("/system/fly-record-data")
public class FlyRecordDataController {

    @Resource
    private IFlyRecordService flyRecordService;


    @GetMapping("/statistic-data/{deptId}")
    @Operation(summary = "飞行数据-飞行数据统计", description = "飞行数据统计")
    public CommonResult<FlyRecordStatisticDataVO> getStatisticData(@PathVariable("deptId") Long deptId) {
        FlyRecordStatisticDataVO flyRecordStatisticDataVO = flyRecordService.getStatisticDataByDeptId(deptId);
        return CommonResult.success(flyRecordStatisticDataVO);
    }


    @PostMapping("/statistic-data/page")
    @Operation(summary = "飞行数据-飞行数据分页", description = "飞行数据分页")
    public CommonResult<PageResult<FlyRecordRespVO>> getFlyRecordPage(@RequestBody FlyRecordSearchDTO searchParams) {
        PageResult<FlyRecordRespVO> flyRecordPage = flyRecordService.getFlyRecordPage(searchParams);
        return CommonResult.success(flyRecordPage);
    }

    @PostMapping("/statistic-data/histogram")
    @Operation(summary = "飞行数据-直方图数据", description = "直方图数据")
    public CommonResult<List<HistogramRespVO>> getHistogramData(@RequestBody HistogramSearchDTO searchParams) {
        List<HistogramRespVO> histogramRespInfo = flyRecordService.getHistogramData(searchParams);
        return CommonResult.success(histogramRespInfo);
    }
}
