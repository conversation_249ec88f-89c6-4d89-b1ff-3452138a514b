package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 机场禁飞区同步统计响应VO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "管理后台 - 机场禁飞区同步统计响应VO")
public class DockNfzSyncStatisticsRespVO {

    @Schema(description = "总机场数量", example = "10")
    private Integer totalDockCount;

    @Schema(description = "已同步机场数量", example = "8")
    private Integer syncedDockCount;

    @Schema(description = "待同步机场数量", example = "1")
    private Integer pendingSyncDockCount;

    @Schema(description = "同步中机场数量", example = "1")
    private Integer syncingDockCount;

    @Schema(description = "同步失败机场数量", example = "0")
    private Integer syncFailedDockCount;

    @Schema(description = "同步成功率（百分比）", example = "80.00")
    private BigDecimal syncSuccessRate;

    /**
     * 计算同步成功率
     */
    public void calculateSyncSuccessRate() {
        if (totalDockCount == null || totalDockCount == 0) {
            this.syncSuccessRate = BigDecimal.ZERO;
            return;
        }
        
        if (syncedDockCount == null) {
            syncedDockCount = 0;
        }
        
        this.syncSuccessRate = BigDecimal.valueOf(syncedDockCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalDockCount), 2, RoundingMode.HALF_UP);
    }
}
