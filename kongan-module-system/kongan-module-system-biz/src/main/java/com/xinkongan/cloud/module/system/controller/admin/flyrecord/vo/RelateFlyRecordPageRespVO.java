package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description 接警关联数据飞行记录RespVO
 */
@Schema(description = "接警关联数据飞行记录RespVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RelateFlyRecordPageRespVO {

    @Schema(description = "飞行记录id")
    private Long id;

    @Schema(description = "飞行记录名称")
    private String name;

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "起飞时间")
    private LocalDateTime takeOffTime;

    @Schema(description = "飞行时长")
    private Float flightDuration;

    @Schema(description = "飞行里程")
    private Float flightMileage;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "素材数")
    private Integer flyMaterialCount;

    @Schema(description = "场景 0巡检1建模2接警3一键试飞4联合行动5一键起飞")
    private Integer sceneType;

    @Schema(description = "接警场景")
    private String alarmScene;

    @Schema(description = "0关联数据 1手动绑定")
    private Integer bindFlag;

}