package com.xinkongan.cloud.module.system.controller.admin.material;

import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.util.spring.SpringUtils;
import com.xinkongan.cloud.framework.datapermission.core.util.DataPermissionUtils;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.MaterialGisDealCallbackDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.ThreeModelUnzipCallbackDTO;
import com.xinkongan.cloud.module.system.enums.material.MaterialParseCallbackEnums;
import com.xinkongan.cloud.module.system.service.material.IGisBusinessService;
import com.xinkongan.cloud.module.system.service.material.ThreeDModelCallbackDealService;
import com.xinkongan.cloud.module.system.service.material.ThreeDModelMaterialParseHandler;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.SYSTEM_ERROR_PARAM_NULL;


@Slf4j
@Validated
@RestController
@Tag(name = "飞控平台 - 素材处理回调")
@RequestMapping("/system/material")
public class MaterialDealCbController {


    /**
     * gis服务下载二维模型tif的回调
     */
    @PermitAll
    @TenantIgnore
    @PostMapping(value = "/downloadCallback")
    @Operation(summary = "回调接口-二维tif回调")
    public CommonResult<Boolean> gisDownloadCallback(@RequestBody MaterialGisDealCallbackDTO downloadCallbackDTO) {
        log.info("【二维素材解析】回调：{}", downloadCallbackDTO);
        if (downloadCallbackDTO == null) {
            throw new ServiceException(SYSTEM_ERROR_PARAM_NULL);
        }
        String callbackHandler = MaterialParseCallbackEnums.findCallbackHandler(downloadCallbackDTO.getCode());
        IGisBusinessService gisBusinessService = SpringUtils.getBean(callbackHandler, IGisBusinessService.class);
        DataPermissionUtils.executeIgnore(() -> gisBusinessService.dealGisCallback(downloadCallbackDTO));
        return CommonResult.success(true);
    }

    /**
     * 三维素材解压成功的回调
     *
     * @return
     */
    @PermitAll
    @TenantIgnore
    @PostMapping(value = "/unzip/success")
    @Operation(summary = "回调接口-三维回调")
    public CommonResult<Void> threeDModelUnzipSuccessCallback(@RequestBody ThreeModelUnzipCallbackDTO threeModelUnzipCallbackDTO) {
        log.info("【三维素材解解压】回调：{}", threeModelUnzipCallbackDTO);
        if (threeModelUnzipCallbackDTO == null) {
            throw new ServiceException(SYSTEM_ERROR_PARAM_NULL);
        }
        ThreeDModelCallbackDealService threeDModelCallbackDealService = SpringUtils.getBean(ThreeDModelMaterialParseHandler.class);
        DataPermissionUtils.executeIgnore(() -> threeDModelCallbackDealService.dealThreeDModelUnzipCallback(threeModelUnzipCallbackDTO));
        return CommonResult.success();
    }
}
