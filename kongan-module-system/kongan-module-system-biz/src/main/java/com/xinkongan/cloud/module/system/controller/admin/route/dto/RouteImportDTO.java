package com.xinkongan.cloud.module.system.controller.admin.route.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 航线导入请求DTO
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-03
 */
@Data
@Schema(description = "航线导入请求DTO")
public class RouteImportDTO implements Serializable {

    @Schema(description = "KMZ文件URL", example = "https://example.com/route.kmz", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "KMZ文件URL不能为空")
    private String kmzUrl;

    @Schema(description = "航线名称", example = "导入的航线", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "航线名称不能为空")
    private String routeName;

    @Schema(description = "航线类型", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "航线类型不能为空")
    private Integer routeType;

    @Schema(description = "航线描述", example = "从KMZ文件导入的航线")
    private String description;
}
