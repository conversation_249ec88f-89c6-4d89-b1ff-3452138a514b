package com.xinkongan.cloud.module.system.controller.admin.user;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.user.dto.UserSettingDTO;
import com.xinkongan.cloud.module.system.service.user.IUserSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@Tag(name = "通用管理 - 用户设置")
@RestController
@RequestMapping("/system/userSetting")
@Validated
public class UserSettingController {

    @Resource
    private IUserSettingService userSettingService;

    @GetMapping("/get")
    @Operation(summary = "根据用户信息查询用户设置")
    public CommonResult<UserSettingDTO> getUserSetting() {
        return CommonResult.success(userSettingService.getByUser());
    }

    @Operation(summary = "新增或者修改个人设置")
    @PostMapping("/insertOrUpdateSpeed")
    public CommonResult<Boolean> insertOrUpdateSpeed(@RequestBody UserSettingDTO dto) {
        return CommonResult.success(userSettingService.insertOrUpdateSpeed(dto));
    }

    @Operation(summary = "设置引导")
    @PostMapping("/dockGuidance")
    public CommonResult<Boolean> dockGuidance() {
        return CommonResult.success(userSettingService.dockGuidance());
    }

    @Operation(summary = "查询当前用户是否已经进行机场接入引导")
    @GetMapping("/getUserDockGuide")
    public CommonResult<Boolean> dockGuide() {
        UserSettingDTO userSettingDTO = userSettingService.getByUser();
        Boolean dockGuide = userSettingDTO != null && userSettingDTO.getDockGuide() != null && userSettingDTO.getDockGuide();
        return CommonResult.success(dockGuide);
    }

}
