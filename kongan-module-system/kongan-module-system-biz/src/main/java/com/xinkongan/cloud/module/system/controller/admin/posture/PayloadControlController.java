package com.xinkongan.cloud.module.system.controller.admin.posture;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.control.PayloadCommandsParam;
import com.xinkongan.cloud.module.system.service.posture.IPayloadControlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/2/18
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 负载控制")
@RequestMapping("/system/payloadControl")
public class PayloadControlController {

    @Resource
    private IPayloadControlService payloadControlService;

    @Operation(summary = "负载控制")
    @PostMapping("/control")
    public CommonResult<Boolean> control(@RequestBody PayloadCommandsParam param) {
        return CommonResult.success(payloadControlService.control(param));
    }

}
