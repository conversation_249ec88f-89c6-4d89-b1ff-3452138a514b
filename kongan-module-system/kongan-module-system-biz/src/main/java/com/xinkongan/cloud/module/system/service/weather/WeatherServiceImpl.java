package com.xinkongan.cloud.module.system.service.weather;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.module.system.controller.admin.weather.vo.DailyWeatherVO;
import com.xinkongan.cloud.module.system.dto.DailyWeatherParamDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.framework.weather.WeatherConfig;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class WeatherServiceImpl implements IWeatherService {

    @Resource
    private WeatherConfig weatherConfig;


    @Override
    public DailyWeatherVO getDailyWeatherInfo(DailyWeatherParamDTO dailyWeatherParamInfo) {
        String url = weatherConfig.buildUrl(dailyWeatherParamInfo.getLongitude(), dailyWeatherParamInfo.getLatitude());
        log.info("[发送请求获取天气信息]，请求url：{}", url);
        String body = HttpRequest.get(url)
                .header("X-QW-Api-Key", weatherConfig.buildToken())
                .timeout(3 << 10)
                .execute()
                .body();
        log.info("[获取的天气结果为]：{}", body);
        JSONObject jsonObject = JSONObject.parseObject(body);
        Integer code = jsonObject.getInteger("code");
        if (code != 200) {
            throw new ServiceException(ErrorCodeConstants.WEATHER_SERVER_ERROR);
        }
        JSONArray jsonArray = jsonObject.getJSONArray("daily");
        List<DailyWeatherVO> dailyWeathers = jsonArray.toJavaList(DailyWeatherVO.class);
        if (CollectionUtil.isEmpty(dailyWeathers)) {
            return null;
        }
        return dailyWeathers.get(0);
    }
}
