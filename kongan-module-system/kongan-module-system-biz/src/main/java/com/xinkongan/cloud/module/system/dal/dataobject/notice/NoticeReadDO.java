package com.xinkongan.cloud.module.system.dal.dataobject.notice;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinkongan.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 通知阅读记录表
 */
@TableName("system_notice_read")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NoticeReadDO extends BaseDO {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 消息通知id
     */
    @TableField("notice_id")
    private Long noticeId;

    /**
     * 是否已读1是0否
     */
    @TableField("read_flag")
    private Boolean readFlag;

    @TableField("tenant_id")
    private Long tenantId;
}



