package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 警情点列表
 * <AUTHOR>
 * @Date 2025/3/25 9:10
 */
@Data
@Schema(description = "警情点列表RespVO")
public class AlarmListRespVO {

    @Schema(description = "警情任务id")
    private Long id;

    @Schema(description = "警情id")
    private Long alarmId;

    @Schema(description = "警情名称")
    private String name;

    @Schema(description = "警情类型")
    private String alarmScene;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "组织名称")
    private String deptName;

    @Schema(description = "状态 0待审批 , 1待执行 , 2执行中 , 3未完成 , 4已取消 , 5已执行 ,6 执行失败, 7已失效")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}