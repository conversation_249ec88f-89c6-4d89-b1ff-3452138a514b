package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 任务类型时间段统计VO
 * <AUTHOR>
 * @Date 2025/4/22 16:54
 */
@Schema(description = "任务类型时间段统计VO")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class JobStatisticByTimeRangeReqVO {

    @Schema(description = "开始时间", example = "2025-04-22 16:54:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime beginTime;

    @Schema(description = "结束时间", example = "2025-04-22 16:54:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
}