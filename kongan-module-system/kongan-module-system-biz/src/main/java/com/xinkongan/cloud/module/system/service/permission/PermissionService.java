package com.xinkongan.cloud.module.system.service.permission;

import com.xinkongan.cloud.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserRoleVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.Collections.singleton;

/**
 * 权限 Service 接口
 * <p>
 * 提供用户-角色、角色-菜单、角色-部门的关联权限处理
 *
 * <AUTHOR>
 */
public interface PermissionService {

    /**
     * 判断是否有权限，任一一个即可
     *
     * @param userId      用户编号
     * @param permissions 权限
     * @return 是否
     */
    boolean hasAnyPermissions(Long userId, String... permissions);

    /**
     * 判断是否有角色，任一一个即可
     *
     * @param roles 角色数组
     * @return 是否
     */
    boolean hasAnyRoles(Long userId, String... roles);

    // ========== 角色-菜单的相关方法  ==========

    /**
     * 设置角色菜单
     *
     * @param roleId  角色编号
     * @param menuIds 菜单编号集合
     */
    void assignRoleMenu(Long roleId, Set<Long> menuIds);

    /**
     * 处理角色删除时，删除关联授权数据
     *
     * @param roleId 角色编号
     */
    void processRoleDeleted(Long roleId);

    /**
     * 处理菜单删除时，删除关联授权数据
     *
     * @param menuId 菜单编号
     */
    void processMenuDeleted(Long menuId);

    /**
     * 获得角色拥有的菜单编号集合
     *
     * @param roleId 角色编号
     * @return 菜单编号集合
     */
    default Set<Long> getRoleMenuListByRoleId(Long roleId) {
        return getRoleMenuListByRoleId(singleton(roleId));
    }

    /**
     * 获取角色拥有的菜单编号集合
     *
     * @param roleCode 角色编码
     * @return 菜单编号集合
     */
    Set<Long> getRoleMenuListByRoleCode(String roleCode);


    /**
     * 获得角色们拥有的菜单编号集合
     *
     * @param roleIds 角色编号数组
     * @return 菜单编号集合
     */
    Set<Long> getRoleMenuListByRoleId(Collection<Long> roleIds);

    /**
     * 获得拥有指定菜单的角色编号数组，从缓存中获取
     *
     * @param menuId 菜单编号
     * @return 角色编号数组
     */
    Set<Long> getMenuRoleIdListByMenuIdFromCache(Long menuId);

    // ========== 用户-角色的相关方法  ==========

    /**
     * 设置用户角色
     *
     * @param userId  角色编号
     * @param roleIds 角色编号集合
     */
    void assignUserRole(Long userId, Set<Long> roleIds);


    /**
     * 更新用户角色
     *
     * @param userId-用户ID
     * @param roleId-角色ID
     */
    void updateUserRole(Long userId, Long roleId);


    /**
     * 处理用户删除时，删除关联授权数据
     *
     * @param userId 用户编号
     */
    void processUserDeleted(Long userId);

    /**
     * 获得拥有多个角色的用户编号集合
     *
     * @param roleIds 角色编号集合
     * @return 用户编号集合
     */
    Set<Long> getUserRoleIdListByRoleId(Collection<Long> roleIds);


    /**
     * 根据用户id获取用户角色列表
     * @param userId 用户id
     * @return 角色列表
     */
    List<UserRoleVO> getUserRoleByUserId(Long userId);

    /**
     * 获得用户拥有的角色编号集合
     *
     * @param userId 用户编号
     * @return 角色编号集合
     */
    Set<Long> getUserRoleIdListByUserId(Long userId);

    /**
     * 获得用户拥有的角色编号集合，从缓存中获取
     *
     * @param userId 用户编号
     * @return 角色编号集合
     */
    Set<Long> getUserRoleIdListByUserIdFromCache(Long userId);


    /**
     *  <p>查询用户拥有的角色的集合</p>
     *  <p>注意： 该方法的用户id 是在内存中过滤的,先查询当前用户能看见的所有人员角色信息，然后通过id过滤</p>
     * @param userIds 用户id
     * @return Map:key 用户id,value 角色集合
     */
    Map<Long, List<UserRoleVO>> getUserRoleMapsByUserIds(Collection<Long> userIds);


    /**
     * 查询角色对应的用户集合
     *
     * @param roleIds 角色id
     * @return Map: key 角色id，value 用户集合
     */
    Map<Long, List<UserRoleVO>> getUserRoleMapsByRoleIds(Collection<Long> roleIds);


    /**
     * 获得登陆用户的部门数据权限
     *
     * @param userId 用户编号
     * @return 部门数据权限
     */
    DeptDataPermissionRespDTO getDeptDataPermission(Long userId);

}
