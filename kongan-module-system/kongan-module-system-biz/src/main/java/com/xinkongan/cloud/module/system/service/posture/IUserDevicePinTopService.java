package com.xinkongan.cloud.module.system.service.posture;

import java.util.List;

/**
 * (SystemUserDevicePinTop)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-12 15:42:03
 */
public interface IUserDevicePinTopService {

    /**
     * 将某个设备置顶
     *
     * @param deviceKey 设备ID
     * @return 置顶结果
     */
    boolean pinTop(String deviceKey);


    /**
     * 查询某个设备是否被置顶
     *
     * @param deviceKey 设备ID
     * @return 置顶结果
     */
    boolean isPinTop(String deviceKey);

    /**
     * 取消某个设备置顶
     *
     * @param deviceKey 设备ID
     * @return 置顶结果
     */
    boolean unPinTop(String deviceKey);

    /**
     * 查询当前用户置顶的设备列表并根据置顶时间倒序排列
     *
     * @return 设备列表
     */
    List<String> getPinTopDeviceList();

    /**
     * 通知置顶改变
     */
    void notifyTheTopChange();

}
