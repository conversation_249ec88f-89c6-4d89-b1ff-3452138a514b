package com.xinkongan.cloud.module.system.controller.admin.measure.vo;

import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * 创建激光打点的 VO
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "激光打点 - 创建激光打点请求VO")
public class CreateMeasureTargetReqVO extends FileSaveInfoDTO {

    @Schema(description = "经度", example = "120.123456")
    @NotNull(message = "经度不能为空")
    private Double longitude;

    @Schema(description = "纬度", example = "30.123456")
    @NotNull(message = "纬度不能为空")
    private Double latitude;

    @Schema(description = "绝对高度", example = "100.0")
    @NotNull(message = "绝对高度不能为空")
    private Double height;

    @Schema(description = "当前飞行记录id", example = "1")
    @NotNull(message = "飞行记录id不能为空")
    private Long flyRecordId;

    @Schema(description = "无人机sn", example = "ABC1231")
    @NotBlank(message = "无人机sn不能为空")
    private String sn;

    @Schema(description = "文件大小", example = "1000000")
    private Long size;
}
