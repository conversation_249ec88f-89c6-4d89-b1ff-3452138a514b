package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlyRecordStatisticDataVO {

    @Schema(description = "飞行里程，单位：m")
    private Double totalMileage;

    @Schema(description = "飞行时长，单位：s")
    private Double totalTime;

    @Schema(description = "飞行次数")
    private Long totalCount;

}
