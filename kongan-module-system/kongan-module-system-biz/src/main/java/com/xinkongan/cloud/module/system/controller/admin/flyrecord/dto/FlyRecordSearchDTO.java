package com.xinkongan.cloud.module.system.controller.admin.flyrecord.dto;

import com.xinkongan.cloud.framework.common.dto.SearchDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FlyRecordSearchDTO extends SearchDTO {

    @Schema(description = "组织id")
    private String deptId;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

}
