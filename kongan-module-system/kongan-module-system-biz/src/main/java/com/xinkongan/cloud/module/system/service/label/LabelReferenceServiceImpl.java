package com.xinkongan.cloud.module.system.service.label;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.datapermission.core.plugins.SharePluginParam;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.label.dto.LabelReferSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.LabelTopVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.label.LabelReferenceDO;
import com.xinkongan.cloud.module.system.dal.mysql.label.LabelReferenceMapper;
import com.xinkongan.cloud.module.system.enums.label.LabelReferenceEnums;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class LabelReferenceServiceImpl implements ILabelReferenceService {

    @Resource
    private LabelReferenceMapper labelReferenceMapper;


    @Override
    public void delLabelReference(String referenceKey, LabelReferenceEnums labelReference) {
        labelReferenceMapper.delete(
                new LambdaQueryWrapperX<LabelReferenceDO>()
                        .eq(LabelReferenceDO::getReferenceKey, referenceKey)
                        .eq(LabelReferenceDO::getType, labelReference.getType())
        );
    }

    @Override
    public void addLabelReference(String referenceKey, List<Long> labelIds, LabelReferenceEnums labelReference) {
        if (CollectionUtil.isEmpty(labelIds)) {
            return;
        }
        labelReferenceMapper.delete(
                new LambdaQueryWrapperX<LabelReferenceDO>()
                        .eq(LabelReferenceDO::getReferenceKey, referenceKey)
                        .eq(LabelReferenceDO::getType, labelReference.getType())
                        .in(LabelReferenceDO::getLabelId, labelIds)
        );

        List<LabelReferenceDO> labelReferenceDOS =
                labelIds.stream().map(id -> {
                    LabelReferenceDO labelReferenceDO = new LabelReferenceDO();
                    labelReferenceDO.setReferenceKey(referenceKey);
                    labelReferenceDO.setType(LabelReferenceEnums.ROUTE_REFERENCE.getType());
                    labelReferenceDO.setLabelId(id);
                    return labelReferenceDO;
                }).toList();
        labelReferenceMapper.insertBatch(labelReferenceDOS);
    }

    @Override
    public Boolean checkLabelReference(Long labelId, LabelReferenceEnums labelReference) {
        LambdaQueryWrapperX<LabelReferenceDO> wrapperX = new LambdaQueryWrapperX<LabelReferenceDO>()
                .eq(LabelReferenceDO::getLabelId, labelId);
        if (labelReference != null) {
            wrapperX.eq(LabelReferenceDO::getType, labelReference.getType());
        }
        Long count = labelReferenceMapper.selectCount(wrapperX);
        return count > 0;
    }

    @Override
    public PageResult<RouteRespVO> getLabelReferenceRoutePage(LabelReferSearchDTO search) {
        Page<RouteRespVO> page = new Page<>(search.getPage(), search.getOffset());

        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.ROUTE_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.ROUTE_RESOURCE.getResourceType())
                .shareFlag(search.getShareFlag())
                .build();

        List<RouteRespVO> records = labelReferenceMapper.selectRouteByLabelIdWithShare(page, sharePluginParam, search);
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public Long getLabelReferenceRouteCount() {
        return labelReferenceMapper.getLabelReferenceRouteCount();
    }

    @Override
    public List<LabelTopVO> getLabelReferenceTop() {
        return labelReferenceMapper.getLabelReferenceTop();
    }

    @Override
    public List<Long> getLabelIdsByReferenceKey(String referenceKey, LabelReferenceEnums labelReferenceEnums) {
        List<LabelReferenceDO> labelReferences = labelReferenceMapper.selectList(
                new LambdaQueryWrapperX<LabelReferenceDO>()
                        .eq(LabelReferenceDO::getReferenceKey, referenceKey)
                        .eq(LabelReferenceDO::getType, labelReferenceEnums.getType())
        );
        return labelReferences.stream().map(LabelReferenceDO::getLabelId).toList();
    }
}
