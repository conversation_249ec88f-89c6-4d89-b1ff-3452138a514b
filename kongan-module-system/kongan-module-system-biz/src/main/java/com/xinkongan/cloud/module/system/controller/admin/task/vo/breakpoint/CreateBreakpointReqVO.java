package com.xinkongan.cloud.module.system.controller.admin.task.vo.breakpoint;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 创建断点请求VO
 * <AUTHOR>
 * @Date 2025/01/20
 */
@Data
@Schema(description = "创建断点请求VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateBreakpointReqVO {

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "任务ID不能为空")
    private Long jobId;

    @Schema(description = "飞行ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "飞行ID不能为空")
    private Long flyId;

    @Schema(description = "机场SN", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "机场SN不能为空")
    private String dockSn;

    @Schema(description = "无人机SN")
    private String droneSn;

    @Schema(description = "断点航点索引", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "断点航点索引不能为空")
    private Integer index;

    @Schema(description = "断点经度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "断点经度不能为空")
    @DecimalMin(value = "-180.0", message = "经度必须在-180到180之间")
    @DecimalMax(value = "180.0", message = "经度必须在-180到180之间")
    private Float longitude;

    @Schema(description = "断点纬度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "断点纬度不能为空")
    @DecimalMin(value = "-90.0", message = "纬度必须在-90到90之间")
    @DecimalMax(value = "90.0", message = "纬度必须在-90到90之间")
    private Float latitude;

    @Schema(description = "断点高度(米)")
    private Float height;

    @Schema(description = "姿态航向角")
    private Float attitudeHead;

    @Schema(description = "断点原因")
    private Integer breakReason;

    @Schema(description = "断点状态：0-在航段上，1-在航点上", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "断点状态不能为空")
    private Integer state;

    @Schema(description = "飞行进度百分比")
    private Float progress;
}
