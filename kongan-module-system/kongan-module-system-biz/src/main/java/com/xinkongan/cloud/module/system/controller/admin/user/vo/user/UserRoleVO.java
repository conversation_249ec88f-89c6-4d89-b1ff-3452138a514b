package com.xinkongan.cloud.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserRoleVO {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "角色id")
    private Long roleId;

    @Schema(description = "排序sort")
    private Integer sort;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "用户别名")
    private String nickName;
}
