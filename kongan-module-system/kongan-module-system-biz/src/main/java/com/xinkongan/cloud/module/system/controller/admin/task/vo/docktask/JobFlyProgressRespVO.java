package com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask;

import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyProgressDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyProgressDetailDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobFlyProgressRespVO extends JobFlyProgressDO {

    private List<JobFlyProgressDetailDO> details;

}
