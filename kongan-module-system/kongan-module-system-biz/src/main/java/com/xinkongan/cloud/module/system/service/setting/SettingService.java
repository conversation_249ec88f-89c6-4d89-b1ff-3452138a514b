package com.xinkongan.cloud.module.system.service.setting;

import com.xinkongan.cloud.module.system.api.setting.dto.UserMapSettingReqVO;
import com.xinkongan.cloud.module.system.api.setting.dto.SettingRespVO;

/**
 * 用户设置 Service 接口
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-27
 */
public interface SettingService {

    /**
     * 用户手动设置地图信息
     * 
     * @param reqVO 用户地图设置请求VO
     */
    void updateUserMapSetting(UserMapSettingReqVO reqVO);

    /**
     * 查询当前用户组织的地图设置
     * 
     * @return 用户设置响应VO
     */
    SettingRespVO getUserMapSetting();

}
