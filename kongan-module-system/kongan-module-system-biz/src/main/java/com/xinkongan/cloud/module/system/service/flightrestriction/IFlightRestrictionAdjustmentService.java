package com.xinkongan.cloud.module.system.service.flightrestriction;

import com.xinkongan.cloud.module.system.controller.admin.route.dto.WayPointDTO;
import com.xinkongan.cloud.module.system.dto.flightrestriction.HeightAdjustmentResultDTO;

import java.util.List;

/**
 * 限飞区高度调整服务接口
 *
 * <AUTHOR> 4.0 sonnet
 */
public interface IFlightRestrictionAdjustmentService {

    /**
     * 调整航点高度以符合限飞区要求
     *
     * @param wayPoints 航点列表
     * @return 高度调整结果
     */
    List<WayPointDTO> adjustWaypointHeights(List<WayPointDTO> wayPoints);

    /**
     * 检测航线是否与限飞区存在冲突
     *
     * @param wayPoints 航点列表
     * @return 是否存在冲突
     */
    boolean hasFlightRestrictionConflicts(List<WayPointDTO> wayPoints);
}
