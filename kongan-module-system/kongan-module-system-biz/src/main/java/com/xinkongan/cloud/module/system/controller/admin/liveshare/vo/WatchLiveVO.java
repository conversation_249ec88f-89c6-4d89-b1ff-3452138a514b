package com.xinkongan.cloud.module.system.controller.admin.liveshare.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WatchLiveVO {

    @Schema(description = "分享的直播通道")
    private String channel;

    @Schema(description = "token")
    private String token;

}
