package com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

/**
 * 限飞区更新请求VO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "管理后台 - 限飞区更新请求VO")
public class FlightRestrictionZoneUpdateReqVO extends FlightRestrictionZoneSaveReqVO {

    @Schema(description = "限飞区ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "限飞区ID不能为空")
    private Long id;
}
