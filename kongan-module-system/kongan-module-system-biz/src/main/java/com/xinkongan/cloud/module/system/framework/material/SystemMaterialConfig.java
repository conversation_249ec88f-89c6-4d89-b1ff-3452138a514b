package com.xinkongan.cloud.module.system.framework.material;


import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "kongan.material")
public class SystemMaterialConfig {

    @PostConstruct
    public void init() {
        log.info("开始加载素材解析配置文件------------------------------------------------------------");
        log.info("stsUrl:{}", stsUrl);
        log.info("radius:{}", radius);
        log.info("shareUrlPrefix:{}", shareUrlPrefix);
        log.info("gisProperties:{}", gisProperties);
        log.info("--------------------------------------------------------------------------------");
    }

    /**
     * 获取STS的url
     */
    private String stsUrl = "http://192.168.0.121:48080/admin-api/system/file/material/server/get/sts";

    /**
     * 三维模型素材对比半径 单位：米
     */
    private Integer radius = 1000;

    /**
     * 素材外部分享的前缀
     */
    private String shareUrlPrefix;

    /**
     * gis相关配置
     */
    private GisProperties gisProperties;


    @Data
    public static class GisProperties {

        // tif文件的上传目录
        private String path;
        // 下载tif地址
        private String downloadUrl;
        // 查询缓存占用空间大小地址
        private String dirSizeUrl;
        // 下载tif回调
        private String downloadCallbackUrl;
        // 缓存目录的地址 默认/home/<USER>/soft/geoserver-2.25.0/data/gwc
        private String gwcPath = "/home/<USER>/soft/geoserver-2.25.0/data/gwc";
    }

    public String getStsUrl(String dirPrefix, Long tenantId) {
        StringBuilder stsUrl = new StringBuilder(this.stsUrl)
                .append("?dirPrefix=")
                .append(dirPrefix)
                .append("&tenantId=")
                .append(tenantId);
        return stsUrl.toString();
    }


    public String getShareLinkUrl(Long shareKey) {
        return shareUrlPrefix + shareKey;
    }
}
