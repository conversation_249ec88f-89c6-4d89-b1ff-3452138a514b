package com.xinkongan.cloud.module.system.controller.admin.setting;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.api.setting.dto.UserMapSettingReqVO;
import com.xinkongan.cloud.module.system.api.setting.dto.SettingRespVO;
import com.xinkongan.cloud.module.system.service.setting.SettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 用户设置 Controller
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-27
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 用户设置")
@RequestMapping("/system/setting")
public class SettingController {

    @Resource
    private SettingService settingService;

    @PostMapping("/update-user-settings")
    @Operation(summary = "用户手动设置地图信息", description = "用户手动设置组织的经度、纬度、中文地址等地图信息")
    @PreAuthorize("@ss.hasPermission('system:dept:update')")
    public CommonResult<Boolean> updateUserSettings(@Valid @RequestBody UserMapSettingReqVO reqVO) {
        log.info("用户手动设置地图信息，用户ID: {}, 请求参数: {}", SecurityFrameworkUtils.getLoginUserId(), reqVO);
        
        // 调用用户设置服务
        settingService.updateUserMapSetting(reqVO);
        
        log.info("用户地图设置更新成功，用户ID: {}", SecurityFrameworkUtils.getLoginUserId());
        return success(true);
    }

    @GetMapping("/user-settings")
    @Operation(summary = "查询当前用户组织的地图设置", description = "查询当前登录用户所属组织的地图设置信息")
    @PreAuthorize("@ss.hasPermission('system:dept:query')")
    public CommonResult<SettingRespVO> getUserSettings() {
        log.info("用户查询地图设置，用户ID: {}", SecurityFrameworkUtils.getLoginUserId());
        
        // 调用用户设置服务
        SettingRespVO respVO = settingService.getUserMapSetting();
        
        log.info("用户地图设置查询成功，用户ID: {}", SecurityFrameworkUtils.getLoginUserId());
        return success(respVO);
    }

}
