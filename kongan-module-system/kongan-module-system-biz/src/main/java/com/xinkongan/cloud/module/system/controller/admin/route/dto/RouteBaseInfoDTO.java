package com.xinkongan.cloud.module.system.controller.admin.route.dto;


import com.xinkongan.cloud.framework.kmz.enums.FinishActionEnum;
import com.xinkongan.cloud.framework.kmz.enums.HeightModeEnum;
import com.xinkongan.cloud.framework.kmz.enums.TemplateTypeEnum;
import com.xinkongan.cloud.framework.kmz.enums.WaypointHeadingModeEnum;
import com.xinkongan.cloud.module.system.controller.admin.label.dto.TabSaveDTO;
import com.xinkongan.cloud.module.system.enums.route.RouteLostActionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
public class RouteBaseInfoDTO implements Serializable {

    /**
     * {@link TemplateTypeEnum}
     */
    @Schema(description = "航线模板类型")
    private String templateType;

    @Schema(description = "航点个数", example = "10")
    private Integer wayPointCount;

    @Schema(description = "预计飞行里程，单位 千米（km）", example = "1.88")
    private Float distance;

    @Schema(description = "预计飞行时间，单位 秒（s）", example = "500")
    private Integer duration;

    @Schema(description = "配置的算法个数", example = "12")
    private Integer algorithmCount;

    // 参考起飞点相关字段

    @Schema(description = "参考起飞点经度", example = "117.31")
    private Double takeOffRefPointLongitude;

    @Schema(description = "参考起飞点纬度", example = "31.111")
    private Double takeOffRefPointLatitude;

    @Schema(description = "参考起飞点高度", example = "50")
    private Double takeOffRefPointHeight;


    /**
     * 参考：{@link HeightModeEnum}
     */
    @Schema(description = "高度模式", example = "EGM96", implementation = HeightModeEnum.class)
    private String heightMode;

    @Schema(description = "航线高度", example = "100")
    private Integer airlineHeight;

    @Schema(description = "全局航线飞行速度 m/s", example = "5")
    private Integer autoFlightSpeed;

    @Schema(description = "全局航线过渡速度(起飞速度)", example = "5")
    private Integer globalTransitionalSpeed;

    @Schema(description = "安全起飞高度 单位:m", example = "100")
    private Integer takeOffSecurityHeight;

    /**
     * 全局返航高度
     * 飞行器返航时，先爬升至该高度，再进行返航 20 - 500 (m)
     */
    @Schema(description = "全局返航高度", example = "120")
    private Integer globalRTHHeight;

    @Schema(description = "是否使用最后一个点的返航高度（1 是，0 否）", example = "1")
    private Integer useLastWayPointRthHeight;

    /**
     * {@link WaypointHeadingModeEnum}
     */
    @Schema(description = "飞行器偏航角模式", example = "followWayline")
    private String globalWaypointHeadingParam;

    /**
     * {@link RouteLostActionTypeEnum}
     */
    @Schema(description = "失控动作 0返航 1继续执行", example = "1")
    private Integer lostAction;

    /**
     * {@link FinishActionEnum}
     */
    @Schema(description = "航线结束动作", example = "goHome")
    private String finishAction;

    /**
     * <p>coordinateTurn：协调转弯，不过点，提前转弯</p>
     * <p>toPointAndStopWithDiscontinuityCurvature：直线飞行，飞行器到点停</p>
     * <p>toPointAndStopWithContinuityCurvature：曲线飞行，飞行器到点停</p>
     * <p>toPointAndPassWithContinuityCurvature：曲线飞行，飞行器过点不停</p>
     */
    @Schema(description = "全局航点转弯模式", example = "coordinateTurn")
    private String globalWaypointTurnMode;


    @Schema(description = "飞行器机型主类型", example = "91")
    private Integer droneEnumValue;

    @Schema(description = "飞行器机型子类型", example = "1")
    private Integer droneSubEnumValue;

    @Schema(description = "负载机型主类型")
    private Integer payloadEnumValue;

    @Schema(description = "负载机型子类型")
    private Integer payloadSubEnumValue;

    @Schema(description = "航线具体条数")
    private List<RouteFolderDTO> folders;
    
    @Schema(description = "建模航线对象参数")
    private ModelRouteDTO modelRouteDTO;

    @Schema(description = "照片存储类型", example = "wide")
    private List<String> imageFormat;

    @Schema(description = "航线中加载的素材id")
    private List<Long> materialIds;

    @Schema(description = "航线中加载的标注id")
    private List<Long> labelIds;

    @Schema(description = "标记数据保存")
    private List<TabSaveDTO> tabSaveInfos;

    public List<RouteFolderDTO> getFolders() {
        if (folders == null) {
            folders = new ArrayList<>();
        }
        return folders;
    }

    public List<String> getImageFormat() {
        if (imageFormat == null) {
            this.imageFormat = new ArrayList<>();
        }
        return imageFormat;
    }

    public String getImageFormatStr() {
        return String.join(",", getImageFormat());
    }

    public List<Long> getMaterialIds() {
        if (materialIds == null) {
            materialIds = new ArrayList<>();
        }
        return materialIds;
    }

    public List<Long> getLabelIds() {
        if (labelIds == null) {
            labelIds = new ArrayList<>();
        }
        return labelIds;
    }

    public List<TabSaveDTO> getTabSaveInfos() {
        if(tabSaveInfos == null){
            tabSaveInfos = new ArrayList<>();
        }
        return tabSaveInfos;
    }
}
