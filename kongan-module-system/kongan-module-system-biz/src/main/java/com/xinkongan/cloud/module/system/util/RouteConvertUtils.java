package com.xinkongan.cloud.module.system.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xinkongan.cloud.framework.kmz.constant.RouteDocumentConstant;
import com.xinkongan.cloud.framework.kmz.dto.*;
import com.xinkongan.cloud.framework.kmz.enums.*;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.*;
import com.xinkongan.cloud.module.system.enums.route.RouteLostActionTypeEnum;
import com.xinkongan.cloud.module.system.enums.route.RouteTypeEnum;
import com.xinkongan.cloud.module.system.enums.route.WayPointActionTransTypeEnum;
import com.xinkongan.cloud.module.system.enums.route.WayPointActionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
@Slf4j
public class RouteConvertUtils {

    public static void main(String[] args) {
        RouteBaseInfoDTO routeBaseInfo = new RouteBaseInfoDTO();
        routeBaseInfo.setTemplateType("mapping3d");
        routeBaseInfo.setWayPointCount(3);
        routeBaseInfo.setDistance(20000f);
        routeBaseInfo.setDuration(111);
        routeBaseInfo.setTakeOffRefPointLongitude(117.31);
        routeBaseInfo.setTakeOffRefPointLatitude(31.111);
        routeBaseInfo.setTakeOffRefPointHeight(50.0);
        routeBaseInfo.setHeightMode("EGM96"); // 对应HeightModeEnum.EGM96
        routeBaseInfo.setAirlineHeight(100);
        routeBaseInfo.setAutoFlightSpeed(5);
        routeBaseInfo.setGlobalTransitionalSpeed(5);
        routeBaseInfo.setTakeOffSecurityHeight(100);
        routeBaseInfo.setGlobalRTHHeight(120);
        routeBaseInfo.setUseLastWayPointRthHeight(1);
        routeBaseInfo.setGlobalWaypointHeadingParam("followWayline"); // 对应WaypointHeadingModeEnum.FOLLOW_WAYLINE
        routeBaseInfo.setLostAction(1); // 对应RouteLostActionTypeEnum.CONTINUE
        routeBaseInfo.setFinishAction("goHome"); // 对应FinishActionEnum.GO_HOME
        routeBaseInfo.setGlobalWaypointTurnMode("coordinateTurn");
        routeBaseInfo.setDroneEnumValue(91);
        routeBaseInfo.setDroneSubEnumValue(1);
        routeBaseInfo.setPayloadEnumValue(53);
        routeBaseInfo.setPayloadSubEnumValue(2);
        routeBaseInfo.setFolders(getList());
        ModelRouteDTO modelRouteDTO = new ModelRouteDTO();
        List<PolygonLocation> polygonLocations = new ArrayList<>();
        polygonLocations.add(new PolygonLocation().setLongitude("117.222").setLatitude("31.88888").setAltitude("0"));
        polygonLocations.add(new PolygonLocation().setLongitude("117.333").setLatitude("31.77777").setAltitude("0"));
        polygonLocations.add(new PolygonLocation().setLongitude("117.444").setLatitude("31.66666").setAltitude("0"));
        polygonLocations.add(new PolygonLocation().setLongitude("117.555").setLatitude("31.55555").setAltitude("0"));
        modelRouteDTO.setRouteId(111L).setDirection(160).setOverlap(new Overlap().setOrthoCameraOverlapH(70)
                .setOrthoCameraOverlapW(80).setInclinedCameraOverlapH(70).setInclinedCameraOverlapW(80))
                .setPolygonLocations(polygonLocations).setEllipsoidHeight(100d).setHeight(100d).setGlobalShootHeight(55d)
                .setSurfaceAbsoluteHeight(55d).setGlobalTransitionalSpeed(10f).setInclinedGimbalPitch(-45)
                .setInclinedFlightSpeed(5f);
        routeBaseInfo.setModelRouteDTO(modelRouteDTO);
        List<String> im =new ArrayList<>();
        im.add("wide");
        routeBaseInfo.setImageFormat(im);


        WpmlRouteInfo wpmlRouteInfo = convertRouteContent(routeBaseInfo, 3);
        System.out.println(wpmlRouteInfo);


    }

    static  List<RouteFolderDTO> getList(){
        List<RouteFolderDTO> folderList = new ArrayList<>();
        RouteFolderDTO folder1 = new RouteFolderDTO();
        folder1.setDistance(1613f);
        folder1.setDuration(76f);
        WayPointDTO wayPointDTO1 = new WayPointDTO();
        WayPointDTO wayPointDTO2 = new WayPointDTO();
        WayPointActionInfoDTO wayPointActionInfoDTO1 = new WayPointActionInfoDTO();
        WayPointActionInfoDTO wayPointActionInfoDTO2 = new WayPointActionInfoDTO();
        wayPointActionInfoDTO1.setActionType(1).setType("gimbalRotate").setValue("-90").setNumber(0).
                setActionGroupMode("sequence").setActionTriggerType("multipletiming");
        wayPointActionInfoDTO2.setActionType(1).setType("takePhoto").setValue("2").setNumber(0).
                setActionGroupMode("sequence").setActionTriggerType("multipletiming");
        List<WayPointActionInfoDTO> wayPointActionInfoDTOList = new ArrayList<>();
        wayPointActionInfoDTOList.add(wayPointActionInfoDTO1);
        wayPointActionInfoDTOList.add(wayPointActionInfoDTO2);
        wayPointDTO1.setPointIndex(0).setLongitude(117.258d).setLatitude(31.839651d).
                setExecuteHeight(100f).setWaypointSpeed(5f).setGimbalPitchAngle(-90f).setWaypointHeadingParam("followWayline").
        setWaypointHeadingAngle(-45d).setWaypointTurnMode("coordinateTurn").setLensMode("wide").setElevation(10f)
                .setActions(wayPointActionInfoDTOList);
        wayPointDTO2.setPointIndex(1).setLongitude(117.22222d).setLatitude(31.33333d).
                setExecuteHeight(101f).setWaypointSpeed(5f).setGimbalPitchAngle(-90f).setWaypointHeadingParam("followWayline").
                setWaypointHeadingAngle(-45d).setWaypointTurnMode("coordinateTurn").setLensMode("wide").setElevation(10f);
        List<WayPointDTO> wayPointDTOList = new ArrayList<>();
        wayPointDTOList.add(wayPointDTO1);
        wayPointDTOList.add(wayPointDTO2);
        folder1.setPoints(wayPointDTOList);


        RouteFolderDTO folder2 = new RouteFolderDTO();
        folder2.setDistance(1613f);
        folder2.setDuration(76f);
        WayPointDTO wayPointDTO3 = new WayPointDTO();
        WayPointDTO wayPointDTO4 = new WayPointDTO();
        WayPointActionInfoDTO wayPointActionInfoDTO3 = new WayPointActionInfoDTO();
        WayPointActionInfoDTO wayPointActionInfoDTO4 = new WayPointActionInfoDTO();
        wayPointActionInfoDTO3.setActionType(1).setType("gimbalRotate").setValue("-45").setNumber(0).
                setActionGroupMode("sequence").setActionTriggerType("multipletiming");
        wayPointActionInfoDTO4.setActionType(1).setType("takePhoto").setValue("2").setNumber(0).
                setActionGroupMode("sequence").setActionTriggerType("multipletiming");
        List<WayPointActionInfoDTO> wayPointActionInfoDTOList2 = new ArrayList<>();
        wayPointActionInfoDTOList2.add(wayPointActionInfoDTO3);
        wayPointActionInfoDTOList2.add(wayPointActionInfoDTO4);
        wayPointDTO3.setPointIndex(0).setLongitude(117.258d).setLatitude(31.839651d).
                setExecuteHeight(100f).setWaypointSpeed(5f).setGimbalPitchAngle(-45f).setWaypointHeadingParam("followWayline").
                setWaypointHeadingAngle(-45d).setWaypointTurnMode("coordinateTurn").setLensMode("wide").setElevation(10f)
                .setActions(wayPointActionInfoDTOList2);
        wayPointDTO4.setPointIndex(1).setLongitude(117.22222d).setLatitude(31.33333d).
                setExecuteHeight(101f).setWaypointSpeed(5f).setGimbalPitchAngle(-45f).setWaypointHeadingParam("followWayline").
                setWaypointHeadingAngle(-45d).setWaypointTurnMode("coordinateTurn").setLensMode("wide").setElevation(10f);
        List<WayPointDTO> wayPointDTOList2 = new ArrayList<>();
        wayPointDTOList2.add(wayPointDTO3);
        wayPointDTOList2.add(wayPointDTO4);
        folder2.setPoints(wayPointDTOList2);

        folderList.add(folder1);
        folderList.add(folder2);
        return folderList;
    }


    public static WpmlRouteInfo convertRouteContent(RouteBaseInfoDTO routeSaveInfo, Integer routeType) {

        // 处理一下高度模式
        dealHeightMode(routeSaveInfo);

        MissionConfig missionConfig = buildMissionConfigBean(routeSaveInfo);
        List<Folder> folders = buildFolders(routeSaveInfo, routeType);

        WpmlRouteInfo wpmlRouteInfo = WpmlRouteInfo.builder().author(RouteDocumentConstant.ROUTE_DEFAULT_AUTHOR).createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis()).build();
        wpmlRouteInfo.setMissionConfig(missionConfig);
        wpmlRouteInfo.setFolders(folders);

        return wpmlRouteInfo;
    }


    private static MissionConfig buildMissionConfigBean(RouteBaseInfoDTO routeSaveInfo) {

        MissionConfig missionConfig = MissionConfig.builder().finishAction(routeSaveInfo.getFinishAction()).takeOffSecurityHeight(routeSaveInfo.getTakeOffSecurityHeight().doubleValue()).globalTransitionalSpeed(routeSaveInfo.getGlobalTransitionalSpeed().doubleValue()).globalRTHHeight(routeSaveInfo.getGlobalRTHHeight().doubleValue()).takeOffRefPoint(buildTakeOffRefPoint(routeSaveInfo)).droneInfo(buildDroneInfo(routeSaveInfo)).payloadInfo(PayloadInfo.builder().payloadEnumValue(routeSaveInfo.getPayloadEnumValue()).payloadSubEnumValue(routeSaveInfo.getPayloadSubEnumValue()).build()).autoRerouteInfo(buildAutoRerouteInfo()).build();

        if (RouteLostActionTypeEnum.isContinue(routeSaveInfo.getLostAction())) {
            // 继续执行航线
            missionConfig.setExitOnRCLost(ExitOnRCLostEnum.goContinue.getAction());
        }
        if (RouteLostActionTypeEnum.isGoBack(routeSaveInfo.getLostAction())) {
            // 执行失控动作进行返航
            missionConfig.setExitOnRCLost(ExitOnRCLostEnum.executeLostAction.getAction());
            missionConfig.setExecuteRCLostAction(ExecuteRCLostActionEnum.goBack.getAction());
        }
        return missionConfig;
    }


    private static List<Folder> buildFolders(RouteBaseInfoDTO routeSaveInfo, Integer routeType) {

        // 处理航点航线的folder
        if (RouteTypeEnum.isWayPointRoute(routeType)) {
            return dealWayPointRoute(routeSaveInfo);
        }

        // 处理建模航线的folder
        if (RouteTypeEnum.isModelingRoute(routeType)) {
            return dealBuildModelRoute(routeSaveInfo);
        }
        return new ArrayList<>();
    }

    private static List<Folder> dealWayPointRoute(RouteBaseInfoDTO routeSaveInfo) {

        List<Folder> routeFolders = new ArrayList<>();
        List<RouteFolderDTO> folders = routeSaveInfo.getFolders();

        int templateId = 0;
        int waylineId = 0;

        for (RouteFolderDTO routeFolder : folders) {
            Folder folder = new DefaultFolder();
            routeFolders.add(folder);
            folder.setGlobalWaypointTurnMode(routeSaveInfo.getGlobalWaypointTurnMode());
            folder.setGimbalPitchMode(GimbalPitchModeEnum.manual.getMode());
            folder.setGlobalHeight(routeSaveInfo.getAirlineHeight().doubleValue());
            folder.setWaypointHeadingParam(buildWaypointHeadingParam());
            folder.setTemplateType(TemplateTypeEnum.waypoint.getMode());
            folder.setTemplateId(templateId++);
            folder.setWaylineId(waylineId++);
            folder.setAutoFlightSpeed(routeSaveInfo.getAutoFlightSpeed().doubleValue());
            folder.setExecuteHeightMode(routeSaveInfo.getHeightMode());
            folder.setWaylineCoordinateSysParam(buildWaylineCoordinateSysParam(routeSaveInfo));
            folder.setPayloadParam(buildPayloadParam(routeSaveInfo));
            folder.setDistance(Double.valueOf(routeSaveInfo.getDistance()));
            folder.setDuration(routeSaveInfo.getDuration().doubleValue());

            folder.setPlacemarks(buildPlacemarkInfo(routeFolder, routeSaveInfo));
        }
        return routeFolders;
    }

    private static void dealHeightMode(RouteBaseInfoDTO routeSaveInfo) {
        String heightMode = routeSaveInfo.getHeightMode();
        if (HeightModeEnum.isRelativeGround(heightMode)) {
            // 相对地面高度，需要转化为WGS84进行执行
            routeSaveInfo.setHeightMode(HeightModeEnum.WGS84.getCode());

            // 转化航点高度
            List<RouteFolderDTO> folders = routeSaveInfo.getFolders();
            for (RouteFolderDTO folder : folders) {
                List<WayPointDTO> points = folder.getPoints();
                for (WayPointDTO point : points) {
                    point.setExecuteHeight(point.getExecuteHeight() + point.getElevation());
                }
            }
        }
    }

    /**
     * 处理建模航线的template的单个placemark信息
     * @param routeSaveInfo
     * @return
     */
    private static Placemark buildTemplatePlacemarkInfo(RouteBaseInfoDTO routeSaveInfo) {
        Placemark placemark = new Placemark();
        placemark.setMargin(0);
        placemark.setInclinedGimbalPitch(routeSaveInfo.getModelRouteDTO().getInclinedGimbalPitch());
        placemark.setCaliFlightEnable(0);
        placemark.setEllipsoidHeight(routeSaveInfo.getModelRouteDTO().getEllipsoidHeight());
        placemark.setHeight(routeSaveInfo.getModelRouteDTO().getHeight());
        placemark.setOverlap(routeSaveInfo.getModelRouteDTO().getOverlap());
        placemark.setDirection(routeSaveInfo.getModelRouteDTO().getDirection());
        placemark.setInclinedFlightSpeed(Double.valueOf(routeSaveInfo.getAutoFlightSpeed()));
        placemark.setShootType(routeSaveInfo.getModelRouteDTO().getShootType());
        placemark.setElevationOptimizeEnable(routeSaveInfo.getModelRouteDTO().getElevationOptimizeEnable());
        placemark.setSmartObliqueEnable(0);
        placemark.setFacadeWaylineEnable(0);
        placemark.setMappingHeadingParam(routeSaveInfo.getModelRouteDTO().getMappingHeadingParam());
        placemark.setGimbalPitchMode(routeSaveInfo.getModelRouteDTO().getGimbalPitchMode());
        placemark.setGimbalPitchAngle(Double.valueOf(routeSaveInfo.getModelRouteDTO().getGimbalPitchAngle()));
        placemark.setPolygon(Polygon.builder().points(routeSaveInfo.getModelRouteDTO().getPolygonLocations()).build());
        return placemark;
    }

    private static List<Placemark> buildPlacemarkInfo(RouteFolderDTO routeFolder, RouteBaseInfoDTO routeSaveInfo) {
        List<WayPointDTO> points = routeFolder.getPoints();
        if (CollectionUtil.isEmpty(points)) {
            return new ArrayList<>();
        }
        List<Placemark> placemarks = new ArrayList<>();
        // 添加等时、等距拍照栈，用于判断合理性,该栈只存储等时、等距拍照开启动作
        Stack<PointActionInfoDTO> multiTakePhoneStack = new Stack<>();

        points.forEach(p -> placemarks.add(buildPlacemarkPoint(p, routeSaveInfo, multiTakePhoneStack)));
        // 这里航点构建完成，可能会有后置处理
        while (!multiTakePhoneStack.isEmpty()) {
            // 统一设置为最后一个航点
            PointActionInfoDTO pointActionInfoDTO = multiTakePhoneStack.pop();
            ActionGroup actionGroup = pointActionInfoDTO.getActionGroup();
            actionGroup.setActionGroupEndIndex(points.size() - 1);
        }
        return placemarks;
    }

    /**
     * 处理建模航线waylines的多个placemark信息
     * @param routeSaveInfo
     * @return
     */
    private static List<Placemark> buildWaylinesPlacemarkInfo(RouteFolderDTO routeFolder, RouteBaseInfoDTO routeSaveInfo,int waylineId) {
        List<WayPointDTO> points = routeFolder.getPoints();
        if (CollectionUtil.isEmpty(points)) {
            return new ArrayList<>();
        }
        List<Placemark> placemarks = new ArrayList<>();
        points.forEach(p -> placemarks.add(buildPlacemarkPointModel(p, routeSaveInfo,points.size()-1,waylineId)));
        return placemarks;
    }

    private static Placemark buildPlacemarkPoint(WayPointDTO point, RouteBaseInfoDTO routeSaveInfo, Stack<PointActionInfoDTO> multiTakePhoneStack) {
        Placemark placemark = new Placemark();
        placemark.setPoint(Point.builder().longitude(point.getLongitude()).latitude(point.getLatitude()).build());

        placemark.setIndex(point.getPointIndex());
        placemark.setUseGlobalHeight(point.getUseGlobalHeight());

        if (point.getUseGlobalHeight() == 0) {
            placemark.setEllipsoidHeight(point.getExecuteHeight().doubleValue());
        }
        placemark.setUseGlobalSpeed(point.getUseGlobalSpeed());
        placemark.setWaypointSpeed(point.getWaypointSpeed().doubleValue());
        placemark.setExecuteHeight(point.getExecuteHeight().doubleValue());

        placemark.setUseGlobalHeadingParam(0); // 不使用全局全局偏航角模式
        placemark.setWaypointHeadingParam(WaypointHeadingParam.builder().waypointHeadingMode(point.getWaypointHeadingParam()).build());
        placemark.setGimbalPitchAngle(point.getGimbalPitchAngle().doubleValue());
        placemark.setActionGroups(buildActionGroup(point, routeSaveInfo, multiTakePhoneStack));
        return placemark;
    }
    private static Placemark buildPlacemarkPointModel(WayPointDTO point, RouteBaseInfoDTO routeSaveInfo,int endIndex,int waylineId) {
        Placemark placemark = new Placemark();
        // 填充template相关参数
        placemark.setMargin(0);
        placemark.setInclinedGimbalPitch(-45);
        placemark.setCaliFlightEnable(0);
        placemark.setEllipsoidHeight(routeSaveInfo.getModelRouteDTO().getEllipsoidHeight());
        placemark.setHeight(routeSaveInfo.getModelRouteDTO().getHeight());
        placemark.setOverlap(routeSaveInfo.getModelRouteDTO().getOverlap());
        placemark.setDirection(routeSaveInfo.getModelRouteDTO().getDirection());
        placemark.setInclinedFlightSpeed(Double.valueOf(routeSaveInfo.getAutoFlightSpeed()));
        placemark.setShootType(routeSaveInfo.getModelRouteDTO().getShootType());
        placemark.setElevationOptimizeEnable(routeSaveInfo.getModelRouteDTO().getElevationOptimizeEnable());
        placemark.setSmartObliqueEnable(0);
        placemark.setFacadeWaylineEnable(0);
        placemark.setMappingHeadingParam(routeSaveInfo.getModelRouteDTO().getMappingHeadingParam());
        placemark.setGimbalPitchMode(routeSaveInfo.getModelRouteDTO().getGimbalPitchMode());
        placemark.setGimbalPitchAngle(Double.valueOf(routeSaveInfo.getModelRouteDTO().getGimbalPitchAngle()));
        placemark.setPolygon(Polygon.builder().points(routeSaveInfo.getModelRouteDTO().getPolygonLocations()).build());
        // 填充wayline相关参数
        placemark.setPoint(Point.builder().longitude(point.getLongitude()).latitude(point.getLatitude()).build());
        placemark.setIndex(point.getPointIndex());
        placemark.setWaypointSpeed(point.getWaypointSpeed().doubleValue());
        placemark.setExecuteHeight(point.getExecuteHeight().doubleValue());
        placemark.setUseStraightLine(1);
        placemark.setIsRisky(0);
        placemark.setWaypointWorkType(0);
        placemark.setUseGlobalHeadingParam(0); // 不使用全局偏航角模式
        placemark.setWaypointHeadingParam(WaypointHeadingParam.builder().waypointHeadingMode(point.getWaypointHeadingParam()).
                waypointHeadingAngle(point.getWaypointHeadingAngle()).waypointHeadingAngleEnable(point.getWaypointHeadingAngleEnable()).build());
        placemark.setUseGlobalTurnParam(0); // 不使用全局转弯模式
        double waypointTurnDampingDist = 0.00;
        if (StringUtils.equals(WaypointTurnModeEnum.toPointAndPassWithContinuityCurvature.getMode(), point.getWaypointTurnMode())) {
            waypointTurnDampingDist = 0.20;
            placemark.setUseStraightLine(0);
        }
        placemark.setWaypointTurnParam(WaypointTurnParam.builder().waypointTurnMode(point.getWaypointTurnMode()).
                waypointTurnDampingDist(waypointTurnDampingDist).build());
        // 每条航线的第一个航点设置动作组，起始于第一个航点，结束于最后一个航点
        if (ObjectUtil.equals(0, point.getPointIndex())) {
            placemark.setActionGroups(buildActionGroupModel(point, routeSaveInfo,endIndex,waylineId));
        }
        return placemark;
    }

    private static List<ActionGroup> buildActionGroup(WayPointDTO point, RouteBaseInfoDTO routeSaveInfo, Stack<PointActionInfoDTO> multiTakePhoneStack) {
        int actionGroupId = 0;
        // 航点动作区分动作组，分为：开启等距间隔拍照，开启等时间隔拍照，其他动作
        Map<String, List<ActionGroup>> actionGroupMaps = new HashMap<>();
        for (WayPointActionInfoDTO wayPointAction : point.getActions()) {
            if (!WayPointActionTypeEnum.isDjiAction(wayPointAction.getActionType())) {
                // 过滤掉非大疆的动作
                continue;
            }
            // 以下类型分别会产生动作组
            WayPointActionTransTypeEnum wayPointActionTransTypeEnum = WayPointActionTransTypeEnum.findByCode(wayPointAction.getType());
            // 根据类型获取动作分组
            List<ActionGroup> actionGroupLists = actionGroupMaps.get(wayPointActionTransTypeEnum.getCode());
            if (CollectionUtil.isEmpty(actionGroupLists)) {
                actionGroupLists = new ArrayList<>();
                actionGroupMaps.put(wayPointActionTransTypeEnum.getCode(), actionGroupLists);
            }
            switch (wayPointActionTransTypeEnum) {
                case multipleDistance -> {
                    // 先根据栈数据判断是否需要添加该动作
                    if (!multiTakePhoneStack.isEmpty()) {
                        PointActionInfoDTO pointActionInfoDTO = multiTakePhoneStack.peek();
                        WayPointActionInfoDTO wayPointActionInfo = pointActionInfoDTO.getWayPointActionInfo();
                        if (WayPointActionTransTypeEnum.isMultipleAction(wayPointActionInfo.getType())) {
                            // 说明之前有设置过该动作，则后面设置失效
                            continue;
                        }
                    }
                    // 等距间隔拍照
                    ActionGroup actionGroup = new ActionGroup();
                    actionGroup.setActionGroupId(actionGroupId++);
                    actionGroup.setActionGroupStartIndex(point.getPointIndex());  // 起点为当前航点

                    ActionTrigger actionTrigger = new ActionTrigger();
                    actionTrigger.setActionTriggerType(ActionTriggerTypeEnum.multipleDistance.getType());
                    actionTrigger.setActionTriggerParam(Double.valueOf(wayPointAction.getValue()));
                    actionGroup.setActionTrigger(actionTrigger);
                    actionGroupLists.add(actionGroup);

                    // 添加拍照动作
                    List<Action> actions = actionGroup.getActions();
                    Action action = new Action();
                    action.setActionId(actions.size());
                    action.setActionActuatorFunc(ActionActuatorFuncEnum.takePhoto.getAction());
                    WayPointActionInfoDTO takePhoneActionInfo = new WayPointActionInfoDTO();
                    takePhoneActionInfo.setActionType(WayPointActionTypeEnum.DJI_ROUTE_ACTION.getType());
                    takePhoneActionInfo.setType(ActionActuatorFuncEnum.takePhoto.getAction());
                    action.setActionActuatorFuncParam(getActionActuatorFuncParamByType(takePhoneActionInfo, point.getPointIndex(), routeSaveInfo.getImageFormatStr()));
                    actions.add(action);

                    // 添加入栈操作
                    multiTakePhoneStack.push(PointActionInfoDTO.builder().point(point).wayPointActionInfo(wayPointAction).actionGroup(actionGroup).build());
                }
                case multipleTiming -> {
                    // 先根据栈数据判断是否需要添加该动作
                    if (!multiTakePhoneStack.isEmpty()) {
                        PointActionInfoDTO pointActionInfoDTO = multiTakePhoneStack.peek();
                        WayPointActionInfoDTO wayPointActionInfo = pointActionInfoDTO.getWayPointActionInfo();
                        if (WayPointActionTransTypeEnum.isMultipleAction(wayPointActionInfo.getType())) {
                            // 说明之前有设置过该动作，则后面设置失效
                            continue;
                        }
                    }
                    // 等距间隔拍照
                    ActionGroup actionGroup = new ActionGroup();
                    actionGroup.setActionGroupId(actionGroupId++);
                    actionGroup.setActionGroupStartIndex(point.getPointIndex());  // 起点为当前航点

                    ActionTrigger actionTrigger = new ActionTrigger();
                    actionTrigger.setActionTriggerType(ActionTriggerTypeEnum.multipleTiming.getType());
                    actionTrigger.setActionTriggerParam(Double.valueOf(wayPointAction.getValue()));
                    actionGroup.setActionTrigger(actionTrigger);
                    actionGroupLists.add(actionGroup);

                    // 添加拍照动作
                    List<Action> actions = actionGroup.getActions();
                    Action action = new Action();
                    action.setActionId(actions.size());
                    action.setActionActuatorFunc(ActionActuatorFuncEnum.takePhoto.getAction());
                    WayPointActionInfoDTO takePhoneActionInfo = new WayPointActionInfoDTO();
                    takePhoneActionInfo.setActionType(WayPointActionTypeEnum.DJI_ROUTE_ACTION.getType());
                    takePhoneActionInfo.setType(ActionActuatorFuncEnum.takePhoto.getAction());
                    action.setActionActuatorFuncParam(getActionActuatorFuncParamByType(takePhoneActionInfo, point.getPointIndex(), routeSaveInfo.getImageFormatStr()));
                    actions.add(action);

                    // 添加入栈操作
                    multiTakePhoneStack.push(PointActionInfoDTO.builder().point(point).wayPointActionInfo(wayPointAction).actionGroup(actionGroup).build());
                }
                case multiplePhotoOver -> {
                    // 对等时间隔拍照和等距间隔拍照进行结束操作,对缓存栈进行操作
                    while (!multiTakePhoneStack.isEmpty()) {
                        PointActionInfoDTO pop = multiTakePhoneStack.pop();
                        ActionGroup actionGroup = pop.getActionGroup();
                        actionGroup.setActionGroupEndIndex(point.getPointIndex());
                    }
                }
                case otherActionType -> {
                    if (CollectionUtil.isEmpty(actionGroupLists)) {
                        ActionGroup actionGroup = new ActionGroup();
                        actionGroup.setActionGroupId(actionGroupId++);
                        actionGroup.setActionGroupStartIndex(point.getPointIndex());
                        actionGroup.setActionGroupEndIndex(point.getPointIndex());
                        // 这些动作均为到达航点触发
                        ActionTrigger actionTrigger = new ActionTrigger();
                        actionTrigger.setActionTriggerType(ActionTriggerTypeEnum.reachPoint.getType());
                        actionGroup.setActionTrigger(actionTrigger);
                        actionGroupLists.add(actionGroup);
                    }
                    ActionGroup actionGroup = actionGroupLists.get(0);
                    List<Action> actions = actionGroup.getActions();

                    Action action = new Action();
                    action.setActionId(actions.size());
                    action.setActionActuatorFunc(wayPointAction.getType());
                    action.setActionActuatorFuncParam(getActionActuatorFuncParamByType(wayPointAction, point.getPointIndex(), routeSaveInfo.getImageFormatStr()));
                    actions.add(action);
                }
            }
        }
        // 将动作分组合并
        List<ActionGroup> allActionGroup = new ArrayList<>();
        for (Map.Entry<String, List<ActionGroup>> entry : actionGroupMaps.entrySet()) {
            List<ActionGroup> value = entry.getValue();
            allActionGroup.addAll(value);
        }
        return allActionGroup;
    }
    private static List<ActionGroup> buildActionGroupModel(WayPointDTO point, RouteBaseInfoDTO routeSaveInfo,int endIndex,int waylineId) {
        // 建模航线动作组集合 （单个动作组的话等时间隔拍照无法生效，必须分开为2个，将multipleTiming和takePhoto放在一块）
        List<ActionGroup> actionGroups = new ArrayList<>();
        // 第一个动作组 包含旋转云台动作，每条航线首航点生效即可
        ActionGroup actionGroup1 = new ActionGroup();
        actionGroup1.setActionGroupId(0);
        actionGroup1.setActionGroupStartIndex(point.getPointIndex());  // 起点为当前航点
        actionGroup1.setActionGroupEndIndex(point.getPointIndex()); // 终点为当前航点
        actionGroup1.setActionGroupMode(ActionGroupModeEnum.sequence.getMode());
        ActionTrigger actionTrigger1 = new ActionTrigger();
        actionTrigger1.setActionTriggerType(ActionTriggerTypeEnum.reachPoint.getType());
        actionGroup1.setActionTrigger(actionTrigger1);
        List<Action> actions1 = new ArrayList<>();
        // 第二个动作组 包含等时间隔拍照动作，每条航线全部航点生效
        ActionGroup actionGroup2 = new ActionGroup();
        actionGroup2.setActionGroupId(1);
        actionGroup2.setActionGroupStartIndex(point.getPointIndex());  // 起点为当前航点
        actionGroup2.setActionGroupEndIndex(endIndex); // 终点为当前航点序号
        actionGroup2.setActionGroupMode(ActionGroupModeEnum.sequence.getMode());
        List<Action> actions2 = new ArrayList<>();
        if (CollectionUtil.isEmpty(point.getActions())) {
            // 无动作  填充默认动作
            List<WayPointActionInfoDTO> actions = new ArrayList<>();
            WayPointActionInfoDTO action1 = new WayPointActionInfoDTO();
            action1.setType(ActionActuatorFuncEnum.gimbalRotate.getAction());
            if (waylineId == 1){
                action1.setValue("-90");
            }else {
                action1.setValue("-45");
            }
            actions.add(action1);
            WayPointActionInfoDTO action2 = new WayPointActionInfoDTO();
            action2.setType("multipleTiming");
            action2.setValue("2");
            actions.add(action2);
            point.setActions(actions);
        }
        // 建模航线目前只支持等时/距间隔拍照,两个动作组，每个组内包含一个动作 第一个为旋转云台gimbalRotate，第二个为等时/距间隔拍照takePhoto
        point.getActions().forEach(wayPointAction -> {
            WayPointActionTransTypeEnum wayPointActionTransTypeEnum = WayPointActionTransTypeEnum.findByCode(wayPointAction.getType());
            Action action = new Action();
            action.setActionId(0);
            // 旋转云台  第一个动作组的动作
            if (ObjectUtil.equals(wayPointActionTransTypeEnum, WayPointActionTransTypeEnum.gimbalRotate)) {
                action.setActionActuatorFunc(wayPointAction.getType());
                action.setActionActuatorFuncParam(getActionActuatorFuncParamByType(wayPointAction, point.getPointIndex(), routeSaveInfo.getImageFormatStr()));
                actions1.add(action);
                actionGroup1.setActions(actions1);
            } else if (ObjectUtil.equals(wayPointActionTransTypeEnum, WayPointActionTransTypeEnum.multipleTiming) ||
                    ObjectUtil.equals(wayPointActionTransTypeEnum, WayPointActionTransTypeEnum.multipleDistance)) {
                // 等时/距间隔拍照动作 第二个动作组的动作
                action.setActionActuatorFunc(ActionActuatorFuncEnum.takePhoto.getAction());
                WayPointActionInfoDTO takePhoneActionInfo = new WayPointActionInfoDTO();
                takePhoneActionInfo.setActionType(WayPointActionTypeEnum.DJI_ROUTE_ACTION.getType());
                takePhoneActionInfo.setType(ActionActuatorFuncEnum.takePhoto.getAction());
                action.setActionActuatorFuncParam(getActionActuatorFuncParamByType(takePhoneActionInfo, point.getPointIndex(), routeSaveInfo.getImageFormatStr()));
                ActionTrigger actionTrigger =new ActionTrigger();
                actionTrigger.setActionTriggerType(wayPointAction.getActionTriggerType() == null ? ActionTriggerTypeEnum.multipleTiming.getType() : wayPointAction.getActionTriggerType());
                actionTrigger.setActionTriggerParam(Double.valueOf(wayPointAction.getValue() == null ? "2" : wayPointAction.getValue()));
                actionGroup2.setActionTrigger(actionTrigger);
                actions2.add(action);
                actionGroup2.setActions(actions2);
            }
        });
      actionGroups.add(actionGroup1);
      actionGroups.add(actionGroup2);
        return actionGroups;
    }

    private static ActionActuatorFuncParam getActionActuatorFuncParamByType(WayPointActionInfoDTO wayPointAction, Integer index, String imageFormat) {

        ActionActuatorFuncParam actionActuatorFuncInfo = null;
        ActionActuatorFuncEnum actionEnum = ActionActuatorFuncEnum.findByType(wayPointAction.getType());
        switch (Objects.requireNonNull(actionEnum)) {
            case takePhoto -> {
                TakePhotoActionActuatorFuncParam actionActuatorFuncParam = new TakePhotoActionActuatorFuncParam();
                actionActuatorFuncParam.setFileSuffix(RouteDocumentConstant.POINT_SUFFIX_NAME + index);
                actionActuatorFuncParam.setPayloadLensIndex(imageFormat);
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case startRecord -> {
                StartRecordActionActuatorFuncParam actionActuatorFuncParam = new StartRecordActionActuatorFuncParam();
                actionActuatorFuncParam.setFileSuffix(RouteDocumentConstant.POINT_SUFFIX_NAME + index);
                actionActuatorFuncParam.setPayloadLensIndex(imageFormat);
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case stopRecord -> {
                StopRecordActionActuatorFuncParam actionActuatorFuncParam = new StopRecordActionActuatorFuncParam();
                actionActuatorFuncParam.setPayloadLensIndex(imageFormat);
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case focus -> {
                FocusActionActuatorFuncParam actionActuatorFuncParam = new FocusActionActuatorFuncParam();
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case zoom -> {
                ZoomActionActuatorFuncParam actionActuatorFuncParam = new ZoomActionActuatorFuncParam();
                actionActuatorFuncParam.setFocalLength(Double.parseDouble(wayPointAction.getValue()) * 24);
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case customDirName -> {
                CustomDirNameActionActuatorFuncParam actionActuatorFuncParam = new CustomDirNameActionActuatorFuncParam();
                actionActuatorFuncParam.setDirectoryName(wayPointAction.getValue());
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case gimbalRotate -> {
                GimbalRotateActionActuatorFuncParam actionActuatorFuncParam = new GimbalRotateActionActuatorFuncParam();
                actionActuatorFuncParam.setGimbalPitchRotateAngle(Double.valueOf(wayPointAction.getValue()));
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case rotateYaw -> {
                RotateYawActionActuatorFuncParam actionActuatorFuncParam = new RotateYawActionActuatorFuncParam();
                actionActuatorFuncParam.setAircraftHeading(Double.valueOf(wayPointAction.getValue()));
                actionActuatorFuncParam.setAircraftPathMode(wayPointAction.getAircraftPathMode());
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case hover -> {
                HoverActionActuatorFuncParam actionActuatorFuncParam = new HoverActionActuatorFuncParam();
                actionActuatorFuncParam.setHoverTime(Double.valueOf(wayPointAction.getValue()));
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case gimbalEvenlyRotate -> {
                GimbalEvenlyRotateActionActuatorFuncParam actionActuatorFuncParam = new GimbalEvenlyRotateActionActuatorFuncParam();
                actionActuatorFuncParam.setGimbalPitchRotateAngle(Double.valueOf(wayPointAction.getValue()));
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case accurateShoot -> {

            }
            case orientedShoot -> {
                OrientedShootActionActuatorFuncParam actionActuatorFuncParam = new OrientedShootActionActuatorFuncParam();
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case panoShot -> {
                PanoShotActionActuatorFuncParam actionActuatorFuncParam = new PanoShotActionActuatorFuncParam();
                actionActuatorFuncParam.setPayloadLensIndex(imageFormat);
                actionActuatorFuncParam.setPanoShotSubMode(wayPointAction.getValue());
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
            case recordPointCloud -> {
                RecordPointCloudActionActuatorFuncParam actionActuatorFuncParam = new RecordPointCloudActionActuatorFuncParam();
                actionActuatorFuncParam.setRecordPointCloudOperate(wayPointAction.getValue());
                actionActuatorFuncInfo = actionActuatorFuncParam;
            }
        }
        return actionActuatorFuncInfo;
    }


    private static WaypointHeadingParam buildWaypointHeadingParam() {
        WaypointHeadingParam waypointHeadingParam = WaypointHeadingParam.builder().waypointHeadingMode(WaypointHeadingModeEnum.followWayline.getMode()).waypointHeadingPathMode(WaypointHeadingPathModeEnum.followBadArc.getMode()).build();
        return waypointHeadingParam;
    }


    private static PayloadParam buildPayloadParam(RouteBaseInfoDTO routeSaveInfo) {
        PayloadParam payloadParam = PayloadParam.builder().imageFormat(routeSaveInfo.getImageFormatStr()).build();
        return payloadParam;
    }


    private static WaylineCoordinateSysParam buildWaylineCoordinateSysParam(RouteBaseInfoDTO routeSaveInfo) {
        return WaylineCoordinateSysParam.builder().heightMode(routeSaveInfo.getHeightMode()).positioningType(PositioningTypeEnum.GPS.getType()).build();
    }

    private static WaylineCoordinateSysParam buildWaylineCoordinateSysParamModel(RouteBaseInfoDTO routeSaveInfo) {
        return WaylineCoordinateSysParam.builder().heightMode(routeSaveInfo.getHeightMode()).positioningType(PositioningTypeEnum.GPS.getType())
                .globalShootHeight(routeSaveInfo.getModelRouteDTO().getGlobalShootHeight()).build();
    }


    private static List<Folder> dealBuildModelRoute(RouteBaseInfoDTO routeSaveInfo) {
        List<Folder> routeFolders = new ArrayList<>();
        List<RouteFolderDTO> folders = routeSaveInfo.getFolders();
        int templateId = 0;
        int waylineId = 0;
        for (RouteFolderDTO routeFolder : folders) {
            Folder folder = new DefaultFolder();
            folder.setWaylineCoordinateSysParam(buildWaylineCoordinateSysParamModel(routeSaveInfo));
            folder.setAutoFlightSpeed(routeSaveInfo.getAutoFlightSpeed().doubleValue());
            folder.setPayloadParam(buildPayloadParam(routeSaveInfo));
            folder.setExecuteHeightMode(routeSaveInfo.getHeightMode());
            folder.setTemplateType(routeSaveInfo.getTemplateType());
            folder.setTemplateId(templateId++);
            folder.setWaylineId(waylineId++);
            folder.setPlacemarks(buildWaylinesPlacemarkInfo(routeFolder, routeSaveInfo,waylineId));
            //2d建模 建图航拍
            if (StringUtils.equals(routeSaveInfo.getTemplateType(), TemplateTypeEnum.mapping2d.getMode())){
                folder.setDistance(Double.valueOf(routeSaveInfo.getDistance()));
                folder.setDuration(routeSaveInfo.getDuration().doubleValue());
                //3d建模 倾斜摄影
            }else if (StringUtils.equals(routeSaveInfo.getTemplateType(), TemplateTypeEnum.mapping3d.getMode())){
                folder.setDistance(Double.valueOf(routeFolder.getDistance()));
                folder.setDuration(routeFolder.getDuration().doubleValue());
                // TODO folder中的startActionGroup 通用初始化动作组 目前暂时参照司空 旋转云台以及悬停
                folder.setStartActionGroup(buildStartActionGroup(waylineId));
            }
            routeFolders.add(folder);
        }
        return routeFolders;
    }

    private static StartActionGroup buildStartActionGroup(int waylineId) {
        StartActionGroup startActionGroup = new StartActionGroup();
        List<Action> actions = new ArrayList<>();
        actions.add(new Action().setActionId(0).setActionActuatorFunc(ActionActuatorFuncEnum.gimbalRotate.getAction())
                .setActionActuatorFuncParam(new GimbalRotateActionActuatorFuncParam().setGimbalPitchRotateAngle(waylineId == 1 ? -90d : -45d)));
        actions.add(new Action().setActionId(1).setActionActuatorFunc(ActionActuatorFuncEnum.hover.getAction())
               .setActionActuatorFuncParam(new HoverActionActuatorFuncParam().setHoverTime(1d)));
        startActionGroup.setActions(actions);
        return startActionGroup;
    }

    private static TakeOffRefPoint buildTakeOffRefPoint(RouteBaseInfoDTO routeSaveInfo) {
        return TakeOffRefPoint.builder().longitude(routeSaveInfo.getTakeOffRefPointLongitude()).latitude(routeSaveInfo.getTakeOffRefPointLatitude()).height(routeSaveInfo.getTakeOffRefPointHeight()).build();
    }

    private static DroneInfo buildDroneInfo(RouteBaseInfoDTO routeSaveInfo) {
        return DroneInfo.builder().droneEnumValue(routeSaveInfo.getDroneEnumValue()).droneSubEnumValue(routeSaveInfo.getDroneSubEnumValue()).build();
    }

    private static AutoRerouteInfo buildAutoRerouteInfo() {
        return new AutoRerouteInfo();
    }

    // ==================== 逆向转换方法：WpmlRouteInfo -> RouteBaseInfoDTO ====================

    /**
     * 将WpmlRouteInfo对象转换为RouteBaseInfoDTO对象（航线导入功能）
     *
     * @param wpmlRouteInfo 完整的WpmlRouteInfo对象
     * @param routeType     航线类型
     * @return RouteBaseInfoDTO对象
     */
    public static RouteBaseInfoDTO convertWpmlToRouteBaseInfo(WpmlRouteInfo wpmlRouteInfo, Integer routeType) {
        if (wpmlRouteInfo == null) {
            throw new IllegalArgumentException("WpmlRouteInfo不能为空");
        }

        RouteBaseInfoDTO routeBaseInfo = new RouteBaseInfoDTO();

        // 转换基本信息
        convertBasicInfo(wpmlRouteInfo, routeBaseInfo);

        // 转换任务配置信息
        if (wpmlRouteInfo.getMissionConfig() != null) {
            convertMissionConfigToRouteBase(wpmlRouteInfo.getMissionConfig(), routeBaseInfo);
        }

        // 转换Folders信息
        if (CollectionUtil.isNotEmpty(wpmlRouteInfo.getFolders())) {
            List<RouteFolderDTO> routeFolders = convertFoldersToRouteFolders(wpmlRouteInfo.getFolders(), routeType);
            routeBaseInfo.setFolders(routeFolders);

            // 计算统计信息
            calculateRouteStatistics(routeBaseInfo, wpmlRouteInfo.getFolders());
        }

        // 设置航线类型
        routeBaseInfo.setRouteType(routeType);

        return routeBaseInfo;
    }

    /**
     * 转换基本信息
     */
    private static void convertBasicInfo(WpmlRouteInfo wpmlRouteInfo, RouteBaseInfoDTO routeBaseInfo) {
        // 这些字段需要从Folders中推断或使用默认值
        routeBaseInfo.setWayPointCount(0); // 将在calculateRouteStatistics中计算
        routeBaseInfo.setDistance(0f); // 将在calculateRouteStatistics中计算
        routeBaseInfo.setDuration(0); // 将在calculateRouteStatistics中计算
        routeBaseInfo.setAlgorithmCount(0); // 默认值，需要根据实际情况调整
    }

    /**
     * 转换任务配置信息到RouteBaseInfoDTO
     */
    private static void convertMissionConfigToRouteBase(MissionConfig missionConfig, RouteBaseInfoDTO routeBaseInfo) {
        // 转换起飞点信息
        if (missionConfig.getTakeOffRefPoint() != null) {
            TakeOffRefPoint takeOffPoint = missionConfig.getTakeOffRefPoint();
            routeBaseInfo.setTakeOffRefPointLongitude(takeOffPoint.getLongitude());
            routeBaseInfo.setTakeOffRefPointLatitude(takeOffPoint.getLatitude());
            routeBaseInfo.setTakeOffRefPointHeight(takeOffPoint.getHeight());
        }

        // 转换高度相关信息
        if (missionConfig.getTakeOffRefPointAGLHeight() != null) {
            routeBaseInfo.setTakeOffRefPointAGLHeight(missionConfig.getTakeOffRefPointAGLHeight().floatValue());
        }

        // 转换飞行参数
        if (missionConfig.getTakeOffSecurityHeight() != null) {
            routeBaseInfo.setTakeOffSecurityHeight(missionConfig.getTakeOffSecurityHeight().intValue());
        }
        if (missionConfig.getGlobalTransitionalSpeed() != null) {
            routeBaseInfo.setGlobalTransitionalSpeed(missionConfig.getGlobalTransitionalSpeed().intValue());
        }
        if (missionConfig.getGlobalRTHHeight() != null) {
            routeBaseInfo.setGlobalRTHHeight(missionConfig.getGlobalRTHHeight().intValue());
        }

        // 转换动作设置
        routeBaseInfo.setFinishAction(missionConfig.getFinishAction());

        // 转换失控动作
        if (StringUtils.equals(missionConfig.getExitOnRCLost(), ExitOnRCLostEnum.executeLostAction.getAction())) {
            routeBaseInfo.setLostAction(RouteLostActionTypeEnum.CONTINUE.getType());
        } else {
            routeBaseInfo.setLostAction(RouteLostActionTypeEnum.RETURN_HOME.getType());
        }

        // 转换飞行器信息
        if (missionConfig.getDroneInfo() != null) {
            DroneInfo droneInfo = missionConfig.getDroneInfo();
            routeBaseInfo.setDroneEnumValue(droneInfo.getDroneEnumValue());
            routeBaseInfo.setDroneSubEnumValue(droneInfo.getDroneSubEnumValue());
        }

        // 转换负载信息
        if (missionConfig.getPayloadInfo() != null) {
            PayloadInfo payloadInfo = missionConfig.getPayloadInfo();
            routeBaseInfo.setPayloadEnumValue(payloadInfo.getPayloadEnumValue());
            routeBaseInfo.setPayloadSubEnumValue(payloadInfo.getPayloadSubEnumValue());
        }
    }
}
