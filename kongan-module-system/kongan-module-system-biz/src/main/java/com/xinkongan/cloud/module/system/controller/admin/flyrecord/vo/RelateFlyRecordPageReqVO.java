package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 接警关联数据 飞行记录请求
 * <AUTHOR>
 * @Date 2025/3/24 19:37
 */
@Schema(description = "接警关联数据-飞行记录请求ReqVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RelateFlyRecordPageReqVO extends PageParam {

    @Schema(description = "任务id")
    @NotNull(message = "任务id不能为空")
    private Long jobId;
}