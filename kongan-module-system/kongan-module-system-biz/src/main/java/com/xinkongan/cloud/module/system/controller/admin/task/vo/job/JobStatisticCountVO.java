package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 任务统计数量
 */
@Schema(description = "任务统计数量")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class JobStatisticCountVO {

    @Schema(description = "任务总数")
    private Long count = 0L;

    @Schema(description = "今日待完成数量")
    private Long todayWaitExecuted = 0L;

    @Schema(description = "今日已完成数量")
    private Long todayExecuted = 0L;
}