package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 禁飞区响应VO
 */
@Data
@Schema(description = "管理后台 - 禁飞区响应VO")
public class NfzRespVO {

    @Schema(description = "禁飞区ID", example = "1")
    private Long id;

    @Schema(description = "禁飞区名称", example = "测试禁飞区")
    private String name;

    @Schema(description = "多边形坐标，以 GeoJSON 格式存储")
    private String dataJson;

    @Schema(description = "状态：true表示启用，false表示禁用", example = "true")
    private Boolean status;

    @Schema(description = "区域面积", example = "100.5")
    private Float area;

    @Schema(description = "创建人ID", example = "1")
    private Long creator;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "修改人ID", example = "1")
    private Long updater;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;
}
