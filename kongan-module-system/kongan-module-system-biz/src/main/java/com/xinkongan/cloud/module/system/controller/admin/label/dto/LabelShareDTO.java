package com.xinkongan.cloud.module.system.controller.admin.label.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LabelShareDTO {
    @NotNull
    @Schema(description = "标注id")
    private Long labelId;

    @Schema(description = "分享的组织列表")
    private List<Long> deptIds;

    public List<Long> getDeptIds() {
        if (deptIds == null) {
            return new ArrayList<>();
        }
        return deptIds;
    }
}
