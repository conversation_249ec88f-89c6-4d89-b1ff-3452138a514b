package com.xinkongan.cloud.module.system.service.upgrade;

import com.xinkongan.cloud.module.system.api.upgrade.dto.UpgradeNoticeDTO;

/**
 * @Description 系统升级Serice
 * <AUTHOR>
 * @Date 2025/5/22 15:39
 */
public interface IUpgradeService {

    /**
     * 获取升级通知
     */
    UpgradeNoticeDTO getUpgradeNotice();

    /**
     * 推送系统升级通知
     */
    void pushUpgradeNotice(UpgradeNoticeDTO upgradeNoticeDTO);

    /**
     * 设置不在提醒
     */
    void notRemind(Long id);

    /**
     * 是否提醒
     */
    boolean isNotRemind(Long id);
}
