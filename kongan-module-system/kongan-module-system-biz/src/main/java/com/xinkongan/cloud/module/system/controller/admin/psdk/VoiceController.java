package com.xinkongan.cloud.module.system.controller.admin.psdk;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.psdk.dto.VoiceCreateDTO;
import com.xinkongan.cloud.module.system.controller.admin.psdk.dto.VoiceSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.psdk.dto.VoiceUpdateDTO;
import com.xinkongan.cloud.module.system.controller.admin.psdk.vo.*;
import com.xinkongan.cloud.module.system.service.psdk.IVoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;


@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 喊话器语音")
@RequestMapping("/system/voice")
public class VoiceController {

    @Resource
    private IVoiceService voiceService;


    @GetMapping("/list")
    @Operation(summary = "喊话器语音-语音列表")
    //@PreAuthorize("@ss.hasPermission('action:voice:query')")
    public CommonResult<List<VoiceRespVO>> getVoiceList(@Valid VoiceSearchDTO searchParams) {
        List<VoiceRespVO> records = voiceService.getVoiceList(searchParams);
        return success(records);
    }

    @GetMapping("/page")
    @Operation(summary = "喊话器语音-分页查询")
    //@PreAuthorize("@ss.hasPermission('action:voice:query')")
    public CommonResult<PageResult<VoiceRespVO>> getVoicePage(@Valid VoiceSearchDTO pageVO) {
        PageResult<VoiceRespVO> pageResult = voiceService.page(pageVO);
        return success(pageResult);
    }

    @GetMapping("/get")
    @Operation(summary = "喊话器语音-语音查询")
    //@PreAuthorize("@ss.hasPermission('action:voice:query')")
    public CommonResult<VoiceRespVO> getVoice(@Valid @NotNull @RequestParam("id") Long id) {
        VoiceRespVO voiceRespInfo = voiceService.getVoiceInfoById(id);
        return success(voiceRespInfo);
    }

    @PostMapping(value = "/create")
    @Operation(summary = "喊话器语音-创建语音")
//    @PreAuthorize("@ss.hasPermission('system:voice:create')")
    public CommonResult<Long> createVoice(@Valid @RequestBody VoiceCreateDTO createReqVO) {
        Long voiceInfoId = voiceService.createVoice(createReqVO);
        return success(voiceInfoId);
    }

    @PostMapping("/update")
    @Operation(summary = "喊话器语音-更新语音")
    //@PreAuthorize("@ss.hasPermission('action:voice:update')")
    public CommonResult<Long> updateVoice(@Valid @RequestBody VoiceUpdateDTO voiceUpdateParams) {
        Long id = voiceService.updateVoice(voiceUpdateParams);
        return success(id);
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "喊话器语音-删除语音")
    //@PreAuthorize("@ss.hasPermission('action:voice:delete')")
    public CommonResult<Boolean> deleteVoice(@Valid @NotNull @PathVariable("id") Long id) {
        voiceService.deleteVoice(id);
        return success(Boolean.TRUE);
    }
}
