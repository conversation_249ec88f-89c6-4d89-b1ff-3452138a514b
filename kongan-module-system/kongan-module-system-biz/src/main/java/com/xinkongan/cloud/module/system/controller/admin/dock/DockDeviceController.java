package com.xinkongan.cloud.module.system.controller.admin.dock;


import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.*;
import com.xinkongan.cloud.module.system.controller.admin.posture.PostureDateParam;
import com.xinkongan.cloud.module.system.dto.DeptDeviceSearchDTO;
import com.xinkongan.cloud.module.system.dto.DeviceSearchDTO;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机场及舱内无人机表(SystemDockDevice)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-04 15:10:12
 */
@Validated
@RestController
@Tag(name = "管理后台 - 机场设备管理")
@RequestMapping("/system/dockDevice")
public class DockDeviceController {

    @Resource
    private DockDeviceService dockDeviceService;

    @PostMapping("/updateDeviceNameBySn")
    @Operation(summary = "根据设备SN修改设备名称", description = "根据SN修改设备信息")
    @PreAuthorize("@ss.hasPermission('system:dock:info:edit')")
    public CommonResult<Boolean> updateDeviceNameBySn(@NotEmpty @RequestParam String deviceSn, @Size(max = 50) @RequestParam String deviceName) {
        boolean flag = dockDeviceService.updateDeviceNameBySn(deviceSn, deviceName);
        return CommonResult.success(flag);
    }

    @GetMapping("/getDeviceTreeInfo")
    @Operation(summary = "查询设备组织树", description = "查询设备组织树")
    @PreAuthorize("@ss.hasPermission('system:dock:query')")
    public CommonResult<DockDeviceTreeVO> getDeviceTreeInfo() {
        List<BaseTreeNode> interior = dockDeviceService.getDeviceTreeInfo();
        List<BaseTreeNode> without = dockDeviceService.getShareDock();
        DockDeviceTreeVO deviceTreeVO = DockDeviceTreeVO.builder()
                .interior(interior)
                .without(without)
                .build();
        return CommonResult.success(deviceTreeVO);
    }

    @GetMapping("/getDeviceListInfo")
    @Operation(summary = "查询设备列表（包括共享设备）", description = "查询设备组织列表")
    @PreAuthorize("@ss.hasPermission('system:dock:query')")
    public CommonResult<List<DockDeviceVO>> getDeviceListInfo() {
        List<DockDeviceVO> deviceListInfo = dockDeviceService.getDeviceListInfo();
        return CommonResult.success(deviceListInfo);
    }

    @GetMapping("/getDockByDockSn/{dockSn}")
    @Operation(summary = "查询机场详情", description = "查询机场详情")
    @PreAuthorize("@ss.hasAnyPermissions('system:dock:info:query','system:dock:debug:query')")
    public CommonResult<DockDeviceVO> getDockByDockSn(@PathVariable String dockSn) {
        return CommonResult.success(dockDeviceService.getByDockSn(dockSn, SecurityFrameworkUtils.getLoginUser().getTenantId()));
    }


    @GetMapping("/getDeptDeviceListInfo/{deptId}")
    @Operation(summary = "根据组织id查询组织下的机场", description = "查询组织机场列表")
    @PreAuthorize("@ss.hasPermission('system:dock:query')")
    public CommonResult<List<DockDeviceVO>> getDeviceListInfoByDeptId(@PathVariable Long deptId) {
        return CommonResult.success(dockDeviceService.getDeviceListInfoByDeptIds(deptId, Boolean.FALSE));
    }

    @GetMapping("/getDeptAndSubDeviceListInfoForRoute/{deptId}")
    @Operation(summary = "根据组织id查询数据权限范围内的机场和共享机场", description = "查询数据权限范围内以及共享机场列表")
    @PreAuthorize("@ss.hasPermission('system:dock:query')")
    public CommonResult<List<DockDeviceVO>> getDeptAndSubDeviceListInfoForRoute(@PathVariable Long deptId) {
        return CommonResult.success(dockDeviceService.getDeptAndSubDeviceListInfoForRoute(deptId));
    }

    @GetMapping("/getDeptAndSubDeviceListInfo/{deptId}")
    @Operation(summary = "根据组织id查询数据权限范围内的机场和共享机场", description = "查询数据权限范围内以及共享机场列表")
    @PreAuthorize("@ss.hasPermission('system:dock:query')")
    public CommonResult<List<DockDeviceVO>> getDeptAndSubDeviceListInfoByDeptId(@PathVariable Long deptId) {
        return CommonResult.success(dockDeviceService.getDeviceListInfoByDeptIds(deptId, Boolean.TRUE));
    }

    @PostMapping("/getDeptAndSubDeviceListInfo")
    @Operation(summary = "查询指定组织的机场和共享机场", description = "查询数据权限范围内以及共享机场列表")
    @PreAuthorize("@ss.hasPermission('system:dock:query')")
    public CommonResult<List<DockDeviceVO>> getDeptAndSubDeviceListInfoByDeptIds(@RequestBody DeviceSearchDTO deviceSearchParams) {
        List<DockDeviceVO> deviceInfos = dockDeviceService.getDeviceListInfoByDeptIds(deviceSearchParams.getDeptIds(), Boolean.TRUE);
        return CommonResult.success(deviceInfos);
    }

    @PostMapping("/dept/page")
    @Operation(summary = "组织设备数据分页查询", description = "组织设备数据分页查询")
    public CommonResult<PageResult<DockDeviceVO>> getDeptPage(@RequestBody DeptDeviceSearchDTO deptDeviceSearchInfo) {
        PageResult<DockDeviceVO> result = dockDeviceService.getDeptDevicePage(deptDeviceSearchInfo);
        return CommonResult.success(result);
    }

    @GetMapping("/getDockDeviceCount")
    @Operation(summary = "查询机场设备数量", description = "查询机场设备数量")
    @PreAuthorize("@ss.hasPermission('system:dock:query')")
    public CommonResult<DocKDeviceNumberCountVO> getDockDeviceCount() {
        return CommonResult.success(dockDeviceService.getDockDeviceCount());
    }

    @Operation(summary = "不同类型设备数量统计", description = "不同类型设备数量统计")
    @GetMapping("/getDockDeviceTypeCount")
    public CommonResult<DeviceTypeNumberCountVO> getDockDeviceTypeCount() {
        return CommonResult.success(dockDeviceService.getDockDeviceTypeCount());
    }

    @Operation(summary = "内部机场执行任务数量排行(仅需传时间类型)", description = "内部机场执行任务数量排行(仅需传时间类型)")
    @PostMapping("/getDockDeviceExecJobCount")
    public CommonResult<List<DockExecJobRanking>> getDockDeviceExecJobCount(@RequestBody PostureDateParam param) {
        return CommonResult.success(dockDeviceService.getDockDeviceExecJobCount(param));
    }

    @GetMapping("/isDockInUserPermission")
    @Operation(summary = "是否有当前机场的查看权限", description = "是否有当前机场的查看权限")
    public CommonResult<Boolean> isDockInUserPermission(@RequestParam("dockSn") String dockSn) {
        return CommonResult.success(dockDeviceService.isDockInUserPermission(dockSn, SecurityFrameworkUtils.getLoginUser().getTenantId()));
    }

}

