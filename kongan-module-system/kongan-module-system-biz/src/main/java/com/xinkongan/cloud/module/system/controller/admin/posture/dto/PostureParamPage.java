package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "态势页面公共参数（分页）")
public class PostureParamPage extends PageParam {

    /**
     * 态势页面所选择的组织树信息
     */
    @NotNull(message = "组织列表信息不能为空")
    @NotEmpty(message = "组织列表信息不能为空")
    private Set<Long> deptIds;

}
