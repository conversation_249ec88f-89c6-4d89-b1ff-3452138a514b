package com.xinkongan.cloud.module.system.controller.admin.report;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialNumStatisticVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialTopVO;
import com.xinkongan.cloud.module.system.dto.TaskTopReportDTO;
import com.xinkongan.cloud.module.system.service.material.IMaterialManageService;
import com.xinkongan.cloud.module.system.service.material.IMaterialReferenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RestController
@Tag(name = "数据报表-素材管理")
@RequestMapping("/system/report/material")
public class MaterialReportController {

    @Resource
    private IMaterialManageService materialManageService;

    @Resource
    private IMaterialReferenceService materialReferenceService;


    @GetMapping(value = "/statistic")
    @Operation(summary = "数据报表-素材数目统计")
    @PreAuthorize("@ss.hasPermission('system:material:report:query')")
    public CommonResult<MaterialNumStatisticVO> getMaterialNumStatistic() {
        MaterialNumStatisticVO materialStatistic = materialManageService.getMaterialStatistic();
        return CommonResult.success(materialStatistic);
    }

    @PostMapping(value = "/top")
    @Operation(summary = "数据报表-top排行榜")
    @PreAuthorize("@ss.hasPermission('system:material:report:query')")
    public CommonResult<List<MaterialTopVO>> getMaterialTop(@Valid @RequestBody TaskTopReportDTO taskTopReportDTO) {
        List<MaterialTopVO> materialReferenceTop = materialReferenceService.getMaterialReferenceTop(taskTopReportDTO);
        return CommonResult.success(materialReferenceTop);
    }
}
