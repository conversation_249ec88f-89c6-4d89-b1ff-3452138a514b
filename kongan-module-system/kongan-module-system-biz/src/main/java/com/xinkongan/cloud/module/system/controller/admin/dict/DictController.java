package com.xinkongan.cloud.module.system.controller.admin.dict;


import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.controller.admin.dict.dto.DictInfoDTO;
import com.xinkongan.cloud.module.system.controller.admin.dict.vo.DictInfoVO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.dict.DictType;
import com.xinkongan.cloud.module.system.service.dict.IDictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;


@Validated
@RestController
@Tag(name = "管理后台 - 字典")
@RequestMapping("/system/dict")
public class DictController {

    @Resource
    private IDictService dictService;


    /**
     * 注意：该接口权限开放，忽略租户
     * @param type 字典类型
     * @return 字典列表
     */
    @PermitAll
    @GetMapping(value = "/list")
    @Operation(summary = "获取字典列表", description = "根据类型获取字典列表")
    public CommonResult<List<DictInfoVO>> getDictList(@RequestParam Integer type) {

        DictType dictType = DictType.findByType(type);
        if (dictType == null) {
            throw new ServiceException(ErrorCodeConstants.DICT_TYPE_ERROR);
        }
        AtomicReference<List<DictInfoVO>> dictInfoReference = new AtomicReference<>();
        // 忽略租户
        TenantUtils.executeIgnore(() -> dictInfoReference.set(dictService.getDictListByType(dictType)));
        return CommonResult.success(dictInfoReference.get());
    }


    @PostMapping("/add")
    @Operation(summary = "添加字典信息", description = "添加字典相关信息")
    public CommonResult<Long> addDict(@Valid @RequestBody DictInfoDTO dictInfo) {
        DictType dictType = DictType.findByType(dictInfo.getType());
        if (dictType == null) {
            throw new ServiceException(ErrorCodeConstants.DICT_TYPE_ERROR);
        }
        Long dictId = dictService.addDict(dictInfo);
        return CommonResult.success(dictId);
    }
}
