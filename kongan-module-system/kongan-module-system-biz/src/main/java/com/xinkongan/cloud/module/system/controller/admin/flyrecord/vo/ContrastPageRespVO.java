package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 对比记录分页RespVO
 * <AUTHOR>
 * @Date 2025/2/20 9:45
 */
@Schema(description = "对比记录分页RespVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContrastPageRespVO {

    @Schema(description = "对比记录id")
    private Long id;

    /**
     * 对比记录名称
     */
    @Schema(description = "对比记录名称")
    private String name;

    /**
     * 所属组织id
     **/
    @Schema(description = "所属组织id")
    private Long deptId;
    /**
     * 所属组织名称
     */
    @Schema(description = "所属组织名称")
    private String deptName;
    /**
     * 缩略图
     */
    @Schema(description = "缩略图")
    private String thumbnail;

    /**
     * 异常id
     */
    @Schema(description = "异常id")
    private Long exceptionId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Integer createTime;

}