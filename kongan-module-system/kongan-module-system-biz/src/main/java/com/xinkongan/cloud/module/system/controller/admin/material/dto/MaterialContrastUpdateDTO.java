package com.xinkongan.cloud.module.system.controller.admin.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MaterialContrastUpdateDTO {

    @NotNull(message = "对比记录id不能为空")
    @Schema(description = "对比记录id")
    private Long contrastId;

    @NotBlank(message = "对比名称不能为空")
    @Schema(description = "对比名称")
    private String contrastName;
}
