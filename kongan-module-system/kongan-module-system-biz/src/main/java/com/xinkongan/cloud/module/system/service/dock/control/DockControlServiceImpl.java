package com.xinkongan.cloud.module.system.service.dock.control;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.datapermission.core.util.DataPermissionUtils;
import com.xinkongan.cloud.framework.mybatis.config.IdGenerator;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.redis.enums.RedisKeyExpireEnums;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.algorithm.api.AlgControlApi;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.control.*;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.FlyIncidentTypeEnum;
import com.xinkongan.cloud.module.system.enums.MqttAclAccessEnum;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.dock.mqtt.progress.FlightTaskService;
import com.xinkongan.cloud.module.system.service.dock.mqtt.takeoff.TakeoffToPointProgressService;
import com.xinkongan.cloud.module.system.service.task.IJobFlyProgressService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.api.DockCloudApiControlService;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.dto.DrcModeMqttBroker;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.dto.FlyToPointProgress;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.request.DrcModeEnterRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.request.FlyToPointRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.request.FlyToPointUpdateRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.OsdDock;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.OsdDockDrone;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DockModeCodeEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DrcStateEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DroneModeCodeEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.api.DockCloudApiWayLineService;
import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.dto.FlightTaskProgress;
import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.enums.FlighttaskStatusEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.enums.WaylineMissionStateEnum;
import com.xinkongan.cloud.sdk.dock.mqtt.config.MqttConfiguration;
import com.xinkongan.cloud.sdk.dock.mqtt.events.EventsDataRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.model.MqttBrokerDTO;
import com.xinkongan.cloud.sdk.dock.mqtt.model.TopicConst;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.xinkongan.cloud.framework.redis.enums.RedisKeyExpireEnums.OSD_DOCK;
import static com.xinkongan.cloud.framework.redis.enums.RedisKeyExpireEnums.OSD_DOCK_DRONE;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */
@Slf4j
@Service
public class DockControlServiceImpl implements IDockControlService {

    @Resource
    private DockDeviceService dockDeviceService;

    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private FlightTaskService flightTaskService;

    @Resource
    private IJobService jobService;

    @Resource
    private DockCloudApiWayLineService dockCloudApiWayLineService;

    @Resource
    private TakeoffToPointProgressService takeoffToPointProgressService;

    @Resource
    private DockCloudApiControlService dockCloudApiControlService;

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private WebSocketSendApi webSocketSendApi;

    @Resource
    private AlgControlApi algControlApi;

    @Resource
    private IJobFlyProgressService progressService;

    @Override
    public void pauseWaylineByDroneSn(String droneSn, String operator) {
        DockDeviceDO dockDeviceDO = dockDeviceService.getByChildSn(droneSn);
        if (dockDeviceDO == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.DEVICE_DOCK_NOT_EXISTS);
        }
        pauseWayline(dockDeviceDO.getDeviceSn());
        progressService.saveDetail(dockDeviceDO.getDeviceSn(), FlyIncidentTypeEnum.PAUSE_WAYLINE, null, null, operator);
    }

    @Override
    public void pauseWayline(String dockSn) {
        // 判断机场在作业中
        if (!checkInJob(dockSn)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NO_TASK);
        }
        // 判断无人机 自动飞行 航线飞行
        if (droneStateCanPause(dockSn)) {
            // 判断机场任务状态为 航线执行状态 执行中
            if (checkWaylineStateInProgress(dockSn)) {
                // 暂停航线
                CommonTopicResponse<ServicesReplyData> response = dockCloudApiWayLineService.flightTaskPause(dockSn);
                if (response != null && response.getData().getResult().isSuccess()) {
                    return;
                }
                log.error("机场响应: {}", response);
                throw ServiceExceptionUtil.exception(response.getData().getResult());
            }
        }
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.ROUTE_SUSPENSION_FAILURE);
    }

    @Override
    public void resumeWayline(String dockSn) {
        // 判断机场在作业中
        if (!checkInJob(dockSn)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NO_TASK);
        }
        if (droneStateCanResume(dockSn)) {
            // 判断机场任务状态为 航线执行状态 暂停
            if (checkWaylineStatePaused(dockSn)) {
                // 恢复
                CommonTopicResponse<ServicesReplyData> response = dockCloudApiWayLineService.flightTaskRecovery(dockSn);
                if (response != null && response.getData().getResult().isSuccess()) {
                    // 清除远程控制缓存
                    drcExitClearCache(dockSn);
                    return;
                }
                log.error("机场响应: {}", response);
                throw ServiceExceptionUtil.exception(response.getData().getResult());
            }
        }
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.ROUTE_RECOVER_FAILURE);
    }

    @Override
    public void resumeWaylineBrDroneSn(String droneSn, String operator) {
        AtomicReference<DockDeviceDO> atomicReference = new AtomicReference<>();
        TenantUtils.executeIgnore(() -> atomicReference.set(dockDeviceService.getByChildSn(droneSn)));
        DockDeviceDO dockDeviceDO = atomicReference.get();
        if (dockDeviceDO == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.DEVICE_DOCK_NOT_EXISTS);
        }
        RemoteControlUserAuth userRemoteControlAuth = getUserRemoteControlAuth(dockDeviceDO.getDeviceSn());
        if (userRemoteControlAuth == null) {
            resumeWayline(dockDeviceDO.getDeviceSn());
            progressService.saveDetail(dockDeviceDO.getDeviceSn(), FlyIncidentTypeEnum.RESUME_WAYLINE, null, null, operator);
        } else {
            log.info("远程控制中，无需恢复航线任务");
        }
    }

    @Override
    public MqttBrokerDTO getDrcWsConnect(String dockSn) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        assert userId != null;
        String clientId = userId + "_" + System.currentTimeMillis();
        // 清理授权
        clearClientIdAuth(dockSn);
        accreditClientIdAuth(clientId, dockSn);
        return MqttConfiguration.getMqttBrokerWithDrc(
                clientId, userId.toString(), 3600L, Map.of("username", userId));
    }

    @Override
    public DrcRespDTO remoteControl(DrcReqDTO reqDTO) {
        DrcRespDTO respDTO = checkRemoteControl(reqDTO);
        // 判断是否被标志
        if (respDTO.getLowChargeReturnNotAuth() || respDTO.getNeedToRequestControl()) {
            return respDTO;
        }
        return drcEnter(reqDTO);
    }

    @Override
    public DrcRespDTO checkRemoteControl(DrcReqDTO reqDTO) {
        DrcRespDTO respDTO = new DrcRespDTO();
        String dockSn = reqDTO.getDockSn();
        // 判断机场在作业中
        if (!checkInJob(dockSn)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NO_TASK);
        }
        // 触发过低电量返航 并且 未授权低电量返航取消
        if (checkDroneLowPower(dockSn) && !reqDTO.getAuthorizedToInterruptLowPowerReentry()) {
            // 标志低电量 需要进行授权
            respDTO.setLowChargeReturnNotAuth(true);
        }
        // 判断是否有机场远程控制权
        if (!checkUserRemoteControlAuth(dockSn)) {
            // 标志需要申请远程控制权
            respDTO.setNeedToRequestControl(true);
            RemoteControlUserAuth userRemoteControlAuth = getUserRemoteControlAuth(dockSn);
            respDTO.setCurrentNickname(userRemoteControlAuth.getNickname());
        }
        return respDTO;
    }

    @Override
    public DrcRespDTO drcEnter(DrcReqDTO drcReqDTO) {
        String dockSn = drcReqDTO.getDockSn();
        DrcRespDTO respDTO = new DrcRespDTO();
        // 生成 DRC 消息主题前缀
        String topic = TopicConst.THING_MODEL_PRE + TopicConst.PRODUCT + dockSn + TopicConst.DRC;
        String pubTopic = topic + TopicConst.DOWN;
        String subTopic = topic + TopicConst.UP;
        respDTO.setJwtAclDTO(JwtAclDTO.builder().sub(List.of(subTopic)).pub(List.of(pubTopic)).build());
        // 判断机场在作业中
        if (!checkInJob(dockSn)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NO_TASK);
        }
        // 是否返航
        if (checkDroneReturnHome(dockSn)) {
            // 判断是否触发低电量返航
            if (checkDroneLowPower(dockSn)) {
                // 触发低电量返航
                if (!drcReqDTO.getAuthorizedToInterruptLowPowerReentry()) {
                    // 标志低电量 需要进行授权
                    respDTO.setLowChargeReturnNotAuth(true);
                    return respDTO;
                }
            }
            // 取消返航
            CommonTopicResponse<ServicesReplyData> response = dockCloudApiWayLineService.returnHomeCancel(dockSn);
            if (!response.getData().getResult().isSuccess()) {
                throw ServiceExceptionUtil.exception(response.getData().getResult());
            }
        } else {
            // 判断是否在航线任务执行中
            if (checkWaylineStateInProgress(dockSn)) {
                // 暂停航线
                pauseWayline(dockSn);
            }
            // 判断是否在执行一键起飞任务 有则取消
            if (takeoffToPointProgressService.checkStatusWaylineProgress(dockSn)) {
                // 取消一键起飞任务
                dockCloudApiControlService.flyToPointStop(dockSn);
            }
        }

        // 获取飞行器控制权
        dockCloudApiControlService.flightAuthorityGrab(dockSn);
        // 建立DRC连接
        drcModeEnter(dockSn);
        // 给用户颁发WS连接信息
        MqttBrokerDTO drcWsConnect = getDrcWsConnect(dockSn);
        respDTO.setDrcWsConnect(drcWsConnect);
        clearAllRequestRemoteControlCache(dockSn);

        RemoteControlUserAuth userRemoteControlAuth = getUserRemoteControlAuth(dockSn);
        // 关闭所有算法
        DockDeviceDO deviceInfo = dockDeviceService.getByCacheDeviceSn(dockSn);
        if (deviceInfo != null && (userRemoteControlAuth == null || !userRemoteControlAuth.getUserId().equals(SecurityFrameworkUtils.getLoginUserId()))) {
            algControlApi.stopAllAlgorithm(deviceInfo.getChildSn());
        }
        // 设置缓存
        setUserRemoteControlAuth(dockSn);
        return respDTO;
    }

    @Override
    public Boolean requestRemoteControl(String dockSn) {
        // 判断用户已申请
        if (existRequestRemoteControlCache(dockSn, SecurityFrameworkUtils.getLoginUserId())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ALREADY_REQUESTED);
        }
        RemoteControlUserAuth userRemoteControlAuth = getUserRemoteControlAuth(dockSn);
        if (userRemoteControlAuth == null) {
            return true;
        }
        // 申请进行远程控制
        requestRemoteControl(userRemoteControlAuth);
        return false;
    }

    @Override
    public Boolean autoApproveRemoteControl(String dockSn, Long userId) {
        RemoteControlUserAuth userRemoteControlAuth = getUserRemoteControlAuth(dockSn);
        if (userRemoteControlAuth != null) {
            // 允许控制
            // 设置远程控制权
            userRemoteControlAuth.setAuthUserId(userId);
            setUserRemoteControlAuth(dockSn, userRemoteControlAuth);
        }
        // 清除申请缓存
        clearRequestRemoteControlCache(dockSn, userId);

        ApproveReqDTO reqDTO = ApproveReqDTO.builder()
                .dockSn(dockSn)
                .authUserId(userId)
                .flag(true)
                .build();

        // 发送消息
        webSocketSendApi.sendByTenantUserSet(
                WebSocketMessageDTO.builder()
                        .userIdSet(Set.of(userId))
                        .tenantId(TenantContextHolder.getTenantId())
                        .message(CustomWebSocketMessage.builder()
                                .bizCode(BizCodeEnum.APPROVE_REMOTE_CONTROL.getCode())
                                .data(reqDTO)
                                .build())
                        .build());
        return true;
    }

    @Override
    public Boolean approveRemoteControl(ApproveReqDTO reqDTO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 判断审批人是否为当前机场控制人
        RemoteControlUserAuth userRemoteControlAuth = getUserRemoteControlAuth(reqDTO.getDockSn());
        if (userRemoteControlAuth == null || !userRemoteControlAuth.getUserId().equals(userId)) {
            return false;
        }
        if (reqDTO.getFlag()) {
            // 允许控制
            // 设置远程控制权
            userRemoteControlAuth.setAuthUserId(reqDTO.getAuthUserId());
            setUserRemoteControlAuth(reqDTO.getDockSn(), userRemoteControlAuth);
        }
        // 清除申请缓存
        clearRequestRemoteControlCache(reqDTO.getDockSn(), Objects.requireNonNull(reqDTO.getAuthUserId()));
        reqDTO.setNickname(SecurityFrameworkUtils.getLoginUserNickname());
        // 发送消息
        webSocketSendApi.sendByTenantUserSet(
                WebSocketMessageDTO.builder()
                        .userIdSet(Set.of(reqDTO.getAuthUserId()))
                        .tenantId(TenantContextHolder.getTenantId())
                        .message(CustomWebSocketMessage.builder()
                                .bizCode(BizCodeEnum.APPROVE_REMOTE_CONTROL.getCode())
                                .data(reqDTO)
                                .build())
                        .build());
        return true;
    }

    @Override
    @DataPermission(enable = false)
    public Boolean exitRemoteControl(String dockSn) {
        deviceDrcExit(dockSn);
        // 恢复任务
        resumeWayline(dockSn);
        // 结束算法
        DockDeviceVO deviceVO = dockDeviceService.getByDBDockSn(dockSn);
        if (deviceVO != null){
            algControlApi.stopAllAlgorithm(deviceVO.getChildSn());
        }
        return true;
    }

    @Override
    public Boolean deviceDrcExit(String dockSn) {
        if (checkDrcConnection(dockSn)) {
            // 取消指令飞行
            CommonTopicResponse<ServicesReplyData> response = dockCloudApiControlService.drcModeExit(dockSn);
            if (!response.getData().getResult().isSuccess()) {
                throw ServiceExceptionUtil.exception(response.getData().getResult());
            }
        }
        drcExitClearCache(dockSn);
        return true;
    }

    // 退出远程控制时清理缓存
    public void drcExitClearCache(String dockSn) {
        log.info("清除客户端控制权限");
        clearClientIdAuth(dockSn);
        // 清理当前机场的远程控制权限
        clearUserRemoteControlAuth(dockSn);
    }

    @Override
    public Boolean flyToPoint(FlyToPointRequest request, String dockSn) {

        String key = String.format(RedisKeyConstants.DOCK_FLY_TO_PROGRESS, dockSn);
        FlyToPointProgress progress = redisCacheService.get(key, FlyToPointProgress.class);
        if (progress == null) {
            long flyToId = IdGenerator.generateId();
            request.setFlyToId(Long.toString(flyToId));
            CommonTopicResponse<ServicesReplyData> response = dockCloudApiControlService.flyToPoint(request, dockSn);
            if (!response.getData().getResult().isSuccess()) {
                throw ServiceExceptionUtil.exception(response.getData().getResult());
            }
        } else {
            //  更新目标点信息
            FlyToPointUpdateRequest updateRequest = BeanUtil.toBean(request, FlyToPointUpdateRequest.class);
            dockCloudApiControlService.flyToPointUpdate(updateRequest, dockSn);
        }
        return true;
    }

    void drcModeEnter(String dockSn) {
        // 生成 DRC 消息主题前缀
        String topic = TopicConst.THING_MODEL_PRE + TopicConst.PRODUCT + dockSn + TopicConst.DRC;
        String pubTopic = topic + TopicConst.DOWN;
        String subTopic = topic + TopicConst.UP;
        String clientId = dockSn + "_" + System.currentTimeMillis();

        // 修改 Map 结构生成方式
        Map<String, Object> aclMap = new HashMap<>();
        aclMap.put("pub", pubTopic);
        aclMap.put("sub", subTopic);

        MqttBrokerDTO mqttBrokerDTO = MqttConfiguration.getMqttBrokerWithDrc(
                clientId,
                dockSn,
                3600L,
                Map.of("acl", aclMap, "username", dockSn) // 使用简单 Map 结构代替对象转换
        );
        DrcModeMqttBroker drcModeMqttBroker = objectMapper.convertValue(mqttBrokerDTO, DrcModeMqttBroker.class);
        accreditDockClientIdAuth(drcModeMqttBroker.getClientId(), dockSn);
        DrcModeEnterRequest request = DrcModeEnterRequest.builder()
                .mqttBroker(drcModeMqttBroker)
                .osdFrequency(10)
                .hsiFrequency(1)
                .build();
        CommonTopicResponse<ServicesReplyData> response = dockCloudApiControlService.drcModeEnter(request, dockSn);
        if (!response.getData().getResult().isSuccess()) {
            throw ServiceExceptionUtil.exception(response.getData().getResult());
        }
    }

    // 检查是否在作业中
    public boolean checkInJob(String dockSn) {
        DockModeCodeEnum dockModeCode = dockDeviceService.getDockModeCode(dockSn);
        return dockModeCode != null && dockModeCode.equals(DockModeCodeEnum.WORKING);
    }

    // 检查是否建立了drc连接
    public boolean checkDrcConnection(String dockSn) {
        String key = OSD_DOCK.getKey(dockSn);
        OsdDock osdDock = redisCacheService.get(key, OsdDock.class);
        return osdDock != null && osdDock.getDrcState() != null && osdDock.getDrcState() != DrcStateEnum.DISCONNECTED;
    }

    // 判断无人机状态是否满足恢复航线
    public boolean droneStateCanResume(String dockSn) {
        // 判断无人机 手动飞行 虚拟摇杆
        DroneModeCodeEnum droneModeCodeEnum = getDroneModeCodeByDockSn(dockSn);
        return droneModeCodeEnum != null && (droneModeCodeEnum.equals(DroneModeCodeEnum.MANUAL) || droneModeCodeEnum.equals(DroneModeCodeEnum.VIRTUAL_JOYSTICK));
    }

    // 判断无人机状态是否满足航线暂停
    public boolean droneStateCanPause(String dockSn) {
        // 判断无人机 自动飞行 航线飞行
        DroneModeCodeEnum droneModeCodeEnum = getDroneModeCodeByDockSn(dockSn);
        return droneModeCodeEnum != null && (droneModeCodeEnum.equals(DroneModeCodeEnum.WAYLINE) || droneModeCodeEnum.equals(DroneModeCodeEnum.TAKEOFF_AUTO));
    }

    // 获取机场对应无人机状态
    public DroneModeCodeEnum getDroneModeCodeByDockSn(String dockSn) {
        AtomicReference<DockDeviceVO> atomicReference = new AtomicReference<>();
        DataPermissionUtils.executeIgnore(() -> atomicReference.set(dockDeviceService.getByDBDockSn(dockSn)));
        DockDeviceVO dockDeviceVO = atomicReference.get();
        if (dockDeviceVO == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.DEVICE_DOCK_NOT_EXISTS);
        }
        String droneKey = OSD_DOCK_DRONE.getKey(dockDeviceVO.getChildSn());
        OsdDockDrone droneOsd = redisCacheService.get(droneKey, OsdDockDrone.class);
        if (droneOsd != null && droneOsd.getModeCode() != null) {
            return droneOsd.getModeCode();
        }
        return null;
    }

    // 查询机场的航线任务状态是否为任务执行中
    public boolean checkWaylineStateInProgress(String dockSn) {
        EventsDataRequest<FlightTaskProgress> taskProgress = flightTaskService.getTaskProgress(dockSn);
        return (taskProgress != null && taskProgress.getOutput().getStatus() != null
                && taskProgress.getOutput().getStatus().equals(FlighttaskStatusEnum.IN_PROGRESS));
    }

    // 查询机场的航线任务状态是否为任务已暂停
    public boolean checkWaylineStatePaused(String dockSn) {
        EventsDataRequest<FlightTaskProgress> taskProgress = flightTaskService.getTaskProgress(dockSn);
        return (taskProgress != null && taskProgress.getOutput().getStatus() != null
                && taskProgress.getOutput().getStatus().equals(FlighttaskStatusEnum.PAUSED));
    }

    // 判断当前机场对应的无人机状态是否为返航
    public boolean checkDroneReturnHome(String dockSn) {
        DroneModeCodeEnum droneModeCodeEnum = getDroneModeCodeByDockSn(dockSn);
        return droneModeCodeEnum.equals(DroneModeCodeEnum.RETURN_AUTO) || droneModeCodeEnum.equals(DroneModeCodeEnum.LANDING_AUTO);
    }

    // 判断当前机场对应的无人机此次飞行是否触发低电量返航
    public boolean checkDroneLowPower(String dockSn) {
        JobFlyInfo jobFlyInfo = jobService.getExecTaskCacheByDockSn(dockSn);
        return jobFlyInfo.getLowPower();
    }

    // 判断当前用户是否有当前机场的远程权
    public boolean checkUserRemoteControlAuth(String dockSn) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String key = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_AUTH, dockSn);
        RemoteControlUserAuth remoteControlUserAuth = redisCacheService.get(key, RemoteControlUserAuth.class);
        assert userId != null;
        return remoteControlUserAuth == null || userId.equals(remoteControlUserAuth.getUserId())
                || userId.equals(remoteControlUserAuth.getAuthUserId());
    }

    // 设置远程控制权缓存
    public void setUserRemoteControlAuth(String dockSn) {
        RemoteControlUserAuth remoteControlUserAuth = new RemoteControlUserAuth();
        remoteControlUserAuth.setUserId(SecurityFrameworkUtils.getLoginUserId());
        remoteControlUserAuth.setNickname(SecurityFrameworkUtils.getLoginUserNickname());
        remoteControlUserAuth.setDockSn(dockSn);
        setUserRemoteControlAuth(dockSn, remoteControlUserAuth);
    }

    // 设置远程控制权缓存
    public void setUserRemoteControlAuth(String dockSn, RemoteControlUserAuth remoteControlUserAuth) {
        String key = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_AUTH, dockSn);
        redisCacheService.put(key, remoteControlUserAuth, 3600L);
    }

    // 获取远程控制权缓存
    public RemoteControlUserAuth getUserRemoteControlAuth(String dockSn) {
        String key = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_AUTH, dockSn);
        return redisCacheService.get(key, RemoteControlUserAuth.class);
    }

    // 清除远程控制权缓存
    public void clearUserRemoteControlAuth(String dockSn) {
        String key = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_AUTH, dockSn);
        redisCacheService.delete(key);
    }

    // 清理客户端控制权
    public void clearClientIdAuth(String dockSn) {
        String key = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_CLIENT_ID, dockSn);
        String clientId = redisCacheService.get(key, String.class);
        redisCacheService.delete(String.format(RedisKeyConstants.MQTT_ACL_PREFIX, clientId));
        if (clientId != null) {
            Long userId = Long.parseLong(clientId.split("_")[0]);
            webSocketSendApi.sendByTenantUserSet(
                    WebSocketMessageDTO.builder()
                            .userIdSet(Set.of(userId))
                            .tenantId(TenantContextHolder.getTenantId())
                            .message(CustomWebSocketMessage.builder()
                                    .bizCode(BizCodeEnum.LOSS_OF_CONTROL.getCode())
                                    .data(clientId)
                                    .build())
                            .build());
        }
    }

    // 为客户端授予远程控制订阅发布权
    public void accreditClientIdAuth(String clientId, String dockSn) {
        String clientIdKey = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_CLIENT_ID, dockSn);
        redisCacheService.put(clientIdKey, clientId, 3600L);
        String key = String.format(RedisKeyConstants.MQTT_ACL_PREFIX, clientId);
        // 生成 DRC 消息主题前缀
        String topic = TopicConst.THING_MODEL_PRE + TopicConst.PRODUCT + dockSn + TopicConst.DRC;
        String pubTopic = topic + TopicConst.DOWN;
        String subTopic = topic + TopicConst.UP;
        redisCacheService.hashPut(key, pubTopic, MqttAclAccessEnum.PUB.getValue(), 3600L);
        redisCacheService.hashPut(key, subTopic, MqttAclAccessEnum.SUB.getValue(), 3600L);
    }

    // 为机场授予远程控制订阅发布权
    public void accreditDockClientIdAuth(String clientId, String dockSn) {
        String key = String.format(RedisKeyConstants.MQTT_ACL_PREFIX, clientId);
        // 生成 DRC 消息主题前缀
        String topic = TopicConst.THING_MODEL_PRE + TopicConst.PRODUCT + dockSn + TopicConst.DRC;
        String subTopic = topic + TopicConst.DOWN;
        String pubTopic = topic + TopicConst.UP;
        redisCacheService.hashPut(key, pubTopic, MqttAclAccessEnum.PUB.getValue(), 3600L);
        redisCacheService.hashPut(key, subTopic, MqttAclAccessEnum.SUB.getValue(), 3600L);
    }

    public void requestRemoteControl(RemoteControlUserAuth remoteControlUserAuth) {
        String dockSn = remoteControlUserAuth.getDockSn();
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        webSocketSendApi.sendByTenantUserSet(
                WebSocketMessageDTO.builder()
                        .userIdSet(Set.of(remoteControlUserAuth.getUserId()))
                        .tenantId(TenantContextHolder.getTenantId())
                        .message(CustomWebSocketMessage.builder()
                                .bizCode(BizCodeEnum.REQUEST_REMOTE_CONTROL.getCode())
                                .data(RequestRemoteControlMessage.builder()
                                        .dockSn(dockSn)
                                        .requestNickname(SecurityFrameworkUtils.getLoginUserNickname())
                                        .requestUserId(userId)
                                        .build())
                                .build())
                        .build());
        redisCacheService.put(RedisKeyExpireEnums.REQUEST_REMOTE_CONTROL.getKey(dockSn + ":" + userId), dockSn);
        addRequestRemoteControlCache(dockSn, userId);
    }

    // 添加请求控制缓存
    public void addRequestRemoteControlCache(String dockSn, Long userId) {
        String key = RedisKeyExpireEnums.REQUEST_REMOTE_CONTROL.getKey(dockSn + ":" + userId);
        redisCacheService.put(key, dockSn, RedisKeyExpireEnums.REQUEST_REMOTE_CONTROL.getExpireTime());
        String masterKey = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_APPLY_FOR, dockSn);
        redisCacheService.hashPut(masterKey, userId.toString(), userId);
    }

    // 控制权发生变化时清空所有申请信息
    public void clearAllRequestRemoteControlCache(String dockSn) {
        String masterKey = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_APPLY_FOR, dockSn);
        Set<String> userIdSet = redisCacheService.hashKeys(masterKey, String.class);
        if (userIdSet.isEmpty()) {
            return;
        }
        userIdSet.forEach(userId -> {
            String key = RedisKeyExpireEnums.REQUEST_REMOTE_CONTROL.getKey(dockSn + ":" + userId);
            redisCacheService.delete(key);
        });
        redisCacheService.hashDel(masterKey, userIdSet.toArray(new String[0]));
    }

    // 清理申请控制的缓存
    public void clearRequestRemoteControlCache(String dockSn, Long userId) {
        String key = RedisKeyExpireEnums.REQUEST_REMOTE_CONTROL.getKey(dockSn + ":" + userId);
        redisCacheService.delete(key);
        String masterKey = String.format(RedisKeyConstants.DOCK_REMOTE_CONTROL_APPLY_FOR, dockSn);
        redisCacheService.hashDel(masterKey, new String[]{userId.toString()});
    }

    // 查询申请控制的缓存
    public Boolean existRequestRemoteControlCache(String dockSn, Long userId) {
        String key = RedisKeyExpireEnums.REQUEST_REMOTE_CONTROL.getKey(dockSn + ":" + userId);
        String sn = redisCacheService.get(key, String.class);
        return sn != null;
    }

}
