package com.xinkongan.cloud.module.system.controller.admin.psdk.vo;

import com.xinkongan.cloud.module.system.dal.dataobject.psdk.VoiceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023/6/7 16:08
 */
@Data
public class ActionVoiceSendToUserVo extends VoiceDO {

    /** 声音大小 */
    @Schema(description = "声音大小")
    @Pattern(regexp = "\\d{1,3}", message = "声音大小必须在 0 和 99之间")
    private String voice;

    /** 语速 */
    @Schema(description = "语速")
    @Pattern(regexp = "\\d+(\\.\\d+)?", message = "语速必须是正数")
    private String speed;

    /** 播放模式 */
    @Schema(description = "播放模式（0：单次  1：循环）")
    @Pattern(regexp = "0|1", message = "播放模式必须是 0 或者 1")
    private String mode;

    /** socket发送指定飞手id */
    @Schema(description = "socket发送指定飞手id")
    private String userId;
}
