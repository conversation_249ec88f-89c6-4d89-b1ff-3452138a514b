package com.xinkongan.cloud.module.system.controller.admin.dock.vo.device;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/3
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CameraTypeFeature {

    @Schema(description = "当前相机的最小变焦范围")
    private Integer minZoom;

    @Schema(description = "当前相机的最大变焦范围")
    private Integer maxZoom;

    @Schema(description = "当前红外相机的最小变焦范围")
    private Integer irMinZoom;

    @Schema(description = "当前红外相机的最大变焦范围")
    private Integer irMaxZoom;

    @Schema(description = "当前相机支持的镜头列表")
    private List<String> lensList;

    @Schema(description = "当前相机支持的照片存储类型列表")
    private List<String> imageFormat;

    @Schema(description = "相机名称")
    private String cameraName;

}
