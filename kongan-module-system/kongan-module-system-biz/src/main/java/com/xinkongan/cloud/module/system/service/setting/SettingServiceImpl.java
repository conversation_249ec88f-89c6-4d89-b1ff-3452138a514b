package com.xinkongan.cloud.module.system.service.setting;

import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.api.setting.dto.UserMapSettingReqVO;
import com.xinkongan.cloud.module.system.api.setting.dto.SettingRespVO;
import com.xinkongan.cloud.module.system.service.dept.DeptMapSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 用户设置 Service 实现类
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-27
 */
@Slf4j
@Service
public class SettingServiceImpl implements SettingService {

    @Resource
    private DeptMapSettingService deptMapSettingService;

    @Override
    public void updateUserMapSetting(UserMapSettingReqVO reqVO) {
        log.info("用户手动设置地图信息，用户ID: {}, 请求参数: {}", SecurityFrameworkUtils.getLoginUserId(), reqVO);
        
        // 获取当前用户所属组织ID
        Long currentUserDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
        if (currentUserDeptId == null) {
            log.warn("账号未登录，无法设置地图信息，用户ID: {}", SecurityFrameworkUtils.getLoginUserId());
            throw new RuntimeException("账号未登录");
        }
        
        // 直接调用组织地图设置服务
        deptMapSettingService.updateDeptMapSetting(currentUserDeptId, reqVO);
        
        log.info("用户地图设置更新成功，组织ID: {}, 用户ID: {}", currentUserDeptId, SecurityFrameworkUtils.getLoginUserId());
    }

    @Override
    public SettingRespVO getUserMapSetting() {
        log.info("用户查询地图设置，用户ID: {}", SecurityFrameworkUtils.getLoginUserId());
        
        // 获取当前用户所属组织ID
        Long currentUserDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
        if (currentUserDeptId == null) {
            log.warn("账号未登录，无法查询地图设置，用户ID: {}", SecurityFrameworkUtils.getLoginUserId());
            throw new RuntimeException("账号未登录");
        }
        
        // 直接调用组织地图设置服务
        SettingRespVO respVO = deptMapSettingService.getDeptMapSetting(currentUserDeptId);

        log.info("用户地图设置查询成功，组织ID: {}, 用户ID: {}", currentUserDeptId, SecurityFrameworkUtils.getLoginUserId());
        return respVO;
    }

}
