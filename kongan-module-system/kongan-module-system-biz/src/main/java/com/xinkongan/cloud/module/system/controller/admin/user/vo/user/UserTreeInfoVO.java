package com.xinkongan.cloud.module.system.controller.admin.user.vo.user;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserTreeInfoVO extends BaseTreeNode {

    @Schema(description = "用户id")
    private Long id;

    @Schema(description = "username")
    private String username;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "角色id")
    private Long roleId;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "部门id")
    private Long deptId;
}
