package com.xinkongan.cloud.module.system.service.dock.mqtt.device;

import com.xinkongan.cloud.framework.datapermission.core.util.DataPermissionUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.config.DockDeviceConfigVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.dto.DockMessageDTO;
import com.xinkongan.cloud.module.system.framework.dock.core.SystemDeviceContext;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.dock.dockconfig.IDockDeviceConfigService;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2024/12/27
 */
@Service
public class DockWebSocketService {

    @Resource
    private DeptService deptService;

    @Resource
    private SystemDeviceContext systemDeviceContext;

    @Resource
    private DockDeviceService dockDeviceService;

    @Resource
    private WebSocketSendApi webSocketSendApi;

    @Resource
    private IDockDeviceConfigService dockDeviceConfigService;

    /**
     * 消息推送 - Java
     *
     * @param message 消息内容
     * @param isDock  是否为机场
     */
    public <T> void messageSendForJava(CustomWebSocketMessage<DockMessageDTO<T>> message, Boolean isDock, String dockSn) {

        AtomicReference<DockDeviceDO> atomicReference = new AtomicReference<>();

        DataPermissionUtils.executeIgnore(() -> atomicReference.set(dockDeviceService.getByCacheDeviceSn(dockSn)));

        DockDeviceDO deviceDO = atomicReference.get();
        TenantContextHolder.setTenantId(deviceDO.getTenantId());

        // 根据类型处理消息
        messageSend(message, isDock, dockSn, deviceDO.getTenantId(), deviceDO.getDeptId());

    }

    /**
     * 消息推送
     *
     * @param message 消息内容
     * @param isDock  是否为机场
     */
    public <T> void messageSend(CustomWebSocketMessage<DockMessageDTO<T>> message, Boolean isDock) {
        messageSend(message, isDock, systemDeviceContext.getDockSn(), systemDeviceContext.getTenantId(), systemDeviceContext.getDeptId());
    }

    /**
     * 消息推送
     *
     * @param message 消息内容
     * @param isDock  是否为机场
     */
    public <T> void messageSend(CustomWebSocketMessage<DockMessageDTO<T>> message, Boolean isDock, String dockSn, Long tenantId, Long deptId) {
        // 查询是否开启共享
        DockDeviceConfigVO dockDeviceConfigVO = dockDeviceConfigService.getDockDeviceConfigByDockSn(dockSn, tenantId);
        boolean isShared = dockDeviceConfigVO != null && dockDeviceConfigVO.getShareStatus().equals(1);
        message.getData().setIsShared(isShared);

        // 根据类型处理消息
        processMessage(message, isDock, isShared, deptId);
    }

    /**
     * 处理消息
     *
     * @param message  消息内容
     * @param isDock   是否为机场
     * @param isShared 是否共享
     */
    private void processMessage(CustomWebSocketMessage<?> message, boolean isDock, boolean isShared, Long deptId) {
        if (isShared) {
            shareMessageSend((CustomWebSocketMessage<Object>) message);
        } else {
            Set<Long> parentDeptIds = deptService.getParentDeptIds(deptId);
            noShareMessageSend((CustomWebSocketMessage<Object>) message, parentDeptIds);
        }
    }

    /**
     * 未共享消息推送
     *
     * @param message       消息内容
     * @param parentDeptIds 部门ID集合
     */
    private void noShareMessageSend(CustomWebSocketMessage<Object> message, Set<Long> parentDeptIds) {
        webSocketSendApi.sendByTenantDeptSet(
                WebSocketMessageDTO.builder()
                        .tenantId(TenantContextHolder.getTenantId())
                        .deptIdSet(parentDeptIds)
                        .message(message)
                        .build());
    }

    /**
     * 共享消息推送
     *
     * @param message 消息内容
     */
    private void shareMessageSend(CustomWebSocketMessage<Object> message) {
        webSocketSendApi.sendByTenant(
                WebSocketMessageDTO.builder()
                        .tenantId(TenantContextHolder.getTenantId())
                        .message(message)
                        .build());
    }
}
