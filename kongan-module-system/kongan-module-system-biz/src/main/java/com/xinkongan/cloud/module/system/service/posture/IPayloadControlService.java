package com.xinkongan.cloud.module.system.service.posture;

import com.xinkongan.cloud.module.system.api.dock.dto.payload.CameraScreenDragDTO;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.control.PayloadCommandsParam;
import com.xinkongan.cloud.module.system.enums.PayloadCommandsEnum;

/**
 * <AUTHOR>
 * @date 2025/2/19
 */
public interface IPayloadControlService {

    void cameraScreenDrag(CameraScreenDragDTO dto);

    Boolean control(PayloadCommandsParam param);

    Boolean simpleControlByDroneSn(String droneSn, PayloadCommandsEnum commandsEnum);

}
