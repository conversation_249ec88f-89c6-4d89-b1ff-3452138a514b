package com.xinkongan.cloud.module.system.controller.admin.route.vo;

import com.xinkongan.cloud.module.system.enums.route.WayPointActionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WayPointActionRespVO {

    @Schema(description = "航点动作id")
    private Long id;

    @Schema(description = "航点id", example = "11")
    private Long pointId;

    @Schema(description = "航线id", example = "11")
    private Long routeId;

    /**
     * 参考{@link WayPointActionTypeEnum}
     */
    @Schema(description = "动作标识", example = "1:大疆航线执行动作，2:平台自定义动作")
    private Integer actionType;

    @Schema(description = "航点动作类型")
    private String type;

    @Schema(description = "动作参数值")
    private String value;

    @Schema(description = "航点顺序")
    private Integer number;

    @Schema(description = "动作执行模式 sequence：串行执行。即动作组内的动作依次按顺序执行。")
    private String actionGroupMode;

    /**
     * 动作触发器类型
     * reachPoint：到达航点时执行
     * multipleTiming：等时触发
     * multipleDistance：等距触发
     */
    @Schema(description = "动作触发器类型")
    public String actionTriggerType;


    @Schema(description = "云台转动模式 relativeAngle：相对角度 absoluteAngle：绝对角度")
    public String gimbalRotateMode;


    @Schema(description = "飞行器偏航角转动模式 clockwise：顺时针旋转 counterClockwise：逆时针旋转")
    public String aircraftPathMode;

    @Schema(description = "具体的算法动作信息")
    private RouteAlgorithmVO routeAlgorithmInfo;

    @Schema(description = "具体的喊话动作信息")
    private WayPointShoutVO wayPointShoutInfo;
}
