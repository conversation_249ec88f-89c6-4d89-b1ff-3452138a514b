package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.xinkongan.cloud.framework.common.util.json.databind.StringLocalDateTimeDeserializer;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileSourceEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 上传文件
 * <AUTHOR>
 * @Date 2025/2/13 20:01
 */
@Data
@Schema(description = "上传文件")
@AllArgsConstructor
@NoArgsConstructor
public class UploadFileReqVO {

    @Schema(description = "飞行记录id")
    @NotNull(message = "飞行记录id不能为空")
    private Long flyRecordId;

    @Schema(description = "图片名称")
    private String name;

    @Schema(description = "动作时间")
    @NotNull(message = "动作时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonDeserialize(using = StringLocalDateTimeDeserializer.class)
    private LocalDateTime actionTime;

    /**
     * {@link FileSourceEnum}
     *
     * <AUTHOR>
     * @date 2025/2/14 10:20
     **/
    @Schema(description = "文件来源0拍摄1截图2导入3录制")
    @NotNull(message = "文件来源不能为空")
    private Integer fileSource;

    @Schema(description = "文件类型0视频1图片2全景图")
    @NotNull(message = "文件类型不能为空")
    private Integer fileType;

    @Schema(description = "高")
    private Integer height;

    @Schema(description = "长/像素")
    private Integer width;

    @Schema(description = "纬度")
    private String lat;

    @Schema(description = "经度")
    private String lon;

    @Schema(description = "绝对高度")
    private String absoluteAltitude;

    @Schema(description = "相对高度")
    private String relativeAltitude;

    @Schema(description = "文件id")
    @NotNull(message = "文件id不能为空")
    private Long fileId;
}