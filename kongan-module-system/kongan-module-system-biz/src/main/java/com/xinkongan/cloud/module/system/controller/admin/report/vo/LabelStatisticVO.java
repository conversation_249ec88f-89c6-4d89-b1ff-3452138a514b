package com.xinkongan.cloud.module.system.controller.admin.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LabelStatisticVO {

    @Schema(description = "标注总数",example = "50")
    private Integer labelTotal;

    @Schema(description = "内部标注",example = "40")
    private Integer labelInnerTotal;

    @Schema(description = "外部标注",example = "10")
    private Integer labelOuterTotal;

    @Schema(description = "航线引用次数",example = "10")
    private Long routeReferTotal;
}
