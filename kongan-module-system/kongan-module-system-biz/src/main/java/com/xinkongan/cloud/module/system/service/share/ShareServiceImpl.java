package com.xinkongan.cloud.module.system.service.share;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import com.xinkongan.cloud.module.system.dal.dataobject.share.ResourceShareDO;
import com.xinkongan.cloud.module.system.dal.mysql.share.ResourceShareMapper;
import com.xinkongan.cloud.module.system.dto.ResourceShareDTO;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.permission.PermissionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ShareServiceImpl implements IShareService {

    @Resource
    private ResourceShareMapper resourceShareMapper;

    @Resource
    private DeptService deptService;

    @Resource
    private PermissionService permissionService;


    @Override
    public void removeResourceShare(Long resourceId, Integer resourceType) {
        resourceShareMapper.delete(
                new LambdaQueryWrapperX<ResourceShareDO>()
                        .eq(ResourceShareDO::getResourceId, resourceId)
                        .eq(ResourceShareDO::getType, resourceType)
        );
    }

    @Override
    public void resourceShareDeptIds(ResourceShareDTO resourceShareInfo) {
        // 查询该资源之前的分享记录
        List<ResourceShareDO> resourceShares = resourceShareMapper.selectList(
                new LambdaQueryWrapperX<ResourceShareDO>()
                        .eq(ResourceShareDO::getResourceId, resourceShareInfo.getResourceId())
                        .eq(ResourceShareDO::getType, resourceShareInfo.getResourceType())
                        .eq(ResourceShareDO::getDeptId, SecurityFrameworkUtils.getLoginUserDeptId())
        );
        // 移除之前的分享记录
        if (!CollectionUtil.isEmpty(resourceShares)) {
            resourceShareMapper.deleteByIds(resourceShares.stream().map(ResourceShareDO::getId).toList());
        }
        // 插入新的记录
        List<ResourceShareDO> resourceShareRecords = resourceShareInfo.getShareDeptIds()
                .stream()
                .map(deptId -> this.convert(resourceShareInfo, deptId))
                .toList();
        resourceShareMapper.insertBatch(resourceShareRecords);
    }


    private ResourceShareDO convert(ResourceShareDTO resourceShareInfo, Long deptId) {
        ResourceShareDO resourceShareDO = new ResourceShareDO();

        resourceShareDO.setResourceId(resourceShareInfo.getResourceId());
        resourceShareDO.setShareDeptId(deptId);
        resourceShareDO.setType(resourceShareInfo.getResourceType());
        resourceShareDO.setOriDeptId(resourceShareInfo.getOriDeptId());

        return resourceShareDO;
    }


    @Override
    public boolean checkResourceShare(Long resourceId, Integer resourceType) {

        // 获取当前用户的数据权限
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(SecurityFrameworkUtils.getLoginUserId());
        Set<Long> deptIds = deptDataPermission.getDeptIds();
        Long count = resourceShareMapper.selectCount(
                new LambdaQueryWrapperX<ResourceShareDO>()
                        .eq(ResourceShareDO::getResourceId, resourceId)
                        .eq(ResourceShareDO::getType, resourceType)
                        .eq(ResourceShareDO::getShareDeptId, SecurityFrameworkUtils.getLoginUserDeptId())
                        .notIn(!CollectionUtil.isEmpty(deptIds), ResourceShareDO::getOriDeptId, deptIds)
        );
        return count > 0;
    }

    @Override
    public Map<Long, Boolean> checkResourceShare(List<Long> resourceIds, Integer resourceType) {
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(SecurityFrameworkUtils.getLoginUserId());
        Set<Long> deptIds = deptDataPermission.getDeptIds();

        List<ResourceShareDO> resourceShareInfos = resourceShareMapper.selectList(
                new LambdaQueryWrapperX<ResourceShareDO>()
                        .in(ResourceShareDO::getResourceId, resourceIds)
                        .eq(ResourceShareDO::getType, resourceType)
                        .eq(ResourceShareDO::getShareDeptId, SecurityFrameworkUtils.getLoginUserDeptId())
                        .notIn(!CollectionUtil.isEmpty(deptIds), ResourceShareDO::getOriDeptId, deptIds)
        );
        Set<Long> shareResourceIds = resourceShareInfos.stream().map(ResourceShareDO::getResourceId).collect(Collectors.toSet());
        return resourceIds.stream().collect(Collectors.toMap(k -> k, shareResourceIds::contains));
    }

    @Override
    public List<Long> getResourceSharesDept(Long resourceId) {
        List<ResourceShareDO> resourceShareInfos = resourceShareMapper.selectList(
                new LambdaQueryWrapperX<ResourceShareDO>()
                        .eq(ResourceShareDO::getResourceId, resourceId)
        );
        return resourceShareInfos.stream().map(ResourceShareDO::getShareDeptId).toList();
    }

    /**
     * 查询哪些资源被分享到指定组织中
     *
     * @date 2025/2/19 10:54
     **/
    @Override
    public List<Long> getDeptShareIds(Long deptId, ResourceShareTypeEnum resourceType) {
        List<ResourceShareDO> resourceShareDOS = resourceShareMapper.selectList(Wrappers.lambdaQuery(ResourceShareDO.class)
                .eq(ResourceShareDO::getType, resourceType.getResourceType())
                .eq(ResourceShareDO::getShareDeptId, deptId)
                .select(ResourceShareDO::getResourceId));
        if (CollUtil.isEmpty(resourceShareDOS)) {
            return List.of();
        } else {
            return resourceShareDOS.stream().map(ResourceShareDO::getResourceId).toList();
        }
    }

    @DataPermission(enable = false)
    @Override
    public boolean checkResourceShare(Long resourceId, Integer resourceType, Set<Long> deptIds) {
        return resourceShareMapper.exists(
                Wrappers.lambdaQuery(ResourceShareDO.class)
                        .eq(ResourceShareDO::getResourceId, resourceId)
                        .eq(ResourceShareDO::getType, resourceType)
                        .in(ResourceShareDO::getShareDeptId, deptIds)
        );
    }
}
