package com.xinkongan.cloud.module.system.controller.admin.label.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinkongan.cloud.framework.datapermission.core.plugins.ShareResultVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;


@Data
public class LabelRespVO  extends ShareResultVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "标注名称")
    private String name;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度")
    private Double height;

    @Schema(description = "缩略图")
    private String thumbnailUrl;

    @Schema(description = "地理位置")
    private String address;

    @Schema(description = "组织名称")
    private String deptName;

    @Schema(description = "创建人姓名")
    private String createName;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2024-12-31 14:50:00")
    private Date createTime;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2024-12-31 14:50:00")
    private Date updateTime;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "锁定标志")
    private Long lockFlag;
}
