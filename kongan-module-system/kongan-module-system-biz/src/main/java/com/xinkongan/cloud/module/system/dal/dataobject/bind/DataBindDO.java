package com.xinkongan.cloud.module.system.dal.dataobject.bind;

import com.baomidou.mybatisplus.annotation.*;
import com.xinkongan.cloud.framework.mybatis.core.dataobject.DeptBaseDO;
import lombok.*;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;

/**
 * 数据绑定表
 */
@TableName("system_data_bind")
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DataBindDO {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("fly_record_id")
    private Long flyRecordId;

    /**
     * 任务id
     */
    @TableField("job_id")
    private Long jobId;

    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    @TableField(fill = FieldFill.INSERT, jdbcType = JdbcType.VARCHAR)
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, jdbcType = JdbcType.VARCHAR)
    private String updater;
}



