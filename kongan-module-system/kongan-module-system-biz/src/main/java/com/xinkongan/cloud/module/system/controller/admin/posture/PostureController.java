package com.xinkongan.cloud.module.system.controller.admin.posture;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageParam;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.algorithm.api.AlgControlApi;
import com.xinkongan.cloud.module.system.common.annotation.DeviceControlCheck;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.*;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.posture.IOneTakeOffService;
import com.xinkongan.cloud.module.system.service.posture.IPostureService;
import com.xinkongan.cloud.module.system.service.posture.IUserDevicePinTopService;
import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.api.DockCloudApiWayLineService;
import com.xinkongan.cloud.sdk.dock.common.DockErrorCode;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
@Validated
@RestController
@Tag(name = "管理后台 - 态势大屏")
@RequestMapping("/system/posture")
public class PostureController {

    @Resource
    private IPostureService postureService;

    @Resource
    private IOneTakeOffService oneTakeOffService;

    @Resource
    private DockCloudApiWayLineService wayLineService;

    @Resource
    private IUserDevicePinTopService userDevicePinTopService;

    @Resource
    private AlgControlApi algControlApi;

    @Resource
    private DockDeviceService dockDeviceService;


    @Operation(summary = "任务列表分页查询")
    @PostMapping("/mixedJob")
    CommonResult<PageResult<MixedJobPageResp>> mixedJob(@RequestBody @Valid PostureParamPage page) {
        PageResult<MixedJobPageResp> mixedJob = postureService.mixedJob(page);
        return CommonResult.success(mixedJob);
    }

    @Operation(summary = "场景任务数量统计")
    @PostMapping("/jobSceneNumber")
    CommonResult<JobSceneNumberVO> jobSceneNumber(@RequestBody @Valid PostureParam param) {
        JobSceneNumberVO mixedJob = postureService.jobSceneNumber(param);
        return CommonResult.success(mixedJob);
    }

    @Operation(summary = "按月统计任务数量走势")
    @PostMapping("/jobNumberTrend")
    CommonResult<List<JobNumberTrendVO>> jobNumberTrend(@RequestBody @Valid PostureParam param) {
        List<JobNumberTrendVO> mixedJob = postureService.jobNumberTrend(param);
        return CommonResult.success(mixedJob);
    }

    @PostMapping("/multipleDeviceList")
    @Operation(summary = "多种设备列表")
    CommonResult<MultipleDeviceVO> multipleDeviceList(@RequestBody @Valid PostureParam param) {
        MultipleDeviceVO multipleDeviceVO = postureService.multipleDeviceList(param);
        return CommonResult.success(multipleDeviceVO);
    }

    @PostMapping("/devicePinTop/{deviceKey}")
    @Operation(summary = "设备置顶")
    CommonResult<Boolean> devicePinTop(@PathVariable("deviceKey") @NotNull String deviceKey) {
        boolean pinTop = userDevicePinTopService.pinTop(deviceKey);
        return CommonResult.success(pinTop);
    }

    @PostMapping("/deviceUnPinTop/{deviceKey}")
    @Operation(summary = "设备取消置顶")
    CommonResult<Boolean> deviceUnPinTop(@PathVariable("deviceKey") @NotNull String deviceKey) {
        boolean unPinTop = userDevicePinTopService.unPinTop(deviceKey);
        return CommonResult.success(unPinTop);
    }

    // 查询某个用户置顶的设备列表
    @GetMapping("/devicePinTopList")
    @Operation(summary = "设备置顶列表")
    CommonResult<List<String>> devicePinTopList() {
        List<String> devicePinTopList = userDevicePinTopService.getPinTopDeviceList();
        return CommonResult.success(devicePinTopList);
    }

    @Operation(summary = "一键起飞")
    @PostMapping("/takeOff")
    @DeviceControlCheck(sn = "#takeoffTakeoffReqVO.dockSn")
    public CommonResult<Boolean> takeoffToPoint(@Valid @RequestBody OneKeyTakeoffTakeoffReqVO takeoffTakeoffReqVO) {
        Boolean takeoff = oneTakeOffService.takeOff(takeoffTakeoffReqVO);
        return CommonResult.success(takeoff);
    }

    @PostMapping("/returnHome/{dockSn}")
    @Operation(summary = "一键返航")
    @DeviceControlCheck(sn = "#dockSn")
    public CommonResult<Boolean> deviceDebug(@PathVariable @NotNull String dockSn) {
        CommonTopicResponse<ServicesReplyData> response = wayLineService.returnHome(dockSn);
        DockErrorCode result = response.getData().getResult();

        // 关闭所有算法
        DockDeviceDO deviceInfo = dockDeviceService.getByCacheDeviceSn(dockSn);
        if (deviceInfo != null) {
            algControlApi.stopAllAlgorithm(deviceInfo.getChildSn());
        }

        return result.getCode().equals(0) ? CommonResult.success(true) : CommonResult.error(result.getCode(), result.getMessage());
    }

    @Operation(summary = "查询机场设备正在执行的航线和历史轨迹")
    @GetMapping("/getDockRouteAndFlyRecord/{dockSn}")
    public CommonResult<DockRouteAndFlyRecordRespVO> getDockRouteAndFlyRecord(@NotNull @PathVariable String dockSn) {
        DockRouteAndFlyRecordRespVO respVO = postureService.getDockRouteAndFlyRecord(dockSn);
        return CommonResult.success(respVO);
    }

    @Operation(summary = "设备飞行排名")
    @PostMapping("/deviceFlyRank")
    CommonResult<List<DeviceRankVO>> deviceRank(@RequestBody @Valid FlightRankParam param) {
        List<DeviceRankVO> deviceRank = postureService.deviceRank(param);
        return CommonResult.success(deviceRank);
    }

    @Operation(summary = "设备飞行统计")
    @PostMapping("/deviceFlyStatistics")
    CommonResult<DeviceRankVO> deviceFlyStatistics(@RequestBody @Valid PostureDateParam param) {
        DeviceRankVO deviceFlyStatistics = postureService.deviceFlyStatistics(param);
        return CommonResult.success(deviceFlyStatistics);
    }

    @Operation(summary = "警情分页查询")
    @PostMapping("/alertListPage")
    CommonResult<PageResult<PostureAlarmListVO>> alertListPage(@RequestBody @Valid PostureParamPage param) {
        PageResult<PostureAlarmListVO> alertListPage = postureService.alertListPage(param);
        return CommonResult.success(alertListPage);
    }

    @PostMapping("/page")
    @Operation(summary = "消息通知列表", description = "消息通知列表")
    CommonResult<PageResult<PostureNoticeListVO>> noticeListPage(@RequestBody @Valid PageParam param) {
        PageResult<PostureNoticeListVO> noticeListPage = postureService.noticeListPage(param);
        return success(noticeListPage);
    }

}
