package com.xinkongan.cloud.module.system.controller.admin.auth.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "管理后台 - Reset password Request VO")
public class AuthResetPwdVO {

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18376542181")
    private String mobile;

    @Schema(description = "验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "9999")
    private String verificationCode;

    @Schema(description = "新密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456Aa.")
    private String password;
}
