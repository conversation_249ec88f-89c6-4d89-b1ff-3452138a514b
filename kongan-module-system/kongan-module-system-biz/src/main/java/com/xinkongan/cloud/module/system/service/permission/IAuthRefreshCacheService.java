package com.xinkongan.cloud.module.system.service.permission;

public interface IAuthRefreshCacheService {


    /**
     * 使某个用户的权限缓存移除，用于更新缓存
     *
     * @param userId 用户id
     * @param auth   权限
     */
    void invalidUserAuthCacheById(Long userId, String auth);


    /**
     * 使某个用户的权限缓存失效
     *
     * @param userId 用户id
     */
    void invalidUserAuth(Long userId);


    /**
     * 刷新系统用户的权限缓存，一般在运营端修改租户权限时调用
     */
    void refreshSystemUserAuthCache(Long tenantId);
}
