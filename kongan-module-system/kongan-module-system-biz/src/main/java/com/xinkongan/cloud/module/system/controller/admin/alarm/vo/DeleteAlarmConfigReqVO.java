package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 删除接警配置ReqVO
 * <AUTHOR>
 * @Date 2025/3/19 14:29
 */
@Schema(description = "删除接警配置ReqVO")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeleteAlarmConfigReqVO {

    @Schema(description = "警情场景id")
    @NotNull(message = "警情场景id不能为空")
    private Long id;
}