package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction.FlightRestrictionZoneRespVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction.FlightRestrictionZoneSaveReqVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction.FlightRestrictionZoneStatusReqVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction.FlightRestrictionZoneUpdateReqVO;
import com.xinkongan.cloud.module.system.service.dock.flightrestriction.ISystemFlightRestrictionZoneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 限飞区管理Controller
 *
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 限飞区管理")
@RequestMapping("/system/flight-restriction-zone")
public class SystemFlightRestrictionZoneController {

    @Resource
    private ISystemFlightRestrictionZoneService flightRestrictionZoneService;

    @GetMapping("/list")
    @Operation(summary = "查询限飞区全部列表", description = "查询所有限飞区，不分页")
    public CommonResult<List<FlightRestrictionZoneRespVO>> getFlightRestrictionZoneList() {
        List<FlightRestrictionZoneRespVO> list = flightRestrictionZoneService.getFlightRestrictionZoneList();
        return success(list);
    }


    @GetMapping("/get/{id}")
    @Operation(summary = "根据ID查询限飞区详情")
    @Parameter(name = "id", description = "限飞区ID", required = true, example = "1")
    public CommonResult<FlightRestrictionZoneRespVO> getFlightRestrictionZoneById(@PathVariable("id") Long id) {
        FlightRestrictionZoneRespVO respVO = flightRestrictionZoneService.getFlightRestrictionZoneById(id);
        return success(respVO);
    }

    @PostMapping("/create")
    @Operation(summary = "创建限飞区")
    public CommonResult<Long> createFlightRestrictionZone(@Valid @RequestBody FlightRestrictionZoneSaveReqVO saveReqVO) {
        Long id = flightRestrictionZoneService.createFlightRestrictionZone(saveReqVO);
        return success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "修改限飞区")
    public CommonResult<Boolean> updateFlightRestrictionZone(@Valid @RequestBody FlightRestrictionZoneUpdateReqVO updateReqVO) {
        flightRestrictionZoneService.updateFlightRestrictionZone(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除限飞区")
    @Parameter(name = "id", description = "限飞区ID", required = true, example = "1")
    public CommonResult<Boolean> deleteFlightRestrictionZone(@PathVariable("id") Long id) {
        flightRestrictionZoneService.deleteFlightRestrictionZone(id);
        return success(true);
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询当前租户启用的限飞区列表")
    public CommonResult<List<FlightRestrictionZoneRespVO>> getEnabledFlightRestrictionZones() {
        List<FlightRestrictionZoneRespVO> list =
                flightRestrictionZoneService.getEnabledFlightRestrictionZones();
        return success(list);
    }

    @PostMapping("/status")
    @Operation(summary = "修改限飞区状态", description = "启用或禁用指定的限飞区")
    public CommonResult<Boolean> updateFlightRestrictionZoneStatus(
            @Valid @RequestBody FlightRestrictionZoneStatusReqVO statusReqVO) {
        flightRestrictionZoneService.updateFlightRestrictionZoneStatus(statusReqVO);
        return success(true);
    }
}
