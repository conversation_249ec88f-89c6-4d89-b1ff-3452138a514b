package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 接警自动下发机场的websocket消息
 * <AUTHOR>
 * @Date 2025/3/21 14:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AutoExecuteAlarmVO {

    private Long jobId;

    private String name;

    private String deviceSn;

    private Integer capacityPercent;

    private String deviceName;

    private String childSn;

    private Integer type;

    private Integer distance;

    private String reason;
}