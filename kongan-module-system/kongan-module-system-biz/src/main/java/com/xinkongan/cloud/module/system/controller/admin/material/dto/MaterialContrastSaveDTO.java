package com.xinkongan.cloud.module.system.controller.admin.material.dto;

import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MaterialContrastSaveDTO {

    @NotNull(message = "原素材id不能为空")
    @Schema(description = "原始素材主键id")
    private Long orgId;

    @NotNull(message = "对比素材主键id")
    @Schema(description = "对比素材主键id")
    private Long contrastId;

    @NotBlank(message = "对比名称不能为空")
    @Schema(description = "对比名称")
    private String contrastName;

    @NotNull(message = "缩略图文件信息不能为空")
    @Schema(description = "缩略图文件信息")
    private FileSaveInfoDTO thumbnailFileInfo;
}
