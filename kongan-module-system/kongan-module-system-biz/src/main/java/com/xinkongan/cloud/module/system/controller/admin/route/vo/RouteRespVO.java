package com.xinkongan.cloud.module.system.controller.admin.route.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinkongan.cloud.framework.datapermission.core.plugins.ShareResultVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class RouteRespVO extends ShareResultVO {

    @Schema(description = "航线id", example = "xx巡检航线")
    private Long id;

    @Schema(description = "航线名称", example = "xx巡检航线")
    private String routeName;

    @Schema(description = "航线类型", example = "1")
    private Integer routeType;

    @Schema(description = "航点个数", example = "10")
    private Integer wayPointCount;

    @Schema(description = "预计飞行里程，单位 千米（km）", example = "0.88")
    private Float distance;

    @Schema(description = "预计飞行时间，单位 秒（s）", example = "500")
    private Integer duration;

    @Schema(description = "配置的算法个数", example = "12")
    private Integer algorithmCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2024-12-31 14:50:00")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间", example = "2024-12-31 14:50:00")
    private Date updateTime;

    @Schema(description = "创建人id", example = "11")
    private String creator;

    @Schema(description = "创建人姓名", example = "admin")
    private String createName;

    @Schema(description = "创建人id", example = "11")
    private String updater;

    @Schema(description = "航线锁定状态", example = "1：锁定，0：解锁")
    private Integer lockFlag;

    @Schema(description = "航线文件的url", example = "xxx")
    private String routeUrl;

    @Schema(description = "航线复制次数", example = "0")
    private Integer copyCount;

    @Schema(description = "所属组织id", example = "xxx")
    private Long deptId;

    @Schema(description = "所属组织名称", example = "xxx")
    private String deptName;

    @Schema(description = "机场SN", example = "xxx")
    private String dockSn;

    @Schema(description = "无人机SN", example = "xxx")
    private String droneSn;
}
