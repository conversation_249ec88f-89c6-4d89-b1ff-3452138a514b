package com.xinkongan.cloud.module.system.service.tenant;

import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.DeviceNumberVO;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.TenantPackageVO;
import com.xinkongan.cloud.module.system.dal.dataobject.tenant.TenantDO;
import com.xinkongan.cloud.module.system.framework.file.core.client.s3.S3FileClientConfig;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.module.system.util.file.AliOssUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.TENANT_NOT_EXISTS;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Slf4j
@Service
public class TenantPackageServiceImpl implements TenantPackageService {

    @Resource
    private TenantService tenantService;

    @Resource
    private DockDeviceService dockDeviceService;

    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private FileService fileService;

    @Value("${tenant-package.storage-cache-expired}")
    private Long storageCacheExpired;

    @Override
    public TenantPackageVO getPackageInfo() {
        // 查询租户信息
        Long tenantId = TenantContextHolder.getTenantId();
        TenantDO tenant = tenantService.getTenant(tenantId);
        if (tenant == null) {
            throw ServiceExceptionUtil.exception(TENANT_NOT_EXISTS);
        }
        // 查询存储占用情况
        FileStorageVO fileStorageVO = getUseStorageCount(tenant);
        // 查询设备数量
        DeviceNumberVO deviceNumberVO = getDeviceNumber(tenant);
        // 判断到期状态
        boolean before = tenant.getExpireTime().minusDays(7).isBefore(LocalDateTime.now());
        return TenantPackageVO.builder()
                .deviceNumberVO(deviceNumberVO)
                .fileStorageVO(fileStorageVO)
                .expireTime(tenant.getExpireTime())
                .expireStatus(before)
                .build();
    }

    @Override
    public FileStorageVO getUseStorageCount(Long tenantId) {
        TenantDO tenant = tenantService.getTenant(tenantId);
        return getUseStorageCount(tenant);
    }

    @Override
    public FileStorageVO getUseStorageCount(TenantDO tenantDO) {
        Long tenantId = tenantDO.getId();
        FileStorageVO fileStorageVO = FileStorageVO.builder()
                .totalStorage(tenantDO.getStorageSize() / 1024.0 / 1024.0 / 1024.0)
                .build();
        log.info("查询当前租户: {} 存储占用情况！", tenantId);
        String key = String.format(RedisKeyConstants.TENANT_USE_STORAGE_COUNT, tenantId);
        Double useStorageCount = redisCacheService.get(key, Double.class);
        if (useStorageCount != null) {
            log.info("从缓存中获取到当前租户: {} 存储占用情况为: {}GB", tenantId, useStorageCount);
        } else {
            S3FileClientConfig fileClientConfig = (S3FileClientConfig) fileService.getFileClientConfig();
            useStorageCount = AliOssUtils.tenantStorageCount(fileClientConfig, tenantId);
            redisCacheService.put(key, useStorageCount, storageCacheExpired);
        }
        fileStorageVO.setUsedStorage(useStorageCount);
        return fileStorageVO;
    }

    @Override
    public Boolean isStorageFull(Long tenantId) {
        TenantDO tenant = tenantService.getTenant(tenantId);
        FileStorageVO fileStorageVO = getUseStorageCount(tenant);
        return fileStorageVO.isStorageFull();
    }

    @Override
    public Boolean isDeviceFull(Long tenantId) {
        TenantDO tenant = tenantService.getTenant(tenantId);
        DeviceNumberVO deviceNumber = getDeviceNumber(tenant);
        return deviceNumber.getCurrentDeviceNumber() >= deviceNumber.getMaxDeviceNumber();
    }

    @Override
    public DeviceNumberVO getDeviceNumber(TenantDO tenantDO) {
        Integer tenantDockCount = dockDeviceService.getTenantDockCount();
        return DeviceNumberVO.builder()
                .currentDeviceNumber(tenantDockCount)
                .maxDeviceNumber(tenantDO.getDeviceCount())
                .build();
    }
}
