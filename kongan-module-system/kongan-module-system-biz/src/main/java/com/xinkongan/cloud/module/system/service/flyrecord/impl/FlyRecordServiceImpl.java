package com.xinkongan.cloud.module.system.service.flyrecord.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.map.GeoLocationUtil;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.datapermission.core.plugins.SharePluginParam;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.mybatis.core.util.MyBatisUtils;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.algorithm.api.AlgorithmApi;
import com.xinkongan.cloud.module.algorithm.api.AlgorithmFlowApi;
import com.xinkongan.cloud.module.algorithm.api.DroneMessageApi;
import com.xinkongan.cloud.module.algorithm.api.exception.ExceptionApi;
import com.xinkongan.cloud.module.algorithm.dto.AlgorithmIdNameVO;
import com.xinkongan.cloud.module.algorithm.dto.ExceptionDTO;
import com.xinkongan.cloud.module.algorithm.dto.FlowSegmentCountDTO;
import com.xinkongan.cloud.module.algorithm.dto.LandOnMessageDTO;
import com.xinkongan.cloud.module.system.api.dock.dto.flyrecord.FlyRecordOsdData;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.DroneFlyPoint;
import com.xinkongan.cloud.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.dto.FlyRecordSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.dto.HistogramSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.*;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteBaseRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.file.FileDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobDO;
import com.xinkongan.cloud.module.system.dal.mysql.flyrecord.FlyRecordMapper;
import com.xinkongan.cloud.module.system.dto.ResourceShareDTO;
import com.xinkongan.cloud.module.system.enums.file.S3FileDirPrefixEnum;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileSourceEnum;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileTypeEnum;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;
import com.xinkongan.cloud.module.system.enums.task.TaskSceneTypeEnum;
import com.xinkongan.cloud.module.system.service.alarm.IAlarmService;
import com.xinkongan.cloud.module.system.service.bind.IDataBindService;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordFileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import com.xinkongan.cloud.module.system.service.permission.PermissionService;
import com.xinkongan.cloud.module.system.service.route.IRouteService;
import com.xinkongan.cloud.module.system.service.share.IShareService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.module.system.vo.FileUploadVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.date.TimeUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.powerjob.worker.log.OmsLogger;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xinkongan.cloud.framework.common.exception.enums.GlobalErrorCodeConstants.ID_NOT_EXISTS;
import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants.DOCK_SN_DRONE_POINT;
import static com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants.DOCK_SN_FLY_RECORD_OSD;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.FLY_RECORD_NOT_FOUND;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.FLY_RECORD_PARAM_ERROR;

/**
 * @Description 飞行记录ServiceImpl
 * <AUTHOR>
 * @Date 2025/2/10 14:19
 */
@Slf4j
@Service
public class FlyRecordServiceImpl implements IFlyRecordService {

    @Resource
    private FlyRecordMapper flyRecordMapper;// 飞行记录Mapper
    @Resource
    private IRedisCacheService redisCacheService;// 缓存Service
    @Resource
    private FileService fileService;// 文件Service
    @Resource
    private IRouteService routeService;// 航线Service
    @Resource
    private IJobService jobService;// 任务Service
    @Resource
    private IFlyRecordFileService flyRecordFileService;// 飞行记录文件Service
    @Resource
    private IShareService shareService;// 分享Service
    @Resource
    private PermissionService permissionService;// 权限Service
    @Resource
    private DockDeviceService dockDeviceService;// 机场设备Service
    @Resource
    private IDataBindService dataBindService;
    @Resource
    private IAlarmService alarmService;
    @Resource
    private DroneMessageApi droneMessageApi;
    @Resource
    private ExceptionApi exceptionApi;
    @Resource
    private AlgorithmApi algorithmApi;
    @Resource
    private AlgorithmFlowApi algorithmFlowApi;


    /**
     * 保存飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/10 14:18
     **/
    @Override
    public Long save(FlyRecordDO flyRecordDO) {
        if (flyRecordDO == null) {
            throw exception(FLY_RECORD_PARAM_ERROR, "保存飞行记录参数不能为空");
        }
        flyRecordMapper.insert(flyRecordDO);
        log.info("保存飞行记录 flyRecordDO={}", JSONUtil.toJsonStr(flyRecordDO));
        return flyRecordDO.getId();
    }

    @Override
    public Boolean updateById(FlyRecordDO flyRecordDO) {
        log.info("修改飞行记录 flyRecordDO={}", JSONUtil.toJsonStr(flyRecordDO));
        if (flyRecordDO == null || flyRecordDO.getId() == null) {
            throw exception(FLY_RECORD_PARAM_ERROR, "修改飞行记录参数错误");
        }
        int i = flyRecordMapper.updateById(flyRecordDO);
        return i > 0;
    }

    /**
     * 结束机场飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/11 10:17
     **/
    @Override
    public void endDockFlyRecord(String dockSn, Long flyRecordId) {
        // 查询飞行记录
        FlyRecordDO flyRecordDO = flyRecordMapper.selectById(flyRecordId);
        if (flyRecordDO == null) {
            log.info("结束飞行记录失败 id：{}", flyRecordId);
            return;
        }
        Set<String> hashKeys = redisCacheService.hashKeys(String.format(DOCK_SN_FLY_RECORD_OSD, dockSn, flyRecordId), String.class);
        if (hashKeys != null && !hashKeys.isEmpty()) {

            // 对timestamps进行排序，最新的飞行详情点的时间戳放在前面
            List<String> timestamps = new ArrayList<>(hashKeys);
            timestamps.sort((o1, o2) -> Long.valueOf(o2).compareTo(Long.valueOf(o1)));

            // 最新的无人机osd
            String droneFirstOsdStr = null;

            // 计算飞行里程的临时经纬度变量
            Double latA = null;
            Double lonA = null;
            Double heightA = null;

            // 飞行里程
            double flightMileage = 0F;

            // 所有的osd
            List<FlyRecordOsdData> list = new ArrayList<>();
            for (String timestamp : timestamps) {
                FlyRecordOsdData flyRecordOsdData = redisCacheService.hashGet(String.format(DOCK_SN_FLY_RECORD_OSD, dockSn, flyRecordId), timestamp, FlyRecordOsdData.class);
                if (flyRecordOsdData != null && flyRecordOsdData.getDroneOsd() != null && droneFirstOsdStr == null) {
                    // 记录最新的无人机详情点后续更新飞行记录最后一个坐标用
                    droneFirstOsdStr = flyRecordOsdData.getDroneOsd();
                    JSON parse = JSONUtil.parse(droneFirstOsdStr);

                    // 从最新的坐标点往起飞坐标点计算飞行里程
                    latA = ((BigDecimal) parse.getByPath("latitude")).doubleValue();
                    lonA = ((BigDecimal) parse.getByPath("longitude")).doubleValue();
                    heightA = ((BigDecimal) parse.getByPath("height")).doubleValue();
                } else if (flyRecordOsdData != null && flyRecordOsdData.getDroneOsd() != null) {
                    // 获取当前点坐标
                    String droneOsd = flyRecordOsdData.getDroneOsd();
                    JSON parse = JSONUtil.parse(droneOsd);
                    Double currentLat = ((BigDecimal) parse.getByPath("latitude")).doubleValue();
                    Double currentLon = ((BigDecimal) parse.getByPath("longitude")).doubleValue();
                    double currentHeight = ((BigDecimal) parse.getByPath("height")).doubleValue();
                    // 计算水平距离
                    double width = GeoLocationUtil.calculateDistance(latA, lonA, currentLat, currentLon);
                    // 计算飞行高度
                    double height = Math.abs(currentHeight - heightA);
                    if (width == 0 || height == 0) {
                        // 水平或垂直飞 直接取线段距离
                        flightMileage += width == 0 ? height : width;
                    } else {
                        // 计算斜边长度
                        double hypotenuse = Math.sqrt(width * width + height * height);
                        // 计算飞行里程
                        flightMileage += hypotenuse;
                    }
                    // 更新下次计算的坐标点为当前坐标
                    latA = currentLat;
                    lonA = currentLon;
                    heightA = currentHeight;
                }
                list.add(flyRecordOsdData);
            }

            // 将实时信息转成大json
            String jsonStr = JSONUtil.toJsonStr(list);
            // 释放大列表，方便垃圾回收
            list.clear();
            // 将结果保存成json文件到oss中
            FileUploadVO fileUploadVO = saveToJsonFileAndUploadToOss(flyRecordId, jsonStr);
            log.info("上传飞行记录oss数据成功 flyRecordOsd={}", JSONUtil.toJsonStr(fileUploadVO));
            jsonStr = null;// 释放jsonStr，方便垃圾回收
            // 计算飞行时间
            Date takeOffTime = new Date(Long.parseLong(timestamps.get(timestamps.size() - 1)));
            Date landTime = new Date(Long.parseLong(timestamps.get(0)));
            long millisBetween = DateUtil.between(takeOffTime, landTime, DateUnit.SECOND);

            // 结束飞行记录
            FlyRecordDO flyRecordUpdateDO = FlyRecordDO.builder()
                    .id(flyRecordId)
                    .flightMileage((float) flightMileage)
                    .flightDuration((float) millisBetween)
                    .endFlag(YesNoEnum.YES.getCode())
                    .takeOffTime(DateUtil.toLocalDateTime(takeOffTime))
                    .landTime(DateUtil.toLocalDateTime(landTime))
                    .fileId(fileUploadVO.getFileId())
                    .osdUrl(fileUploadVO.getUrl())
                    .build();

            // 保存最终坐标位置
            if (droneFirstOsdStr != null) {
                JSON parse = JSONUtil.parse(droneFirstOsdStr);
                Double latitude = ((BigDecimal) parse.getByPath("latitude")).doubleValue();
                Double longitude = ((BigDecimal) parse.getByPath("longitude")).doubleValue();
                flyRecordUpdateDO.setLastLat(latitude);
                flyRecordUpdateDO.setLastLon(longitude);
            }
            this.updateById(flyRecordUpdateDO);
            log.info("结束飞行记录成功,flyRecordDO:{}", JSONUtil.toJsonStr(flyRecordUpdateDO));

            LandOnMessageDTO landOnMessageInfo = LandOnMessageDTO
                    .builder()
                    .tenantId(flyRecordDO.getTenantId())
                    .flyId(flyRecordDO.getFlyId())
                    .flyRecordId(flyRecordId)
                    .dockSn(flyRecordDO.getDockSn())
                    .droneSn(flyRecordDO.getDroneSn())
                    .build();
            droneMessageApi.onLandOn(landOnMessageInfo);
        }
    }

    @Override
    public FlyRecordDO getByFlyId(Long flyId) {
        if (flyId == null) {
            throw exception(ID_NOT_EXISTS);
        }
        FlyRecordDO flyRecordDO = flyRecordMapper.selectOne(FlyRecordDO::getFlyId, flyId);
        if (flyRecordDO == null) {
            throw exception(FLY_RECORD_NOT_FOUND);
        }
        return flyRecordDO;
    }


    /**
     * 飞行记录分页
     *
     * <AUTHOR>
     * @date 2025/2/13 11:20
     **/
    @Override
    public PageResult<FlyRecordPageRespVO> page(FlyRecordPageReqVO reqVO) {
        log.info("[计划管理] 分页查询 reqVO:{}", JSONUtil.toJsonStr(reqVO));
        // MyBatis Plus 查询
        IPage<FlyRecordPageRespVO> mpPage = MyBatisUtils.buildPage(reqVO);
        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.FLY_RECORD_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.FLY_RECORD_RESOURCE.getResourceType())
                .build();
        IPage<FlyRecordPageRespVO> pageRes = flyRecordMapper.selectPageWithShare(mpPage, sharePluginParam, reqVO);
        if (pageRes.getRecords().isEmpty()) {
            return PageResult.empty();
        }
        // 当前账号的数据权限
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(SecurityFrameworkUtils.getLoginUserId());
        Set<Long> dataPermissionDeptIds = deptDataPermission.getDeptIds();
        // 设置是否是共享的标志
        for (FlyRecordPageRespVO record : pageRes.getRecords()) {
            if (dataPermissionDeptIds.contains(record.getDeptId())) {
                record.setShareFlag(YesNoEnum.NO.getCode());
            } else {
                record.setShareFlag(YesNoEnum.YES.getCode());
            }
        }
        return new PageResult<>(pageRes.getRecords(), pageRes.getTotal());
    }

    /**
     * 飞行记录详情
     * 注意：仅有登录信息的接口可以使用本方法
     *
     * <AUTHOR>
     * @date 2025/2/13 14:52
     **/
    @Override
    @DataPermission(enable = false)
    public FlyRecordDetailRespVO detailById(Long id) {
        FlyRecordDetailRespVO detailById = flyRecordMapper.detailById(id);
        log.info("飞行记录详情 id:{} detailById:{}", id, JSONUtil.toJsonStr(detailById));
        if (detailById == null) {
            throw exception(FLY_RECORD_NOT_FOUND);
        }
        // 获取当前账号的角色数据权限范围
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(SecurityFrameworkUtils.getLoginUserId());
        Set<Long> dataPermissionDeptIds = deptDataPermission.getDeptIds();
        String dockSn = detailById.getDockSn();
        // 如果当前账号没有机场的权限，且机场没有共享 就去除机场信息
        if (dockSn != null) {
            Boolean hasDockPermission = dockDeviceService.hasDockPermission(dockSn, SecurityFrameworkUtils.getLoginUser().getTenantId(), dataPermissionDeptIds);
            if (!hasDockPermission) {
                detailById.setDockSn(null);
                detailById.setDockName(null);
            }
        }

        // 是否分享
        boolean share = shareService.checkResourceShare(id, ResourceShareTypeEnum.FLY_RECORD_RESOURCE.getResourceType());
        detailById.setShareFlag(share ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        // 当前账号无权限或者权限不包含飞行记录且飞行记录未分享给当前组织
        if (dataPermissionDeptIds.isEmpty() || !dataPermissionDeptIds.contains(detailById.getDeptId()) && !share) {
            return null;
        }

        // 当前飞行记录所属组织是否能看
        if (!dataPermissionDeptIds.contains(detailById.getDeptId())) {
            detailById.setDeptId(null);
            detailById.setDeptName(null);
        }

        // 补充航线信息
        if (detailById.getRouteId() != null) {
            RouteBaseRespVO routeBaseById = routeService.getRouteBaseById(detailById.getRouteId());
            log.info("飞行记录详情 routeBaseById:{}", JSONUtil.toJsonStr(routeBaseById));
            // 航线存在且当前账号可查看
            if (routeBaseById != null && dataPermissionDeptIds.contains(routeBaseById.getDeptId()) ||
                    (routeBaseById != null && YesNoEnum.YES.getCode().equals(routeBaseById.getShareFlag()))) {
                detailById.setRouteInfo(FlyRecordRouteInfo.builder()
                        .id(detailById.getRouteId())
                        .name(routeBaseById.getRouteName()).build());
            } else {
                detailById.setRouteId(null);
            }
        }

        // 补充任务信息
        if (detailById.getJobId() != null) {
            JobDO byId = jobService.getById(detailById.getJobId());
            log.info("飞行记录详情 byId:{}", JSONUtil.toJsonStr(byId));
            if (byId != null && dataPermissionDeptIds.contains(byId.getDeptId())) {
                detailById.setJobInfo(FlyRecordJobInfo.builder()
                        .id(detailById.getJobId()).scene(byId.getScene()).name(byId.getName()).build());
            } else {
                detailById.setJobId(null);
            }
        }

        // 补充照片、视频、异常数量
        List<FlyRecordFileDO> fileDOS = flyRecordFileService.listByFlyRecordId(id);
        Integer picCount = 0;
        Integer videoCount = 0;
        Integer exceptionCount = 0;
        for (FlyRecordFileDO fileDO : fileDOS) {
            if (FileTypeEnum.IMAGE.equals(FileTypeEnum.find(fileDO.getFileType())) && fileDO.getExceptionId() == null) {
                // 非异常图片
                picCount++;
            } else if (FileTypeEnum.VIDEO.equals(FileTypeEnum.find(fileDO.getFileType()))) {
                // 视频
                videoCount++;
            }
        }

        // 合并普通图片和异常图片
        List<FlyRecordFileAndExceptionVO> fileAndExceptionVOS = new ArrayList<>();
        // 普通图片
        fileDOS.stream().filter(fileDO -> fileDO.getExceptionId() == null).forEach(fileDO -> {
            FlyRecordFileAndExceptionVO flyRecordFileAndExceptionVO = BeanUtils.toBean(fileDO, FlyRecordFileAndExceptionVO.class);
            fileAndExceptionVOS.add(flyRecordFileAndExceptionVO);
        });

        // 查询异常
        List<ExceptionDTO> exceptionDTOList = exceptionApi.getExceptionListByFlyId(detailById.getFlyId()).getCheckedData();
        if (CollUtil.isNotEmpty(exceptionDTOList)) {
            // 查询文件列表
            Map<Long, FileDO> fileMap = fileService.mapByFileIds(exceptionDTOList.stream().map(ExceptionDTO::getFileId).collect(Collectors.toList()));
            exceptionDTOList.forEach(exceptionDTO -> {
                FileDO fileDO = fileMap.get(exceptionDTO.getFileId());
                FlyRecordFileAndExceptionVO fileAndExceptionVO = BeanUtils.toBean(exceptionDTO, FlyRecordFileAndExceptionVO.class);
                fileAndExceptionVO.setExceptionId(exceptionDTO.getId());
                fileAndExceptionVO.setFileType(FileTypeEnum.IMAGE.getCode());
                fileAndExceptionVO.setFlyRecordId(id);
                fileAndExceptionVO.setFileSource(FileSourceEnum.ALGORITHM.getCode());
                if (fileDO != null) {
                    fileAndExceptionVO.setFileSize(fileDO.getSize());
                }
                fileAndExceptionVOS.add(fileAndExceptionVO);
            });
            exceptionCount = exceptionDTOList.size();
        }

        // 补充使用算法历史列表
        if (StrUtil.isNotBlank(detailById.getAlgorithmIds())) {
            try {
                // 使用TypeReference明确泛型类型
                List<Long> algorithmIds = JSONUtil.toBean(detailById.getAlgorithmIds(),
                        new cn.hutool.core.lang.TypeReference<>() {
                        }, false);
                List<AlgorithmIdNameVO> algorithmIdNameVOS = algorithmApi.getAlgorithmList(algorithmIds).getCheckedData();
                detailById.setAlgorithmHistoryList(algorithmIdNameVOS);
            } catch (Exception e) {
                log.error("获取算法列表异常", e);
            }
        }

        detailById.setExceptionCount(exceptionCount);
        detailById.setPicCount(picCount);
        detailById.setVideoCount(videoCount);
        detailById.setFileList(fileAndExceptionVOS);

        // 补充绑定任务列表
        List<BindJobVO> bindJobVOS = dataBindService.getBindJobByFlyRecordId(id);
        detailById.setBindJobList(bindJobVOS);
        // 是否有人流量车流量的分段累计折线图
        List<FlowSegmentCountDTO> flowSegmentCountDTOS = algorithmFlowApi.listCountByFlyId(detailById.getFlyId()).getCheckedData();

        boolean flowPointNotEmpty = CollUtil.isNotEmpty(flowSegmentCountDTOS);
        if (flowPointNotEmpty) {
            // 补充分段累计折线图数据
            try {
                addFlowCountList(detailById, flowSegmentCountDTOS);
            } catch (Exception e) {
                log.error("获取分段累计折线图数据异常", e);
            }
        }
        return detailById;
    }

    /**
     * 补充分段累计折线图数据
     **/
    private void addFlowCountList(FlyRecordDetailRespVO flyRecordDetailRespVO, List<FlowSegmentCountDTO> flowSegmentCountDTOS) {
        // 人流量分段累计数据
        List<FlowSegmentCountDTO> personFlowCountList = new ArrayList<>();
        List<FlowSegmentCountDTO> carFlowCountList = new ArrayList<>();

        for (FlowSegmentCountDTO flowPointDTO : flowSegmentCountDTOS) {
            String oneKey = flowPointDTO.getOneKey();
            if (oneKey.contains("vehicle-flow")) {
                // 车流量算法分段累计数据
                carFlowCountList.add(flowPointDTO);
            } else if (oneKey.contains("people-flow")) {
                // 人流量算法分段累计数据
                personFlowCountList.add(flowPointDTO);
            }
        }

        // 按时间戳排序
        personFlowCountList.sort(Comparator.comparing(FlowSegmentCountDTO::getTimestamp));
        carFlowCountList.sort(Comparator.comparing(FlowSegmentCountDTO::getTimestamp));

        if (CollUtil.isNotEmpty(personFlowCountList)) {
            flyRecordDetailRespVO.setHasPersonFlowCount(YesNoEnum.YES.getCode());
            flyRecordDetailRespVO.setPersonFlowCountList(personFlowCountList);
        }
        if (CollUtil.isNotEmpty(carFlowCountList)) {
            flyRecordDetailRespVO.setHasCarFlowCount(YesNoEnum.YES.getCode());
            flyRecordDetailRespVO.setCarFlowCountList(carFlowCountList);
        }
    }

    /**
     * 飞行记录分享
     *
     * <AUTHOR>
     * @date 2025/2/13 16:00
     **/
    @Override
    public void share(FlyRecordShareVO flyRecordShareVO) {
        FlyRecordDO flyRecordDO = flyRecordMapper.selectById(flyRecordShareVO.getFlyRecordId());
        if (flyRecordDO == null) {
            throw new ServiceException(FLY_RECORD_NOT_FOUND);
        }

        ResourceShareDTO resourceShareParam = ResourceShareDTO
                .builder()
                .resourceId(flyRecordShareVO.getFlyRecordId())
                .resourceType(ResourceShareTypeEnum.FLY_RECORD_RESOURCE.getResourceType())
                .shareDeptIds(flyRecordShareVO.getDeptIds())
                .oriDeptId(flyRecordDO.getDeptId())
                .build();
        shareService.resourceShareDeptIds(resourceShareParam);
    }

    /**
     * 查询飞行记录分享的组织
     *
     * <AUTHOR>
     * @date 2025/2/19 9:30
     **/
    @Override
    public FlyRecordShareVO getFlyRecordShareDept(Long flyRecordId) {
        List<Long> deptIds = shareService.getResourceSharesDept(flyRecordId);
        return FlyRecordShareVO.builder().deptIds(deptIds).flyRecordId(flyRecordId).build();
    }

    /**
     * 飞行记录实时轨迹
     *
     * <AUTHOR>
     * @date 2025/2/13 18:52
     **/
    @Override
    public List<DroneFlyPoint> getDroneFlyPoints(Long flyRecordId) {
        Set<String> hashKeys = redisCacheService.hashKeys(String.format(DOCK_SN_DRONE_POINT, flyRecordId), String.class);
        if (hashKeys != null && !hashKeys.isEmpty()) {
            // 对timestamps进行排序，最新的飞行详情点的时间戳放在最后
            List<String> timestamps = new ArrayList<>(hashKeys);
            timestamps.sort(Comparator.comparing(Long::valueOf));
            List<DroneFlyPoint> droneFlyPoints = new ArrayList<>();
            for (String hashKey : timestamps) {
                DroneFlyPoint droneFlyPoint = redisCacheService.hashGet(String.format(DOCK_SN_DRONE_POINT, flyRecordId), hashKey, DroneFlyPoint.class);
                if (droneFlyPoint != null) {
                    droneFlyPoints.add(droneFlyPoint);
                }
            }
            return droneFlyPoints;
        }
        return List.of();
    }

    @Override
    @DataPermission(enable = false)
    public FlyRecordDO getById(Long flyRecordId) {
        FlyRecordDO flyRecordDO = flyRecordMapper.selectById(flyRecordId);
        if (flyRecordDO == null) {
            throw new ServiceException(FLY_RECORD_NOT_FOUND);
        }
        // 获取当前账号的角色数据权限范围
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(SecurityFrameworkUtils.getLoginUserId());
        Set<Long> dataPermissionDeptIds = deptDataPermission.getDeptIds();
        // 是否分享
        boolean share = shareService.checkResourceShare(flyRecordId, ResourceShareTypeEnum.FLY_RECORD_RESOURCE.getResourceType());
        // 当前账号无权限或者权限不包含飞行记录且飞行记录未分享给当前组织
        if (dataPermissionDeptIds.isEmpty() || !dataPermissionDeptIds.contains(flyRecordDO.getDeptId()) && !share) {
            throw new ServiceException(FLY_RECORD_NOT_FOUND);
        }
        return flyRecordDO;
    }

    /**
     * 更新飞行记录重要性
     *
     * <AUTHOR>
     * @date 2025/2/14 11:04
     **/
    @Override
    public Boolean updateImportant(UpdateImportantReqVO reqVO) {
        FlyRecordDO flyRecordDO = BeanUtils.toBean(reqVO, FlyRecordDO.class);
        return this.updateById(flyRecordDO);
    }


    /**
     * 删除飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/17 11:29
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteById(Long id) {
        FlyRecordDO flyRecordDO = flyRecordMapper.selectById(id);
        if (flyRecordDO == null) {
            throw exception(FLY_RECORD_NOT_FOUND);
        }
        // 删除飞行记录
        flyRecordMapper.deleteById(id);
        // 删除飞行记录文件
        flyRecordFileService.deleteByFlyRecord(id);
        // 删除关联数据
        dataBindService.deleteDataBindByFlyRecordIds(List.of(id));
        // 删除异常
        exceptionApi.deleteByFlyRecordId(id).getCheckedData();
        // 删除osd文件
        fileService.deleteBatchIds(List.of(flyRecordDO.getFileId()));
        return true;
    }

    /**
     * 删除不重要的飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/17 10:40
     **/
    @Transactional(rollbackFor = Exception.class)
    @DataPermission(enable = false)
    @TenantIgnore
    @Override
    public void deleteUnImportantFlyRecord(Long deptId, LocalDateTime endTime, OmsLogger omsLogger) {
        omsLogger.info("删除不重要的飞行记录 deptId:{} endTime:{}", deptId, endTime);
        // 查询时间段内不重要的飞行记录
        List<FlyRecordDO> flyRecordList = flyRecordMapper.selectList(Wrappers.<FlyRecordDO>lambdaQuery()
                .eq(FlyRecordDO::getDeptId, deptId)
                .eq(FlyRecordDO::getImportantLevel, 0)
                .le(FlyRecordDO::getCreateTime, endTime));// 删小于等于删除期限就除
        // 删除飞行记录
        for (FlyRecordDO flyRecordDO : flyRecordList) {
            omsLogger.info("删除飞行记录:{}", JSONUtil.toJsonStr(flyRecordDO));
            deleteById(flyRecordDO.getId());
        }
    }

    @DataPermission(enable = false)
    @Override
    public FlyRecordDO getByIdNoPermission(Long id) {
        return flyRecordMapper.selectById(id);
    }

    /**
     * 飞行记录统计
     *
     * <AUTHOR>
     * @date 2025/2/14 11:04
     **/
    @Override
    public FlyRecordCountRespVO getFlyRecordCount() {
        return flyRecordMapper.getFlyRecordCount();
    }

    @Override
    public List<FlyRecordCountByTimeRespVO> getFlyRecordCountByTime(FlyRecordCountByTimeReqVO reqVO) {
        // 本周/本月 都是统计开始到结束时间每一天的数据
        if (reqVO.getTimeType() == 0 || reqVO.getTimeType() == 1) {
            List<FlyRecordCountByTimeRespVO> flyRecordCountByDays = flyRecordMapper.getFlyRecordCountByDays(reqVO.getStartTime(), reqVO.getEndTime());
            if (CollUtil.isNotEmpty(flyRecordCountByDays)) {
                // 根据开始时间结束时间生成每一天的完整日期序列
                List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.date(reqVO.getStartTime()), DateUtil.date(reqVO.getEndTime()), DateField.DAY_OF_MONTH);
                List<LocalDateTime> dateRange = dateTimes.stream().map(DateTime::toLocalDateTime).toList();
                // 创建日期映射表
                Map<String, FlyRecordCountByTimeRespVO> dateMap = flyRecordCountByDays.stream()
                        .collect(Collectors.toMap(
                                vo -> TimeUtil.format(vo.getDate(), "yyyy-MM-dd"),
                                Function.identity()
                        ));

                // 补全缺失日期数据
                List<FlyRecordCountByTimeRespVO> result = new ArrayList<>();
                for (LocalDateTime date : dateRange) {
                    String key = TimeUtil.format(date, "yyyy-MM-dd");
                    FlyRecordCountByTimeRespVO vo = dateMap.get(key);
                    if (vo == null) {
                        result.add(new FlyRecordCountByTimeRespVO()
                                .setDate(date)
                                .setInspectionCount(0)
                                .setAlarmCount(0)
                                .setModelingCount(0));
                    } else {
                        result.add(vo);
                    }
                }
                return result.stream()
                        .sorted(Comparator.comparing(FlyRecordCountByTimeRespVO::getDate))
                        .collect(Collectors.toList());
            }
            return Collections.emptyList();
        }
        // 本年 按照月份查询每个月的数据
        if (reqVO.getTimeType() == 2) {
            List<FlyRecordCountByTimeRespVO> flyRecordCountByMonths = flyRecordMapper.getFlyRecordCountByMonths(reqVO.getStartTime(), reqVO.getEndTime());
            if (CollUtil.isNotEmpty(flyRecordCountByMonths)) {
                // 生成完整月份序列
                List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.date(reqVO.getStartTime()), DateUtil.date(reqVO.getEndTime()), DateField.MONTH);
                List<LocalDateTime> monthRange = dateTimes.stream().map(DateTime::toLocalDateTime).toList();
                // 创建月份映射表
                Map<String, FlyRecordCountByTimeRespVO> monthMap = flyRecordCountByMonths.stream()
                        .collect(Collectors.toMap(
                                vo -> TimeUtil.format(vo.getDate(), "yyyy-MM"),
                                Function.identity()
                        ));

                // 补全缺失月份数据
                List<FlyRecordCountByTimeRespVO> result = new ArrayList<>();
                for (LocalDateTime month : monthRange) {
                    String key = TimeUtil.format(month, "yyyy-MM");
                    FlyRecordCountByTimeRespVO vo = monthMap.get(key);
                    if (vo == null) {
                        result.add(new FlyRecordCountByTimeRespVO()
                                .setDate(month.withDayOfMonth(1))
                                .setInspectionCount(0)
                                .setAlarmCount(0)
                                .setModelingCount(0));
                    } else {
                        result.add(vo);
                    }
                }
                return result.stream()
                        .sorted(Comparator.comparing(FlyRecordCountByTimeRespVO::getDate))
                        .collect(Collectors.toList());
            }
            return Collections.emptyList();
        }
        return Collections.emptyList();
    }

    @DataPermission(enable = false)
    @Override
    public Boolean hasFlyRecordPermission(Long flyRecordId, Long tenantId, Set<Long> dataPermissionIds) {
        // 当前飞行记录是否共享
        boolean share = shareService.checkResourceShare(flyRecordId, ResourceShareTypeEnum.FLY_RECORD_RESOURCE.getResourceType(), dataPermissionIds);
        if (share) {
            return true;
        } else {
            // 非共享的查看飞行记录是否在当前组织列表中
            return flyRecordMapper.exists(Wrappers.<FlyRecordDO>lambdaQuery().eq(FlyRecordDO::getId, flyRecordId).in(FlyRecordDO::getDeptId, dataPermissionIds));
        }
    }

    @Override
    public Integer getPageNumById(Long id, Integer pageSize) {
        // 查询当前飞行记录
        FlyRecordDO flyRecordDO = getById(id);
        if (flyRecordDO == null) {
            throw new ServiceException(FLY_RECORD_NOT_FOUND);
        }
        // 当前飞行记录的起飞时间
        LocalDateTime takeOffTime = flyRecordDO.getTakeOffTime();
        // 计算时间比当前id时间更早的所有记录的总数
        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.FLY_RECORD_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.FLY_RECORD_RESOURCE.getResourceType())
                .build();
        Integer count = flyRecordMapper.selectAfterCountWithShare_mpCount(sharePluginParam, takeOffTime);
        log.info("当前飞行记录的起飞时间:{} 总记录数:{}", takeOffTime, count);
        // 根据页大小计算当前id在哪一页
        return (count + pageSize - 1) / pageSize;
    }

    @Override
    public PageResult<RelateFlyRecordPageRespVO> relateFlyRecordPage(RelateFlyRecordPageReqVO reqVO) {
        log.info("[接警管理] 关联数据-飞行记录分页查询 reqVO:{}", JSONUtil.toJsonStr(reqVO));
        // MyBatis Plus 查询
        IPage<RelateFlyRecordPageRespVO> mpPage = MyBatisUtils.buildPage(reqVO);
        IPage<RelateFlyRecordPageRespVO> page = flyRecordMapper.relateFlyRecordPage(mpPage, reqVO);
        if (page.getRecords().isEmpty()) {
            return PageResult.empty();
        }
        List<Long> ids = page.getRecords().stream().map(RelateFlyRecordPageRespVO::getId).toList();
        Map<Long, Integer> countMap = flyRecordFileService.flyRecordMaterialCountMap(ids);
        // 根据任务id列表查询接警场景
        List<Long> jobIds = page.getRecords().stream().map(RelateFlyRecordPageRespVO::getJobId).toList();
        if (CollUtil.isNotEmpty(jobIds)) {
            Map<Long, String> jobSceneMap = alarmService.getJobSceneMap(jobIds);
            // 添加飞行素材统计
            for (RelateFlyRecordPageRespVO record : page.getRecords()) {
                record.setFlyMaterialCount(countMap.get(record.getId()));
                record.setAlarmScene(jobSceneMap.get(record.getJobId()));
            }
        }

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public FlyRecordDO getByIdIgnorePermission(Long id) {
        return flyRecordMapper.selectById(id);
    }

    @Override
    public Float getTimeSumByJobId(Long jobId) {
        List<FlyRecordDO> flyRecordDOS = flyRecordMapper.selectList(Wrappers.<FlyRecordDO>lambdaQuery()
                .eq(FlyRecordDO::getJobId, jobId).select(FlyRecordDO::getFlightDuration));
        if (CollUtil.isNotEmpty(flyRecordDOS)) {
            return flyRecordDOS.stream()
                    .filter(Objects::nonNull)
                    .map(FlyRecordDO::getFlightDuration)
                    .filter(Objects::nonNull)
                    .reduce(0f, Float::sum);
        }
        return 0f;
    }

    /**
     * 上传阿里云
     *
     * <AUTHOR>
     * @date 2025/2/12 10:00
     **/
    private FileUploadVO saveToJsonFileAndUploadToOss(Long flyRecordId, String jsonContent) {
        long start = System.currentTimeMillis();
        String path = S3FileDirPrefixEnum.FLY_RECORD_OSD_FILE.getCompleteDir(flyRecordId + ".json", TenantContextHolder.getTenantId());
        // 上传文件到OSS
        FileUploadVO fileUploadVO = fileService.uploadFile(flyRecordId + ".json", path, jsonContent.getBytes());
        long end = System.currentTimeMillis();
        log.info("上传 {} osd数据耗时:[{}]ms", flyRecordId, (end - start));
        return fileUploadVO;
    }

    @Override
    public FlyRecordStatisticDataVO getStatisticDataByDeptId(Long deptId) {
        // 查询飞行里程统计
        Double totalMileage = flyRecordMapper.getFlyRecordTotalMaileByDeptId(deptId);
        // 查询飞行次数统计
        Long totalFlyCount = flyRecordMapper.selectCount(
                new LambdaQueryWrapper<FlyRecordDO>()
                        .eq(FlyRecordDO::getDeptId, deptId)
        );
        // 查询飞行时间统计
        Double totalFlyTime = flyRecordMapper.getFlyRecordTotalFlyTimeByDeptId(deptId);
        return FlyRecordStatisticDataVO.builder().totalMileage(totalMileage).totalCount(totalFlyCount).totalTime(totalFlyTime).build();
    }

    @Override
    public PageResult<FlyRecordRespVO> getFlyRecordPage(FlyRecordSearchDTO searchParams) {
        // 查询本组织数据，不用考虑分享数据
        Page<FlyRecordRespVO> page = new Page<>(searchParams.getPage(), searchParams.getOffset());
        List<FlyRecordRespVO> records = flyRecordMapper.getFlyRecordPage(page, searchParams);
        for (FlyRecordRespVO record : records) {
            Long count = flyRecordFileService.countMediaCountByJobId(record.getId());
            record.setMediaCount(count);
        }
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public List<HistogramRespVO> getHistogramData(HistogramSearchDTO histogramSearchInfo) {

        Integer type = histogramSearchInfo.getType();
        Long deptId = histogramSearchInfo.getDeptId();

        // 获取时间范围和日期格式
        LocalDateTime[] timeRange = getTimeRange(type);
        String dateFormat = getDateFormat(type);

        // 获取数据库统计结果
        List<HistogramRespVO> dbResults = flyRecordMapper.getHistogramData(deptId, dateFormat, timeRange);

        // 生成完整日期序列并填充数据
        return fillMissingDates(dbResults, timeRange, type);
    }

    private String getDateFormat(Integer type) {
        return switch (type) {
            case 1 -> "%Y-%m-%d";     // 本周
            case 2 -> "%Y-%m-%d";     // 本月
            case 3 -> "%Y-%m";        // 本年
            default -> throw new IllegalArgumentException("Invalid type: " + type);
        };
    }

    /**
     * 获取时间范围（优化后的版本）
     */
    private LocalDateTime[] getTimeRange(Integer type) {
        LocalDateTime now = LocalDateTime.now();
        return switch (type) {
            case 1 -> { // 本周
                LocalDateTime start = now.with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0);
                yield new LocalDateTime[]{start, start.plusDays(6).withHour(23).withMinute(59).withSecond(59)};
            }
            case 2 -> { // 本月
                LocalDateTime start = now.with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0);
                yield new LocalDateTime[]{start, now.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59)};
            }
            case 3 -> { // 本年
                LocalDateTime start = now.with(TemporalAdjusters.firstDayOfYear()).withHour(0).withMinute(0).withSecond(0);
                yield new LocalDateTime[]{start, now.with(TemporalAdjusters.lastDayOfYear()).withHour(23).withMinute(59).withSecond(59)};
            }
            default -> throw new IllegalArgumentException("Invalid type: " + type);
        };
    }

    /**
     * 填充缺失的日期数据
     */
    private List<HistogramRespVO> fillMissingDates(List<HistogramRespVO> dbResults, LocalDateTime[] timeRange, Integer type) {
        Map<String, HistogramRespVO> resultMap = dbResults.stream().collect(Collectors.toMap(HistogramRespVO::getTimePeriod, Function.identity()));
        List<HistogramRespVO> filledResults = new ArrayList<>();
        List<String> timeKeys = this.generateTimeKeys(timeRange, type);
        for (String key : timeKeys) {
            filledResults.add(resultMap.getOrDefault(key,
                    new HistogramRespVO()
                            .setTimePeriod(key)
                            .setAlarmCount(0L)
                            .setOtherCount(0L)
                            .setInspectCount(0L)
            ));
        }
        return filledResults;
    }

    private List<String> generateTimeKeys(LocalDateTime[] timeRange, Integer type) {
        List<String> timeKeys = new ArrayList<>();
        LocalDate startDate = timeRange[0].toLocalDate();
        LocalDate endDate = timeRange[1].toLocalDate();

        switch (type) {
            case 1, 2 -> { // 本周
                DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    timeKeys.add(date.format(dayFormatter));
                }
            }
            case 3 -> { // 本年
                DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusMonths(1)) {
                    timeKeys.add(date.format(monthFormatter));
                }
            }
            default -> throw new IllegalArgumentException("Invalid type: " + type);
        }
        return timeKeys;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DataPermission(enable = false)
    public void deleteFlyRecordByDeptId(Long deptId) {
        log.info("[删除组织下的飞行记录] 组织ID：{}", deptId);
        try {
            // 查询当前组织下的所有飞行记录
            List<FlyRecordDO> flyRecordList = flyRecordMapper.selectList(
                    new LambdaQueryWrapperX<FlyRecordDO>()
                            .eq(FlyRecordDO::getDeptId, deptId)
            );

            if (CollectionUtil.isEmpty(flyRecordList)) {
                log.info("[删除组织下的飞行记录] 组织ID：{} 下没有飞行记录数据", deptId);
                return;
            }

            // 逐个删除飞行记录（调用现有的删除方法，确保完整删除逻辑）
            for (FlyRecordDO flyRecord : flyRecordList) {
                try {
                    deleteById(flyRecord.getId());
                    log.info("[删除组织下的飞行记录] 成功删除飞行记录ID：{}", flyRecord.getId());
                } catch (Exception e) {
                    log.error("[删除组织下的飞行记录] 删除飞行记录ID：{} 失败", flyRecord.getId(), e);
                }
            }

            log.info("[删除组织下的飞行记录] 组织ID：{} 删除完成，共删除 {} 个飞行记录", deptId, flyRecordList.size());
        } catch (Exception e) {
            log.error("[删除组织下的飞行记录] 组织ID：{} 删除失败", deptId, e);
            throw e;
        }
    }
}