package com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description 机场安全范围页面机场信息
 * <AUTHOR>
 * @Date 2024/12/31 13:42
 */
@Data
@Schema(description = "机场安全范围页面机场信息")
public class CoverageDockInfo {

    @Schema(description = "机场sn")
    private String deviceSn;
    @Schema(description = "机场名称")
    private String deviceName;
    @Schema(description = "机场地址")
    private String address;
    @Schema(description = "机场纬度")
    private Double latitude;
    @Schema(description = "机场经度")
    private Double longitude;
    @Schema(description = "设备类型")
    private Integer deviceType;
}