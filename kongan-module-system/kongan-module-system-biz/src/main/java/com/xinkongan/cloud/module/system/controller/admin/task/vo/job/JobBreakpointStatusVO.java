package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 任务断点状态VO
 * 用于在任务列表中显示断点续飞相关信息
 * <AUTHOR>
 * @Date 2025/01/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务断点状态VO")
public class JobBreakpointStatusVO {

    @Schema(description = "是否有断点")
    private Boolean hasBreakpoint;

    @Schema(description = "断点ID")
    private Long breakpointId;

    @Schema(description = "断点原因")
    private Integer breakReason;

    @Schema(description = "断点状态：0-在航段上，1-在航点上")
    private Integer state;

    @Schema(description = "断点状态名称")
    private String stateName;

    @Schema(description = "飞行进度百分比")
    private BigDecimal progress;

    /**
     * 获取断点状态颜色
     */
    @Schema(description = "断点状态颜色")
    public String getStatusColor() {
        if (!hasBreakpoint) {
            return "";
        }

        // 根据断点状态返回颜色
        if (state != null) {
            switch (state) {
                case 0:
                    return "warning"; // 橙色 - 在航段上
                case 1:
                    return "primary"; // 蓝色 - 在航点上
                default:
                    return "default"; // 灰色 - 未知状态
            }
        }

        return "default";
    }
}
