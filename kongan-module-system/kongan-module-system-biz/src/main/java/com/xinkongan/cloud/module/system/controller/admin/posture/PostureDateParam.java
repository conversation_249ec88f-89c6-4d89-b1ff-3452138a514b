package com.xinkongan.cloud.module.system.controller.admin.posture;

import com.xinkongan.cloud.module.system.controller.admin.posture.dto.PostureParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Getter
@Slf4j
@Schema(description = "态势大屏 - 日期参数")
public class PostureDateParam extends PostureParam {

    @Schema(description = "查询的时间类型：1 本日，2 本周，3 本月，4 本年", example = "1")  // 修改描述
    private Integer periodType;

    @Schema(description = "开始时间-客户端不需要传参，自动根据periodType计算")
    private Date startTime;

    @Schema(description = "结束时间-客户端不需要传参，自动根据periodType计算")
    private Date endTime;

    public void setPeriodType(Integer periodType) {
        this.periodType = periodType;
        this.startTime = getStartingTime();
        this.endTime = getEndingTime();
    }

    public Date getStartingTime() {
        Calendar calendar = Calendar.getInstance();
        switch (periodType) {
            case 1:
                break; // 本日
            case 2: // 本周
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                break;
            case 3: // 本月
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                break;
            case 4: // 新增本年处理
                calendar.set(Calendar.DAY_OF_YEAR, 1);
                break;
            default:
                log.error("getStartingTime periodType is not valid");
        }
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public Date getEndingTime() {
        Calendar calendar = Calendar.getInstance();
        switch (periodType) {
            case 1: // 本日
                break;
            case 2: // 本周
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
                calendar.add(Calendar.WEEK_OF_YEAR, 1);
                break;
            case 3: // 本月
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                break;
            case 4: // 新增本年处理
                calendar.set(Calendar.MONTH, 11); // 12月
                calendar.set(Calendar.DAY_OF_MONTH, 31);
                break;
            default:
                log.error("getEndingTime periodType is not valid");
                return new Date();
        }
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

}
