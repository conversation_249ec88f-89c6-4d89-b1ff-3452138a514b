package com.xinkongan.cloud.module.system.controller.admin.flyrecord;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.*;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordContrastDO;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordContrastService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 飞行记录对比记录
 * <AUTHOR>
 * @Date 2025/2/19 11:15
 */
@Validated
@RestController
@Tag(name = "管理后台 - 飞行记录对比记录")
@RequestMapping("/system/fly-record-contrast")
public class FlyRecordContrastController {

    @Resource
    private IFlyRecordContrastService flyRecordContrastService;

    @GetMapping("/picRightList")
    @Operation(summary = "右侧附近图片列表", description = "右侧附近图片列表")
    CommonResult<List<ContrastFileVO>> picList(@Valid ContrastPicListReqVO picListReqVO) {
        List<ContrastFileVO> flyRecordFileDOS = flyRecordContrastService.radiusFiles(picListReqVO.getLongitude(), picListReqVO.getLatitude(),
                picListReqVO.getRadius(), picListReqVO.getStartTime(), picListReqVO.getEndTime());
        return success(flyRecordFileDOS);
    }

    @GetMapping("/picLeftList")
    @Operation(summary = "左侧飞行记录图片列表", description = "左侧飞行记录图片列表")
    CommonResult<List<ContrastFileVO>> picLeftList(@Valid ContrastPicLeftListReqVO reqVO) {
        List<ContrastFileVO> list = flyRecordContrastService.picLeftList(reqVO);
        return success(list);
    }

    @GetMapping("/page")
    @Operation(summary = "对比记录列表", description = "对比记录列表")
    CommonResult<PageResult<FlyRecordContrastDO>> page(@Valid ContrastPageReqVO contrastPageReqVO) {
        PageResult<FlyRecordContrastDO> page = flyRecordContrastService.page(contrastPageReqVO);
        return success(page);
    }

    @PostMapping("/save")
    @Operation(summary = "保存对比记录", description = "保存对比记录")
    CommonResult<Boolean> save(@Valid @RequestBody ContrastSaveReqVO contrastSaveReqVO) {
        Boolean save = flyRecordContrastService.save(contrastSaveReqVO);
        return success(save);
    }

}