package com.xinkongan.cloud.module.system.controller.admin.dock.vo;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description 设备日志分页
 * <AUTHOR>
 * @Date 2025/5/27 11:43
 */
@Schema(description = "设备日志分页")
@Data
public class DeviceLogPageReqVO extends PageParam {

    @Schema(description = "机场sn")
    @NotBlank(message = "机场sn不能为空")
    private String dockSn;
}