package com.xinkongan.cloud.module.system.service.route;

import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointShoutVO;
import com.xinkongan.cloud.module.system.dto.WayPointActionDTO;
import com.xinkongan.cloud.module.system.dto.WayPointShoutDTO;

import java.util.List;
import java.util.Map;

public interface IWayPointShoutService {


    List<Long> saveWayPointShoutInfo(Long routeId, Long pointId, List<WayPointShoutDTO> wayPointShouts, List<WayPointActionDTO> wayPointActions);


    void deleteWayPointShoutInfoByRouteIdAndPointId(Long routeId, Long pointId);


    Map<Long,List<WayPointShoutVO>> getWayPointShoutInfoByRouteId(Long routeId);

    List<WayPointShoutVO> getWayPointShoutInfoByPointId(Long id);
}
