package com.xinkongan.cloud.module.system.controller.admin.task;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.apply.*;
import com.xinkongan.cloud.module.system.service.task.IDockTaskApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 机场任务申请表
 * <AUTHOR>
 * @Date 2024/12/23 15:39
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 机场任务申请表")
@RequestMapping("/system/taskApply")
public class DockTaskApplyController {

    @Resource
    private IDockTaskApplyService dockTaskApplyService;

    /**
     * 查询机场任务申请
     *
     * <AUTHOR>
     * @date 2024/12/23 15:48
     **/
    @PreAuthorize("@ss.hasPermission('system:dock:apply:query')")
    @GetMapping("/getById")
    @Operation(summary = "查询机场任务申请", description = "查询机场任务申请")
    CommonResult<DockTaskApplyVO> getByDockSn(Long applyId) {
        DockTaskApplyVO dockTaskApplyVO = dockTaskApplyService.getById(applyId);
        return success(dockTaskApplyVO);
    }

    /**
     * 申请、审批记录分页
     *
     * <AUTHOR>
     * @date 2024/12/25 11:39
     **/
    @PreAuthorize("@ss.hasPermission('system:dock:apply:query')")
    @GetMapping("/page")
    @Operation(summary = "申请、审批记录分页", description = "申请、审批记录分页")
    CommonResult<PageResult<DockTaskApplyVO>> page(ApplyPageReqVO pageReqVO) {
        PageResult<DockTaskApplyVO> page = dockTaskApplyService.page(pageReqVO);
        return success(page);
    }

    /**
     * 审批前检查接口
     *
     * <AUTHOR>
     * @date 2024/12/26 13:46
     **/
    @GetMapping("/checkAgree")
    @Operation(summary = "审批前检查", description = "审批前检查")
    public CommonResult<CheckApplyRespVO> checkAgree(Long applyId) {
        CheckApplyRespVO checkAgree = dockTaskApplyService.checkAgree(applyId);
        return success(checkAgree);
    }

    /**
     * 审批通过
     * 审批状态为“已通过”，任务状态变为“待执行”
     *
     * <AUTHOR>
     * @date 2024/12/26 13:43
     **/
    @PreAuthorize("@ss.hasPermission('system:dock:apply:edit')")
    @PostMapping("/agree")
    @Operation(summary = "审批通过", description = "审批通过")
    CommonResult<Boolean> agreeApply(@RequestBody AgreeApplyReqVO reqVO) {
        Boolean agree = dockTaskApplyService.agreeApply(reqVO);
        return success(agree);
    }

    /**
     * 审批不通过
     *
     * <AUTHOR>
     * @date 2024/12/26 14:28
     **/
    @PreAuthorize("@ss.hasPermission('system:dock:apply:edit')")
    @PostMapping("/disAgree")
    @Operation(summary = "审批不通过", description = "审批不通过")
    CommonResult<Boolean> disAgreeApply(@RequestBody DisAgreeApplyReqVO reqVO) {
        Boolean disAgree = dockTaskApplyService.disAgreeApply(reqVO);
        return success(disAgree);
    }
    
    /**
     * 取消申请
     * <AUTHOR>
     * @date 2024/12/26 14:35
     **/
    @PreAuthorize("@ss.hasPermission('system:dock:apply:edit')")
    @PostMapping("/cancel")
    @Operation(summary = "取消申请", description = "取消申请")
    CommonResult<Boolean> cancelApply(@RequestBody CancelApplyReqVO reqVO) {
        Boolean cancel = dockTaskApplyService.cancelApply(reqVO);
        return success(cancel);
    }

    @GetMapping("/getPageNumById")
    @Operation(summary = "查询页码", description = "查询申请审批页码")
    CommonResult<Integer> getPageNumById(@RequestParam("id") Long id,@RequestParam("pageSize") Integer pageSize) {
        Integer pageNum = dockTaskApplyService.getPageNumById(id,pageSize);
        return success(pageNum);
    }

}