package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.DeviceLogPageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.log.LogFileParam;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.log.LogStartUploadDTO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DeviceLogDO;
import com.xinkongan.cloud.module.system.service.dock.log.IDeviceLogService;
import com.xinkongan.cloud.sdk.dock.cloudapi.log.dto.FileUploadListFile;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 机场日志管理接口
 * <AUTHOR>
 * @Date 2025/5/23 17:14
 */
@Validated
@RestController
@Tag(name = "管理后台 - 机场日志管理")
@RequestMapping("/system/deviceLog")
public class DockDeviceLogController {

    @Resource
    private IDeviceLogService deviceLogService;

    @Operation(summary = "根据机场sn查询已经获取的日志文件列表")
    @GetMapping("/getListByDockSn/{dockSn}")
    public CommonResult<List<DeviceLogDO>> getListByDockSn(@PathVariable @NotNull String dockSn) {
        List<DeviceLogDO> list = deviceLogService.getListByDockSn(dockSn);
        return CommonResult.success(list);
    }

    @Operation(summary = "查询机场以及无人机内部日志文件列表")
    @PostMapping("/getFileUploadList")
    public CommonResult<List<FileUploadListFile>> getFileUploadList(@Valid @RequestBody LogFileParam logFileParam) {
        List<FileUploadListFile> fileUploadList = deviceLogService.getFileUploadList(logFileParam);
        return CommonResult.success(fileUploadList);
    }

    @Operation(summary = "获取无人机以及机场日志文件")
    @PostMapping("/fileUploadStart")
    public CommonResult<Boolean> fileUploadStart(@Valid @RequestBody LogStartUploadDTO logStartUploadDTO) {
        return CommonResult.success(deviceLogService.fileUploadStart(logStartUploadDTO));
    }

    @Operation(summary = "获取无人机以及机场日志文件")
    @PostMapping("/reUpload/{id}")
    public CommonResult<Boolean> reUpload(@PathVariable @NotNull Long id) {
        return CommonResult.success(deviceLogService.reUpload(id));
    }

    @Operation(summary = "取消日志文件上传")
    @PostMapping("/cancelById/{id}")
    public CommonResult<Boolean> cancelLogUploading(@PathVariable @NotNull Long id) {
        return CommonResult.success(deviceLogService.cancelLogUploading(id));
    }

    @Operation(summary = "根据ID删除日志记录")
    @PostMapping("/deleteById/{id}")
    public CommonResult<Boolean> deleteById(@PathVariable @NotNull Long id) {
        return CommonResult.success(deviceLogService.deleteById(id));
    }
}