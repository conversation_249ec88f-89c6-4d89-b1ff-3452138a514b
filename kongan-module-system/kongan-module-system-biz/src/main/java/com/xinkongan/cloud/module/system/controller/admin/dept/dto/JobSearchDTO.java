package com.xinkongan.cloud.module.system.controller.admin.dept.dto;

import com.xinkongan.cloud.framework.common.dto.SearchDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Data
public class JobSearchDTO extends SearchDTO {

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "场景 0巡检1建模2接警3一键试飞4联合行动5一键起飞")
    private Integer scene;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "搜索时间段(任务执行时间)开始时间")
    private String beginTime;

    @Schema(description = "搜索时间段(任务执行时间)结束时间")
    private String endTime;
}
