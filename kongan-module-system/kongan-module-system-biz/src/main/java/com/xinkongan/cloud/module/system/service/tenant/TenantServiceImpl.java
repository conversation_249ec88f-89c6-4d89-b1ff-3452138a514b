package com.xinkongan.cloud.module.system.service.tenant;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.framework.common.enums.CommonStatusEnum;
import com.xinkongan.cloud.framework.common.enums.redis.RedisKeyPrefix;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.common.util.collection.CollectionUtils;
import com.xinkongan.cloud.framework.common.util.crypto.DigestUtils;
import com.xinkongan.cloud.framework.common.util.date.DateUtils;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.tenant.config.TenantProperties;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.DeptSaveReqVO;
import com.xinkongan.cloud.module.system.controller.admin.dict.vo.DictInfoVO;
import com.xinkongan.cloud.module.system.controller.admin.permission.vo.role.RoleSaveReqVO;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.TenantRegisterReqVO;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.dict.DictDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.MenuDO;
import com.xinkongan.cloud.module.system.dal.dataobject.tenant.TenantDO;
import com.xinkongan.cloud.module.system.dal.mysql.tenant.TenantMapper;
import com.xinkongan.cloud.module.system.enums.SystemConstants;
import com.xinkongan.cloud.module.system.enums.common.SexEnum;
import com.xinkongan.cloud.module.system.enums.dict.SystemGeneralDictCode;
import com.xinkongan.cloud.module.system.enums.permission.RoleCodeEnum;
import com.xinkongan.cloud.module.system.enums.permission.RoleTypeEnum;
import com.xinkongan.cloud.module.system.enums.tenant.TenantType;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.dict.IDictService;
import com.xinkongan.cloud.module.system.service.permission.MenuService;
import com.xinkongan.cloud.module.system.service.permission.PermissionService;
import com.xinkongan.cloud.module.system.service.permission.RoleService;
import com.xinkongan.cloud.module.system.service.tenant.handler.TenantInfoHandler;
import com.xinkongan.cloud.module.system.service.tenant.handler.TenantMenuHandler;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 租户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantServiceImpl implements TenantService {

    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired(required = false) // 由于 kongan.tenant.enable 配置项，可以关闭多租户的功能，所以这里只能不强制注入
    private TenantProperties tenantProperties;

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private AdminUserService userService;
    @Resource
    private RoleService roleService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private IDictService dictService;

    @Resource
    private DeptService deptService;

    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private MenuService menuService;


    @Override
    @TenantIgnore
    @DataPermission(enable = false)
    public Boolean checkTenantNameRepeat(Long id, String tenantName) {
        Long count = tenantMapper.selectCount(
                new LambdaQueryWrapperX<TenantDO>()
                        .eq(TenantDO::getName, tenantName)
                        .ne(id != null, TenantDO::getId, id)
        );
        return count > 0;
    }

    @Override
    @TenantIgnore
    @Transactional(rollbackFor = Exception.class)
    public Long registerTenant(TenantRegisterReqVO tenantRegisterInfo) {

        if (this.checkTenantNameRepeat(null, tenantRegisterInfo.getCompany())) {
            throw new ServiceException(TENANT_NAME_HAS_EXIST);
        }

        // 1、创建租户
        TenantDO tenant = this.convertTenant(tenantRegisterInfo);
        tenantMapper.insert(tenant);
        // 2、创建顶级组织
        Long deptId = createTenantAdminDept(tenant);

        // 3、创建用户
        Long userId = createTenantAdminUser(tenantRegisterInfo, deptId, tenant);

        // 将租户 EMQX 连接信息 缓存
        this.setEmqxInfoToRedis(tenant.getEmqxUsername(), tenant.getEmqxPassword(), tenant.getEmqxSalt(), tenant.getId());

        // 4、更新组织信息
        DeptDO deptInfo = deptService.getDept(deptId);
        deptInfo.setLeaderUserId(userId);
        deptInfo.setPhone(tenantRegisterInfo.getMobile());
        deptInfo.setCreator(String.valueOf(userId));
        deptService.updateDeptById(deptInfo);

        // 4、创建租户管理员角色
        Long roleId = createTenantAdminRole(tenant.getId(), deptId, userId);

        // 5、给角色关联菜单
        permissionService.assignRoleMenu(roleId, new HashSet<>(tenant.getMenuIds()));

        // 6、给用户关联角色
        permissionService.assignUserRole(userId, Collections.singleton(roleId));

        // 更新下租户的 联系人用户编号
        tenant.setContactUserId(userId);
        tenant.setCreator(String.valueOf(userId));
        tenant.setUpdater(String.valueOf(userId));
        tenantMapper.updateById(tenant);

        return tenant.getId();
    }

    private Long createTenantAdminDept(TenantDO tenant) {
        DictDO dictInfo = dictService.getById(tenant.getIndustry());
        DeptSaveReqVO deptSaveReqVO = new DeptSaveReqVO();
        deptSaveReqVO.setName(tenant.getName());
        deptSaveReqVO.setSort(SystemConstants.SYSTEM_DEFAULT_SORT_VALUE);
        deptSaveReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        deptSaveReqVO.setTenantId(tenant.getId());
        deptSaveReqVO.setDeptTypeCode(dictInfo != null ? dictInfo.getCode() : null);
        return deptService.createDeptForTenantRegister(deptSaveReqVO);
    }

    private Long createTenantAdminUser(TenantRegisterReqVO tenantRegisterInfo, Long deptId, TenantDO tenant) {
        String username = tenantRegisterInfo.getUsername();
        UserSaveReqVO userSaveReqVO = new UserSaveReqVO();
        userSaveReqVO.setUsername(username);
        userSaveReqVO.setNickname(tenantRegisterInfo.getNickName());
        userSaveReqVO.setDeptId(deptId);
        userSaveReqVO.setMobile(tenantRegisterInfo.getMobile());
        userSaveReqVO.setSex(SexEnum.UNKNOWN.getSex());
        userSaveReqVO.setPassword(tenantRegisterInfo.getPassword());
        userSaveReqVO.setTenantId(tenant.getId());
        return userService.createUserForTenantRegister(userSaveReqVO);
    }

    private Long createTenantAdminRole(Long tenantId, Long deptId, Long userId) {
        RoleSaveReqVO roleSaveReqVO = new RoleSaveReqVO();
        roleSaveReqVO.setName(RoleCodeEnum.TENANT_ADMIN.getName());
        roleSaveReqVO.setCode(RoleCodeEnum.TENANT_ADMIN.getCode());
        roleSaveReqVO.setSort(SystemConstants.SYSTEM_DEFAULT_SORT_VALUE);
        roleSaveReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        roleSaveReqVO.setRemark(RoleCodeEnum.TENANT_ADMIN.getName());
        roleSaveReqVO.setTenantId(tenantId);
        roleSaveReqVO.setDeptId(deptId);
        roleSaveReqVO.setUserId(userId);
        return roleService.createRoleForTenantRegister(roleSaveReqVO, RoleTypeEnum.SYSTEM.getType());
    }


    private TenantDO convertTenant(TenantRegisterReqVO tenantRegisterInfo) {
        // 校验租户名称是否重复
        validTenantNameDuplicate(tenantRegisterInfo.getCompany(), null);
        TenantDO tenant = BeanUtils.toBean(tenantRegisterInfo, TenantDO.class);

        // 查询默认试用天数字典
        DictInfoVO expireDictInfo = dictService.getDictByCode(SystemGeneralDictCode.SYSTEM_DEFAULT_TENANT_TRY_EXPIRE.getCode());
        int expireTime = Integer.parseInt(expireDictInfo.getDictValue());

        // 获取系统默认的租户菜单列表
        DictInfoVO menuDictInfo = dictService.getDictByCode(SystemGeneralDictCode.SYSTEM_DEFAULT_TENANT_MENUS.getCode());
//        List<Long> menuIds = JSONObject.parseArray(menuDictInfo.getDictValue(), Long.class);

        List<Long> menuIds = menuService.getMenuList().stream().map(MenuDO::getId).toList();

        // 查询默认系统租户账号数目
        DictInfoVO accountCountDictInfo = dictService.getDictByCode(SystemGeneralDictCode.SYSTEM_DEFAULT_ACCOUNT_COUNT.getCode());
        int accountCount = Integer.parseInt(accountCountDictInfo.getDictValue());

        // 生成 租户 EMQX 连接的用户名和密码
        String emqxUsername = this.buildEmqxUsername();
        String emqxPassword = RandomUtil.randomStringUpper(6);
        String emqxSalt = RandomUtil.randomString(6);

        tenant.setName(tenantRegisterInfo.getCompany());

        // 修改为两个字段控制租户状态
        tenant.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 状态为审核中
        tenant.setApproval(0); // 状态为审核中


        tenant.setExpireTime(LocalDateTime.now().plusDays(expireTime));
        tenant.setAccountCount(accountCount);
        tenant.setTenantType(TenantType.BUSINESS_TENANT.getType());
        tenant.setMenuIds(menuIds);
        tenant.setContactName(tenantRegisterInfo.getUsername());
        tenant.setContactMobile(tenantRegisterInfo.getMobile());
        tenant.setEmqxUsername(emqxUsername);
        tenant.setEmqxPassword(emqxPassword);
        tenant.setEmqxSalt(emqxSalt);
        tenant.setCreator(tenantRegisterInfo.getUsername());
        tenant.setUpdater(tenantRegisterInfo.getUsername());
        tenant.setRegisterTime(LocalDateTime.now());
        return tenant;
    }


    @Override
    public List<Long> getTenantIdList() {
        List<TenantDO> tenants = tenantMapper.selectList();
        return CollectionUtils.convertList(tenants, TenantDO::getId);
    }

    @Override
    public void validTenant(Long id) {
        TenantDO tenant = getTenant(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        // 审批状态
        if (tenant.getApproval() == 0) {
            throw new ServiceException(AUTH_TENANT_IS_AUDIT_ERROR);
        }

        if (tenant.getApproval() == 2) {
            throw new ServiceException(AUTH_TENANT_AUDIT_NOT_PASS_ERROR);
        }

        if (tenant.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(TENANT_DISABLE, tenant.getName());
        }
        if (DateUtils.isExpired(tenant.getExpireTime())) {
            throw exception(TENANT_EXPIRE);
        }
    }

    private void validTenantNameDuplicate(String name, Long id) {
        TenantDO tenant = tenantMapper.selectByName(name);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的租户
        if (id == null) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
    }

    @Override
    public TenantDO getTenant(Long id) {
        return tenantMapper.selectById(id);
    }

    @Override
    public void handleTenantInfo(TenantInfoHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        // 执行处理器
        handler.handle(tenant);
    }

    @Override
    public void handleTenantMenu(TenantMenuHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户，然后获得菜单
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        // 执行处理器
        handler.handle(new HashSet<>(tenant.getMenuIds()));
    }

    @Override
    public String buildEmqxUsername() {
        // 获取字典数据
        DictInfoVO incrementDict = dictService.getDictByCode(SystemGeneralDictCode.SYSTEM_TENANT_AUTO_INCREMENT_VALUE.getCode());
        DictInfoVO emqxNameDict = dictService.getDictByCode(SystemGeneralDictCode.SYSTEM_TENANT_EMQX_USERNAME_PREFIX.getCode());

        // 校验字典数据是否存在
        if (incrementDict == null || emqxNameDict == null) {
            throw ServiceExceptionUtil.exception(DICT_DATA_NOT_EXISTS);
        }

        // 获取当前计数值并进行校验
        int count;
        try {
            count = Integer.parseInt(incrementDict.getDictValue());
        } catch (NumberFormatException e) {
            log.error("当前字段不是数字，字典信息为: {}", incrementDict);
            throw ServiceExceptionUtil.exception(DICT_CODE_ILLEGAL_ERROR);
        }

        // 生成用户名数字部分
        String formatCount = String.format("%04d", count);
        String username = emqxNameDict.getDictValue() + formatCount;

        // 更新字典中的计数值
        String updatedCount = String.format("%04d", count + 1);
        dictService.updateById(DictDO.builder()
                .id(incrementDict.getId())
                .dictValue(updatedCount)
                .build());

        return username;
    }

    @Override
    public void setEmqxInfoToRedis(String username, String password, String salt, Long tenantId) {
        // 密码生成摘要
        String pwd = DigestUtils.sha256(salt + password);

        // 缓存到 Redis 中 供 EMQX-AUTH 查询
        redisCacheService.hashPut(RedisKeyPrefix.MQTT_USER_PREFIX + username, RedisKeyPrefix.PASSWORD, pwd);
        redisCacheService.hashPut(RedisKeyPrefix.MQTT_USER_PREFIX + username, RedisKeyPrefix.SALT, salt);
        redisCacheService.hashPut(RedisKeyPrefix.MQTT_USER_PREFIX + username, RedisKeyPrefix.TENANT, tenantId);
    }

    @Override
    public List<TenantDO> getTenantList() {
        return tenantMapper.selectList();
    }

    private boolean isTenantDisable() {
        return tenantProperties == null || Boolean.FALSE.equals(tenantProperties.getEnable());
    }

    @Override
    public void updateTenantInfo(TenantDO tenantInfo) {
        tenantMapper.updateById(tenantInfo);
    }
}
