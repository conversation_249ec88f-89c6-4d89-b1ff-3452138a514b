package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import com.xinkongan.cloud.module.system.enums.task.DockTaskModeEnum;
import com.xinkongan.cloud.module.system.enums.task.JobStatusTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 计划分页ReqVO
 * <AUTHOR>
 * @Date 2025/1/13 18:57
 */
@Data
@Schema(description = "计划分页ReqVO")
public class JobPageReqVO extends PageParam {

    @Schema(description = "场景 0巡检1建模2接警3一键试飞4联合行动5一键起飞")
    private Integer scene;

    @Schema(description = "任务/组织名称模糊查询")
    private String taskOrDeptName;

    /**
     * 执行模式 0立即执行1定时任务2循环任务 {@link DockTaskModeEnum}
     *
     * <AUTHOR>
     * @date 2025/1/13 19:22
     **/
    @Schema(description = "执行模式 0立即执行1定时任务2循环任务")
    private List<Integer> execMode;

    /**
     * {@link JobStatusTypeEnum} 任务状态 0待审批 , 1待执行 , 2执行中 , 3已执行 , 4未完成 , 5执行失败 ,6 已失效, 7已取消
     *
     * <AUTHOR>
     * @date 2025/1/13 19:13
     **/
    @Schema(description = "执行状态 0待审批 , 1待执行 , 2执行中 , 3已执行 , 4未完成 , 5执行失败 ,6 已失效, 7已取消")
    private List<Integer> status;

    @Schema(description = "搜索时间段(任务执行时间)开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime beginTime;

    @Schema(description = "搜索时间段(任务执行时间)结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @Schema(description = "任务来源 0 系统 1 第三方")
    private List<Integer> source;

    @Schema(description = "接警场景id列表")
    private List<Long> alarmSceneIds;

    @Schema(description = "排除的任务id")
    private Long excludeJobId;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "用户id")
    private Long userId;
}