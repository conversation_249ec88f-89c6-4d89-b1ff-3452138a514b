package com.xinkongan.cloud.module.system.service.material;

import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialMapper;
import com.xinkongan.cloud.module.system.dto.MaterialParseDTO;
import com.xinkongan.cloud.module.system.dto.MaterialParseNotifyDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.material.MaterialNotifyEnums;
import com.xinkongan.cloud.module.system.enums.material.MaterialParseStatus;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Set;

@Slf4j
public abstract class DefaultParseHandler implements IMaterialParseHandler {

    @Resource
    private WebSocketSendApi webSocketSendApi;

    @Resource
    private MaterialMapper materialMapper;


    @Override
    public void doHandler0(MaterialParseDTO materialLoad) {
        // 发送解析状态通知
        this.sendMaterialParseStatusNotify(materialLoad.getId(), MaterialParseStatus.PARSING);
        try {
            // 发送解析禁进度通知
            this.sendMaterialParseProcessNotify(materialLoad.getId(), 0);
            this.doHandler(materialLoad);
        } catch (Exception e) {
            log.error("[素材解析失败],失败信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));

            MaterialDO materialDO = materialMapper.selectById(materialLoad.getId());
            if (materialDO == null) {
                log.error("[素材解析失败],素材不存在，素材id为：{}", materialLoad.getId());
                return;
            }
            materialDO.setStatus(MaterialParseStatus.FAILED.getCode());
            materialMapper.updateById(materialDO);
            // 发送websocket 通知，解析失败
            this.sendMaterialParseStatusNotify(materialLoad.getId(), MaterialParseStatus.FAILED);
        }
    }


    /**
     * 具体的解析方法
     *
     * @param materialParse 素材解析信息
     */
    public void doHandler(MaterialParseDTO materialParse) throws Exception {
        log.info("素材解析空实现");
    }


    /**
     * 素材模块发送websocket解析状态
     */
    protected void sendWebSocketMessage(Long tenant, MaterialParseNotifyDTO message) {
        WebSocketMessageDTO<MaterialParseNotifyDTO> websocketMessage =
                WebSocketMessageDTO
                        .<MaterialParseNotifyDTO>builder()
                        .tenantId(tenant)
                        .message(CustomWebSocketMessage
                                .<MaterialParseNotifyDTO>builder()
                                .bizCode(BizCodeEnum.MATERIAL_PARSE_PROCESS.getCode())
                                .data(message)
                                .build())
                        .build();
        webSocketSendApi.sendByTenant(websocketMessage);
    }

    protected <T> void sendMaterialParseStatusNotify(Long materialId, MaterialParseStatus materialParseStatus, T data) {
        MaterialDO materialDO = materialMapper.selectById(materialId);
        if (materialDO == null) {
            log.error("[素材解析失败],素材不存在，素材id为：{}", materialId);
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        MaterialParseNotifyDTO<T> materialParseNotify = MaterialParseNotifyDTO.<T>builder()
                .materialId(materialDO.getId())
                .function(MaterialNotifyEnums.PARSE_STATUS.getFunction())
                .status(materialParseStatus.getCode())
                .data(data)
                .build();
        this.sendWebSocketMessage(materialDO.getTenantId(), materialParseNotify);
    }


    protected void sendMaterialParseStatusNotify(Long materialId, MaterialParseStatus materialParseStatus) {
        this.sendMaterialParseStatusNotify(materialId, materialParseStatus, null);
    }

    protected <T> void sendMaterialParseProcessNotify(Long materialId, Integer process, T data) {
        MaterialDO materialDO = materialMapper.selectById(materialId);
        if (materialDO == null) {
            log.error("[素材解析失败],素材不存在，素材id为：{}", materialId);
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        MaterialParseNotifyDTO<T> materialParseNotify = MaterialParseNotifyDTO.<T>builder()
                .materialId(materialDO.getId())
                .function(MaterialNotifyEnums.PARSE_PROCESS.getFunction())
                .process(process)
                .data(data)
                .build();
        this.sendWebSocketMessage(materialDO.getTenantId(), materialParseNotify);
    }

    protected void sendMaterialParseProcessNotify(Long materialId, Integer process) {
        this.sendMaterialParseProcessNotify(materialId, process, null);
    }
}
