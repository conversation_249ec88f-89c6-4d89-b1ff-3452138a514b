package com.xinkongan.cloud.module.system.service.flyrecord.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordContrastCacheService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.geo.Metrics;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 飞行记录对比缓存Service
 * <AUTHOR>
 * @Date 2025/2/19 9:41
 */
@Service
@Slf4j
public class FlyRecordContrastCacheServiceImpl implements IFlyRecordContrastCacheService {

    @Resource
    private IRedisCacheService redisCacheService;

    /**
     * 添加飞行记录图片位置缓存
     *
     * <AUTHOR>
     * @date 2025/2/19 9:41
     **/
    @Override
    public void addContrastFileCache(FlyRecordFileDO flyRecordFileDO) {
        try {
            String key = String.format(RedisKeyConstants.FLY_PICTURE_MAP, flyRecordFileDO.getTenantId());
            redisCacheService.geoPut(
                    key,
                    Double.parseDouble(flyRecordFileDO.getLon()),
                    Double.parseDouble(flyRecordFileDO.getLat()),
                    flyRecordFileDO.getId().toString());
        } catch (Exception e) {
            log.error("添加飞行记录图片位置缓存失败，flyRecordFileDO:{}", JSONUtil.toJsonStr(flyRecordFileDO), e);
        }
    }

    /**
     * 根据距离获取附近的图片
     *
     * <AUTHOR>
     * @date 2025/2/19 9:39
     **/
    @Override
    public List<Long> getFileByRadius(Long tenantId, Double lon, Double lat, Double radius) {
        String key = String.format(RedisKeyConstants.FLY_PICTURE_MAP, tenantId);
        List<String> isStrList = redisCacheService.geoRadius(key, lon, lat, radius, Metrics.MILES);
        if (CollUtil.isEmpty(isStrList)) {
            return List.of();
        }
        return isStrList.stream().map(Long::parseLong).toList();
    }
}