package com.xinkongan.cloud.module.system.service.material;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.xinkongan.cloud.module.system.controller.admin.file.vo.file.FileCreateReqVO;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.MaterialDownloadCallbackDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.MaterialGisDealCallbackDTO;
import com.xinkongan.cloud.module.system.dal.dataobject.file.FileDO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialMapper;
import com.xinkongan.cloud.module.system.enums.file.S3FileDirPrefixEnum;
import com.xinkongan.cloud.module.system.enums.material.MaterialFormatTypeEnum;
import com.xinkongan.cloud.module.system.enums.material.MaterialParseStatus;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.sdk.geo.utils.LocationUtil;
import com.xinkongan.cloud.sdk.gis.dto.store.TifBoundDTO;
import com.xinkongan.cloud.sdk.gis.service.IGwcService;
import com.xinkongan.cloud.sdk.gis.service.ILayerService;
import com.xinkongan.cloud.sdk.gis.service.IStoreService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.opengis.geometry.DirectPosition;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class GisBusinessServiceImpl extends DefaultParseHandler implements IGisBusinessService {

    @Resource
    private IStoreService storeService;
    @Resource
    private ILayerService layerService;
    @Resource
    private IGwcService gwcService;
    @Resource
    private MaterialMapper materialMapper;
    @Resource
    private FileService fileService;
    @Resource
    private LocationUtil locationUtil;

    /**
     * 下载成功后自动创建图层和缓存任务
     */
    @Override
    public void dealGisCallback(MaterialGisDealCallbackDTO downloadCallbackInfo) {
        log.info("[tif素材下载完成回调]，参数为：{}", downloadCallbackInfo);
        String jsonString = JSONObject.toJSONString(downloadCallbackInfo.getData());
        MaterialDownloadCallbackDTO downloadCallbackDTO = JSONObject.parseObject(jsonString, MaterialDownloadCallbackDTO.class);
        Long materialId = Long.valueOf(downloadCallbackDTO.getBusinessId());

        try {
            MaterialDO materialInfo = materialMapper.selectById(materialId);
            if (materialInfo == null) {
                throw new RuntimeException("二维素材未找到");
            }
            materialInfo.setProcess(30);
            materialMapper.updateById(materialInfo);
            // 发送 30% 进度
            this.sendMaterialParseProcessNotify(materialId, 30);

            Long deptId = materialInfo.getDeptId();
            String url = materialInfo.getUrl();
            String filePath = downloadCallbackDTO.getPath();
            String workspace = "w_" + deptId;
            log.info("【二维素材解析】workspace：{},filePath:{}", workspace, filePath);
            // 创建tiff数据源
            String storeName = storeService.createTifStore(workspace, filePath);
            log.info("【二维素材解析】 STEP1 创建tiff数据源res:{}", storeName);
            if (StrUtil.isBlank(storeName)) {
                throw new RuntimeException("创建数据源失败");
            }
            // 创建tiff图层
            String layerName = "l_" + materialId + "_" + materialInfo.getTenantId();
            log.info("【二维素材解析】layerName:{}", layerName);
            layerService.createTifLayer(workspace, url, layerName, storeName);
            log.info("【二维素材解析】 STEP2 创建tiff图层layerName:{}", layerName);
            // 创建缓存任务
            Boolean gwcTask = gwcService.createGwcTask(workspace, layerName);
            log.info("【二维素材解析】 STEP3 创建缓存任务:{}", gwcTask);
            // 解析中心点坐标
            DirectPosition tiffCenter = storeService.getTiffCenter(url);
            Double lon = tiffCenter.getOrdinate(1);
            Double lat = tiffCenter.getOrdinate(0);
            log.info("【二维素材解析】 STEP4 解析中心点经纬度,lon:{} lat:{}", lon, lat);
            // 解析二维模型边界
            TifBoundDTO tiffBounds = storeService.getTiffBounds(url);
            log.info("【二维素材解析】 STEP5 解析边界范围 tiffBounds:{}", tiffBounds);
            MaterialDO materialDO = materialMapper.selectById(materialId);
            // 更新二维模型信息
            // 中心点坐标
            materialDO.setCenterLon(lon);
            materialDO.setCenterLat(lat);
            // 进行地理逆编码
            String address = locationUtil.getLocationByWg84(lon, lat);
            materialDO.setAddress(address);

            // 二维模型范围
            Double leftUpLon = Math.max(tiffBounds.getBounds()[1].y, tiffBounds.getBounds()[1].x);
            Double leftUpLat = Math.min(tiffBounds.getBounds()[1].y, tiffBounds.getBounds()[1].x);
            materialDO.setLeftUpLon(leftUpLon);
            materialDO.setLeftUpLat(leftUpLat);

            Double rightDownLon = Math.max(tiffBounds.getBounds()[3].y, tiffBounds.getBounds()[3].x);
            Double rightDownLat = Math.min(tiffBounds.getBounds()[3].y, tiffBounds.getBounds()[3].x);
            materialDO.setRightDownLon(rightDownLon);
            materialDO.setRightDownLat(rightDownLat);
            // 数据源名称
            materialDO.setStoreName(storeName);
            // 命名空间名称
            materialDO.setWorkspaceName(workspace);
            // 图层名称
            materialDO.setLayerName(layerName);
            // 图层瓦片格式
            materialDO.setFormat(MaterialFormatTypeEnum.PNG.getValue());
            materialDO.setMinimumLevel(0);
            materialDO.setMaximumLevel(21);
            log.info("【二维素材解析】 开始下载缩略图");
            // 下载缩略图
            String format = MaterialFormatTypeEnum.PNG.getValue();
            byte[] thumbnailByte = storeService.getThumbnailByte(workspace, layerName, tiffBounds, format);
            // 将缩略图的背景透明度调为透明
            thumbnailByte = makePngTransparent(thumbnailByte);

            log.info("【二维素材解析】 STEP5 缩略图下载成功");
            // 上传阿里云
            String dir = S3FileDirPrefixEnum.TWO_DIMENSION.getDir(String.valueOf(materialDO.getTenantId()));
            String fileUrl = fileService.createFile(null, dir + "/" + IdWorker.getIdStr() + ".png", thumbnailByte);
            log.info("【二维素材解析】上传缩略图成功，缩略图的访问路径为：{}", fileUrl);
            // 保存文件id
            FileDO fileInfo = fileService.getFileRecordByUrl(fileUrl);
            if (fileInfo != null && fileInfo.getId() != null) {
                // 填充下组织信息和租户信息
                fileInfo.setDeptId(deptId);
                fileInfo.setTenantId(materialDO.getTenantId());
                fileService.updateFileInfoById(fileInfo);

                List<Long> ids = materialDO.getFileIds();
                ids.add(fileInfo.getId());
            }
            materialDO.setJpgUrl(fileUrl);
            materialDO.setProcess(60);
            // 保存数据库
            materialMapper.updateById(materialDO);

            // 发送 60% 进度
            this.sendMaterialParseProcessNotify(materialId, 60);
        } catch (Exception e) {
            log.error("【二维素材解析】解析过程发生异常：{}", e.getMessage());
            // 修改素材解析状态为失败
            MaterialDO materialDO = new MaterialDO();
            materialDO.setId(materialId);
            materialDO.setStatus(MaterialParseStatus.FAILED.getCode());
            materialMapper.updateById(materialDO);
            // 通知前端解析失败
            this.sendMaterialParseStatusNotify(materialId, MaterialParseStatus.FAILED);
        }
    }


    private byte[] makePngTransparent(byte[] imageData) {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(imageData);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            BufferedImage image = ImageIO.read(bais);
            if (image != null) {
                // 创建一个新的BufferedImage，设置背景为透明
                BufferedImage newImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_ARGB);
                Graphics2D g2d = newImage.createGraphics();
                g2d.setComposite(AlphaComposite.Src);
                g2d.drawImage(image, 0, 0, null);
                g2d.dispose();

                // 将白色像素设置为透明
                for (int x = 0; x < newImage.getWidth(); x++) {
                    for (int y = 0; y < newImage.getHeight(); y++) {
                        int argb = newImage.getRGB(x, y);
                        if (argb == Color.WHITE.getRGB()) {
                            newImage.setRGB(x, y, 0x00FFFFFF); // 设置为透明
                        }
                    }
                }
                // 将新的BufferedImage写入ByteArrayOutputStream
                ImageIO.write(newImage, "png", baos);
                return baos.toByteArray();
            }
        } catch (IOException e) {
            log.error("[修改背景图失败],异常信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
        }
        return imageData;
    }


    /**
     * 素材模块发送websocket解析状态
     *
     * @param type
     * @param parsingType
     */
    protected void sendWebSocketMessage(String type, String parsingType, String materialId, String url, Long tenantId, Long deptId) {
//        log.info("素材模块发送websocket解析状态");
//        WebSocketSendReqDTO webSocketSendReqDTO = new WebSocketSendReqDTO();
//        webSocketSendReqDTO.setTenantId(tenantId); // 设置租户
//        webSocketSendReqDTO.setDeptId(deptId); // 设置部门id
//
//        CustomWebSocketMessage message = new CustomWebSocketMessage();
//        message.setBizCode(BizCodeEnum.MATERIAL_PARSEING.getCode());
//        message.setTypeEnum(ReceivingTypeEnum.BY_THIS_DEPT);
//        MaterialMessageVO msg = MaterialMessageVO.builder().materialId(materialId).parsingType(parsingType).type(type).url(url).build();
//        message.setData(msg);
//        webSocketSendReqDTO.setMessage(message);
//        log.info("消息体: {}", JSONUtil.toJsonStr(webSocketSendReqDTO));
//        webSocketApi.sendByTenant(webSocketSendReqDTO);
    }
}
