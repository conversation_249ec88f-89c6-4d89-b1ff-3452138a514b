package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.common.annotation.LivePermissionCheck;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.live.CameraFocalLengthSetParam;
import com.xinkongan.cloud.module.system.service.live.AgoraBaseService;
import com.xinkongan.cloud.module.system.service.live.LiveService;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.DockLiveStatusData;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.enums.VideoQualityEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.enums.VideoTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Validated
@RestController
@Tag(name = "管理后台 - 直播管理")
@RequestMapping("/system/live")
public class LiveController {

    @Resource
    private LiveService liveService;

    @Resource
    private AgoraBaseService agoraBaseService;

    @Operation(summary = "开启设备直播/机场SN/开启的设备SN")
    @GetMapping("/start/{dockSn}/{deviceSn}")
    @LivePermissionCheck(sn = "#dockSn")
    public CommonResult<Boolean> start(@PathVariable String dockSn, @PathVariable String deviceSn) {
        boolean push = liveService.startLivePush(dockSn, deviceSn);
        return CommonResult.success(push);
    }

    @Operation(summary = "关闭设备直播/机场SN/关闭开启的设备SN")
    @GetMapping("/stop/{dockSn}/{deviceSn}")
    @LivePermissionCheck(sn = "#dockSn")
    public CommonResult<Boolean> stop(@PathVariable String dockSn, @PathVariable String deviceSn) {
        boolean push = liveService.stopLivePush(dockSn, deviceSn);
        return CommonResult.success(push);
    }


    @Operation(summary = "切换直播镜头")
    @GetMapping("/setLiveVideoType/{dockSn}/{deviceSn}/{videoType}")
    @LivePermissionCheck(sn = "#dockSn")
    public CommonResult<Boolean> setLiveVideoType(@PathVariable String dockSn, @PathVariable String deviceSn, @PathVariable String videoType) {
        boolean push = liveService.setLiveVideoType(dockSn, deviceSn, VideoTypeEnum.find(videoType));
        return CommonResult.success(push);
    }

    @Operation(summary = "切换直播清晰度")
    @GetMapping("/setLiveVideoQuality/{dockSn}/{deviceSn}/{videoQuality}")
    @LivePermissionCheck(sn = "#dockSn")
    public CommonResult<Boolean> setLiveVideoQuality(@PathVariable String dockSn, @PathVariable String deviceSn, @PathVariable Integer videoQuality) {
        boolean push = liveService.setLiveVideoQuality(dockSn, deviceSn, VideoQualityEnum.find(videoQuality));
        return CommonResult.success(push);
    }

    @Operation(summary = "查询当前设备的直播状态")
    @GetMapping("/getLiveStatus/{dockSn}/{deviceSn}")
    public CommonResult<DockLiveStatusData> getLiveStatus(@PathVariable String dockSn, @PathVariable String deviceSn) {
        DockLiveStatusData liveStatusData = liveService.getDockLiveStatusData(dockSn, deviceSn);
        return CommonResult.success(liveStatusData);
    }

    @Operation(summary = "设置变焦倍数")
    @PostMapping("/setZoomFactor")
    public CommonResult<Boolean> setZoomFactor(@RequestBody CameraFocalLengthSetParam param) {
        boolean zoom = liveService.cameraFocalLengthSet(param);
        return CommonResult.success(zoom);
    }

    @Operation(summary = "获取声网 RTC Token")
    @GetMapping("/getRtcToken/{channelName}")
    @LivePermissionCheck(sn = "#channelName")
    public CommonResult<String> getRtcToken(@PathVariable String channelName) {
        String rtcToken = agoraBaseService.getRtcToken(channelName);
        return CommonResult.success(rtcToken);
    }

    @Operation(summary = "开启直播录制")
    @GetMapping("/startLiveRecord/{channelName}")
    public CommonResult<Boolean> startLiveRecord(@PathVariable String channelName) {
        boolean start = liveService.startLiveRecord(channelName);
        return CommonResult.success(start);
    }

    @Operation(summary = "停止直播录制")
    @GetMapping("/stopLiveRecord/{channelName}")
    public CommonResult<Boolean> stopLiveRecord(@PathVariable String channelName) {
        boolean start = liveService.stopLiveRecord(channelName);
        return CommonResult.success(start);
    }

    @Operation(summary = "机场2直播相机切换{\"0\":\"舱内\",\"1\":\"舱外\"}")
    @GetMapping("/liveCameraChange/{dockSn}/{cameraPosition}")
    public CommonResult<Boolean> liveCameraChange(@PathVariable String dockSn, @PathVariable Integer cameraPosition) {
        return CommonResult.success(liveService.liveCameraChange(dockSn, cameraPosition));
    }


}
