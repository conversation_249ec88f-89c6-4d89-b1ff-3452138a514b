package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
@Data
public class PostureAlarmListVO {

    @Schema(description = "告警ID")
    private Long alarmId;

    @Schema(description = "警情场景名称")
    private String alarmSceneName;

    @Schema(description = "警情名称")
    private String alarmName;

    @Schema(description = "所属组织")
    private String deptName;

    @Schema(description = "警情描述")
    private String description;

    @Schema(description = "警情时间")
    private LocalDateTime createTime;

}
