package com.xinkongan.cloud.module.system.controller.admin.dock.vo.log;

import com.xinkongan.cloud.sdk.dock.cloudapi.log.enums.LogModuleEnum;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogFileParam {

    @NotNull(message = "机场sn不能为空")
    private String dockSn;

    @NotNull
    @Size(min = 1, max = 2)
    private List<LogModuleEnum> moduleList;

}