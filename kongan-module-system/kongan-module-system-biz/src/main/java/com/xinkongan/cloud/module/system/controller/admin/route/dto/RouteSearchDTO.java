package com.xinkongan.cloud.module.system.controller.admin.route.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RouteSearchDTO {

    @Schema(description = "分页页码", example = "1")
    private Integer page;

    @Schema(description = "分页偏移量", example = "10")
    private Integer offset;

    @Schema(description = "搜索关键字", example = "智能")
    private String searchKey;

    @Schema(description = "航线类型（1：巡检航线，3：建模航线）", example = "1")
    private Integer routeType;

    @Schema(description = "分享标志（1：分享，0：不是分享，2：全部）", example = "1")
    private Integer shareFlag;

    @Schema(description = "组织列表", example = "[1,2]")
    private List<Long> deptIds;

    @Schema(description = "机场SN")
    private String dockSn;

    @Schema(description = "航线ID")
    private Long routeId;

    public List<Long> getDeptIds() {
        if (deptIds == null) {
            deptIds = new ArrayList<>();
        }
        return deptIds;
    }
}
