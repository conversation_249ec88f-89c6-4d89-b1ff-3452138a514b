package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 任务类型数量统计
 * <AUTHOR>
 * @Date 2025/4/22 16:27
 */
@Schema(description = "任务类型数量统计")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class JobSceneStatisticCountVO {

    @Schema(description = "巡检任务数量")
    private Long inspectionCount;

    @Schema(description = "建模任务数量")
    private Long modelingCount;

    @Schema(description = "接警任务数量")
    private Long alarmCount;
}