package com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DockModeCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 接警下发机场列表
 * <AUTHOR>
 * @Date 2025/3/20 9:54
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "接警下发机场列表")
public class AlarmDockDeviceVO {

    @Schema(description = "机场sn")
    private String deviceSn;

    @Schema(description = "无人机sn")
    private String childSn;

    @Schema(description = "机场名称")
    private String deviceName;

    @Schema(description = "是否是权限外共享机场")
    private Integer without;

    @Schema(description = "所属部门id")
    private Long deptId;

    @Schema(description = "所属部门名称")
    private String deptName;

    @Schema(description = "机场状态")
    private DockModeCodeEnum modeCode;

    @Schema(description = "距离警情点距离")
    private Integer distance;

    /**
     * {@link com.xinkongan.cloud.module.system.enums.alarm.AlarmSuggestTypeEnum}
     **/
    @Schema(description = "建议类型 0最优1推荐2不推荐")
    private Integer suggestType;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "是否在可飞覆盖范围内")
    private Integer inCoverage;

    @Schema(description = "无人机电量")
    private Integer capacityPercent;

    @JsonIgnore
    private Integer autoExecute;

}