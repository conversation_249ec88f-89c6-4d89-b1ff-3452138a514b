package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.exception.ErrorCode;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.common.annotation.DeviceControlCheck;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.dock.mqtt.debug.DockCloudApiDebugService;
import com.xinkongan.cloud.module.system.service.fly.WaylineTaskService;
import com.xinkongan.cloud.sdk.dock.cloudapi.debug.enums.DebugMethodEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.debug.response.RemoteDebugResponse;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DockModeCodeEnum;
import com.xinkongan.cloud.sdk.dock.common.DockErrorCode;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.property.PropertySetPublish;
import com.xinkongan.cloud.sdk.dock.mqtt.property.PropertySetReplyResultEnum;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */
@RestController
@Slf4j
@RequestMapping("/system/dockDebug")
@Tag(name = "管理后台 - 机场设备调试")
@Validated
public class DockDebugController {

    @Resource
    private DockCloudApiDebugService debugService;

    @Resource
    private PropertySetPublish propertySetPublish;

    @Resource
    private WaylineTaskService waylineTaskService;

    @Resource
    private DockDeviceService dockDeviceService;

    /**
     * 远程调试接口
     *
     * @param dockSn       机场SN
     * @param debugCommand 远程控制指令
     * @return 控制结果
     */
    @PostMapping("/deviceDebug/{dockSn}/{debugCommand}")
    @Operation(summary = "远程调试接口")
    @PreAuthorize("@ss.hasPermission('system:dock:debug:edit')")
    @DeviceControlCheck(sn = "#dockSn")
    public CommonResult<Boolean> deviceDebug(@PathVariable @NotNull String dockSn,
                                             @PathVariable @NotNull String debugCommand,
                                             @RequestBody Map<String, Object> map) {
        DockModeCodeEnum dockModeCode = dockDeviceService.getDockModeCode(dockSn);
        // 除了打开远程调试 其他远程调试指令都需要在远程调试状态下进行
        if (DebugMethodEnum.find(debugCommand) != DebugMethodEnum.DEBUG_MODE_OPEN) {
            if (dockModeCode == null || !dockModeCode.equals(DockModeCodeEnum.REMOTE_DEBUGGING)) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.DOCK_STATUS_NOT_SUPPORT_DEBUGGING);
            }
        }
        CommonTopicResponse<ServicesReplyData<RemoteDebugResponse>> response = debugService.debugByDockSn(dockSn, DebugMethodEnum.find(debugCommand), map);
        DockErrorCode result = response.getData().getResult();
        return result.getCode().equals(0) ? CommonResult.success(true) : CommonResult.error(result.getCode(), result.getMessage());
    }


    /**
     * 远程调试接口
     *
     * @param dockSn 机场SN
     * @return 控制结果
     */
    @PostMapping("/deviceSet/{dockSn}")
    @Operation(summary = "设备属性设置")
    @PreAuthorize("@ss.hasPermission('system:dock:debug:edit')")
    public CommonResult<Boolean> deviceSet(@PathVariable @NotNull String dockSn, @RequestBody Map<String, Object> map) {
        PropertySetReplyResultEnum resultEnum = propertySetPublish.publish(dockSn, map);
        return PropertySetReplyResultEnum.SUCCESS.equals(resultEnum) ?
                CommonResult.success(true) :
                CommonResult.error(new ErrorCode(resultEnum.getResult(), resultEnum.getResultMsg()));
    }

    @GetMapping("/oneClickTestFlight/{dockSn}")
    @Operation(summary = "一键试飞")
    @DeviceControlCheck(sn = "#dockSn")
    @PreAuthorize("@ss.hasPermission('system:dock:debug:fly')")
    public CommonResult<Boolean> oneClickTestFlight(@PathVariable @NotNull String dockSn) {
        waylineTaskService.oneClickTestFlight(dockSn);
        return CommonResult.success(true);
    }


}
