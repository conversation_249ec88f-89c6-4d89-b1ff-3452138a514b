package com.xinkongan.cloud.module.system.service.route;

import com.xinkongan.cloud.module.algorithm.dto.ArriveWaypointDTO;
import com.xinkongan.cloud.module.system.controller.admin.psdk.vo.VoiceSendToDockVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointShoutVO;

public interface IWayPointActionExecService {


    void onArriveWaypoint(ArriveWaypointDTO arriveWaypointInfo);

    void handleCountLoop(WayPointShoutVO shout, VoiceSendToDockVO payload);

    }
