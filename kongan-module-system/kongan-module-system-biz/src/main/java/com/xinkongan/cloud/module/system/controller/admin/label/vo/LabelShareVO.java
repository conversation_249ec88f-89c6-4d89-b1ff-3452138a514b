package com.xinkongan.cloud.module.system.controller.admin.label.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelShareVO {

    @Schema(description = "标注id")
    private Long labelId;

    @Schema(description = "分享的组织id")
    private List<Long> deptIds;

    public List<Long> getDeptIds() {
        if (deptIds == null) {
            return new ArrayList<>();
        }
        return deptIds;
    }
}
