package com.xinkongan.cloud.module.system.controller.admin.dept;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.module.system.controller.admin.dept.dto.HistogramSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.dept.dto.JobSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.dept.dto.PieParamsInfo;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.HistogramVO;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.JobStatisticReqVO;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.PieReqVO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobPageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobPageRespVO;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Validated
@RestController
@Tag(name = "飞控平台 - 任务列表")
@RequestMapping("/system/dept/job")
public class JobStatisticController {


    @Resource
    private IJobService jobService;


    @PostMapping(value = "/page")
    @Operation(summary = "任务列表-任务分页")
    public CommonResult<PageResult<JobStatisticReqVO>> jobPage(@RequestBody JobSearchDTO jobSearchInfo) {
        JobPageReqVO jobPageReqVO = BeanUtils.toBean(jobSearchInfo, JobPageReqVO.class);
        jobPageReqVO.setPageNo(jobSearchInfo.getPage());
        jobPageReqVO.setPageSize(jobSearchInfo.getOffset());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String beginTime = jobSearchInfo.getBeginTime();
        if (!StringUtils.isEmpty(beginTime)) {
            jobPageReqVO.setBeginTime(LocalDateTime.parse(beginTime, formatter));
        }
        String endTime = jobSearchInfo.getEndTime();
        if(!StringUtils.isEmpty(endTime)){
            jobPageReqVO.setEndTime(LocalDateTime.parse(endTime, formatter));
        }
        PageResult<JobPageRespVO> jobPageRespInfo = jobService.page(jobPageReqVO);
        List<JobStatisticReqVO> records = jobPageRespInfo.getList().stream().map(jobPageRespVO -> BeanUtils.toBean(jobPageRespVO, JobStatisticReqVO.class)).toList();
        PageResult<JobStatisticReqVO> result = new PageResult<>(records, jobPageRespInfo.getTotal());
        return CommonResult.success(result);
    }

    @PostMapping(value = "/pie")
    public CommonResult<List<PieReqVO>> pieStatistic(@RequestBody PieParamsInfo pieParamsInfo) {
        List<PieReqVO> jobPieInfo = jobService.getJobPieStatistic(pieParamsInfo);
        return CommonResult.success(jobPieInfo);
    }


    @PostMapping(value = "/histogram")
    @Operation(summary = "任务列表-直方图数据")
    public CommonResult<List<HistogramVO>> histogramStatistic(@RequestBody HistogramSearchDTO histogramSearchInfo) {
        List<HistogramVO> result = jobService.getJobHistogramStatistic(histogramSearchInfo);
        return CommonResult.success(result);
    }
}
