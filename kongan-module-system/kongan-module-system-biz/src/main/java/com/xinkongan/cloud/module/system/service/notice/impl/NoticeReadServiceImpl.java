package com.xinkongan.cloud.module.system.service.notice.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeStatisticsVO;
import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeReadDO;
import com.xinkongan.cloud.module.system.dal.mysql.notice.NoticeReadMapper;
import com.xinkongan.cloud.module.system.service.notice.INoticeReadService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 已读消息ServiceImpl
 * <AUTHOR>
 * @Date 2025/3/6 13:54
 */
@Slf4j
@Service
public class NoticeReadServiceImpl implements INoticeReadService {

    @Resource
    private NoticeReadMapper noticeReadMapper;

    @Override
    public Boolean saveNoticeRead(List<NoticeReadDO> noticeReadDOList) {
        return noticeReadMapper.insertBatch(noticeReadDOList);
    }

    @Override
    public Boolean markAsRead(Long userId, List<Long> noticeIds) {
        int update = noticeReadMapper.update(Wrappers.<NoticeReadDO>lambdaUpdate()
                .eq(NoticeReadDO::getUserId, userId)
                .in(NoticeReadDO::getNoticeId, noticeIds)
                .set(NoticeReadDO::getReadFlag, YesNoEnum.YES.getCode()));
        return update > 0;
    }

    @Override
    public void deleteBatchByNoticeIds(List<Long> noticeIds) {
        noticeReadMapper.delete(Wrappers.<NoticeReadDO>lambdaUpdate().in(NoticeReadDO::getNoticeId, noticeIds));
    }

    @Override
    public Long unreadCount(Long userId) {
        return noticeReadMapper.selectCount(Wrappers.<NoticeReadDO>lambdaQuery().eq(NoticeReadDO::getUserId, userId).eq(NoticeReadDO::getReadFlag, YesNoEnum.NO.getCode()));
    }

    @Override
    public NoticeStatisticsVO noticeStatistics(Long userId) {
        return noticeReadMapper.noticeStatistics(userId);
    }
}