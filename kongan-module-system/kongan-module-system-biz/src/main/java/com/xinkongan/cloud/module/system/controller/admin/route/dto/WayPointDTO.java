package com.xinkongan.cloud.module.system.controller.admin.route.dto;


import com.xinkongan.cloud.module.system.dto.WayPointShoutDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class WayPointDTO implements Serializable {

    @Schema(description = "航点名称", example = "航点1")
    private String wayPointName;

    @Schema(description = "航点序号", example = "1")
    private Integer pointIndex;

    @Schema(description = "经度", example = "117.1")
    private Double longitude;

    @Schema(description = "纬度", example = "31.73")
    private Double latitude;

    @Schema(description = "坐标系(1 WGS84,2 Baidu, 3 Gaode)", example = "1")
    private Integer coordinate;

    @Schema(description = "航点飞行高度", example = "100")
    private Float executeHeight;

    @Schema(description = "航点速度", example = "5")
    private Float waypointSpeed;

    @Schema(description = "航点云台俯仰角", example = "45")
    private Float gimbalPitchAngle;

    @Schema(description = "飞行器偏航角模式", example = "followWayline")
    private String waypointHeadingParam;

    @Schema(description = "飞行器偏航角度", example = "0.00")
    private Double waypointHeadingAngle;

    @Schema(description = "飞行器偏航角度是否启用", example = "0")
    private Integer waypointHeadingAngleEnable;

    @Schema(description = "航点转弯模式", example = "coordinateTurn")
    private String waypointTurnMode;

    @Schema(description = "是否使用全局飞行速度", example = "1")
    private Integer useGlobalSpeed;

    @Schema(description = "是否使用全局飞行高度", example = "1")
    private Integer useGlobalHeight;

    @Schema(description = "镜头模式", example = "wide")
    private String lensMode;

    @Schema(description = "航点的高程，仅在相对地面高度使用")
    private Float elevation;

    @Schema(description = "航点喊话信息")
    private List<WayPointShoutDTO> wayPointShouts;

    @Schema(description = "航点动作列表")
    private List<WayPointActionInfoDTO> actions;

    @Schema(description = "航点中配置的算法信息")
    private List<RouteAlgorithmSaveDTO> routeAlgorithmInfos;

    public List<WayPointActionInfoDTO> getActions() {
        if (actions == null) {
            actions = new ArrayList<>();
        }
        return actions;
    }

    public List<RouteAlgorithmSaveDTO> getRouteAlgorithmInfos() {
        if (routeAlgorithmInfos == null) {
            routeAlgorithmInfos = new ArrayList<>();
        }
        return routeAlgorithmInfos;
    }

    public List<WayPointShoutDTO> getWayPointShouts() {
        if (wayPointShouts == null) {
            wayPointShouts = new ArrayList<>();
        }
        return wayPointShouts;
    }
}
