package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
@Data
public class PostureNoticeListVO {

    @Schema(description = "消息ID")
    private Long id;

    @Schema(description = "通知模块1任务模块2机场模块3系统模块")
    private Integer module;

    @Schema(description = "通知类型")
    private String type;

    @Schema(description = "通知类型描述")
    private String typeDesc;

    @Schema(description = "消息详情")
    private String content;

    @Schema(description = "消息时间")
    private LocalDateTime createTime;

}
