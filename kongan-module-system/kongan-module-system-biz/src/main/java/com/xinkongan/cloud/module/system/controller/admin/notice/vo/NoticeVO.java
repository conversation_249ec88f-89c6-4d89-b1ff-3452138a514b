package com.xinkongan.cloud.module.system.controller.admin.notice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description 消息通知
 * <AUTHOR>
 * @Date 2025/3/10 11:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeVO {

    @Schema(description = "消息id")
    private Long id;

    @Schema(description = "通知名称")
    private String name;

    @Schema(description = "通知模块1任务模块2机场模块3系统模块")
    private Integer module;

    @Schema(description = "通知类型")
    private Integer type;

    @Schema(description = "通知类型描述")
    private String typeDesc;

    @Schema(description = "通知内容")
    private String content;

    @Schema(description = "是否需要弹窗提醒")
    private Integer needPop;

    @Schema(description = "通知时间")
    private LocalDateTime createTime;

    @Schema(description = "已读未读 0未读 1已读")
    private Integer readFlag;

    @Schema(description = "任务ID")
    private Long jobId;

    @Schema(description = "任务名称")
    private String jobName;

    @Schema(description = "机场SN")
    private String dockSn;

    @Schema(description = "机场名称")
    private String dockName;

    @Schema(description = "任务申请ID")
    private Long applyId;

}