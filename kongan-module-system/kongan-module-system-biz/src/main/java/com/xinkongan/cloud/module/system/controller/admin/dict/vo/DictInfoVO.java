package com.xinkongan.cloud.module.system.controller.admin.dict.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DictInfoVO {

    @Schema(description = "字典主键")
    private Long id;

    @Schema(description = "字典名称")
    private String name;

    @Schema(description = "字典编码")
    private String code;

    @Schema(description = "字典类型")
    private Integer type;

    @Schema(description = "字典值")
    private String dictValue;
}
