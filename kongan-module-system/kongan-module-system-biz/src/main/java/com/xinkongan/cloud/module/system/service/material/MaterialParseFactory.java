package com.xinkongan.cloud.module.system.service.material;

import com.xinkongan.cloud.framework.common.util.spring.SpringUtils;
import com.xinkongan.cloud.module.system.enums.material.MaterialTypeEnums;
import org.springframework.stereotype.Service;

@Service
public class MaterialParseFactory {

    public IMaterialParseHandler getMaterialParseHandlerByType(Integer type) {
        String materialParseHandler = MaterialTypeEnums.getMaterialParseByType(type);
        assert materialParseHandler != null;
        return SpringUtils.getBean(materialParseHandler, IMaterialParseHandler.class);
    }
}
