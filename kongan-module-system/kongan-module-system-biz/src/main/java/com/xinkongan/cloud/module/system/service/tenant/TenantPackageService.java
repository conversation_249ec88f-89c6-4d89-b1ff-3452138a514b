package com.xinkongan.cloud.module.system.service.tenant;

import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.DeviceNumberVO;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.TenantPackageVO;
import com.xinkongan.cloud.module.system.dal.dataobject.tenant.TenantDO;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
public interface TenantPackageService {

    /**
     * 获取套餐信息及使用情况
     *
     * @return 套餐信息
     */
    TenantPackageVO getPackageInfo();



    /**
     * 获取当前租户的存储占用情况
     *
     * @param tenantId 租户ID
     * @return 存储占用情况
     */
    FileStorageVO getUseStorageCount(Long tenantId);

    /**
     * 获取当前租户的存储占用情况
     *
     * @param tenantDO 租户信息
     * @return 存储占用情况
     */
    FileStorageVO getUseStorageCount(TenantDO tenantDO);

    /**
     * 判断当前租户的存储是否已满
     *
     * @param tenantId 租户id
     * @return 是否已满
     */
    Boolean isStorageFull(Long tenantId);

    /**
     * 判断当前租户的设备是否已满
     *
     * @param tenantId 租户id
     * @return 是否已满
     */
    Boolean isDeviceFull(Long tenantId);

    /**
     * 获取当前租户的设备数量
     *
     * @param tenantDO 租户信息
     * @return 设备数量
     */
    DeviceNumberVO getDeviceNumber(TenantDO tenantDO);

}
