package com.xinkongan.cloud.module.system.service.material;


import com.xinkongan.cloud.module.system.controller.admin.material.vo.PanoConfReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.PanoConfigurationDO;

public interface PanoConfigurationService {

    /**
     * 更新全景配置
     *
     * @param reqVO 全景配置更新请求
     * @return 是否成功
     */
    Boolean updatePanoConf(PanoConfReqVO reqVO);


    PanoConfigurationDO getPanoConf(Long materialId);
}
