package com.xinkongan.cloud.module.system.service.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileStorageVO {

    @Schema(description = "已使用存储空间GB")
    private Double usedStorage;
    @Schema(description = "总存储空间GB")
    private Double totalStorage;

    public Double getRemainderStorageGB() {
        return totalStorage - usedStorage;
    }

    public Double getRemainderStorageMB() {
        return getRemainderStorageGB() * 1024;
    }

    public Double getRemainderStorageKB() {
        return getRemainderStorageMB() * 1024;
    }

    public Boolean isStorageFull() {
        return usedStorage >= totalStorage;
    }

}
