package com.xinkongan.cloud.module.system.controller.admin.task.vo.breakpoint;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 断点续飞请求VO
 * <AUTHOR>
 * @Date 2025/01/20
 */
@Data
@Schema(description = "断点续飞请求VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BreakpointResumeReqVO {

    private Long jobId;
}
