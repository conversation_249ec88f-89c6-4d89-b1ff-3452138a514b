package com.xinkongan.cloud.module.system.service.material;

import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.util.crypto.GmPasswordEncoder;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.MaterialPublicShareDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialDetailVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialShareResultVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialViewShareVO;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialShareDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialShareMapper;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.framework.material.SystemMaterialConfig;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

@Service
public class MaterialShareServiceImpl implements IMaterialShareService {


    @Resource
    private MaterialShareMapper materialShareMapper;

    @Resource
    private SystemMaterialConfig systemMaterialConfig;

    @Resource
    private IMaterialManageService materialManageService;

    @Resource
    private GmPasswordEncoder gmPasswordEncoder;

    @Resource
    private AdminUserService adminUserService;


    @Override
    public MaterialViewShareVO createMaterialShareLink(MaterialPublicShareDTO materialPublicShare) {
        MaterialDetailVO materialInfo = materialManageService.getMaterialInfoById(materialPublicShare.getMaterialId());
        if (materialInfo == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        MaterialShareDO materialShareInfo = BeanUtils.toBean(materialPublicShare, MaterialShareDO.class);
        if (Objects.equals(materialShareInfo.getShareType(), 1)) {
            if (StringUtils.isEmpty(materialShareInfo.getPassword())) {
                throw new ServiceException(ErrorCodeConstants.MATERIAL_SHARE_PASSWORD_EMPTY);
            }
            // 对密码进行加密
            materialShareInfo.setPassword(gmPasswordEncoder.encode(materialShareInfo.getPassword()));
        }
        materialShareMapper.insert(materialShareInfo);
        MaterialViewShareVO materialViewShareVO = BeanUtils.toBean(materialShareInfo, MaterialViewShareVO.class);
        materialViewShareVO.setLinkUrl(systemMaterialConfig.getShareLinkUrl(materialShareInfo.getId()));
        return materialViewShareVO;
    }

    @Override
    public MaterialShareResultVO isPublic(Long shareKey) {
        MaterialShareDO materialShareInfo = materialShareMapper.selectById(shareKey);
        if (materialShareInfo == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_SHARE_RECORD_NOT_FOUND);
        }
        MaterialShareResultVO materialShareResult = MaterialShareResultVO.builder().isPublic(Objects.equals(materialShareInfo.getShareType(), 0)).build();
        if (!StringUtils.isEmpty(materialShareInfo.getCreator())) {
            AdminUserDO creatorInfo = adminUserService.getUser(Long.parseLong(materialShareInfo.getCreator()));
            materialShareResult.setShareCreator(creatorInfo.getNickname());
        }
        return materialShareResult;
    }

    @Override
    public MaterialViewShareVO getShareInfoByShareKey(Long shareKey) {
        MaterialShareDO materialShareInfo = materialShareMapper.selectById(shareKey);
        if (materialShareInfo == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_SHARE_RECORD_NOT_FOUND);
        }
        MaterialViewShareVO materialInfo = BeanUtils.toBean(materialShareInfo, MaterialViewShareVO.class);

        LocalDateTime createTime = materialShareInfo.getCreateTime();
        Date createDateTime = Date.from(createTime.atZone(ZoneId.systemDefault()).toInstant());

        materialInfo.setCreateTime(createDateTime);
        return materialInfo;
    }
}
