package com.xinkongan.cloud.module.system.controller.admin.dock.vo.device;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DockExecJobRanking {

    @Schema(description = "机场名称")
    private String dockName;

    @Schema(description = "机场SN")
    private String dockSn;

    @Schema(description = "执行任务数")
    private Long jobCount;

}
