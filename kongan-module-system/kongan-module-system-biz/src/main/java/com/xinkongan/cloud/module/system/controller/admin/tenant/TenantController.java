package com.xinkongan.cloud.module.system.controller.admin.tenant;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.util.crypto.RSAPasswordEncoder;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.env.config.SystemEnv;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.MqttConnectVO;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.TenantRegisterReqVO;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.tenant.TenantDO;
import com.xinkongan.cloud.module.system.enums.sms.SmsSceneEnum;
import com.xinkongan.cloud.module.system.framework.emqx.core.property.MqttGatewayOptions;
import com.xinkongan.cloud.module.system.service.sms.SmsCodeService;
import com.xinkongan.cloud.module.system.service.tenant.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;
import static com.xinkongan.cloud.framework.common.util.servlet.ServletUtils.getClientIP;

@Tag(name = "管理后台 - 租户")
@RestController
@RequestMapping("/system/tenant")
public class TenantController {

    @Resource
    private TenantService tenantService;

    @Resource
    private SmsCodeService smsCodeService;

    @Resource
    private RSAPasswordEncoder rsaPasswordEncoder;

    @Resource
    private SystemEnv systemEnv;

    @Resource
    private MqttGatewayOptions mqttGatewayOptions;


    @GetMapping("/get")
    @Operation(summary = "获得租户信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<TenantRespVO> getTenant(@RequestParam("id") Long id) {
        TenantDO tenant = tenantService.getTenant(id);
        return success(BeanUtils.toBean(tenant, TenantRespVO.class));
    }


    @PermitAll
    @PostMapping("/register")
    @Operation(summary = "租户注册")
    public CommonResult<Long> tenantRegister(@RequestBody @Valid TenantRegisterReqVO tenantRegisterReq) {
        // 校验验证码,开发环境不用校验
        if (!systemEnv.isDev()) {
            // 校验验证码
            smsCodeService.useSmsCode(
                    SmsCodeUseReqDTO.builder()
                            .mobile(tenantRegisterReq.getMobile())
                            .code(tenantRegisterReq.getVerificationCode())
                            .scene(SmsSceneEnum.BUSINESS_TENANT_REGISTER.getScene())
                            .usedIp(getClientIP())
                            .build()
            );
        }
        // 对用户名和密码进行解密
        tenantRegisterReq.setUsername(rsaPasswordEncoder.decode(tenantRegisterReq.getUsername()));
        tenantRegisterReq.setPassword(rsaPasswordEncoder.decode(tenantRegisterReq.getPassword()));

        Long tenantId = tenantService.registerTenant(tenantRegisterReq);
        return CommonResult.success(tenantId);
    }

    @GetMapping("/getMqttConnect")
    @Operation(summary = "获取MQTT连接信息")
    @PreAuthorize("@ss.hasPermission('system:dock:register')")
    public CommonResult<MqttConnectVO> getMqttConnect() {
        TenantDO tenant = tenantService.getTenant(TenantContextHolder.getTenantId());
        MqttConnectVO mqttConnectVO = MqttConnectVO.builder()
                .gatewayUrl(mqttGatewayOptions.getUrl())
                .username(tenant.getEmqxUsername())
                .password(tenant.getEmqxPassword())
                .build();
        return CommonResult.success(mqttConnectVO);
    }

    @PermitAll
    @GetMapping("/check/repeat")
    @Operation(summary = "租户-租户名称判重")
    public CommonResult<Boolean> checkTenantNameRepeat(@RequestParam String tenantName,
                                                       @RequestParam(required = false) Long id) {
        Boolean res = tenantService.checkTenantNameRepeat(id, tenantName);
        return CommonResult.success(res);
    }
}
