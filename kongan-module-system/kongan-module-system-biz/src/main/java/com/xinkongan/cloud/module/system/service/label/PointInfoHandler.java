package com.xinkongan.cloud.module.system.service.label;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.module.system.dto.PointInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.util.CollectionUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;


public class PointInfoHandler extends BaseTypeHandler<List<PointInfoDTO>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<PointInfoDTO> points, JdbcType jdbcType) throws SQLException {
        String val = "";
        if (points != null && !CollectionUtils.isEmpty(points)) {
            val = JSONObject.toJSONString(points);
        }
        ps.setString(i, val);
    }

    @Override
    public List<PointInfoDTO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String data = rs.getString(columnName);
        if (StringUtils.isBlank(data)) {
            return new ArrayList<>();
        }
        return JSONArray.parseArray(data, PointInfoDTO.class);
    }

    @Override
    public List<PointInfoDTO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String data = rs.getString(columnIndex);
        if (StringUtils.isBlank(data)) {
            return new ArrayList<>();
        }
        return JSONArray.parseArray(data, PointInfoDTO.class);
    }

    @Override
    public List<PointInfoDTO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String data = cs.getString(columnIndex);
        if (StringUtils.isBlank(data)) {
            return new ArrayList<>();
        }
        return JSONArray.parseArray(data, PointInfoDTO.class);
    }
}
