package com.xinkongan.cloud.module.system.service.posture;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.common.pojo.PageParam;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.mybatis.core.util.MyBatisUtils;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.DroneFlyPoint;
import com.xinkongan.cloud.module.system.controller.admin.posture.PostureDateParam;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.*;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteDetailRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyDO;
import com.xinkongan.cloud.module.system.dal.mysql.posture.PostureMapper;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import com.xinkongan.cloud.module.system.service.route.IRouteService;
import com.xinkongan.cloud.module.system.service.task.IJobFlyService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
@Slf4j
@Service
public class PostureServiceImpl implements IPostureService {

    @Resource
    private DeptService deptService;

    @Resource
    private PostureMapper postureMapper;

    @Resource
    private DockDeviceService dockDeviceService;

    @Resource
    private IRouteService routeService;

    @Resource
    private IJobService jobService;

    @Resource
    private IJobFlyService jobFlyService;

    @Resource
    private IFlyRecordService flyRecordService;

    @Override
    public boolean verifyOrganizationList(Set<Long> deptIdSet) {
        // 先检查maxIdSet和deptIdSet是否为空，避免NullPointerException
        // 获取当前用户权限下所有的组织ID集合
        if (deptIdSet == null || deptIdSet.isEmpty()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_ORGANIZATION_LIST_IS_NOT_VALID);
        }
        Set<Long> maxIdSet = deptService.getDeptList().stream().map(DeptDO::getId).collect(Collectors.toSet());
        // 检查传入的部门ID集合（deptIdSet）是否完全包含在所有部门的ID集合中。
        return maxIdSet.containsAll(deptIdSet);
    }

    @Override
    public PageResult<MixedJobPageResp> mixedJob(PostureParamPage page) {
        if (!verifyOrganizationList(page.getDeptIds())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_ORGANIZATION_LIST_IS_NOT_VALID);
        }
        log.info("[态势大屏] 混合任务 分页查询 参数:{}", page);
        IPage<MixedJobPageResp> mpPage = MyBatisUtils.buildPage(page);
        IPage<MixedJobPageResp> pageRes = postureMapper.mixedJobPage(mpPage, page);
        if (pageRes.getRecords().isEmpty()) {
            return PageResult.empty();
        }
        return new PageResult<>(pageRes.getRecords(), pageRes.getTotal());
    }

    @Override
    public JobSceneNumberVO jobSceneNumber(PostureParam param) {
        return postureMapper.jobSceneNumber(param);
    }

    @Override
    public List<JobNumberTrendVO> jobNumberTrend(PostureParam param) {
        return postureMapper.jobNumberTrend(param);
    }

    @Override
    public MultipleDeviceVO multipleDeviceList(PostureParam param) {
        // 查询机场设备列表
        List<PostureDeviceVO> dockList = dockDeviceService.getDeviceListByDeptIds(param.getDeptIds());
        // TODO 查询手飞无人机设备列表

        // TODO 查询RTMP设备列表

        return MultipleDeviceVO.builder().dockList(dockList).build();
    }

    @Override
    public DockRouteAndFlyRecordRespVO getDockRouteAndFlyRecord(String dockSn) {
        JobFlyInfo jobInfo = jobService.getExecTaskCacheByDockSn(dockSn);
        DockRouteAndFlyRecordRespVO.DockRouteAndFlyRecordRespVOBuilder builder = DockRouteAndFlyRecordRespVO.builder();
        if (jobInfo == null) {
            return builder.build();
        }
        // 查询当前飞行是否在当前用户数据权限内
        JobFlyDO jobFlyDO = jobFlyService.getJobFlyById(jobInfo.getFlyId());
        if (jobFlyDO == null) {
            // 如果数据权限内查询不到飞行信息，则直接返回空
            return builder.build();
        }
        if (jobInfo.getRouteId() != null) {
            RouteDetailRespVO routeDetail = routeService.getRouteDetailById(jobInfo.getRouteId());
            builder.routeDetail(routeDetail);
        }
        if (jobInfo.getFlyRecordId() != null) {
            List<DroneFlyPoint> flyPointList = flyRecordService.getDroneFlyPoints(jobInfo.getFlyRecordId());
            builder.flyPointList(flyPointList);
        }
        return builder.build();
    }

    @Override
    public List<DeviceRankVO> deviceRank(FlightRankParam param) {
        if (!verifyOrganizationList(param.getDeptIds())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_ORGANIZATION_LIST_IS_NOT_VALID);
        }
        return postureMapper.deviceRank(param);
    }

    @Override
    public DeviceRankVO deviceFlyStatistics(PostureDateParam param) {
        if (!verifyOrganizationList(param.getDeptIds())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_ORGANIZATION_LIST_IS_NOT_VALID);
        }
        return postureMapper.deviceFlyStatistics(param);
    }

    @Override
    public PageResult<PostureAlarmListVO> alertListPage(PostureParamPage param) {
        if (!verifyOrganizationList(param.getDeptIds())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_ORGANIZATION_LIST_IS_NOT_VALID);
        }
        IPage<PostureAlarmListVO> page = MyBatisUtils.buildPage(param);
        IPage<PostureAlarmListVO> pageRes = postureMapper.alertListPage(page, param);
        if (pageRes.getRecords().isEmpty()) {
            return PageResult.empty();
        }
        return new PageResult<>(pageRes.getRecords(), pageRes.getTotal());
    }

    @Override
    public PageResult<PostureNoticeListVO> noticeListPage(PageParam param) {
        IPage<PostureNoticeListVO> page = MyBatisUtils.buildPage(param);
        IPage<PostureNoticeListVO> pageRes = postureMapper.noticeListPage(page, param, SecurityFrameworkUtils.getLoginUserId());
        if (pageRes.getRecords().isEmpty()) {
            return PageResult.empty();
        }
        return new PageResult<>(pageRes.getRecords(), pageRes.getTotal());
    }
}
