package com.xinkongan.cloud.module.system.controller.admin.material;


import cn.hutool.core.collection.CollectionUtil;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.api.material.MaterialLoadingStatusDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.*;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.*;
import com.xinkongan.cloud.module.system.service.material.IMaterialManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Validated
@RestController
@Tag(name = "飞控平台 - 素材管理")
@RequestMapping("/system/material/{materialType}")
public class MaterialController {


    @Resource
    private IMaterialManageService materialManageService;


    @PostMapping(value = "/load")
    @Operation(summary = "素材管理-素材导入")
    @PreAuthorize("@ss.hasPermission('system:material:load:' + #materialType)")
    public CommonResult<Long> materialLoad(@PathVariable String materialType,
                                           @RequestBody MaterialLoadVO materialLoad) {
        Long materialId = materialManageService.materialLoad(materialLoad);
        return CommonResult.success(materialId);
    }


    @GetMapping(value = "/del/{materialId}")
    @Operation(summary = "素材管理-素材删除")
    @PreAuthorize("@ss.hasPermission('system:material:del:'  + #materialType)")
    public CommonResult<Void> materialDel(@PathVariable String materialType, @PathVariable Long materialId) {
        materialManageService.delMaterialById(materialId);
        return CommonResult.success();
    }

    @PostMapping(value = "/update")
    @Operation(summary = "素材管理-素材修改")
    @PreAuthorize("@ss.hasPermission('system:material:update:'  + #materialType)")
    public CommonResult<Long> updateMaterialInfo(@PathVariable String materialType, @RequestBody MaterialUpdateDTO materialUpdateInfo) {
        Long materialId = materialManageService.updateMaterialInfo(materialUpdateInfo);
        return CommonResult.success(materialId);
    }


    @PostMapping(value = "/thumbnail/create")
    @Operation(summary = "素材管理-缩略图创建")
    public CommonResult<Void> thumbnailCreate(@RequestBody MaterialThumbnailDTO materialThumbnail) {
        materialManageService.updateMaterialThumbnailInfo(materialThumbnail);
        return CommonResult.success();
    }


    @GetMapping(value = "/get/{materialId}")
    @Operation(summary = "素材管理-素材详情")
    @PreAuthorize("@ss.hasPermission('system:material:query:'  + #materialType)")
    public CommonResult<MaterialDetailVO> getMaterialDetailById(@PathVariable String materialType, @PathVariable Long materialId) {
        MaterialDetailVO materialInfo = materialManageService.getMaterialInfoById(materialId);
        return CommonResult.success(materialInfo);
    }


    @GetMapping(value = "/page")
    @Operation(summary = "素材管理-素材分页")
    @PreAuthorize("@ss.hasPermission('system:material:query:' + #materialType)")
    public CommonResult<PageResult<MaterialInfoVO>> materialPage(@PathVariable String materialType, MaterialSearchDTO materialSearchInfo) {
        PageResult<MaterialInfoVO> materialPageResult = materialManageService.getMaterialPage(materialSearchInfo);
        return CommonResult.success(materialPageResult);
    }


    @GetMapping(value = "/pageByDeptId")
    @Operation(summary = "素材管理-素材分页")
    @PreAuthorize("@ss.hasPermission('system:material:query:' + #materialType)")
    public CommonResult<PageResult<MaterialInfoVO>> materialPageDeptId(@PathVariable String materialType, MaterialSearchDTO materialSearchInfo) {
        PageResult<MaterialInfoVO> materialPageResult = materialManageService.selectMaterialByPageDeptId(materialSearchInfo);
        return CommonResult.success(materialPageResult);
    }


    @GetMapping(value = "/list")
    @Operation(summary = "素材管理-素材列表")
    public CommonResult<List<MaterialInfoVO>> materialList(@PathVariable String materialType, MaterialSearchDTO materialSearchInfo) {
        List<MaterialInfoVO> materialList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(materialSearchInfo.getTypes())) {
            for (Integer type : materialSearchInfo.getTypes()) {
                materialSearchInfo.setType(type);
                materialList.addAll(materialManageService.getMaterialList(materialSearchInfo));
            }
            return CommonResult.success(materialList);
        }
        materialList = materialManageService.getMaterialList(materialSearchInfo);
        return CommonResult.success(materialList);
    }


    @GetMapping("/getPanoByConf/{materialId}")
    @Operation(summary = "素材管理-全景图查询")
    public CommonResult<List<MaterialInfoVO>> getPanoByConf(@PathVariable(value = "materialId") Long materialId) {
        List<MaterialInfoVO> materials = materialManageService.getPanoByConf(materialId);
        return CommonResult.success(materials);
    }


    @PostMapping(value = "/share")
    @Operation(summary = "素材分享-保存分享")
    @PreAuthorize("@ss.hasPermission('system:material:share:save:' + #materialType)")
    public CommonResult<Void> materialShare(@PathVariable String materialType, @RequestBody @Valid MaterialShareDTO materialShare) {
        materialManageService.materialShare(materialShare);
        return CommonResult.success();
    }


    @GetMapping(value = "/share/list/{material}")
    @Operation(summary = "素材分享-分享列表")
    @PreAuthorize("@ss.hasPermission('system:material:share:query:' + #materialType)")
    public CommonResult<MaterialShareVO> getRouteShareDept(@PathVariable String materialType, @PathVariable Long material) {
        MaterialShareVO routeShareDept = materialManageService.getMaterialShareDept(material);
        return CommonResult.success(routeShareDept);
    }


    @GetMapping(value = "/contrast/list")
    @Operation(summary = "素材对比-对比列表")
    @PreAuthorize("@ss.hasPermission('system:material:contrast:query:' + #materialType)")
    public CommonResult<PageResult<MaterialContrastRecordVO>> getMaterialContrastRecordList(@PathVariable String materialType,
                                                                                            @Valid MaterialContrastSearchDTO materialContrastRecordSearch) {
        PageResult<MaterialContrastRecordVO> materialContrastRecord = materialManageService.getMaterialContrastRecord(materialContrastRecordSearch);
        return CommonResult.success(materialContrastRecord);
    }


    @GetMapping(value = "/contrast/{contrastId}")
    @Operation(summary = "素材对比-对比详情")
    @PreAuthorize("@ss.hasPermission('system:material:contrast:query:' + #materialType)")
    public CommonResult<MaterialContrastRecordDetailVO> getMaterialContrastDetail(@PathVariable String materialType,
                                                                                  @PathVariable Long contrastId) {
        MaterialContrastRecordDetailVO materialContrastRecordDetail = materialManageService.getMaterialContrastRecordDetail(contrastId);
        return CommonResult.success(materialContrastRecordDetail);
    }


    @PostMapping(value = "/contrast/save")
    @Operation(summary = "素材对比-保存对比")
    @PreAuthorize("@ss.hasPermission('system:material:contrast:save:' + #materialType)")
    public CommonResult<Long> saveMaterialContrastRecord(@PathVariable String materialType,
                                                         @Valid @RequestBody MaterialContrastSaveDTO materialContrastRecordSearch) {
        Long recordId = materialManageService.saveMaterialContrastRecord(materialContrastRecordSearch);
        return CommonResult.success(recordId);
    }


    @GetMapping(value = "/contrast/del/{contrastId}")
    @Operation(summary = "素材对比-删除对比")
    @PreAuthorize("@ss.hasPermission('system:material:contrast:del:' + #materialType)")
    public CommonResult<Void> delMaterialContrastRecord(@PathVariable String materialType,
                                                        @PathVariable Long contrastId) {
        materialManageService.delMaterialContrastRecord(contrastId);
        return CommonResult.success();
    }


    @PostMapping(value = "/contrast/update")
    @Operation(summary = "素材对比-修改对比")
    @PreAuthorize("@ss.hasPermission('system:material:contrast:update:' + #materialType)")
    public CommonResult<Long> updateMaterialContrastRecord(@PathVariable String materialType,
                                                           @Valid @RequestBody MaterialContrastUpdateDTO materialContrastUpdateInfo) {
        Long recordId = materialManageService.updateMaterialContrastRecord(materialContrastUpdateInfo);
        return CommonResult.success(recordId);
    }


    @GetMapping(value = "/contrast/compare/list")
    @Operation(summary = "素材对比-对比数据栏")
    @PreAuthorize("@ss.hasPermission('system:material:contrast:query:' + #materialType)")
    public CommonResult<List<MaterialInfoVO>> getMaterialCompareList(@PathVariable String materialType,
                                                                     @Valid MaterialCompareSearchDTO materialCompareSearch) {
        List<MaterialInfoVO> materialCompareRecord = materialManageService.getMaterialCompareRecordList(materialCompareSearch);
        return CommonResult.success(materialCompareRecord);
    }

    /**
     * 修改加载状态
     **/
    @PostMapping(value = "/updateLoadingStatus")
    @Operation(summary = "修改加载状态")
    public CommonResult<Boolean> updateLoadingStatus(@PathVariable String materialType, @RequestBody MaterialLoadingStatusDTO materialLoadingStatusDTO) {
        materialManageService.updateLoadingStatus(materialLoadingStatusDTO);
        return CommonResult.success(Boolean.TRUE);
    }

}
