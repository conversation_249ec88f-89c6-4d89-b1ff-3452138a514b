package com.xinkongan.cloud.module.system.controller.admin.user;

import cn.hutool.core.collection.CollUtil;
import com.xinkongan.cloud.framework.common.enums.UserTypeEnum;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.profile.UserProfileRespVO;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import com.xinkongan.cloud.module.system.convert.user.UserConvert;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.PostDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;
import com.xinkongan.cloud.module.system.dal.dataobject.social.SocialUserDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.dept.PostService;
import com.xinkongan.cloud.module.system.service.permission.PermissionService;
import com.xinkongan.cloud.module.system.service.permission.RoleService;
import com.xinkongan.cloud.module.system.service.social.SocialUserService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;
import static com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.FILE_IS_EMPTY;

@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 用户个人中心")
@RequestMapping("/system/user/profile")
public class UserProfileController {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RoleService roleService;
    @Resource
    private SocialUserService socialService;
    @Resource
    private IRedisCacheService redisCacheService;


    @GetMapping("/folder")
    @Operation(summary = "获取用户面板折叠配置")
    public CommonResult<Boolean> folderTopTab() {
        String folderCacheKey = RedisKeyConstants.USER_PROFILE_FOLDER_TOP_TAB_PREFIX + getLoginUserId();
        Boolean folderFlag = (Boolean) redisCacheService.get(folderCacheKey);
        if (folderFlag == null) {
            folderFlag = Boolean.TRUE;
        }
        return CommonResult.success(folderFlag);
    }

    @GetMapping("/set")
    @Operation(summary = "用户面板折叠配置")
    public CommonResult<Void> setFolderTopTab(@RequestParam Boolean folderFlag) {
        String folderCacheKey = RedisKeyConstants.USER_PROFILE_FOLDER_TOP_TAB_PREFIX + getLoginUserId();
        redisCacheService.put(folderCacheKey, folderFlag);
        return CommonResult.success();
    }


    @GetMapping("/get")
    @Operation(summary = "获得登录用户信息")
    @DataPermission(enable = false) // 关闭数据权限，避免只查看自己时，查询不到部门。
    public CommonResult<UserProfileRespVO> getUserProfile() {
        // 获得用户基本信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        // 获得用户角色
        List<RoleDO> userRoles = roleService.getRoleListByIds(permissionService.getUserRoleIdListByUserId(user.getId()));
        // 获得部门信息
        DeptDO dept = user.getDeptId() != null ? deptService.getDept(user.getDeptId()) : null;
        // 获得岗位信息
        List<PostDO> posts = CollUtil.isNotEmpty(user.getPostIds()) ? postService.getPostList(user.getPostIds()) : null;
        // 获得社交用户信息
        List<SocialUserDO> socialUsers = socialService.getSocialUserList(user.getId(), UserTypeEnum.ADMIN.getValue());
        return success(UserConvert.INSTANCE.convert(user, userRoles, dept, posts, socialUsers));
    }

    @PutMapping("/update")
    @Operation(summary = "修改用户个人信息")
    public CommonResult<Boolean> updateUserProfile(@Valid @RequestBody UserProfileUpdateReqVO reqVO) {
        userService.updateUserProfile(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/update-password")
    @Operation(summary = "修改用户个人密码")
    public CommonResult<Boolean> updateUserProfilePassword(@Valid @RequestBody UserProfileUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(getLoginUserId(), reqVO);
        return success(true);
    }

    @RequestMapping(value = "/update-avatar",
            method = {RequestMethod.POST, RequestMethod.PUT}) // 解决 uni-app 不支持 Put 上传文件的问题
    @Operation(summary = "上传用户个人头像")
    public CommonResult<String> updateUserAvatar(@RequestParam("avatarFile") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw exception(FILE_IS_EMPTY);
        }
        String avatar = userService.updateUserAvatar(getLoginUserId(), file.getInputStream());
        return success(avatar);
    }
}
