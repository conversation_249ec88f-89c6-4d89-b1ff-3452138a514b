package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteAlgorithmSaveDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.WayPointActionInfoDTO;
import com.xinkongan.cloud.module.system.dto.WayPointShoutDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 修改接警配置ReqVO
 * <AUTHOR>
 * @Date 2025/3/19 14:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "修改接警配置ReqVO")
public class UpdateAlarmConfigReqVO {

    @Schema(description = "警情场景id")
    @NotNull(message = "警情场景id不能为空")
    private Long id;

    @Schema(description = "警情场景")
    private String alarmScene;

    @Schema(description = "是否自动执行")
    private Integer autoExecute;

    @Schema(description = "过期时间")
    @Max(value = 240, message = "过期时间不能超过240小时")
    @Min(value = 1, message = "过期时间不能小于1小时")
    private Integer expireTime;

    @Schema(description = "算法开启距离")
    private Integer algorithmRadius;

    @Schema(description = "算法开启后飞行高度")
    private Integer algorithmHeight;

    @Schema(description = "算法开启后飞行速度")
    private Integer algorithmSpeed;

    @Schema(description = "算法开启后云台俯仰角")
    private Integer algorithmPitchAngle;

    @Schema(description = "接警动作")
    private List<WayPointActionInfoDTO> action;

    @Schema(description = "照片存储类型", example = "[\"wide\", \"zoom\", \"ir\"]")
    private List<String> imageFormat;

    @Schema(description = "航点喊话信息")
    private List<WayPointShoutDTO> wayPointShouts;

    @Schema(description = "航点中配置的算法信息")
    private List<RouteAlgorithmSaveDTO> routeAlgorithmInfos;
}