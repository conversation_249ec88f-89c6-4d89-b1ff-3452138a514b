package com.xinkongan.cloud.module.system.controller.admin.dict.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class DictInfoDTO {

    @Schema(description = "字典主键")
    private Long id;

    @Schema(description = "字典名称")
    @NotBlank(message = "字典名称不能为空")
    private String name;

    @Schema(description = "字典编码")
    private String code;

    @Schema(description = "字典类型")
    private Integer type;

    @Schema(description = "字典值")
    private String dictValue;

}
