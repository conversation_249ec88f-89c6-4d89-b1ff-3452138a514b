package com.xinkongan.cloud.module.system.controller.admin.user;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.user.*;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 用户")
@RestController
@RequestMapping("/system/user")
@Validated
public class UserController {

    @Resource
    private AdminUserService userService;

    @PostMapping("/create")
    @Operation(summary = "新增用户")
    @PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<UserRespVO> createUser(@Valid @RequestBody UserSaveReqVO reqVO) {
        Long id = userService.createUser(reqVO);
        // 查询用户信息，方便前端显示树节点，避免刷新
        UserRespVO userRespInfo = userService.getUserById(id);
        return success(userRespInfo);
    }

    @PutMapping("update")
    @Operation(summary = "修改用户")
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody UserSaveReqVO reqVO) {
        userService.updateUser(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        userService.deleteUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<UserRespVO> getUser(@RequestParam("id") Long id) {
        UserRespVO userRespInfo = userService.getUserById(id);
        return CommonResult.success(userRespInfo);
    }


    @GetMapping(value = "/check/mobile/repeat/{mobileNum}/{userId}")
    @Operation(summary = "查询手机号是否已经注册", description = "查询手机号是否已经注册")
    @PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<Boolean> mobileCheckRepeat(@PathVariable String mobileNum, @PathVariable Long userId) {
        // 忽略租户
        AtomicReference<Boolean> atomicReference = new AtomicReference<>();
        TenantUtils.executeIgnore(() -> atomicReference.set(userService.checkMobileRepeat(userId, mobileNum)));
        return success(atomicReference.get());
    }


    @PostMapping("/tree")
    @Operation(summary = "获取部门用户信息-树结构")
    @PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<List<BaseTreeNode>> getDeptUserTree(@RequestBody UserSearchReqVO userSearchReq) {
        List<BaseTreeNode> deptUserTreeInfo = userService.getDeptUserTreeInfo(userSearchReq);
        return success(deptUserTreeInfo);
    }

    @PostMapping("/move")
    @Operation(summary = "用户列表移动操作-用户移动操作")
    @PreAuthorize("@ss.hasPermission('system:user:move')")
    public CommonResult<Void> moveUserTreeNodePosition(@RequestBody UserMoveReqVO userMoveParam) {
        userService.userMove(userMoveParam);
        return success();
    }

    @GetMapping("/getListByDeptId")
    @Operation(summary = "根据部门ID获取用户列表")
    @PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<List<UserRespVO>> getListByDeptId() {
        return CommonResult.success(userService.getListByDeptId(SecurityFrameworkUtils.getLoginUserDeptId()));
    }

    @GetMapping("/list")
    @Operation(summary = "获取权限内的用户列表")
    public CommonResult<List<UserIdNicknameVO>> list(Long deptId) {
        List<UserIdNicknameVO> list = userService.list(deptId);
        return CommonResult.success(list);
    }

    @PermitAll
    @GetMapping("/check/repeat")
    @Operation(summary = "用户-用户名查重")
    public CommonResult<Boolean> checkUsernameRepeat(@RequestParam String username,
                                                     @RequestParam(required = false) Long userId) {
        boolean res = userService.checkUsernameRepeat(userId, username);
        return CommonResult.success(res);
    }
}
