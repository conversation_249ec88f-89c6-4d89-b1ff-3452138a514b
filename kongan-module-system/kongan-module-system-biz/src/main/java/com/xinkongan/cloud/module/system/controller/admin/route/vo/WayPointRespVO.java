package com.xinkongan.cloud.module.system.controller.admin.route.vo;

import com.xinkongan.cloud.module.system.dto.WayPointShoutDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class WayPointRespVO {

    @Schema(description = "航点id", example = "111")
    private Long id;

    @Schema(description = "航点名称", example = "xx航点")
    private String wayPointName;

    @Schema(description = "航点序号", example = "1")
    private Integer pointIndex;

    @Schema(description = "航线序号", example = "1")
    private Integer waylineId;

    @Schema(description = "经度", example = "117.1")
    private Double longitude;

    @Schema(description = "纬度", example = "31.73")
    private Double latitude;

    @Schema(description = "航点飞行高度", example = "10")
    private Float executeHeight;

    @Schema(description = "航点云台俯仰角", example = "10")
    private Float gimbalPitchAngle;

    @Schema(description = "飞行器偏航角模式")
    private String waypointHeadingParam;

    @Schema(description = "航点速度", example = "10")
    private Float waypointSpeed;

    @Schema(description = "是否使用全局飞行速度", example = "1")
    private Integer useGlobalSpeed;

    @Schema(description = "是否使用全局飞行高度", example = "1")
    private Integer useGlobalHeight;

    @Schema(description = "镜头模式", example = "wide")
    private String lensMode;

    @Schema(description = "坐标系(1 WGS84,2 Baidu, 3 Gaode)", example = "1")
    private Integer coordinate;

    @Schema(description = "航点的高程，仅在相对地面高度使用")
    private Float elevation;

    @Schema(description = "航点喊话信息")
    private List<WayPointShoutVO> wayPointShouts;

    @Schema(description = "航点动作列表", example = "1")
    private List<WayPointActionRespVO> actions;

    @Schema(description = "航段动作列表", example = "[xx]")
    private List<WayPointActionRespVO> legActions;

    @Schema(description = "航点算法列表", example = "[xx]")
    private List<RouteAlgorithmVO> routeAlgorithms;

    public List<WayPointActionRespVO> getActions() {
        if (actions == null) {
            actions = new ArrayList<>();
        }
        return actions;
    }

    public List<WayPointActionRespVO> getLegActions() {
        if (legActions == null) {
            legActions = new ArrayList<>();
        }
        return legActions;
    }

    public List<RouteAlgorithmVO> getRouteAlgorithms() {
        if (routeAlgorithms == null) {
            routeAlgorithms = new ArrayList<>();
        }
        return routeAlgorithms;
    }

    public List<WayPointShoutVO> getWayPointShouts() {
        if (wayPointShouts == null) {
            wayPointShouts = new ArrayList<>();
        }
        return wayPointShouts;
    }
}
