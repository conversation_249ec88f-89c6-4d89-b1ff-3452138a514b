package com.xinkongan.cloud.module.system.controller.admin.material.dto;

import com.xinkongan.cloud.framework.common.dto.SearchDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MaterialContrastSearchDTO extends SearchDTO {

    @Schema(description = "素材id")
    private Long materialId;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;
}
