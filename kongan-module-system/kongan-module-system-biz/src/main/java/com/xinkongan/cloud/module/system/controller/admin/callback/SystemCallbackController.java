package com.xinkongan.cloud.module.system.controller.admin.callback;

import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.api.callback.dto.SystemCallbackDTO;
import com.xinkongan.cloud.module.system.service.callback.ISystemCallbackService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description 系统回调接口
 * <AUTHOR>
 * @Date 2025/5/22 16:20
 */
@Tag(name = "管理后台 - 系统回调接口")
@RestController
@RequestMapping("/system/callback")
@Validated
@Slf4j
public class SystemCallbackController {

    @Resource
    private ISystemCallbackService systemCallbackService;

    /**
     * 系统回调
     */
    @PermitAll
    @PostMapping("/post")
    public CommonResult<?> post(@Valid @RequestBody SystemCallbackDTO systemCallbackDTO) {
        log.info("系统回调参数:{}", JSONUtil.toJsonStr(systemCallbackDTO));
        Object res = systemCallbackService.callback(systemCallbackDTO);
        return CommonResult.success(res);
    }
}