package com.xinkongan.cloud.module.system.controller.admin.notice.vo;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 消息通知分页请求对象
 * <AUTHOR>
 * @Date 2025/3/10 11:14
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "消息通知分页 ReqVO")
public class NoticePageReqVO extends PageParam {

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @Schema(description = "通知类型 1飞行执行失败 2任务审批 3机场共享 4机场接入 5安全告警 6机场通知 7机场提醒 8机场警告 9系统公告 10 套餐不足提醒")
    private List<Integer> types;

    @Schema(description = "已读未读 0未读 1已读")
    private List<Integer> readFlags;

    @Schema(description = "搜索关键字")
    private String searchKey;
}