package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import com.xinkongan.cloud.module.system.enums.task.TaskSceneTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 飞行记录分页请求
 * <AUTHOR>
 * @Date 2025/2/13 11:03
 */
@Schema(description = "飞行记录分页请求")
@Data
public class FlyRecordPageReqVO extends PageParam {

    @Schema(description = "搜索时间段(任务执行时间)开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime beginTime;

    @Schema(description = "搜索时间段(任务执行时间)结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @Schema(description = "机场sn")
    private List<String> dockSn;

    /**
     * {@link TaskSceneTypeEnum}
     *
     * <AUTHOR>
     * @date 2025/2/13 11:06
     **/
    @Schema(description = "任务类型列表 0巡检1建模2接警")
    private List<Integer> scene;

    @Schema(description = "飞行名称/所属组织名称 模糊查询")
    private String searchKey;

    @Schema(description = "重要程度 1重要0普通", example = "1")
    private List<Integer> importantLevel;

    @Schema(description = "排除任务id")
    private Long excludeJobId;

}