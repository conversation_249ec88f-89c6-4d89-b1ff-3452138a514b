package com.xinkongan.cloud.module.system.service.material;

import cn.hutool.core.util.ObjectUtil;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.PanoConfReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.PanoConfigurationDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.PanoConfigurationMapper;
import com.xinkongan.cloud.module.system.enums.material.PanoConfTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


@Service
public class PanoConfigurationServiceImpl implements PanoConfigurationService {

    @Resource
    private PanoConfigurationMapper panoConfigurationMapper;

    @Override
    public Boolean updatePanoConf(PanoConfReqVO reqVO) {
        Long tenantId = TenantContextHolder.getTenantId();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        PanoConfigurationDO panoConf = panoConfigurationMapper.selectOne(
                new LambdaQueryWrapperX<PanoConfigurationDO>()
                        .eq(reqVO.getMaterialId() != null, PanoConfigurationDO::getMaterialId, reqVO.getMaterialId())
                        .eq(PanoConfigurationDO::getDeptId, deptId)
                        .eq(PanoConfigurationDO::getTenantId, tenantId)
                        .isNull(reqVO.getMaterialId() == null, PanoConfigurationDO::getMaterialId)
        );
        if (panoConf == null) {
            panoConf = new PanoConfigurationDO();
            panoConf.setMaterialId(reqVO.getMaterialId());
            panoConf.setType(reqVO.getType());
            panoConf.setVisualRange(reqVO.getVisualRange());
            panoConf.setDeptId(deptId);
            panoConf.setTenantId(tenantId);
            return panoConfigurationMapper.insert(panoConf) > 0;
        }
        panoConf.setType(reqVO.getType());
        panoConf.setVisualRange(reqVO.getVisualRange());
        return panoConfigurationMapper.updateById(panoConf) > 0;
    }

    private PanoConfigurationDO verifyPanoConf(Long materialId) {
        Long tenantId = TenantContextHolder.getTenantId();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        if (ObjectUtil.isNotEmpty(materialId)) {
            PanoConfigurationDO panoConfigurationDO = panoConfigurationMapper.selectOne(new LambdaQueryWrapperX<PanoConfigurationDO>()
                    .eq(PanoConfigurationDO::getMaterialId, materialId)
                    .eq(PanoConfigurationDO::getDeptId, deptId)
                    .eq(PanoConfigurationDO::getTenantId, tenantId));
            if (ObjectUtil.isNotEmpty(panoConfigurationDO)) {
                return panoConfigurationDO;
            }
        }
        PanoConfigurationDO panoConfigurationDO = panoConfigurationMapper.selectOne(
                new LambdaQueryWrapperX<PanoConfigurationDO>()
                        .eq(PanoConfigurationDO::getDeptId, deptId)
                        .isNull(PanoConfigurationDO::getMaterialId)
                        .eq(PanoConfigurationDO::getTenantId, tenantId));
        return panoConfigurationDO;
    }

    @Override
    public PanoConfigurationDO getPanoConf(Long materialId) {
        PanoConfigurationDO panoConfigurationDO = verifyPanoConf(materialId);
        if (ObjectUtil.isNotEmpty(panoConfigurationDO)) {
            return panoConfigurationDO;
        }
        // 默认配置
        PanoConfigurationDO defaultPanoConf = new PanoConfigurationDO();
        defaultPanoConf.setType(PanoConfTypeEnum.VISUAL_RANGE.getCode());
        defaultPanoConf.setVisualRange(5);
        return defaultPanoConf;
    }
}
