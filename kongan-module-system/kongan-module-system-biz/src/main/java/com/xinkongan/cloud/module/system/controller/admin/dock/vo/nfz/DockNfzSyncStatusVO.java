package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机场禁飞区同步状态VO
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-27
 */
@Data
@Schema(description = "管理后台 - 机场禁飞区同步状态VO")
public class DockNfzSyncStatusVO {

    @Schema(description = "机场SN", example = "DOCK001")
    private String dockSn;

    @Schema(description = "机场名称", example = "测试机场")
    private String dockName;

    @Schema(description = "机场所属组织名称")
    private String deptId;

    @Schema(description = "机场所属组织名称")
    private String deptName;

    @Schema(description = "同步状态：pending_sync-待同步，syncing-同步中，sync_success-同步成功，sync_failed-同步失败", example = "sync_success")
    private String syncStatus;

}
