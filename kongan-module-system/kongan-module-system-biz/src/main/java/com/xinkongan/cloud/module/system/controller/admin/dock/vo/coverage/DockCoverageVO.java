package com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @Description 机场可飞范围 VO
 * <AUTHOR>
 * @Date 2024/12/30 10:29
 */
@Data
@Schema(description = "机场可飞范围 VO")
public class DockCoverageVO {

    @NotNull(message = "id不能为空")
    @Schema(description = "id")
    private Long id;

    @NotBlank(message = "机场sn不能为空")
    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "几等分")
    private Integer uniform;

    @Schema(description = "倾斜角")
    private Integer gradient;

    @Schema(description = "半径")
    private List<Integer> radius;
    
    @Schema(description = "机场信息")
    private CoverageDockInfo coverageDockInfo;
}