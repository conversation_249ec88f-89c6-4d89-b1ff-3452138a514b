package com.xinkongan.cloud.module.system.controller.admin.task.vo.demand;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.xinkongan.cloud.framework.common.util.json.databind.StringLocalDateTimeDeserializer;
import com.xinkongan.cloud.module.system.controller.admin.label.dto.TabSaveDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 飞行需求入参 Req VO
 * <AUTHOR>
 * @Date 2025/08/21 20:43
 */
@Schema(description = "飞行需求Req VO")
@Data
public class FlyDemandUpdateReqVO {

    @Schema(description = "需求id")
    private Long id;

    @Schema(description = "需求名称")
    private String demandName;

    @Schema(description = "申请状态 0 待提交 1 已提交 2 已规划 3 已完成 4 已撤销")
    private Long status;

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "需求所属组织id")
    private Long deptId;

    @Schema(description = "巡检内容")
    private List<String> inspectionContent;


    @Schema(description = "开始时间(循环任务)")
    @JsonDeserialize(using = StringLocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    @Schema(description = "结束时间(循环任务)")
    @JsonDeserialize(using = StringLocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @Schema(description = "循环模式(循环任务) 0按天循环1按周循环2按月循环")
    private Integer cycleMode;

    @Schema(description = "循环间隔")
    private List<String> cycleInterval;

    @Schema(description = "任务执行时间")
    private LocalDateTime executeTime;

    @Schema(description = "执行时间列表")
    private List<LocalDateTime> executeTimeList;

    @Schema(description = "标注数据保存")
    private List<TabSaveDTO> tabSaveInfos;

    @Schema(description = "备注")
    private String description;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "需求所属租户id")
    private Long tenantId;

    
}