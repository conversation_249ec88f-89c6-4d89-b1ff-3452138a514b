package com.xinkongan.cloud.module.system.controller.admin.route;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.ratelimiter.core.annotation.RateLimiter;
import com.xinkongan.cloud.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteSaveDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteShareDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteUpdateDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.*;
import com.xinkongan.cloud.module.system.service.route.IRouteAlgorithmService;
import com.xinkongan.cloud.module.system.service.route.IRouteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@RestController
@Tag(name = "飞控系统 - 航线管理")
@RequestMapping("/system/route")
public class RouteController {

    @Resource
    private IRouteService routeService;

    @Resource
    private IRouteAlgorithmService routeAlgorithmService;

    @PostMapping(value = "/page")
    @Operation(summary = "航线管理-航线分页")
    @PreAuthorize("@ss.hasPermission('system:route:page')")
    public CommonResult<PageResult<RouteRespVO>> routePage(@RequestBody RouteSearchDTO routeSearch) {
        PageResult<RouteRespVO> pageResult = routeService.routePageSearch(routeSearch);
        return CommonResult.success(pageResult);
    }

    @PostMapping(value = "/page/compute")
    @Operation(summary = "航线管理-分页号计算")
    @PreAuthorize("@ss.hasPermission('system:route:page')")
    public CommonResult<Long> computePageNum(@RequestBody RouteSearchDTO routeSearch) {
        Long page = routeService.routePageSearchCount(routeSearch);
        return CommonResult.success(page);
    }

    @GetMapping(value = "/query/{routeId}")
    @Operation(summary = "航线管理-航线详情")
    @PreAuthorize("@ss.hasAnyPermissions('system:route:query','system:job:query')")
    public CommonResult<RouteDetailRespVO> getRouteDetailInfoById(@PathVariable Long routeId) {
        RouteDetailRespVO routeDetailInfo = routeService.getRouteDetailById(routeId);
        return CommonResult.success(routeDetailInfo);
    }

    @PostMapping(value = "/add")
    @Operation(summary = "航线管理-保存航线")
    @PreAuthorize("@ss.hasPermission('system:route:add')")
    @RateLimiter(keyResolver = ClientIpRateLimiterKeyResolver.class, time = 1, count = 3)
    public CommonResult<Long> addRoute(@RequestBody @Valid RouteSaveDTO routeSaveParams) {
        Long routeId = routeService.routeSave(routeSaveParams);
        return CommonResult.success(routeId);
    }

    @GetMapping(value = "/del/{routeId}")
    @Operation(summary = "航线管理-删除航线")
    @PreAuthorize("@ss.hasPermission('system:route:del')")
    public CommonResult<Void> delRoute(@PathVariable Long routeId) {
        routeService.delRouteById(routeId, false);
        return CommonResult.success();
    }

    @PostMapping(value = "/update")
    @Operation(summary = "航线管理-修改航线")
    @PreAuthorize("@ss.hasPermission('system:route:update')")
    @RateLimiter(keyResolver = ClientIpRateLimiterKeyResolver.class, time = 1, count = 3)
    public CommonResult<Long> updateRoute(@RequestBody RouteUpdateDTO routeUpdateParams) {
        Long routeId = routeService.routeUpdate(routeUpdateParams);
        return CommonResult.success(routeId);
    }


    @PostMapping(value = "/share")
    @Operation(summary = "航线分享-分享保存")
    @PreAuthorize("@ss.hasPermission('system:route:share:save')")
    public CommonResult<Void> routeShare(@RequestBody @Valid RouteShareDTO routeShareParam) {
        routeService.routeShare(routeShareParam);
        return CommonResult.success();
    }


    @GetMapping(value = "/list/share/{routeId}")
    @Operation(summary = "航线分享-分享列表")
    @PreAuthorize("@ss.hasPermission('system:route:share:query')")
    public CommonResult<RouteShareVO> getRouteShareDept(@PathVariable Long routeId) {
        RouteShareVO routeShareDept = routeService.getRouteShareDept(routeId);
        return CommonResult.success(routeShareDept);
    }


    @PostMapping(value = "/lock/{routeId}")
    @Operation(summary = "航线管理-锁定航线")
    public CommonResult<Void> lockRoute(@PathVariable Long routeId) {
        routeService.lockRouteById(routeId);
        return CommonResult.success();
    }

    @PostMapping(value = "/unlock/{routeId}")
    @Operation(summary = "航线管理-解锁航线")
    public CommonResult<Void> unlockRoute(@PathVariable Long routeId) {
        routeService.unlockRouteById(routeId);
        return CommonResult.success();
    }

    @GetMapping(value = "/copy/{routeId}")
    @Operation(summary = "航线管理-航线复制")
    public CommonResult<Void> routeCopy(@PathVariable Long routeId) {
        routeService.addRouteCopyCount(routeId);
        return CommonResult.success();
    }

    @Operation(summary = "实例是否被航线占用", description = "true: 被使用 false: 未被使用")
    @GetMapping(value = "/isAlgorithmExampleUsed/{exampleId}")
    public CommonResult<Boolean> isAlgorithmExampleUsed(@PathVariable Long exampleId) {
        return CommonResult.success(routeAlgorithmService.isAlgorithmExampleUsed(exampleId));
    }

    @Operation(summary = "查询当前机场已执行的航线", description = "查询当前机场已执行的航线")
    @PostMapping(value = "/getExecRouteListByDockSn")
    public CommonResult<PageResult<ExecRouteVO>> getExecRouteListByDockSn(@RequestBody DockExecRoutePageParam param) {
        return CommonResult.success(routeService.getExecRouteListByDockSn(param));
    }

    @PostMapping(value = "/import")
    @Operation(summary = "航线管理-从KMZ文件导入航线")
    @PreAuthorize("@ss.hasPermission('system:route:import')")
    @RateLimiter(keyResolver = ClientIpRateLimiterKeyResolver.class, time = 1, count = 2)
    public CommonResult<Long> importRoute(@RequestBody @Valid RouteImportDTO routeImportParams) {
        Long routeId = routeService.importRouteFromKmz(
            routeImportParams.getKmzUrl(),
            routeImportParams.getRouteName(),
            routeImportParams.getRouteType()
        );
        return CommonResult.success(routeId);
    }

}
