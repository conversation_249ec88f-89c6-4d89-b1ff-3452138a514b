package com.xinkongan.cloud.module.system.controller.admin.sms.vo.code;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@Schema(description = "短信发送信息")
@AllArgsConstructor
@NoArgsConstructor
public class SmsCodeSendInfoVO {

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "上次发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = ToStringSerializer.class)
    private LocalDateTime lastSendTime;

    @Schema(description = "是否在有效期内")
    private Boolean hasEffective;

    @Schema(description = "剩余有效时间")
    private Long expire;
}
