package com.xinkongan.cloud.module.system.service.flyrecord;

import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ScheduleDeleteSetReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordTaskDO;
import tech.powerjob.worker.log.OmsLogger;

/**
 * @Description 飞行记录删除任务Service
 * <AUTHOR>
 * @Date 2025/2/17 10:21
 */
public interface IFlyRecordTaskService {

    /**
     * 定期删除
     * <AUTHOR>
     * @date 2025/2/17 10:08
     **/
    void scheduledDelete(OmsLogger omsLogger);

    /**
     * 定期删除设置
     * <AUTHOR>
     * @date 2025/2/17 11:14
     **/
    Boolean scheduleDeleteSet(ScheduleDeleteSetReqVO reqVO);

    /**
     * 获取定期删除设置
     * <AUTHOR>
     * @date 2025/2/17 11:14
     **/
    FlyRecordTaskDO getScheduleDeleteSet();
}
