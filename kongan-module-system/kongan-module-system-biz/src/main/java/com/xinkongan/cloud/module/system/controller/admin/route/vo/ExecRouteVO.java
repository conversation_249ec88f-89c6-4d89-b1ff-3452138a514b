package com.xinkongan.cloud.module.system.controller.admin.route.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExecRouteVO {

    @Schema(description = "航线id", example = "123")
    private Long id;

    @Schema(description = "航线名称", example = "天鹅湖巡检航线")
    private String routeName;

    @Schema(description = "机场SN", example = "123LDSNI2HBX")
    private String dockSn;

    @Schema(description = "组织名称", example = "合肥市消防大队")
    private String deptName;

    @Schema(description = "创建时间", example = "2025-05-07 15:29:43")
    private String createTime;

    @Schema(description = "航线类型", example = "1:巡检航线，3:建模航线")
    private Integer routeType;

}
