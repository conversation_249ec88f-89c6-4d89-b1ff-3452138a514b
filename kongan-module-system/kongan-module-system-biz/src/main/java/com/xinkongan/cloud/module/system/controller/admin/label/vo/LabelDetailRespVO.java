package com.xinkongan.cloud.module.system.controller.admin.label.vo;

import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LabelDetailRespVO extends LabelRespVO{

    @Schema(description = "标记信息")
    private List<TabRespVO> tabInfos;

    @Schema(description = "引用素材的id列表")
    private List<MaterialInfoVO> materialInfos;

    public List<TabRespVO> getTabInfos() {
        if (tabInfos == null) {
            tabInfos = new ArrayList<>();
        }
        return tabInfos;
    }

    public List<MaterialInfoVO> getMaterialInfos() {
        if (materialInfos == null) {
            materialInfos = new ArrayList<>();
        }
        return materialInfos;
    }
}
