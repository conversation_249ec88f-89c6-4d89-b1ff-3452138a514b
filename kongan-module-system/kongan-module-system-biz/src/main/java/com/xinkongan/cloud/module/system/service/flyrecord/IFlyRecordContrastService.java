package com.xinkongan.cloud.module.system.service.flyrecord;

import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ContrastFileVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ContrastPageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ContrastPicLeftListReqVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ContrastSaveReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordContrastDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 飞行记录对比Service
 * <AUTHOR>
 * @Date 2025/2/19 10:18
 */
public interface IFlyRecordContrastService {

    /**
     * 对比飞行记录
     **/
    List<ContrastFileVO> radiusFiles(Double longitude, Double latitude, Integer radius, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 保存对比记录
     **/
    Boolean save(ContrastSaveReqVO contrastSaveReqVO);

    /**
     * 对比记录分页
     **/
    PageResult<FlyRecordContrastDO> page(ContrastPageReqVO contrastPageReqVO);

    /**
     * 左侧飞行记录图片列表
     **/
    List<ContrastFileVO> picLeftList(ContrastPicLeftListReqVO reqVO);
}
