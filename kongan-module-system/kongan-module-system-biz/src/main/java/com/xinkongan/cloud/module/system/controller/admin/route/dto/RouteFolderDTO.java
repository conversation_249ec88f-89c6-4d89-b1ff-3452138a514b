package com.xinkongan.cloud.module.system.controller.admin.route.dto;


import com.xinkongan.cloud.framework.kmz.dto.StartActionGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class RouteFolderDTO implements Serializable {

    @Schema(description = "航点列表")
    private List<WayPointDTO> points;

    /**
     * wpml
     * 距离
     */
    @Schema(description = "预计飞行里程")
    private Float distance;

    /**
     * wpml
     * 时间
     */
    @Schema(description = "预计飞行时间")
    private Float duration;

    @Schema(description = "初始化动作组")
    private StartActionGroup startActionGroup;

    public List<WayPointDTO> getPoints() {
        if (points == null) {
            this.points = new ArrayList<>();
        }
        return points;
    }
}
