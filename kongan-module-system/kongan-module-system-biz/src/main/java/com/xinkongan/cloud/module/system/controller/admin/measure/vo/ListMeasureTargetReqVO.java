package com.xinkongan.cloud.module.system.controller.admin.measure.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "查询激光打点请求VO")
public class ListMeasureTargetReqVO {

    @Schema(description = "飞行记录id")
    private Long flyRecordId;

    @Schema(description = "任务id")
    private Long jobId;
}
