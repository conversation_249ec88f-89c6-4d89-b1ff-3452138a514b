package com.xinkongan.cloud.module.system.controller.admin.flyrecord;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.FlyFileDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordFileVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.UploadFileReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 飞行记录文件Controller
 * <AUTHOR>
 * @Date 2025/2/13 15:49
 */
@Validated
@RestController
@Tag(name = "管理后台 - 飞行记录文件")
@RequestMapping("/system/fly-record-file")
public class FlyRecordFileController {

    @Resource
    private IFlyRecordFileService flyRecordFileService;

    @GetMapping("/listByDockSn/{dockSn}")
    @Operation(summary = "飞行记录文件列表", description = "飞行记录文件列表")
    CommonResult<List<FlyFileDTO>> listByDockSn(@PathVariable("dockSn") String dockSn) {
        List<FlyFileDTO> flyRecordFileDOS = flyRecordFileService.listByDockSn(dockSn);
        return success(flyRecordFileDOS);
    }

    @GetMapping("/list/{flyRecordId}")
    @Operation(summary = "飞行记录文件列表", description = "飞行记录文件列表")
    CommonResult<List<FlyRecordFileDO>> getByFlyRecordId(@PathVariable("flyRecordId") Long flyRecordId) {
        List<FlyRecordFileDO> flyRecordFileDOS = flyRecordFileService.listByFlyRecordId(flyRecordId);
        return success(flyRecordFileDOS);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "文件详情", description = "文件详情")
    CommonResult<FlyRecordFileVO> getById(@PathVariable("id") Long id) {
        FlyRecordFileVO flyRecordFileVO = flyRecordFileService.getFlyRecordFileVO(id);
        return success(flyRecordFileVO);
    }

    @PreAuthorize("@ss.hasAnyPermissions('system:fly-record:detail:delete')")
    @PostMapping("/delete/{id}")
    @Operation(summary = "删除文件", description = "根据ID删除文件")
    CommonResult<Boolean> deleteById(@PathVariable("id") Long id) {
        Boolean deleted = flyRecordFileService.deleteById(id);
        return success(deleted);
    }

    @PreAuthorize("@ss.hasAnyPermissions('system:fly-record:detail:delete')")
    @PostMapping("/deleteBatch")
    @Operation(summary = "批量删除文件", description = "批量删除文件")
    CommonResult<Boolean> deleteBatch(@RequestBody List<Long> ids) {
        ids.forEach(id -> flyRecordFileService.deleteById(id));
        return success(true);
    }

    @PreAuthorize("@ss.hasAnyPermissions('system:fly-record:detail:upload')")
    @PostMapping("/upload")
    @Operation(summary = "导入文件", description = "导入文件")
    CommonResult<Long> uploadFile(@Valid @RequestBody UploadFileReqVO uploadFileReqVO) {
        Long id = flyRecordFileService.uploadFile(uploadFileReqVO);
        return success(id);
    }

    @PreAuthorize("@ss.hasAnyPermissions('system:fly-record:detail:upload')")
    @PostMapping("/uploadBatch")
    @Operation(summary = "批量导入文件", description = "批量导入文件")
    @Transactional(rollbackFor = Exception.class)
    CommonResult<Boolean> uploadBatchFile(@Valid @RequestBody List<UploadFileReqVO> uploadFileReqVO) {
        for (UploadFileReqVO fileReqVO : uploadFileReqVO) {
            flyRecordFileService.uploadFile(fileReqVO);
        }
        return success(true);
    }

}