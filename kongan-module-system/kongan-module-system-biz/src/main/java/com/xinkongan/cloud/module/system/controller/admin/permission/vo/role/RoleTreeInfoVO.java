package com.xinkongan.cloud.module.system.controller.admin.permission.vo.role;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class RoleTreeInfoVO extends BaseTreeNode {

    @Schema(description = "该角色的用户数目")
    private Integer userCount;

    @Schema(description = "角色标识")
    private String code;

}
