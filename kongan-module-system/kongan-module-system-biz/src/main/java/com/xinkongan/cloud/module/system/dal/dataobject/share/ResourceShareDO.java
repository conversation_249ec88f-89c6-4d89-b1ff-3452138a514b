package com.xinkongan.cloud.module.system.dal.dataobject.share;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinkongan.cloud.framework.mybatis.core.dataobject.DeptBaseDO;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@KeySequence("system_resource_share_seq")
@TableName(value = "system_resource_share", autoResultMap = true)
public class ResourceShareDO extends DeptBaseDO {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "分享的资源id", example = "111")
    private Long resourceId;

    @Schema(description = "资源组织id", example = "111")
    private Long oriDeptId;

    @Schema(description = "分享的目标组织id", example = "1111")
    private Long shareDeptId;

    /**
     * 参考：{@link ResourceShareTypeEnum}
     */
    @Schema(description = "分享的资源类型")
    private Integer type;
}
