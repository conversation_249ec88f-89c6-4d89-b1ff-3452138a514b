package com.xinkongan.cloud.module.system.framework.startup.mqtt;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.enums.SystemConstants;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.dock.mqtt.device.StatusSubscribe;
import com.xinkongan.cloud.sdk.dock.mqtt.config.MqttConfiguration;
import com.xinkongan.cloud.sdk.dock.mqtt.model.MqttClientOptions;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
@Slf4j
@Component
public class StartupSubTopic {

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private StatusSubscribe statusSubscribe;

    @Resource
    private DockDeviceService dockDeviceService;

    @PostConstruct
    public void init() {
        MqttClientOptions options = MqttConfiguration.getBasicClientOptions();
        // 客户端
        String url = "/api/v4/clients";
        HttpResponse httpResponse = HttpRequest.get(SystemConstants.HTTP + options.getHost() + ":" + options.getApiPort() + url)
                .header(SystemConstants.AUTHORIZATION, HttpUtil.buildBasicAuth(options.getAppId(), options.getAppSecret(), null))
                .execute();
        if (httpResponse.isOk()) {
            EmqClientBody emqClientBody = null;
            log.info("clients-body(): {}", httpResponse.body());
            try {
                emqClientBody = objectMapper.readValue(httpResponse.body(), EmqClientBody.class);
            } catch (JsonProcessingException e) {
                log.error("启动时查询客户端信息解析异常");
                throw new RuntimeException(e);
            }
            if (Objects.nonNull(emqClientBody) && Objects.nonNull(emqClientBody.getData())) {
                emqClientBody.getData().forEach(
                        emqClientData -> {
                            String dockSn = emqClientData.getClientid();
                            DockDeviceDO dockDO = dockDeviceService.getByDBDeviceSn(dockSn);
                            if (dockDO != null) {
                                // 是机场客户端 订阅机场消息
                                statusSubscribe.subscribeDock(dockSn);
                                // 查询是否存在无人机
                                String childSn = dockDO.getChildSn();
                                if (StringUtils.isNotEmpty(childSn)) {
                                    // 订阅无人机主题
                                    statusSubscribe.subscribeDrone(childSn);
                                }
                            }
                        }
                );
            }
        } else {
            log.error("启动时读取Emqx客户端列表响应失败");
        }
    }

}
