package com.xinkongan.cloud.module.system.controller.admin.measure.vo;

import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "发送创建/删除打点信息到 Web VO")
public class SendMeasureToWebVO {

    /**
     * {@link YesNoEnum}
     */
    @Schema(description = "是否新增 1新增 0删除")
    private Integer isAdd;

    @Schema(description = "打点信息")
    private MeasureTargetBaseVO measureTargetBaseVO;
}
