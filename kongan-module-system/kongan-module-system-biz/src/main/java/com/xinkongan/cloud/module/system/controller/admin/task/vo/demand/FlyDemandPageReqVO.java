package com.xinkongan.cloud.module.system.controller.admin.task.vo.demand;


import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;


import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 飞行需求分页入参 Req VO
 * <AUTHOR>
 * @Date 2025/08/21 20:43
 */
@Schema(description = "飞行需求分页Req VO")
@Data
public class FlyDemandPageReqVO extends PageParam {

    @Schema(description = "搜索时间段(任务执行时间)开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime beginTime;

    @Schema(description = "搜索时间段(任务执行时间)结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @Schema(description = "申请状态 0 待提交 1 已提交 2 已规划 3 已完成 4 已撤销")
    private Integer status;

    @Schema(description = "需求/组织名称")
    private String demandOrDeptName;

}