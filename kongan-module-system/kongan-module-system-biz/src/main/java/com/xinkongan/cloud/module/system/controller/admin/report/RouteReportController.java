package com.xinkongan.cloud.module.system.controller.admin.report;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.RouteDataReportVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.RouteTopStatisticVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.TaskTopStatisticVO;
import com.xinkongan.cloud.module.system.dto.TaskTopReportDTO;
import com.xinkongan.cloud.module.system.service.report.IReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RestController
@Tag(name = "数据报表-航线统计")
@RequestMapping("/system/report/route")
public class RouteReportController {

    @Resource
    private IReportService reportService;


    @GetMapping(value = "/statistic")
    @Operation(summary = "数据报表-数目统计")
//    @PreAuthorize("@ss.hasPermission('system:route:report:query')")
    public CommonResult<RouteDataReportVO> getRouteReport() {
        RouteDataReportVO routeDataReportStatistic = reportService.getRouteDataReportStatistic();
        return CommonResult.success(routeDataReportStatistic);
    }


    @PostMapping(value = "/top")
    @Operation(summary = "数据报表-航线top榜")
//    @PreAuthorize("@ss.hasPermission('system:route:report:query')")
    public CommonResult<List<RouteTopStatisticVO>> routeTopReport(@RequestBody TaskTopReportDTO taskTopReportDTO) {
        List<RouteTopStatisticVO> taskTopStatisticReport = reportService.getRouteTopStatisticReport(taskTopReportDTO);
        return CommonResult.success(taskTopStatisticReport);
    }
}
