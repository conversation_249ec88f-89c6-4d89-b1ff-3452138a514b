package com.xinkongan.cloud.module.system.controller.admin.posture.dto.control;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.enums.CameraTypeEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.enums.GimbalResetModeEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.CameraModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;


/**
 * <AUTHOR>
 * @version 1.4
 * @date 2023/3/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DronePayloadParam {

    @Pattern(regexp = "\\d+-\\d+-\\d+")
    @NotNull
    @Schema(description = "负载索引")
    private String payloadIndex;

    @Schema(description = "相机类型")
    private CameraTypeEnum cameraType;

    @Range(min = 2, max = 200)
    @Schema(description = "缩放倍数")
    private Double zoomFactor;

    @Schema(description = "相机模式枚举")
    private CameraModeEnum cameraMode;

    /**
     * true: 锁定nie偏航角，nie偏航角与无人机一起旋转。
     * false: 只有云台转动，但无人机不转动。
     */
    @Schema(description = "锁定偏航角")
    private Boolean locked;

    @Schema(description = "pitchSpeed")
    private Double pitchSpeed;

    /**
     * 仅当locked为false时有效。
     */
    @Schema(description = "仅当locked为false时有效。")
    private Double yawSpeed;

    /**
     * 左上角为中心点
     */
    @Range(min = 0, max = 1)
    @Schema(description = "左上角为中心点-x")
    private Double x;

    @Range(min = 0, max = 1)
    @Schema(description = "左上角为中心点-y")
    private Double y;

    @Range(min = 0, max = 1)
    @Schema(description = "宽 - 框选变焦时")
    private Double width;

    @Range(min = 0, max = 1)
    @Schema(description = "高 - 框选变焦时")
    private Double height;

    @Schema(description = "重置云台")
    private GimbalResetModeEnum resetMode;

    @Schema(description = "是否使能 - 分屏")
    private Boolean enable;
}
