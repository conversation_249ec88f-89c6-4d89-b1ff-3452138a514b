package com.xinkongan.cloud.module.system.controller.admin.notice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * @Description 批量已读
 * <AUTHOR>
 * @Date 2025/3/10 14:54
 */
@Data
@Schema(description = "批量已读 ReqVO")
public class MarkAsReadReqVO {

    @NotEmpty(message = "消息id不能为空")
    @Schema(description = "消息id列表")
    private List<Long> ids;
}