package com.xinkongan.cloud.module.system.controller.admin.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RouteDataReportVO {

    @Schema(description = "航线总数", example = "50")
    private Integer routeNum;

    @Schema(description = "巡检航线总数", example = "23")
    private Integer patrolRouteNum;

    @Schema(description = "建模航线总数", example = "27")
    private Integer modelRouteNum;

    @Schema(description = "航线统计信息")
    private List<RouteStatisticVO> routeStatistic;

    public List<RouteStatisticVO> getRouteStatistic() {
        if (routeStatistic == null) {
            routeStatistic = new ArrayList<>();
        }
        return routeStatistic;
    }
}
