package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm.AlarmDockDeviceReqVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm.AlarmDockDeviceVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm.DockAlarmVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm.UpdateDockAlarmReqVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceVO;
import com.xinkongan.cloud.module.system.service.dock.alarm.IDockAlarmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;


/**
 * @Description 机场接警配置
 * <AUTHOR>
 * @Date 2025/3/19 16:12
 */
@Validated
@RestController
@Tag(name = "管理后台 - 机场接警配置")
@RequestMapping("/system/dockDevice/alarm")
public class DockDeviceAlarmController {

    @Resource
    private IDockAlarmService dockAlarmService;

    @GetMapping("/getByDockSn")
    @Operation(summary = "获取机场接警配置", description = "获取机场接警配置")
    public CommonResult<DockAlarmVO> getAlarmConfig(@RequestParam("dockSn") String dockSn) {
        DockAlarmVO alarmConfig = dockAlarmService.getAlarmConfig(dockSn);
        return success(alarmConfig);
    }

    @PostMapping("/updateByDockSn")
    @Operation(summary = "更新机场接警配置", description = "更新机场接警配置")
    CommonResult<Boolean> updateAlarmConfig(@Valid @RequestBody UpdateDockAlarmReqVO reqVO) {
        Boolean flag = dockAlarmService.updateAlarmConfig(reqVO);
        return success(flag);
    }
}