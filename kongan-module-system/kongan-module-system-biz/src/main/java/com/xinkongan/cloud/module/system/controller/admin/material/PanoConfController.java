package com.xinkongan.cloud.module.system.controller.admin.material;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.PanoConfReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.PanoConfigurationDO;
import com.xinkongan.cloud.module.system.service.material.PanoConfigurationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@Tag(name = "管理后台 - 全景图配置")
@RequestMapping("/system/material/{materialType}/pano")
public class PanoConfController {

    @Resource
    private PanoConfigurationService panoConfigurationService;

    @PostMapping("/updatePanoConf")
    @Operation(summary = "全景图配置-更新配置")
    public CommonResult<Boolean> updatePanoConf(@RequestBody PanoConfReqVO reqVO) {
        return CommonResult.success(panoConfigurationService.updatePanoConf(reqVO));
    }

    @GetMapping("/getPanoConf")
    @Operation(summary = "全景图配置-全局配置")
    public CommonResult<PanoConfigurationDO> getPanoConf() {
        PanoConfigurationDO panoConf = panoConfigurationService.getPanoConf(null);
        return CommonResult.success(panoConf);
    }

    @PostMapping("/getPanoConfByMaterialId")
    @Operation(summary = "全景图配置-查询配置")
    public CommonResult<PanoConfigurationDO> getPanoConfByMaterialId(@RequestParam Long materialId) {
        PanoConfigurationDO panoConf = panoConfigurationService.getPanoConf(materialId);
        return CommonResult.success(panoConf);
    }
}
