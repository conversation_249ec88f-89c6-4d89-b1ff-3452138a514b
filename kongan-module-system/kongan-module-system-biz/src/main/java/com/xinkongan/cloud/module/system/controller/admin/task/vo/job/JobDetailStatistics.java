package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description 任务详情 统计信息
 * <AUTHOR>
 * @Date 2025/1/14 10:47
 */
@Schema(description = "任务详情 统计信息")
@Data
public class JobDetailStatistics {

    @Schema(description = "任务飞行次数")
    private Integer flyCount = 0;

    @Schema(description = "任务实际耗时（飞行记录的飞行时长相加）")
    private Integer flyTimeCount = 0;

    @Schema(description = "识别异常数")
    private Integer exceptionCount = 0;

    /**
     * 飞行产生的照片数量（拍摄+人工截图+视频，不包含识别的异常图片）
     * <AUTHOR>
     * @date 2025/1/14 10:49
     **/
    @Schema(description = "飞行素材数")
    private Integer flyMaterialCount = 0;

    @Schema(description = "建模素材数")
    private Integer modelMaterialCount = 0;

    @Schema(description = "建模次数")
    private Integer modelCount = 0;
}