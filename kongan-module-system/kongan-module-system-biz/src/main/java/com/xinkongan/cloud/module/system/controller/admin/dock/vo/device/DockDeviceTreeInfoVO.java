package com.xinkongan.cloud.module.system.controller.admin.dock.vo.device;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DockModeCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DockDeviceTreeInfoVO extends BaseTreeNode {

    @Schema(description = "设备编号")
    private String deviceSn;

    @Schema(description = "组织ID")
    private Long deptId;

    @Schema(description = "共享状态")
    private Integer shareStatus;

    @Schema(description = "子设备SN")
    private String childSn;

    @Schema(description = "子设备信息")
    private DockDeviceVO childDevice;

    @Schema(description = "机场状态")
    private DockModeCodeEnum modeCode;


}
