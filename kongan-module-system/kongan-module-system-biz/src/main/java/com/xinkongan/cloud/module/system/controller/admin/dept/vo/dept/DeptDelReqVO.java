package com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
@Schema(description = "管理后台 - 部门删除参数")
public class DeptDelReqVO {

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "用户手机号")
    private String mobile;

    @NotEmpty(message = "验证码")
    private String code;
}
