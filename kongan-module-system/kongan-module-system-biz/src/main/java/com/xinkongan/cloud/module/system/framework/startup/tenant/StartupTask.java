package com.xinkongan.cloud.module.system.framework.startup.tenant;

import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.tenant.TenantDO;
import com.xinkongan.cloud.module.system.service.alarm.IAlarmConfigService;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.tenant.TenantService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */

@Component
public class StartupTask {

    @Resource
    private TenantService tenantService;
    @Resource
    private DeptService deptService;
    @Resource
    private IAlarmConfigService alarmConfigService;

    @PostConstruct
    public void init() {
        System.out.println("Spring Boot 项目启动时执行任务...");
        TenantUtils.executeIgnore(
                () -> {
                    List<TenantDO> tenantList = tenantService.getTenantList();
                    for (TenantDO tenantDO : tenantList) {
                        tenantService.setEmqxInfoToRedis(tenantDO.getEmqxUsername(), tenantDO.getEmqxPassword(),
                                tenantDO.getEmqxSalt(), tenantDO.getId());
                    }
                    // 查询所有没有通用场景的组织id
                    List<DeptDO> list = deptService.getDeptNoDefaultAlarmConfig();
                    list.forEach(deptDO -> {
                        // 初始化接警场景
                        alarmConfigService.initDeptAlarmConfig(deptDO.getId(), deptDO.getTenantId());
                    });
                }
        );
    }
}

