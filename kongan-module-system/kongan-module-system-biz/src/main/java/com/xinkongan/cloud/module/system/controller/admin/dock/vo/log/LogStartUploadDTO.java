package com.xinkongan.cloud.module.system.controller.admin.dock.vo.log;

import com.xinkongan.cloud.sdk.dock.cloudapi.log.dto.FileUploadStartFile;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogStartUploadDTO {
    @NotNull(message = "机场sn不能为空")
    private String dockSn;
    private List<FileUploadStartFile> files;

}