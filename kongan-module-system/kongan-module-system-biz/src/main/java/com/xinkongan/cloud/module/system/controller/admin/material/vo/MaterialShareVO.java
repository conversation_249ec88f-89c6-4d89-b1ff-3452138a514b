package com.xinkongan.cloud.module.system.controller.admin.material.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialShareVO {
    @Schema(description = "素材id")
    private Long materialId;

    @Schema(description = "分享的组织id")
    private List<Long> deptIds;

    public List<Long> getDeptIds() {
        if (deptIds == null) {
            deptIds = new ArrayList<>();
        }
        return deptIds;
    }
}
