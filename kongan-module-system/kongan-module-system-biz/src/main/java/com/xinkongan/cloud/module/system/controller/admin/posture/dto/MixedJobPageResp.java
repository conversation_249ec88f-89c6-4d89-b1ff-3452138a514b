package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "混合任务响应")
public class MixedJobPageResp {

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "任务名称")
    private String jobName;

    @Schema(description = "任务描述")
    private String description;

    @Schema(description = "任务所属组织id")
    private Long deptId;

    @Schema(description = "任务所属组织名称")
    private String deptName;

    @Schema(description = "任务执行时间")
    private LocalDateTime executeTime;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "任务场景")
    private Integer scene;

    @Schema(description = "执行任务机场SN")
    private String dockSn;

}
