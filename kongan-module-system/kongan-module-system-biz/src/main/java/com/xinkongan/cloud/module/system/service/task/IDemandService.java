package com.xinkongan.cloud.module.system.service.task;

import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.demand.*;


/**
 * @Description 需求管理Service
 * <AUTHOR>
 * @Date 2025/8/21 20:21
 */
public interface IDemandService {

    /**
     * 需求分页
     **/
    PageResult<FlyDemandPageRespVO> page(FlyDemandPageReqVO reqVO);

    /**
     * 创建需求
     *
     **/
    Long createDemand(FlyDemandReqVO flyDemandReqVO);

    /**
     * 更新需求
     *
     **/
    Boolean updateDemand(FlyDemandUpdateReqVO flyDemandUpdateReqVO);

    /**
     * 根据id获取需求信息
     *
     **/
    FlyDemandRespVO getById(Long demandId);

    /**
     * 删除需求
     *
     **/
    Boolean deleteDemand(Long demandId);

    /**
     * 获取需求总数
     *
     **/
     Long getDemandCount();

    /**
     * 获取需求状态数量统计
     *
     **/
    DemandStatisticCountVO statisticCount();
//    /**
//     * 查询数据范围内top榜单的数据排行
//     *
//     * @return 统计结果
//     */
//    List<TaskTopStatisticVO> getTaskTopStatisticInfo(LocalDateTime[] timeRange);
//
//
//
//
//    /**
//     * 统计某个组织下的各类型任务数目
//     * @param deptId 组织id
//     * @return 统计结果
//     */
//    List<PieReqVO> getJobPieStatistic(PieParamsInfo pieParamsInfo);
//
//    /**
//     * 统计任务执行情况
//     * @param histogramSearchInfo 统计信息
//     * @return 统计结果
//     */
//    List<HistogramVO> getJobHistogramStatistic(HistogramSearchDTO histogramSearchInfo);
//
//
//    /**
//     * 查询用户下发的任务数量
//     * @param userId 用户id
//     * @return 任务数量
//     */
//    Long getJobCountByUserId(Long userId);
//
//    /**
//     * 根据组织ID删除任务
//     * @param deptId 组织ID
//     */
//    void deleteTaskByDeptId(Long deptId);
}
