package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 更新默认接警配置ReqVO
 * <AUTHOR>
 * @Date 2025/3/19 14:27
 */
@Schema(description = "更新默认接警配置ReqVO")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class UpdateDefaultAlarmConfigReqVO {

    @Schema(description = "警情场景id")
    @NotNull(message = "警情场景id不能为空")
    private Long id;
}