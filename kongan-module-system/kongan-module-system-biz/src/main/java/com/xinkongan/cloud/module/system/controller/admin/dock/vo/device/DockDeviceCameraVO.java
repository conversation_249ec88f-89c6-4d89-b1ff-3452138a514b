package com.xinkongan.cloud.module.system.controller.admin.dock.vo.device;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DockDeviceCameraVO {

    @Schema(description = "domain")
    private Integer cameraDomain;

    @Schema(description = "deviceType")
    private Integer cameraDeviceType;

    @Schema(description = "subType")
    private Integer cameraSubType;

    @Schema(description = "M30 Camera")
    private String cameraName;

    @Schema(description = "M30 相机")
    private String cameraNameZh;

    @Schema(description = "默认直播镜头")
    private String livePayloadIndex;

}
