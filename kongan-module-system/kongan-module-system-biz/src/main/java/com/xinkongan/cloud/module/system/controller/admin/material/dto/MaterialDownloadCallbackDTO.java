package com.xinkongan.cloud.module.system.controller.admin.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MaterialDownloadCallbackDTO {

    @Schema(description = "下载的文件路径")
    private String path;

    @Schema(description = "工作空间")
    private String workspace;

    @Schema(description = "状态")
    private Boolean success;

    @Schema(description = "业务id")
    private String businessId;

    @Schema(description = "文件的url")
    private String fileUrl;
}
