package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 禁飞区创建/修改请求VO
 */
@Data
@Schema(description = "管理后台 - 禁飞区创建/修改请求VO")
public class NfzSaveReqVO {

    @Schema(description = "禁飞区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试禁飞区")
    @NotBlank(message = "禁飞区名称不能为空")
    @Size(max = 100, message = "禁飞区名称长度不能超过100个字符")
    private String name;

    @Schema(description = "多边形坐标，以 GeoJSON 格式存储", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "禁飞区坐标数据不能为空")
    private String dataJson;

    @Schema(description = "状态：true表示启用，false表示禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "状态不能为空")
    private Boolean status;

    @Schema(description = "区域面积", example = "100.5")
    private Float area;
}
