package com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PieReqVO {

    @Schema(description = "巡检任务数量")
    private Long count;

    @Schema(description = "图例名称")
    private String key;
}
