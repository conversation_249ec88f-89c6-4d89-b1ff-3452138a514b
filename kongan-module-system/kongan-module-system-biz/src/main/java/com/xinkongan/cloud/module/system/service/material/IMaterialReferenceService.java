package com.xinkongan.cloud.module.system.service.material;

import com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialTopVO;
import com.xinkongan.cloud.module.system.dto.TaskTopReportDTO;
import com.xinkongan.cloud.module.system.enums.material.MaterialReferenceEnums;

import java.util.List;

public interface IMaterialReferenceService {


    /**
     * 获取素材引用排行榜
     *
     * @return 素材引用排行榜
     * <AUTHOR>
     * @date 2023/7/18 17:24
     */
    List<MaterialTopVO> getMaterialReferenceTop(TaskTopReportDTO taskTopReportDTO);


    /**
     * 保存素材引用
     * @param referenceKey 引用key
     * @param referType 应用类型
     * @param materialIds 素材id列表
     */
    void saveMaterialReference(String referenceKey, MaterialReferenceEnums referType, List<Long> materialIds);


    /**
     * 删除素材引用
     * @param referenceKey 引用key
     */
    void deleteMaterialReferenceByReferenceKey(String referenceKey,MaterialReferenceEnums referType);


    /**
     * 删除素材引用
     * @param materialIds 素材id列表
     */
    void deleteMaterialReferenceByMaterialIds(List<Long> materialIds);


    /**
     * 获取素材引用列表
     * @param referenceKey 引用key
     * @param referType 应用类型
     * @return 素材id列表
     */
    List<Long> getMaterialIdsByReferenceKey(String referenceKey,MaterialReferenceEnums referType);
}
