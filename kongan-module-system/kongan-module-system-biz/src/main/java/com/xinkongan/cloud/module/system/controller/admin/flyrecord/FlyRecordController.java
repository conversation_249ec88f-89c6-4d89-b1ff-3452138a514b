package com.xinkongan.cloud.module.system.controller.admin.flyrecord;

import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.DroneFlyPoint;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.*;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordTaskDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyDO;
import com.xinkongan.cloud.module.system.enums.task.TaskSceneTypeEnum;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordTaskService;
import com.xinkongan.cloud.module.system.service.live.LiveService;
import com.xinkongan.cloud.module.system.service.task.IJobFlyService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;
import static com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants.*;

/**
 * @Description 飞行记录Controller
 * <AUTHOR>
 * @Date 2025/2/13 10:57
 */
@Validated
@RestController
@Tag(name = "管理后台 - 飞行记录")
@RequestMapping("/system/fly-record")
@Slf4j
public class FlyRecordController {

    @Resource
    private IFlyRecordService flyRecordService;// 飞行记录分页
    @Resource
    private IFlyRecordTaskService flyRecordTaskService;// 飞行记录定时删除Service
    @Resource
    private IJobService jobService;
    @Resource
    private IJobFlyService jobFlyService;
    @Resource
    private LiveService liveService;
    @Resource
    private DockDeviceService dockDeviceService;
    @Resource
    private IRedisCacheService redisCacheService;

    /**
     * 飞行记录分页
     *
     * <AUTHOR>
     * @date 2025年2月13日11:01:19
     **/
    @PreAuthorize("@ss.hasPermission('system:fly-record:query')")
    @GetMapping("/page")
    @Operation(summary = "飞行记录分页", description = "飞行记录分页")
    CommonResult<PageResult<FlyRecordPageRespVO>> page(@Valid FlyRecordPageReqVO reqVO) {
        PageResult<FlyRecordPageRespVO> page = flyRecordService.page(reqVO);
        return success(page);
    }

    @PreAuthorize("@ss.hasPermission('system:fly-record:info:query')")
    @GetMapping("/getById/{id}")
    @Operation(summary = "飞行记录详情", description = "飞行记录详情")
    CommonResult<FlyRecordDetailRespVO> getById(@PathVariable("id") Long id) {
        FlyRecordDetailRespVO detailRespVO = flyRecordService.detailById(id);
        return success(detailRespVO);
    }

    @PreAuthorize("@ss.hasPermission('system:fly-record:info:query')")
    @GetMapping("/info/{id}")
    @Operation(summary = "飞行记录基础信息", description = "飞行记录基础信息")
    CommonResult<FlyRecordDetailRespVO> info(@PathVariable("id") Long id) {
        FlyRecordDetailRespVO detailRespVO = flyRecordService.detailById(id);
        return success(detailRespVO);
    }

    @PreAuthorize("@ss.hasPermission('system:fly-record:share:edit')")
    @PostMapping(value = "/share")
    @Operation(summary = "飞行记录分享")
    public CommonResult<Void> share(@RequestBody @Valid FlyRecordShareVO flyRecordShareVO) {
        flyRecordService.share(flyRecordShareVO);
        return CommonResult.success();
    }

    @PreAuthorize("@ss.hasPermission('system:fly-record:share:query')")
    @GetMapping(value = "/list/share/{flyRecordId}")
    @Operation(summary = "查询飞行记录分享的组织")
    public CommonResult<FlyRecordShareVO> getFlyRecordShareDept(@PathVariable Long flyRecordId) {
        FlyRecordShareVO flyRecordShareDept = flyRecordService.getFlyRecordShareDept(flyRecordId);
        return CommonResult.success(flyRecordShareDept);
    }

    @PreAuthorize("@ss.hasPermission('system:fly-record:delete')")
    @PostMapping("/deleteById/{id}")
    @Operation(summary = "删除飞行记录", description = "删除飞行记录")
    CommonResult<Void> deleteById(@PathVariable("id") Long id) {
        flyRecordService.deleteById(id);
        return CommonResult.success();
    }

    @PreAuthorize("@ss.hasPermission('system:fly-record:delete-batch')")
    @PostMapping("/deleteByIds")
    @Operation(summary = "批量删除飞行记录", description = "批量删除飞行记录")
    CommonResult<Void> deleteByIds(@RequestBody List<Long> ids) {
        for (Long id : ids) {
            try {
                flyRecordService.deleteById(id);
            } catch (Exception e) {
                log.error("删除飞行记录失败", e);
            }
        }
        return CommonResult.success();
    }

    /**
     * 实时轨迹点
     *
     * <AUTHOR>
     * @date 2025/2/13 18:01
     **/
    @GetMapping("/getDroneFlyPoints/{flyRecordId}")
    @Operation(summary = "实时轨迹点", description = "实时轨迹点")
    public CommonResult<List<DroneFlyPoint>> getDroneFlyPoints(@PathVariable("flyRecordId") Long flyRecordId) {
        List<DroneFlyPoint> list = flyRecordService.getDroneFlyPoints(flyRecordId);
        return success(list);
    }

    /**
     * 修改重要程度
     *
     * <AUTHOR>
     * @date 2025/2/17 9:15
     **/
    @PreAuthorize("@ss.hasPermission('system:fly-record:info:edit')")
    @PostMapping("/updateImportant")
    @Operation(summary = "修改重要程度", description = "修改重要程度")
    public CommonResult<Boolean> updateImportant(@RequestBody @Valid UpdateImportantReqVO reqVO) {
        Boolean updated = flyRecordService.updateImportant(reqVO);
        return CommonResult.success(updated);
    }

    @PostMapping("/scheduleDeleteSet")
    @Operation(summary = "定期删除配置", description = "定期删除配置")
    public CommonResult<Boolean> scheduleDeleteSet(@RequestBody @Valid ScheduleDeleteSetReqVO reqVO) {
        Boolean deleted = flyRecordTaskService.scheduleDeleteSet(reqVO);
        return CommonResult.success(deleted);
    }

    @GetMapping("/getScheduleDeleteSet")
    @Operation(summary = "获取定期删除配置", description = "获取定期删除配置")
    public CommonResult<FlyRecordTaskDO> getScheduleDeleteSet() {
        FlyRecordTaskDO scheduleDeleteSet = flyRecordTaskService.getScheduleDeleteSet();
        return CommonResult.success(scheduleDeleteSet);
    }

    @PreAuthorize("@ss.hasPermission('system:fly-record:report:query')")
    @GetMapping("/flyRecordCount")
    @Operation(summary = "统计飞行记录数量", description = "统计飞行记录数量")
    public CommonResult<FlyRecordCountRespVO> getFlyRecordCount() {
        FlyRecordCountRespVO count = flyRecordService.getFlyRecordCount();
        return CommonResult.success(count);
    }

    @PreAuthorize("@ss.hasPermission('system:fly-record:report:query')")
    @GetMapping("/flyRecordCountByTime")
    @Operation(summary = "根据时间统计飞行记录数量", description = "根据时间统计飞行记录数量")
    public CommonResult<List<FlyRecordCountByTimeRespVO>> getFlyRecordCountByTime(@Valid FlyRecordCountByTimeReqVO reqVO) {
        List<FlyRecordCountByTimeRespVO> timeRespVOS = flyRecordService.getFlyRecordCountByTime(reqVO);
        return CommonResult.success(timeRespVOS);
    }

    @GetMapping("getPageNumById")
    @Operation(summary = "根据id获取页码", description = "根据id获取页码")
    public CommonResult<Integer> getPageNumById(@RequestParam("id") Long id, @RequestParam("pageSize") Integer pageSize) {
        Integer pageNum = flyRecordService.getPageNumById(id, pageSize);
        return CommonResult.success(pageNum);
    }

    @GetMapping("/relateFlyRecordPage")
    @Operation(summary = "关联飞行记录分页", description = "接关联飞行记录分页")
    CommonResult<PageResult<RelateFlyRecordPageRespVO>> relateFlyRecordPage(@Valid RelateFlyRecordPageReqVO reqVO) {
        PageResult<RelateFlyRecordPageRespVO> page = flyRecordService.relateFlyRecordPage(reqVO);
        return success(page);
    }

    /**
     * 当意外情况下飞行记录没有结束掉 手动调用此方法结束飞行记录
     * 例如破降到机场上方的某个地方 无法触发结束飞行记录 例如飞行过程中炸机
     **/
    @GetMapping("/endDockFlyRecord")
    CommonResult<String> endDockFlyRecord(String dockSn, Long flyRecordId, Integer stopLiveRecord, Integer stopLive) {
        log.info("结束飞行记录 dockSn:{},flyRecordId:{},stopLiveRecord:{},stopLive:{}", dockSn, flyRecordId, stopLiveRecord, stopLive);
        // 结束飞行记录
        flyRecordService.endDockFlyRecord(dockSn, flyRecordId);
        try {
            // 删除非任务类型飞行缓存 任务类型的在任务进度上报中删除
            JobFlyInfo jobInfo = jobService.getExecTaskCacheByDockSn(dockSn);
            if (jobInfo != null && !TaskSceneTypeEnum.flyIsJob(jobInfo.getScene())) {
                // 删除缓存
                jobService.deleteExecTaskCacheByDockSn(dockSn);
                // 飞行结束
                jobFlyService.updateById(JobFlyDO.builder().id(jobInfo.getFlyId()).execEndTime(LocalDateTime.now()).status(2).build());
            }
            DockDeviceDO byDBDeviceSn = dockDeviceService.getByDBDeviceSn(dockSn);
            if (stopLiveRecord != null && stopLiveRecord == 1) {
                // 结束录制
                liveService.stopLiveRecord(byDBDeviceSn.getChildSn());
            }
            if (stopLive != null && stopLive == 1) {
                // 结束直播
                liveService.stopLivePush(dockSn, byDBDeviceSn.getChildSn());
            }
            // 删除飞行记录缓存
            redisCacheService.invalidate(String.format(DOCK_SN_FLY_RECORD_ID, dockSn));
            // 删除飞行记录osd数据缓存
            redisCacheService.invalidate(String.format(DOCK_SN_FLY_RECORD_OSD, dockSn, flyRecordId));
            // 删除飞行记录实时轨迹数据
            redisCacheService.invalidate(String.format(DOCK_SN_DRONE_POINT, flyRecordId));
        } catch (Exception e) {
            log.error("结束飞行记录失败", e);
            return CommonResult.success(e.getMessage() + "\n" + JSONUtil.toJsonStr(e.getStackTrace()));
        }
        return CommonResult.success("ok");
    }

}