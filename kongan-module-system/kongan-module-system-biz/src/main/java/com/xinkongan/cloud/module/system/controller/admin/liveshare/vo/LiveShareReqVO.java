package com.xinkongan.cloud.module.system.controller.admin.liveshare.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
public class LiveShareReqVO {

    @Schema(description = "直播分享是否加密")
    private Integer encipher;

    @Schema(description = "直播分享的密码")
    private String password;

    @Schema(description = "分享的直播通道")
    private String channel;

    @Schema(description = "机场SN")
    private String dockSn;

    @Schema(description = "分享的直播有效期，单位为分钟")
    private Integer validity;

}
