package com.xinkongan.cloud.module.system.controller.admin.task.vo.apply;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import com.xinkongan.cloud.module.system.enums.task.TaskApplyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description 任务申请分页ReqVO
 * <AUTHOR>
 * @Date 2024/12/24 9:34
 */
@Data
@Schema(description = "任务申请分页ReqVO")
public class ApplyPageReqVO extends PageParam {

    @Schema(description = "机场sn", required = true)
    @NotBlank(message = "机场sn")
    private String dockSn;
    
    @Schema(description = "任务/组织名称模糊查询")
    private String taskOrDeptName;

    /**
     * {@link TaskApplyEnum}
     * <AUTHOR>
     * @date 2024/12/25 11:47
     **/
    @Schema(description = "审批状态 0待审批1已通过2未通过3已取消4已过期")
    private Integer status;
}