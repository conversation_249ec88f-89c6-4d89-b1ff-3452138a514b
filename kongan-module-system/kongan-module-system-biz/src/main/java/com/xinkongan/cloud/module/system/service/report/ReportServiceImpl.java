package com.xinkongan.cloud.module.system.service.report;


import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.*;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteDO;
import com.xinkongan.cloud.module.system.dal.mysql.route.RouteMapper;
import com.xinkongan.cloud.module.system.dto.TaskTopReportDTO;
import com.xinkongan.cloud.module.system.enums.route.RouteTypeEnum;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.permission.RoleService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

@Data
@Service
public class ReportServiceImpl implements IReportService {

    @Resource
    private DeptService deptService;

    @Resource
    private RoleService roleService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private IJobService jobService;

    @Resource
    private RouteMapper routeMapper;


    @Override
    public AuthDataReportVO getBasicAuthReport() {
        AuthDataReportVO authDataReportVO = new AuthDataReportVO();

        // 查询组织总数
        List<DeptDO> deptList = deptService.getDeptList();
        authDataReportVO.setDeptCount((long) deptList.size());

        // 查询角色总数
        List<RoleDO> roleList = roleService.getRoleList();
        authDataReportVO.setRoleCount((long) roleList.size());

        // 用户数目
        Long userCount = adminUserService.getUserCount();
        authDataReportVO.setUserCount(userCount);

        // 角色饼图查询
        List<RoleStatisticVO> roleStatisticInfo = roleService.getRoleStatisticInfo();
        authDataReportVO.setRoleStatistic(roleStatisticInfo);

        return authDataReportVO;
    }


    @Override
    public List<TaskTopStatisticVO> getTaskTopStatisticReport(TaskTopReportDTO taskTopReport) {
        LocalDateTime[] timeRange = this.getTimeRange(taskTopReport.getPeriodType());
        return jobService.getTaskTopStatisticInfo(timeRange);
    }


    @Override
    public RouteDataReportVO getRouteDataReportStatistic() {
        Long routeNum = routeMapper.selectCount(
                new LambdaQueryWrapperX<RouteDO>()
                        .eq(RouteDO::getVisible, 1)
        );
        Long patrolRouteNum = routeMapper.selectCount(
                new LambdaQueryWrapperX<RouteDO>()
                        .eq(RouteDO::getVisible, 1)
                        .eq(RouteDO::getRouteType, RouteTypeEnum.WAY_POINT_ROUTE.getType())
        );
        Long modelRouteNum = routeMapper.selectCount(
                new LambdaQueryWrapperX<RouteDO>()
                        .eq(RouteDO::getVisible, 1)
                        .eq(RouteDO::getRouteType, RouteTypeEnum.MODELING_ROUTE.getType())
        );
        RouteDataReportVO routeDataReportVO = new RouteDataReportVO();
        routeDataReportVO.setRouteNum(routeNum.intValue());
        routeDataReportVO.setPatrolRouteNum(patrolRouteNum.intValue());
        routeDataReportVO.setModelRouteNum(modelRouteNum.intValue());
        routeDataReportVO.setRouteStatistic(routeMapper.selectRouteStatistic());
        return routeDataReportVO;
    }


    @Override
    public List<RouteTopStatisticVO> getRouteTopStatisticReport(TaskTopReportDTO taskTopReport) {
        LocalDateTime[] timeRange = this.getTimeRange(taskTopReport.getPeriodType());
        return routeMapper.getRouteTopStatisticInfo(timeRange);
    }

    @Override
    public LocalDateTime[] getTimeRange(int type) {
        LocalDate now = LocalDate.now();
        LocalDateTime[] range = new LocalDateTime[2]; // 数组索引0=开始时间，1=结束时间

        switch (type) {
            case 1: // 本周（周一到周日）
                LocalDate startOfWeek = now.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
                LocalDate endOfWeek = now.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY));
                range[0] = startOfWeek.atTime(LocalTime.MIN); // 00:00:00
                range[1] = endOfWeek.atTime(LocalTime.MAX);   // 23:59:59.999999999
                break;
            case 2: // 本月
                LocalDate startOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
                LocalDate endOfMonth = now.with(TemporalAdjusters.lastDayOfMonth());
                range[0] = startOfMonth.atTime(LocalTime.MIN); // 00:00:00
                range[1] = endOfMonth.atTime(LocalTime.MAX);   // 23:59:59.999999999
                break;
            case 3: // 本年
                LocalDate startOfYear = now.with(TemporalAdjusters.firstDayOfYear());
                LocalDate endOfYear = now.with(TemporalAdjusters.lastDayOfYear());
                range[0] = startOfYear.atTime(LocalTime.MIN); // 00:00:00
                range[1] = endOfYear.atTime(LocalTime.MAX);   // 23:59:59.999999999
                break;
            default:
                throw new IllegalArgumentException("Invalid type: " + type);
        }
        return range;
    }
}
