package com.xinkongan.cloud.module.system.controller.admin.task.vo.demand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 需求统计数量
 */
@Schema(description = "需求统计数量")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DemandStatisticCountVO {

    @Schema(description = "已提交数量")
    private Long submitCount = 0L;

    @Schema(description = "已规划数量")
    private Long planCount = 0L;

    @Schema(description = "已完成数量")
    private Long completeCount = 0L;
}