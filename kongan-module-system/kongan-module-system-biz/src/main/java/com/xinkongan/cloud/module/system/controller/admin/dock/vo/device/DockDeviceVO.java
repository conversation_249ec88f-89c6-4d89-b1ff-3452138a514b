package com.xinkongan.cloud.module.system.controller.admin.dock.vo.device;

import com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage.DockCoverageVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DeviceDictionaryDO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DockModeCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DockDeviceVO extends DockDeviceDO {

    @Schema(description = "无人机设备")
    private DockDeviceVO child;

    @Schema(description = "组织名称")
    private String deptName;

    @Schema(description = "设备类型信息")
    private DeviceDictionaryDO dictionary;

    @Schema(description = "共享状态")
    private Integer shareStatus;

    @Schema(description = "机场状态")
    private DockModeCodeEnum modeCode;

    @Schema(description = "是否是外部机场 1是0否")
    private Integer without;

    @Schema(description = "设备相机负载信息")
    private DockDeviceCameraVO cameraPayloadInfo;

    @Schema(description = "机场的可飞范围")
    private DockCoverageVO dockCoverageVO;
}
