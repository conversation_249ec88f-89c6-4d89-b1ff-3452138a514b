package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 图片对比分页ReqVO
 * <AUTHOR>
 * @Date 2025/2/19 16:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "飞行记录对比分页")
public class ContrastPageReqVO extends PageParam {

    @Schema(description = "飞行记录id")
    @NotNull(message = "飞行记录id不能为空")
    private Long flyRecordId;

    @Schema(description = "搜索时间段(创建时间)开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime beginTime;

    @Schema(description = "搜索时间段(创建时间)结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @Schema(description = "对比记录/所属组织名称 模糊查询")
    private String searchKey;
}