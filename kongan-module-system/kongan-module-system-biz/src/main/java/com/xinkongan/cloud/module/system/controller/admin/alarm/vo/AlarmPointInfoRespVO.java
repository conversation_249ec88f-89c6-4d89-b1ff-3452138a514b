package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/24
 */
@Data
public class AlarmPointInfoRespVO {

    @Schema(description = "警情点id")
    private Long alarmId;

    @Schema(description = "任务ID")
    private Long jobId;

    @Schema(description = "警情地址")
    private String address;

    @Schema(description = "警情状态")
    private Integer status;

    @Schema(description = "所属组织名称")
    private String deptName;

}