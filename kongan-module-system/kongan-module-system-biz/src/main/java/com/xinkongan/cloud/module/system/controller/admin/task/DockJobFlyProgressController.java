package com.xinkongan.cloud.module.system.controller.admin.task;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.common.annotation.DeviceControlCheck;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask.JobFlyProgressRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask.RouteDynamicStateRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyProgressDetailDO;
import com.xinkongan.cloud.module.system.service.task.IJobFlyProgressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/17
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 机场任务进度")
@RequestMapping("/system/jobProgress")
public class DockJobFlyProgressController {

    @Resource
    private IJobFlyProgressService progressService;

    @DeviceControlCheck(sn = "#dockSn")
    @Operation(summary = "查询当前机场的任务进度详情")
    @GetMapping("/getByDockSn/{dockSn}")
    public CommonResult<JobFlyProgressRespVO> getByDockSn(@PathVariable("dockSn") String dockSn) {
        JobFlyProgressRespVO respVO = progressService.getByDockSn(dockSn);
        return CommonResult.success(respVO);
    }

    @DeviceControlCheck(sn = "#dockSn")
    @Operation(summary = "查询当前机场航线动态")
    @GetMapping("/getRouteDynamicState/{dockSn}")
    public CommonResult<RouteDynamicStateRespVO> getRouteDynamicState(@PathVariable("dockSn") String dockSn) {
        RouteDynamicStateRespVO respVO = progressService.getRouteDynamicByDockSn(dockSn);
        return CommonResult.success(respVO);
    }

    @Operation(summary = "查询指定飞行记录所有任务进度")
    @GetMapping("/getByFlyRecordId/{flyRecordId}")
    public CommonResult<List<JobFlyProgressDetailDO>> getDetailsByFlyRecordId(@PathVariable("flyRecordId") Long flyRecordId) {
        List<JobFlyProgressDetailDO> respVO = progressService.getDetailsByFlyRecordId(flyRecordId);
        return CommonResult.success(respVO);
    }

}
