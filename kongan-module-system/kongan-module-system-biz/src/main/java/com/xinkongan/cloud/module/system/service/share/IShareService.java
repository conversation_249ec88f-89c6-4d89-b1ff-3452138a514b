package com.xinkongan.cloud.module.system.service.share;

import com.xinkongan.cloud.module.system.dto.ResourceShareDTO;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IShareService {


    /**
     *
     * @param resourceId 资源id
     * @param resourceType 资源类型
     */
    void removeResourceShare(Long resourceId,Integer resourceType);


    /**
     * 资源分享
     * @param resourceShareInfo
     */
    void resourceShareDeptIds(ResourceShareDTO resourceShareInfo);


    /**
     * 判断资源是否被分享
     * @param resourceId 资源id
     * @param resourceType 资源类型 {@link ResourceShareTypeEnum#ROUTE_RESOURCE}
     * @return true 分享，false 没有分享
     */
    boolean checkResourceShare(Long resourceId,Integer resourceType);


    /**
     * 批量查询资源是否被分享
     * @param resourceIds 资源id
     * @param resourceType 资源类型 {@link ResourceShareTypeEnum#ROUTE_RESOURCE}
     * @return true 分享，false 没有分享
     */
    Map<Long,Boolean> checkResourceShare(List<Long> resourceIds, Integer resourceType);


    /**
     * 查询资源分享到那些组织中
     * @param resourceId 资源id
     * @return 组织列表
     */
    List<Long> getResourceSharesDept(Long resourceId);

    /**
     * 查询哪些资源被分享到指定组织中
     * @date 2025/2/19 10:54
     **/
    List<Long> getDeptShareIds(Long deptId, ResourceShareTypeEnum resourceType);

    /**
     * 查询资源是否被分享到指定组织中
     * @date 2025/2/19 10:54
     **/
    boolean checkResourceShare(Long resourceId,Integer resourceType, Set<Long> deptIds);


}
