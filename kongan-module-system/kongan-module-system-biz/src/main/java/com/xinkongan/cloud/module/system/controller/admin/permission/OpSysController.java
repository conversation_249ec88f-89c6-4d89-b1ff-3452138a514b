package com.xinkongan.cloud.module.system.controller.admin.permission;


import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.ratelimiter.core.annotation.RateLimiter;
import com.xinkongan.cloud.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import com.xinkongan.cloud.module.system.controller.admin.permission.dto.TenantCacheRefreshDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.framework.operation.OperationSysConfig;
import com.xinkongan.cloud.module.system.service.permission.IAuthRefreshCacheService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;


@Slf4j
@Validated
@RestController
@Tag(name = "飞控平台 - 运营端访问接口")
@RequestMapping("/system/operation")
public class OpSysController {

    @Resource
    private OperationSysConfig operationSysConfig;

    @Resource
    private IAuthRefreshCacheService authRefreshCacheService;


    @PermitAll
    @PostMapping("/refresh/tenant/cache")
    @Operation(summary = "运营端访问接口-刷新菜单权限缓存")
    @RateLimiter(keyResolver = ClientIpRateLimiterKeyResolver.class, time = 1, count = 5)
    public CommonResult<Void> refreshTenantPermissionCache(@RequestBody @Validated TenantCacheRefreshDTO tenantCacheRefreshInfo) {
        if (!Objects.equals(tenantCacheRefreshInfo.getAccessKey(), operationSysConfig.getAccessKey())) {
            log.info("accessKey 不匹配，不刷新缓存");
            throw new ServiceException(ErrorCodeConstants.ACCESS_KEY_ERROR);
        }
        authRefreshCacheService.refreshSystemUserAuthCache(tenantCacheRefreshInfo.getTenantId());
        return CommonResult.success();
    }
}
