package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import com.xinkongan.cloud.module.system.api.flyrecord.dto.DroneFlyPoint;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteDetailRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DockRouteAndFlyRecordRespVO {

    @Schema(description = "航线线详情")
    private RouteDetailRespVO routeDetail;

    @Schema(description = "正在飞行的飞行记录轨迹点")
    private List<DroneFlyPoint> flyPointList;

}
