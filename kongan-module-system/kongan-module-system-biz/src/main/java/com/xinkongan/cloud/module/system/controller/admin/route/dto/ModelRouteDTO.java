package com.xinkongan.cloud.module.system.controller.admin.route.dto;

import com.xinkongan.cloud.framework.kmz.dto.MappingHeadingParam;
import com.xinkongan.cloud.framework.kmz.dto.Overlap;
import com.xinkongan.cloud.framework.kmz.dto.PolygonLocation;
import com.xinkongan.cloud.framework.kmz.enums.GimbalPitchModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * @Description
 * <AUTHOR>
 * @Date 2025/6/12 9:12
 */
@Data
public class ModelRouteDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 航线id
     */
    @Schema(description = "航线id", example = "1")
    private Long routeId;

    /**
     * 是否开启高程优化(建图航拍)
     */
    @Schema(description = "是否开启高程优化(建图航拍)", example = "false")
    private Integer elevationOptimizeEnable;

    /**
     * 是否开启智能摆拍(建图航拍)
     */
    @Schema(description = "是否开启智能摆拍(建图航拍)", example = "false")
    private Integer smartObliqueEnable = 0;

    /**
     * 智能摆拍拍摄俯仰角(建图航拍)
     */
    @Schema(description = "智能摆拍拍摄俯仰角(建图航拍)", example = "")
    private Integer smartObliqueGimbalPitch;

    /**
     * 拍照模式（定时或定距）(建模航线)
     */
    @Schema(description = "拍照模式（定时或定距）(建模航线) time 或 distance ", example = "time")
    private String shootType = "time";

    /**
     * 航线方向(建模航线)
     */
    @Schema(description = "主航线角度 [0,360]", example = "0")
    private Integer direction;

    /**
     * 重叠率参数(建模航线)
     */
    @Schema(description = "重叠率参数", example = "")
    private Overlap overlap;

    /**
     * 是否开启斜立面(建图航拍)
     * 该元素与 ”LinearRing“ 配合使用，开启后将按照椭球该读取其中的高度值
     * 此处格式如“<Polygon> <outerBoundaryIs> <LinearRing> <coordinates> 经度,纬度,高度 经度,纬度,高度 经度,纬度,高度 </coordinates> </LinearRing> </outerBoundaryIs> </Polygon>”
     * 注：当 wpml:facadeWaylineEnable 为 1 时，测区多边形支持空中面，
     * 如“<Polygon> <outerBoundaryIs> <LinearRing> <coordinates> 经度,纬度,300 经度,纬度,200 经度,纬度,50 </coordinates> </LinearRing> </outerBoundaryIs> </Polygon>”，航线生成方向与端点顺序有关
     */
    @Schema(description = "是否开启斜立面(建图航拍)", example = "true")
    private Integer facadeWaylineEnable = 0;

    @Schema(description = "测区多边形的测区点坐标列表", example = "")
    private List<PolygonLocation> polygonLocations;

    /**
     * 全局航线高度（椭球高）(建图航拍)
     * 如果 wpml:height 选用相对起飞点高度，则 wpml:ellipsoidHeight 和 wpml:height 相同；
     * 如果 wpml:height 选用 EGM96 海拔高度或 AGL 相对地面高度，
     * 则 wpml:wpml:ellipsoidHeight 由 wpml:height 做相应转换得到
     */
    @Schema(description = "全局航线高度（椭球高）(建模航线)", example = "")
    private Double ellipsoidHeight;

    /**
     * 全局航线高度（EGM96海拔高/相对起飞点高度/AGL相对地面高度）(建图航拍)
     * 该元素与 ellipsoidHeight 配合使用，二者是同一位置不同高程参考平面的表达
     */
    @Schema(description = "全局航线高度（EGM96海拔高/相对起飞点高度/AGL相对地面高度）(建模航线)", example = "")
    private Double height;

    /**
     * 飞行器离被摄面高度（相对地面高）
     * 注：仅适用于模板类型mapping2d，mapping3d，mappingStrip
     * 用于计算拍照间距和GSD
     * 航点航线用不到
     */
    @Schema(description = "飞行器离被摄面高度（相对地面高）", example = "")
    private Double globalShootHeight;

    /**
     * 被摄面绝对高度(建模航线)
     */
    @Schema(description = "被摄面绝对高度(建模航线)", example = "")
    private Double surfaceAbsoluteHeight;

    /**
     * 云台俯仰角模式（建图航拍）
     */
    @Schema(description = "云台俯仰角模式（建图航拍）", example = "")
    private String gimbalPitchMode = GimbalPitchModeEnum.fixed.getMode();

    /**
     * 云台俯仰角度（建图航拍）
     */
    @Schema(description = "云台俯仰角度（建图航拍）", example = "")
    private Integer gimbalPitchAngle = -90;


    /**
     * 建图航拍飞行器朝向参数
     */
    @Schema(description = "建图航拍飞行器朝向参数", example = "")
    private MappingHeadingParam mappingHeadingParam;

    /**
     * 全局航线过渡速度(起飞速度)
     */
    @Schema(description = "全局航线过渡速度(起飞速度)", example = "")
    private Float globalTransitionalSpeed;

    /**
     * 地面采样距离(广角)
     */
    @Schema(description = "地面采样距离GSD(广角)", example = "")
    private String wideAngleGsd;

    /**
     * 地面采样距离(倾斜)
     */
    @Schema(description = "地面采样距离GSD(倾斜)", example = "")
    private String inclineGsd;

    /**
     * 地面采样距离(红外)
     */
    @Schema(description = "地面采样距离GSD(红外)", example = "")
    private String infraredGsd;

    /**
     * 是否开启自定义相机角度(建模航线)
     */
    @Schema(description = "是否开启自定义相机角度(建图航拍)", example = "0")
    private Integer customAngleEnable;

    /**
     * 是否开启倾斜航线飞行速度设置(倾斜摄影)
     */
    @Schema(description = "是否开启倾斜航线飞行速度设置(倾斜摄影)", example = "0")
    private Integer inclinedFlightSpeedEnable;

    /**
     * 云台俯仰角度（倾斜）
     */
    @Schema(description = "云台俯仰角度（倾斜）", example = "")
    private Integer  inclinedGimbalPitch = -45;

    /**
     * 航线飞行速度（倾斜）
     */
    @Schema(description = "航线飞行速度（倾斜）", example = "")
    private Float  inclinedFlightSpeed;

}
