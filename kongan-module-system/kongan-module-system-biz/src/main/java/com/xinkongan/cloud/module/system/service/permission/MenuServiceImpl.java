package com.xinkongan.cloud.module.system.service.permission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjUtil;
import com.xinkongan.cloud.framework.common.enums.CommonStatusEnum;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.env.config.SystemEnv;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.module.system.controller.admin.auth.vo.AuthPermissionInfoRespVO;
import com.xinkongan.cloud.module.system.controller.admin.dict.vo.DictInfoVO;
import com.xinkongan.cloud.module.system.controller.admin.permission.vo.menu.MenuListReqVO;
import com.xinkongan.cloud.module.system.controller.admin.permission.vo.menu.MenuSaveVO;
import com.xinkongan.cloud.module.system.convert.auth.AuthConvert;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.MenuDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;
import com.xinkongan.cloud.module.system.dal.dataobject.tenant.TenantDO;
import com.xinkongan.cloud.module.system.dal.mysql.permission.MenuMapper;
import com.xinkongan.cloud.module.system.enums.dict.SystemGeneralDictCode;
import com.xinkongan.cloud.module.system.enums.menu.MenuTypeEnums;
import com.xinkongan.cloud.module.system.enums.permission.MenuTypeEnum;
import com.xinkongan.cloud.module.system.service.dict.IDictService;
import com.xinkongan.cloud.module.system.service.tenant.TenantService;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.framework.common.util.collection.CollectionUtils.*;
import static com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.xinkongan.cloud.module.system.dal.dataobject.permission.MenuDO.ID_ROOT;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 菜单 Service 实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MenuServiceImpl implements MenuService {

    @Resource
    private MenuMapper menuMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private TenantService tenantService;

    @Resource
    private RoleService roleService;

    @Resource
    private SystemEnv systemEnv;

    @Resource
    private IDictService dictService;

    @Override
    public Long createMenu(MenuSaveVO createReqVO) {
        // 校验父菜单存在
        validateParentMenu(createReqVO.getParentId(), null);
        // 校验菜单（自己）
        validateMenu(createReqVO.getParentId(), createReqVO.getName(), null);

        // 插入数据库
        MenuDO menu = BeanUtils.toBean(createReqVO, MenuDO.class);
        menu.setMenuType(MenuTypeEnums.BUSINESS_MENU.getType());
        initMenuProperty(menu);
        menuMapper.insert(menu);
        // 返回
        return menu.getId();
    }

    @Override
    public void updateMenu(MenuSaveVO updateReqVO) {
        // 校验更新的菜单是否存在
        if (menuMapper.selectById(updateReqVO.getId()) == null) {
            throw exception(MENU_NOT_EXISTS);
        }
        // 校验父菜单存在
        validateParentMenu(updateReqVO.getParentId(), updateReqVO.getId());
        // 校验菜单（自己）
        validateMenu(updateReqVO.getParentId(), updateReqVO.getName(), updateReqVO.getId());

        // 更新到数据库
        MenuDO updateObj = BeanUtils.toBean(updateReqVO, MenuDO.class);
        initMenuProperty(updateObj);
        menuMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMenu(Long id) {
        // 校验是否还有子菜单
        if (menuMapper.selectCountByParentId(id) > 0) {
            throw exception(MENU_EXISTS_CHILDREN);
        }
        // 校验删除的菜单是否存在
        if (menuMapper.selectById(id) == null) {
            throw exception(MENU_NOT_EXISTS);
        }
        // 标记删除
        menuMapper.deleteById(id);
        // 删除授予给角色的权限
        permissionService.processMenuDeleted(id);
    }

    @Override
    public List<MenuDO> getMenuList() {
        return menuMapper.selectList();
    }

    @Override
    public List<MenuDO> getMenuListByTenant(MenuListReqVO reqVO) {
        // 查询所有菜单，并过滤掉关闭的节点
        List<MenuDO> menus = getMenuList(reqVO);
        // 开启多租户的情况下，需要过滤掉未开通的菜单
        tenantService.handleTenantMenu(menuIds -> menus.removeIf(menu -> !CollUtil.contains(menuIds, menu.getId())));
        return menus;
    }

    @Override
    public List<MenuDO> filterDisableMenus(List<MenuDO> menuList) {
        if (CollUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }
        Map<Long, MenuDO> menuMap = convertMap(menuList, MenuDO::getId);

        // 遍历 menu 菜单，查找不是禁用的菜单，添加到 enabledMenus 结果
        List<MenuDO> enabledMenus = new ArrayList<>();
        Set<Long> disabledMenuCache = new HashSet<>(); // 存下递归搜索过被禁用的菜单，防止重复的搜索
        for (MenuDO menu : menuList) {
            if (isMenuDisabled(menu, menuMap, disabledMenuCache)) {
                continue;
            }
            enabledMenus.add(menu);
        }
        return enabledMenus;
    }

    private boolean isMenuDisabled(MenuDO node, Map<Long, MenuDO> menuMap, Set<Long> disabledMenuCache) {
        // 如果已经判定是禁用的节点，直接结束
        if (disabledMenuCache.contains(node.getId())) {
            return true;
        }

        // 1. 先判断自身是否禁用
        if (CommonStatusEnum.isDisable(node.getStatus())) {
            disabledMenuCache.add(node.getId());
            return true;
        }

        // 2. 遍历到 parentId 为根节点，则无需判断
        Long parentId = node.getParentId();
        if (ObjUtil.equal(parentId, ID_ROOT)) {
            return false;
        }

        // 3. 继续遍历 parent 节点
        MenuDO parent = menuMap.get(parentId);
        if (parent == null || isMenuDisabled(parent, menuMap, disabledMenuCache)) {
            disabledMenuCache.add(node.getId());
            return true;
        }
        return false;
    }

    @Override
    public List<MenuDO> getMenuList(MenuListReqVO reqVO) {
        return menuMapper.selectList(reqVO);
    }

    @Override
    public List<Long> getMenuIdListByPermissionFromCache(String permission) {
        List<MenuDO> menus = menuMapper.selectListByPermission(permission);
        return convertList(menus, MenuDO::getId);
    }

    @Override
    public MenuDO getMenu(Long id) {
        return menuMapper.selectById(id);
    }

    @Override
    public List<MenuDO> getMenuList(Collection<Long> ids) {
        // 当 ids 为空时，返回一个空的实例对象
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return menuMapper.selectBatchIds(ids);
    }

    /**
     * 校验父菜单是否合法
     * <p>
     * 1. 不能设置自己为父菜单
     * 2. 父菜单不存在
     * 3. 父菜单必须是 {@link MenuTypeEnum#MENU} 菜单类型
     *
     * @param parentId 父菜单编号
     * @param childId  当前菜单编号
     */
    private void validateParentMenu(Long parentId, Long childId) {
        if (parentId == null || ID_ROOT.equals(parentId)) {
            return;
        }
        // 不能设置自己为父菜单
        if (parentId.equals(childId)) {
            throw exception(MENU_PARENT_ERROR);
        }
        MenuDO menu = menuMapper.selectById(parentId);
        // 父菜单不存在
        if (menu == null) {
            throw exception(MENU_PARENT_NOT_EXISTS);
        }
        // 父菜单必须是目录或者菜单类型
        if (!MenuTypeEnum.DIR.getType().equals(menu.getType())
                && !MenuTypeEnum.MENU.getType().equals(menu.getType())) {
            throw exception(MENU_PARENT_NOT_DIR_OR_MENU);
        }
    }

    /**
     * 校验菜单是否合法
     * <p>
     * 1. 校验相同父菜单编号下，是否存在相同的菜单名
     *
     * @param name     菜单名字
     * @param parentId 父菜单编号
     * @param id       菜单编号
     */

    private void validateMenu(Long parentId, String name, Long id) {
        MenuDO menu = menuMapper.selectByParentIdAndName(parentId, name);
        if (menu == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的菜单
        if (id == null) {
            throw exception(MENU_NAME_DUPLICATE);
        }
        if (!menu.getId().equals(id)) {
            throw exception(MENU_NAME_DUPLICATE);
        }
    }

    /**
     * 初始化菜单的通用属性。
     * <p>
     * 例如说，只有目录或者菜单类型的菜单，才设置 icon
     *
     * @param menu 菜单
     */
    private void initMenuProperty(MenuDO menu) {
        // 菜单为按钮类型时，无需 component、icon、path 属性，进行置空
        if (MenuTypeEnum.BUTTON.getType().equals(menu.getType())) {
            menu.setComponent("");
            menu.setComponentName("");
            menu.setIcon("");
            menu.setPath("");
        }
    }

    @Override
    public List<AuthPermissionInfoRespVO.MenuVO> getMenuTreeInfo() {

        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        if (CollectionUtil.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(roleIds);
        List<MenuDO> menuList = this.getMenuList(menuIds);
        menuList = this.filterDisableMenus(menuList);
        return AuthConvert.INSTANCE.buildMenuTree(menuList, false);
    }

    @Override
    public List<AuthPermissionInfoRespVO.MenuVO> getButtonsByMenuId(Long menuId) {
        // 查询该菜单下的按钮
        List<MenuDO> menuButtons = menuMapper.selectList(
                new LambdaQueryWrapperX<MenuDO>()
                        .eq(MenuDO::getParentId, menuId)
                        .eq(MenuDO::getType, MenuTypeEnum.BUTTON)
                        .eq(MenuDO::getStatus, CommonStatusEnum.ENABLE.getStatus()));
        if (CollectionUtil.isEmpty(menuButtons)) {
            return new ArrayList<>();
        }
        // 查询用户拥有的菜单权限id
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        if (CollectionUtil.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(roleIds);

        // 过滤掉没有权限的按钮
        return menuButtons.stream().filter(m -> menuIds.contains(m.getId())).map(AuthConvert.INSTANCE::convertTreeNode).toList();
    }

    @Override
    @TenantIgnore
    @DataPermission(enable = false)
    public void assignMenuToAllTenant(Set<Long> menuIds) {
        if (systemEnv.isPre() || systemEnv.isProd()) {
            return;
        }
        // 查询所有租户
        List<TenantDO> tenantList = tenantService.getTenantList();
        for (TenantDO tenantDO : tenantList) {
            try {
                // 查询租户管理员
                List<RoleDO> tenantAdminRole = roleService.getTenantAdminRole(tenantDO.getId());
                for (RoleDO roleInfo : tenantAdminRole) {
                    // 为该角色分配菜单
                    permissionService.assignRoleMenu(roleInfo.getId(), menuIds);
                }
                // 租户信息修改
                List<Long> tenantMenus = tenantDO.getMenuIds();
                tenantMenus.addAll(menuIds);
                // 集合去重
                Set<Long> tenantMenuSets = new HashSet<>(tenantMenus);
                tenantDO.setMenuIds(tenantMenuSets.stream().toList());
                tenantService.updateTenantInfo(tenantDO);
            } catch (Exception e) {
                log.error("[执行租户菜单分配出现异常]，异常信息为：{}", e.getMessage());
                log.error(ExceptionUtils.getStackTrace(e));
            }
        }
    }
}
