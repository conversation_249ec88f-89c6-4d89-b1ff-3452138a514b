package com.xinkongan.cloud.module.system.framework.datapermission.config;

import com.xinkongan.cloud.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import com.xinkongan.cloud.module.system.dal.dataobject.label.LabelDO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteAlgorithmDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteDO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.TaskDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * system 模块的数据权限 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class DataPermissionConfiguration {

    @Bean
    public DeptDataPermissionRuleCustomizer sysDeptDataPermissionRuleCustomizer() {
        return rule -> {
            // dept
            rule.addDeptColumn(AdminUserDO.class);
            rule.addDeptColumn(DeptDO.class, "id");
            // user
            rule.addUserColumn(AdminUserDO.class, "id");
            // role
            rule.addDeptColumn(RoleDO.class);
            // route
            rule.addDeptColumn(RouteDO.class);
            // dock_device
            rule.addDeptColumn(DockDeviceDO.class);
            // system_job
            rule.addDeptColumn(JobDO.class);
            // system_task
            rule.addUserColumn(TaskDO.class);
            // system_job_fly
            rule.addDeptColumn(JobFlyDO.class);
            // system_fly_record
            rule.addDeptColumn(FlyRecordDO.class);
            // system_fly_record_file
            rule.addDeptColumn(FlyRecordFileDO.class);
            // system_material
            rule.addDeptColumn(MaterialDO.class);
            // system_label
            rule.addDeptColumn(LabelDO.class);
            // system_way_point_algorithm
            rule.addDeptColumn(RouteAlgorithmDO.class);
        };
    }

}
