package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.config.DockDeviceConfigVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.config.UpdateDockDeviceConfigReqVO;
import com.xinkongan.cloud.module.system.service.dock.dockconfig.IDockDeviceConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * // * @Description 机场设置
 *
 * <AUTHOR>
 * @Date 2024/12/23 11:33
 */
@Validated
@RestController
@Tag(name = "管理后台 - 机场设置")
@RequestMapping("/system/dockDeviceConfig")
public class DockDeviceConfigController {

    @Resource
    private IDockDeviceConfigService dockDeviceConfigService;

    /**
     * 查询机场是否开启了"上级可管理机场"设置
     * <AUTHOR>
     * @date 2025/1/10 9:20
     **/
    @GetMapping("/isParentEdit/{dockSn}")
    @Operation(summary = "查询机场是否开启了'上级可管理机场'设置", description = "查询机场是否开启了'上级可管理机场'设置")
    public CommonResult<Boolean> isParentEdit(@PathVariable("dockSn") String dockSn) {
        Long tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        Boolean isParentEdit = dockDeviceConfigService.isParentEdit(dockSn, tenantId);
        return CommonResult.success(isParentEdit);
    }

    @GetMapping("/getByDockSn/{dockSn}")
    @PreAuthorize("@ss.hasPermission('system:dock:config:query')")
    @Operation(summary = "根据机场sn查询共享配置", description = "根据机场sn查询共享配置")
    public CommonResult<DockDeviceConfigVO> getDockShareByDockSn(@PathVariable("dockSn") String dockSn) {
        Long tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        DockDeviceConfigVO dockDeviceConfigVO = dockDeviceConfigService.getDockDeviceConfigByDockSn(dockSn, tenantId);
        return CommonResult.success(dockDeviceConfigVO);
    }

    @PostMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:dock:config:edit')")
    @Operation(summary = "修改共享配置", description = "修改共享配置")
    public CommonResult<Boolean> updateDockDeviceConfig(@Validated @RequestBody UpdateDockDeviceConfigReqVO updateDockDeviceConfigReqVO) {
        Boolean updated = dockDeviceConfigService.updateDockDeviceConfig(updateDockDeviceConfigReqVO);
        return CommonResult.success(updated);
    }
}