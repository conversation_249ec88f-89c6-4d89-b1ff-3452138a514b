package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xinkongan.cloud.module.system.dal.dataobject.alarm.AlarmDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * @Description 创建接警任务RespVO
 * <AUTHOR>
 * @Date 2025/3/21 10:03
 */
@Schema(description = "创建接警任务RespVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateAlarmRespVO {

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "警情id")
    private Long alarmId;

    @Schema(description = "是否自动执行 1是 0否")
    private Integer autoExecute;

    @Schema(description = "场景")
    private String alarmScene;

    @Schema(description = "任务状态")
    private String name;

    @Schema(description = "接警场景id")
    private Long alarmSceneId;

    @Schema(description = "任务描述")
    private String description;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "接警类型 0平台 1第三方")
    private Integer source;

    private Long deptId;
}