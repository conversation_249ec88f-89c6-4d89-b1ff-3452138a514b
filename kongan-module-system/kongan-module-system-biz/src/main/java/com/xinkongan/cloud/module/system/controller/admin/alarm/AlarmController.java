package com.xinkongan.cloud.module.system.controller.admin.alarm;

import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import com.xinkongan.cloud.module.system.controller.admin.alarm.vo.*;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm.AlarmDockDeviceReqVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm.AlarmDockDeviceVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask.RouteDynamicStateRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.AlarmJobDetailRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobDO;
import com.xinkongan.cloud.module.system.enums.task.JobStatusTypeEnum;
import com.xinkongan.cloud.module.system.service.alarm.IAlarmConfigService;
import com.xinkongan.cloud.module.system.service.alarm.IAlarmService;
import com.xinkongan.cloud.module.system.service.bind.IDataBindService;
import com.xinkongan.cloud.module.system.service.dock.alarm.IDockAlarmService;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.permission.PermissionService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * @Description 接警接口
 * <AUTHOR>
 * @Date 2025/3/18 17:13
 */
@Validated
@RestController
@Tag(name = "管理后台 - 接警接口")
@RequestMapping("/system/alarm")
@Slf4j
public class AlarmController {

    @Resource
    private IAlarmService alarmService;
    @Resource
    private IAlarmConfigService alarmConfigService;
    @Resource
    private IDataBindService dataBindService;
    @Resource
    private DockDeviceService dockDeviceService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private IJobService jobService;
    @Resource
    private IDockAlarmService dockAlarmService;

    @PreAuthorize("@ss.hasPermission('system:alarm:create')")
    @PostMapping("/create")
    @Operation(summary = "新建警情", description = "新建警情")
    CommonResult<CreateAlarmRespVO> create(@Valid @RequestBody CreateAlarmReqVO reqVO) {
        CreateAlarmRespVO alarmId = alarmService.createAlarmTask(reqVO);
        return success(alarmId);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:config')")
    @PostMapping("/createAlarmConfig")
    @Operation(summary = "新建接警配置", description = "新建接警配置")
    CommonResult<Long> createAlarmConfig(@Valid @RequestBody CreateAlarmConfigReqVO reqVO) {
        Long id = alarmConfigService.createAlarmConfig(reqVO);
        return success(id);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:config')")
    @PostMapping("/updateDefaultById")
    @Operation(summary = "更新默认接警配置", description = "更新默认接警配置")
    CommonResult<Boolean> updateDefaultAlarmConfig(@Valid @RequestBody UpdateDefaultAlarmConfigReqVO reqVO) {
        Boolean flag = alarmConfigService.updateDefaultById(SecurityFrameworkUtils.getLoginUserDeptId(), reqVO.getId());
        return success(flag);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:config')")
    @PostMapping("/deleteAlarmConfig")
    @Operation(summary = "删除接警配置", description = "删除接警配置")
    CommonResult<Boolean> deleteAlarmConfig(@Valid @RequestBody DeleteAlarmConfigReqVO reqVO) {
        Boolean flag = alarmConfigService.deleteAlarmConfigById(reqVO.getId());
        return success(flag);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:config')")
    @PostMapping("/updateAlarmConfig")
    @Operation(summary = "更新接警配置", description = "更新接警配置")
    CommonResult<Boolean> updateAlarmConfig(@Valid @RequestBody UpdateAlarmConfigReqVO reqVO) {
        Boolean flag = alarmConfigService.updateById(reqVO);
        return success(flag);
    }

    @GetMapping("/alarmSceneExist")
    @Operation(summary = "接警场景是否存在", description = "接警场景是否存在")
    CommonResult<Boolean> alarmSceneExist(String alarmScene) {
        Boolean flag = alarmConfigService.alarmSceneExist(SecurityFrameworkUtils.getLoginUserDeptId(), alarmScene);
        return success(flag);
    }

    @GetMapping("/alarmConfigList")
    @Operation(summary = "接警配置列表", description = "接警配置列表")
    CommonResult<List<AlarmConfigRespVO>> alarmConfigList(@RequestParam(value = "deptId", required = false) Long deptId) {
        List<AlarmConfigRespVO> list = alarmConfigService.list(deptId);
        return success(list);
    }

    @GetMapping("/getById")
    @Operation(summary = "接警配置详情", description = "接警配置详情")
    CommonResult<AlarmConfigRespVO> getById(@RequestParam("id") Long id) {
        AlarmConfigRespVO alarmConfigRespVO = alarmConfigService.getById(id);
        return success(alarmConfigRespVO);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:execute')")
    @PostMapping("/executeAlarm")
    @Operation(summary = "执行接警", description = "执行接警")
    CommonResult<Boolean> executeAlarm(@Valid @RequestBody ExecuteAlarmReqVO reqVO) {
        String dockSn = reqVO.getDockSn();
        // 查询机场
        DockDeviceDO dockDeviceDO = dockDeviceService.getByDockSn(dockSn, SecurityFrameworkUtils.getLoginUser().getTenantId());
        if (dockDeviceDO == null) {
            throw exception(DEVICE_DOCK_NOT_EXISTS);
        }
        JobDO jobDO = jobService.getById(reqVO.getJobId());
        if (jobDO == null) {
            throw exception(JOB_NOT_EXISTS);
        }
        if (YesNoEnum.YES.getCode().equals(jobDO.getAlarmExpiredFlag())) {
            throw exception(DOCK_ALARM_ALREADY_EXPIRED);
        }
        // 获取当前账号的数据权限范围
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(SecurityFrameworkUtils.getLoginUserId());
        Set<Long> deptDataPermissionDeptIds = deptDataPermission.getDeptIds();
        Boolean flag;
        if (deptDataPermissionDeptIds.contains(dockDeviceDO.getDeptId())) {
            // 自己的机场直接执行
            flag = alarmService.executeAlarm(reqVO);
        } else {
            // 共享机场 创建审批
            flag = alarmService.applyAlarmJob(reqVO);
        }

        return success(flag);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:query')")
    @GetMapping("/getByAlarmJobId")
    @Operation(summary = "根据接警任务id获取任务详情", description = "根据接警任务id获取任务详情")
    CommonResult<AlarmJobDetailRespVO> getByAlarmJobId(@RequestParam Long jobId) {
        AlarmJobDetailRespVO alarmJobDetailRespVO = alarmService.getByAlarmJobId(jobId);
        return success(alarmJobDetailRespVO);
    }

    @GetMapping("/list")
    @Operation(summary = "警情点列表", description = "警情点列表")
    public CommonResult<List<AlarmListRespVO>> list(AlarmListReqVO reqVO) {
        List<Integer> status = List.of(
                JobStatusTypeEnum.WAIT_EXECUTION.getCode(), JobStatusTypeEnum.EXECUTING.getCode(),
                JobStatusTypeEnum.EXECUTED.getCode(), JobStatusTypeEnum.UNFINISHED.getCode());
        reqVO.setStatus(status);
        List<AlarmListRespVO> page = alarmService.list(reqVO);
        return success(page);
    }

    @Operation(summary = "警情点状态等信息", description = "警情点状态等信息")
    @GetMapping("/getAlarmPointInfo")
    public CommonResult<AlarmPointInfoRespVO> getAlarmPointInfo(@RequestParam Long id) {
        AlarmPointInfoRespVO alarmPointInfoRespVO = alarmService.getAlarmPointInfo(id);
        return success(alarmPointInfoRespVO);
    }

    @GetMapping("/getDockSnByJobId")
    @Operation(summary = "根据接警任务id获取最新机场sn", description = "根据接警任务id获取最新机场sn")
    public CommonResult<String> getDockSnByJobId(@RequestParam Long jobId) {
        String dockSn = alarmService.getDockSnByJobId(jobId);
        return success(dockSn);
    }

    @GetMapping("/getRouteDynamicState")
    @Operation(summary = "警情点航点动作", description = "警情点航点动作")
    public CommonResult<RouteDynamicStateRespVO> getAlarmList(@RequestParam String dockSn) {
        RouteDynamicStateRespVO respVO = alarmService.getAlarmDynamicState(dockSn);
        return success(respVO);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:fly-record:bind')")
    @PostMapping("/bindFlyRecord")
    @Operation(summary = "接警任务绑定飞行记录", description = "接警任务绑定飞行记录")
    public CommonResult<Boolean> bindFlyRecord(@RequestBody AlarmBindFlyRecordReqVO reqVO) {
        dataBindService.bindAlarmFlyRecordToAlarmJob(reqVO);
        return success(true);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:fly-record:bind')")
    @PostMapping("/bindJob")
    @Operation(summary = "接警飞行记录绑定任务", description = "接警飞行记录绑定任务")
    public CommonResult<Boolean> bindJob(@RequestBody AlarmBindJobReqVO reqVO) {
        dataBindService.bindAlarmJobToFlyRecord(reqVO);
        return success(true);
    }

    @PreAuthorize("@ss.hasPermission('system:alarm:fly-record:unbind')")
    @PostMapping("/unbind")
    @Operation(summary = "接警飞行记录解绑任务", description = "接警飞行记录解绑任务")
    public CommonResult<Boolean> unbind(@RequestBody AlarmUnBindReqVO reqVO) {
        dataBindService.unbindAlarmFlyRecordToAlarmJob(reqVO);
        return success(true);
    }

    @GetMapping("/deviceList")
    @Operation(summary = "查询接警设备列表（包括共享设备）", description = "查询设备组织列表")
    @PreAuthorize("@ss.hasPermission('system:dock:query')")
    public CommonResult<List<AlarmDockDeviceVO>> getDeviceListInfo(@Valid AlarmDockDeviceReqVO reqVO) {
        List<AlarmDockDeviceVO> deviceListVO = dockAlarmService.deviceList(reqVO);
        return CommonResult.success(deviceListVO);
    }
}