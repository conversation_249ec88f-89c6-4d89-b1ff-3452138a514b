package com.xinkongan.cloud.module.system.service.liveshare;

import cn.hutool.core.bean.BeanUtil;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.LiveShareReqVO;
import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.ShareBaseVO;
import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.WatchLiveVO;
import com.xinkongan.cloud.module.system.dal.dataobject.liveshare.LiveShareDO;
import com.xinkongan.cloud.module.system.dal.mysql.liveshare.LiveShareMapper;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.service.live.AgoraBaseService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Service
public class LiveShareServiceImpl implements ILiveShareService {

    @Resource
    private LiveShareMapper liveShareMapper;

    @Resource
    private AgoraBaseService agoraBaseService;

    @Override
    public String liveShare(LiveShareReqVO reqVO) {
        LiveShareDO liveShareDO = BeanUtil.toBean(reqVO, LiveShareDO.class);
        // 生成分享链接Key
        liveShareDO.setShareKey(UUID.randomUUID().toString());
        liveShareDO.setUsername(SecurityFrameworkUtils.getLoginUserNickname());
        int insert = liveShareMapper.insert(liveShareDO);
        if (insert > 0) {
            return liveShareDO.getShareKey();
        }
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.LIVE_SHARING_FAILURE);
    }

    @Override
    public ShareBaseVO getShareInfo(String shareKey) {
        LiveShareDO liveShareDO = liveShareMapper.selectOne(new LambdaQueryWrapperX<LiveShareDO>().eq(LiveShareDO::getShareKey, shareKey));
        if (liveShareDO == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.LIVE_SHARE_DOES_NOT_EXIST);
        }
        ShareBaseVO shareBaseVO = ShareBaseVO.builder()
                .encipher(liveShareDO.getEncipher())
                .username(liveShareDO.getUsername())
                .validity(liveShareDO.getValidity())
                .build();
        if (liveShareDO.getEncipher() == 0) {
            shareBaseVO.setWatchLive(WatchLiveVO.builder()
                    .channel(liveShareDO.getChannel())
                    .token(agoraBaseService.getRtcToken(liveShareDO.getChannel()))
                    .build());
        }
        return shareBaseVO;
    }

    @Override
    public WatchLiveVO getShareInfoByPassword(String shareKey, String password) {
        LiveShareDO liveShareDO = liveShareMapper.selectOne(new LambdaQueryWrapperX<LiveShareDO>().eq(LiveShareDO::getShareKey, shareKey));
        if (liveShareDO == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.LIVE_SHARE_DOES_NOT_EXIST);
        }
        if (liveShareDO.getEncipher() == 1 && password.equals(liveShareDO.getPassword())) {
            return WatchLiveVO.builder()
                    .channel(liveShareDO.getChannel())
                    .token(agoraBaseService.getRtcToken(liveShareDO.getChannel()))
                    .build();
        }
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.LIVE_SHARE_PASSWORD_IS_INCORRECT);
    }

    @Override
    public int deleteShare(String dockSn) {
        return liveShareMapper.delete(new LambdaQueryWrapperX<LiveShareDO>().eq(LiveShareDO::getDockSn, dockSn));
    }
}
