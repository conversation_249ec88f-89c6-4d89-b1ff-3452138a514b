package com.xinkongan.cloud.module.system.controller.admin.task.vo.apply;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description 取消申请请求参数
 * <AUTHOR>
 * @Date 2024/12/26 14:05
 */
@Schema(description = "取消申请请求参数")
@Data
public class CancelApplyReqVO {

    @Schema(description = "申请id")
    @NotNull(message = "申请id不能为空")
    private Long applyId;
}