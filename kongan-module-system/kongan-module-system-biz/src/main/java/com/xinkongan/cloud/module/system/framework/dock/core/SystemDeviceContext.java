package com.xinkongan.cloud.module.system.framework.dock.core;

import com.xinkongan.cloud.framework.common.enums.redis.RedisKeyPrefix;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DeviceDomainEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DeviceEnum;
import com.xinkongan.cloud.sdk.dock.mqtt.config.context.DeviceContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/12
 */
@Slf4j
@Component
public class SystemDeviceContext extends DeviceContext {

    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private DockDeviceService dockDeviceService;

    public SystemDeviceContext() {
        log.info("SystemDeviceContext init ~~~");
    }

    public Long getTenantId() {
        SystemDeviceInfo deviceInfo = (SystemDeviceInfo) getDeviceInfo();
        return deviceInfo.getTenantId();
    }

    @Override
    public void setDeviceInfo(String topic, String deviceSn) {
        // 清空上一次使用这个线程的上下文
        super.clearDeviceInfo();

        // 根据设备SN获取所绑定的租户ID
        Long tenantId = redisCacheService.hashGet(RedisKeyPrefix.DOCK_IN_TENANT, deviceSn, Long.class);

        if (tenantId == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_DEVICE_CONNECTION_INFORMATION_DOES_NOT_EXIST);
        }

        // 为线程设置租户信息
        TenantContextHolder.setTenantId(tenantId);

        // 查询当前设备的信息
        DockDeviceDO deviceDO = dockDeviceService.getByCacheDeviceSn(deviceSn);

        if (deviceDO == null) {
            log.error("设备未注册！！！");
            return;
        }

        DeviceDomainEnum deviceDomainEnum = DeviceDomainEnum.find(deviceDO.getDomain());

        SystemDeviceInfo deviceInfo = SystemDeviceInfo.builder()
                .topic(topic)
                .currentDeviceSn(deviceSn)
                .tenantId(tenantId)
                .deviceDomainEnum(deviceDomainEnum)
                .build();

        DockDeviceDO dockDO;
        DockDeviceDO droneDO;

        // 判断设备种类 设置相关信息
        if (deviceDomainEnum == DeviceDomainEnum.DOCK) {
            dockDO = deviceDO;
            // 查询无人机信息
            droneDO = dockDeviceService.getByCacheDeviceSn(deviceDO.getChildSn());
        } else if (deviceDomainEnum == DeviceDomainEnum.DRONE) {
            droneDO = deviceDO;
            dockDO = dockDeviceService.getByChildSn(deviceDO.getDeviceSn());
        } else {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_DEVICE_CONNECTION_INFORMATION_DOES_NOT_EXIST);
        }

        DeviceEnum dockType = DeviceEnum.find(dockDO.getDomain(), dockDO.getDeviceType(), dockDO.getSubType());
        deviceInfo.setDockType(dockType);
        deviceInfo.setDockSn(dockDO.getDeviceSn());

        if (droneDO == null) {
            super.setDeviceInfo(deviceInfo);
            // 接入后无人机未上线前无法正常获取到无人机信息
            log.error("无人机设备未注册！！！");
            return;
        }

        DeviceEnum droneType = DeviceEnum.find(droneDO.getDomain(), droneDO.getDeviceType(), droneDO.getSubType());
        deviceInfo.setDroneType(droneType);
        deviceInfo.setDroneSn(droneDO.getDeviceSn());

        super.setDeviceInfo(deviceInfo);
    }

    /**
     * 获取当前设备所属的组织ID
     *
     * @return 设备所属的组织ID
     */
    public Long getDeptId() {
        SystemDeviceInfo systemDeviceInfo = (SystemDeviceInfo) getDeviceInfo();
        return systemDeviceInfo.getDeptId();
    }
}