package com.xinkongan.cloud.module.system.service.route;

import com.xinkongan.cloud.module.system.controller.admin.route.dto.WayPointActionInfoDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointActionRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask.RouteDynamicStateRespVO;
import com.xinkongan.cloud.module.system.dto.WayPointActionDTO;

import java.util.List;
import java.util.Stack;

public interface IWayPointActionService {


    /**
     * 根据航线id 删除航点动作
     *
     * @param routeId 航线id
     */
    void deletePointActionByRouteId(Long routeId);


    /**
     * 保存航点动作信息
     *
     * @param waypointId  航点id
     * @param actionInfos 航点动作列表
     */
    List<Long> saveWayPointActionInfo(Long routeId, Long waypointId,
                                      List<WayPointActionInfoDTO> actionInfos,
                                      List<WayPointActionDTO> wayPointActions,
                                      Stack<WayPointActionDTO>wayPointStack,
                                      Stack<WayPointActionDTO> recordStack);


    /**
     * 根据航线id查询整个航线配置的航点动作
     *
     * @param routeId 航线id
     * @return 航点动作
     */
    List<WayPointActionRespVO> getWayPointActionByRouteId(Long routeId);



    /**
     * 根据航线id和航点index查询整个航线配置的航点动作
     *
     * @param routeId    航线id
     * @param pointIndex 航点index,从0开始索引
     * @return 航点动作
     */
    List<WayPointActionRespVO> getWayPointActionByRouteIdAndPointIndex(Long routeId, Integer pointIndex);


    /**
     * 根据航线id和航点index查询航段动作
     *
     * @param routeId    航线id
     * @param pointIndex 航点index,从0开始索引
     * @return 航段动作
     */
    List<WayPointActionRespVO> getWayLegActionByRouteIdAndPointIndex(Long routeId, Integer pointIndex);

    /**
     * 根据航线id和航点index查询动态动作
     *
     * @param routeId    航线id
     * @param pointIndex 航点index,从0开始索引
     * @return 动态动作
     */
    RouteDynamicStateRespVO getDynamicActionByRouteIdAndPointIndex(Long routeId, Integer pointIndex);

}
