package com.xinkongan.cloud.module.system.service.report;

import com.xinkongan.cloud.module.system.controller.admin.report.vo.AuthDataReportVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.RouteDataReportVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.RouteTopStatisticVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.TaskTopStatisticVO;
import com.xinkongan.cloud.module.system.dto.TaskTopReportDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface IReportService {


    /**
     * 获取基本的组织权限的统计数据，该统计根据用户的数据权限查询
     *
     * @return 数据统计结果
     */
    AuthDataReportVO getBasicAuthReport();


    /**
     * 获取数据权限范围内的任务统计排行表数据
     *
     * @param taskTopReport 查询条件
     * @return 统计数据
     */
    List<TaskTopStatisticVO> getTaskTopStatisticReport(TaskTopReportDTO taskTopReport);


    /**
     * 获取航线的基本统计数据
     * @return
     */
    RouteDataReportVO getRouteDataReportStatistic();


    /**
     * 获取数据权限范围内的航线统计排行表数据
     * @param taskTopReport 查询条件
     * @return 统计数据
     */
    List<RouteTopStatisticVO> getRouteTopStatisticReport(TaskTopReportDTO taskTopReport);


    /**
     * 根据类型获取时间范围
     * @param type 1 本周，2 本月，3 本年
     * @return 时间范围
     */
    LocalDateTime[] getTimeRange(int type);
}
