package com.xinkongan.cloud.module.system.controller.admin.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class MaterialNumStatisticVO {

    @Schema(description = "素材总数")
    private Integer materialSum;

    @Schema(description = "二维素材总数")
    private Integer twoMaterialSum;

    @Schema(description = "三维素材总数")
    private Integer threeMaterialSum;

    @Schema(description = "全景图总数")
    private Integer panoraMaterialSum;

    @Schema(description = "二维素材大小，单位（GB）")
    private Double twoMaterialSize;

    @Schema(description = "三维素材大小，单位（GB）")
    private Double threeMaterialSize;

    @Schema(description = "二维素材大小，单位（GB）")
    private Double panoraMaterialSize;

    @Schema(description = "其他文件的大小，单位（GB）")
    private Double otherFileSize;

    @Schema(description = "租户总存储大小，单位（GB）")
    private Double tenantStorageSize;

    @Schema(description = "素材应用排行榜")
    private List<MaterialTopVO> topMaterial;

    public List<MaterialTopVO> getTopMaterial() {
        if (topMaterial == null) {
            topMaterial = new ArrayList<>();
        }
        return topMaterial;
    }
}
