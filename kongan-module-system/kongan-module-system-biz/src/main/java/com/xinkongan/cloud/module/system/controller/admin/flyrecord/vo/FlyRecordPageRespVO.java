package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 飞行记录分页响应
 * <AUTHOR>
 * @Date 2025/2/13 11:11
 */
@Data
@Schema(description = "飞行记录分页响应")
public class FlyRecordPageRespVO {

    @Schema(description = "飞行记录id")
    private Long id;

    @Schema(description = "0巡检1建模2接警")
    private Long scene;

    @Schema(description = "飞行记录名称")
    private String name;

    @Schema(description = "起飞时间")
    private LocalDateTime takeOffTime;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "组织名称")
    private String deptName;

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "任务名称")
    private String jobName;

    @Schema(description = "是否分享")
    private Integer shareFlag;
}