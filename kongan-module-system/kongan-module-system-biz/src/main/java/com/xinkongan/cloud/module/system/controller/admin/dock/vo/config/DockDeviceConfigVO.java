package com.xinkongan.cloud.module.system.controller.admin.dock.vo.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description 机场设置
 * <AUTHOR>
 * @Date 2024/12/23 11:36
 */
@Schema(description = "机场设置")
@Data
public class DockDeviceConfigVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "共享状态1共享0不共享")
    private Integer shareStatus;

    @Schema(description = "是否允许上级修改")
    private Integer parentDeptEdit;

    @Schema(description = "紧急任务自动审批1是0否")
    private Integer urgentAutoApproval;

    @Schema(description = "是否运行其他组织打断任务1是0否")
    private Integer interruptTask;

}