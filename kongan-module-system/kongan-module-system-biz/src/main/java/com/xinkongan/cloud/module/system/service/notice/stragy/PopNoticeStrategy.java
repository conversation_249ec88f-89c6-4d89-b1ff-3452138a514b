package com.xinkongan.cloud.module.system.service.notice.stragy;

import com.xinkongan.cloud.framework.common.enums.WebSocketUserTypeEnum;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeDO;
import com.xinkongan.cloud.module.system.enums.notice.NoticeSendTypeEnum;
import com.xinkongan.cloud.module.system.service.notice.INoticeStrategy;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * @Description 弹窗通知
 * <AUTHOR>
 * @Date 2025/3/6 15:02
 */
@Service
public class PopNoticeStrategy implements INoticeStrategy {

    private final NoticeSendTypeEnum noticeSendTypeEnum = NoticeSendTypeEnum.POP;
    @Resource
    private WebSocketSendApi webSocketSendApi;
    @Override
    public void sendNotice(Set<NoticeSendTypeEnum> noticeSendTypeEnumSet, Set<Long> userIds, NoticeDO noticeDO) {
        if (noticeSendTypeEnumSet.contains(noticeSendTypeEnum)) {
            // 发送websocket给在线用户
            webSocketSendApi.sendByTenantUserSet(
                    WebSocketMessageDTO.builder()
                            .userIdSet(userIds)
                            .tenantId(TenantContextHolder.getTenantId())
                            .userTypeEnumSet(Set.of(WebSocketUserTypeEnum.WEB))
                            .message(CustomWebSocketMessage.builder()
                                    .bizCode(BizCodeEnum.NOTICE_POP.getCode())
                                    .data(noticeDO)
                                    .build())
                            .build()
            );
        }
    }
}