package com.xinkongan.cloud.module.system.service.notice.stragy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.datapermission.core.util.DataPermissionUtils;
import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.enums.notice.NoticeSendTypeEnum;
import com.xinkongan.cloud.module.system.enums.sms.SmsSceneEnum;
import com.xinkongan.cloud.module.system.service.notice.INoticeStrategy;
import com.xinkongan.cloud.module.system.service.sms.SmsSendService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Set;

/**
 * @Description 短信通知
 * <AUTHOR>
 * @Date 2025/3/6 15:05
 */
@Slf4j
@Service
public class SmsNoticeStrategy implements INoticeStrategy {

    private final NoticeSendTypeEnum noticeSendTypeEnum = NoticeSendTypeEnum.SMS;

    @Resource
    private AdminUserService adminUserService;
    @Resource
    private SmsSendService smsSendService;

    @Override
    public void sendNotice(Set<NoticeSendTypeEnum> noticeSendTypeEnumSet, Set<Long> userIds, NoticeDO noticeDO) {
        if (noticeSendTypeEnumSet.contains(noticeSendTypeEnum)) {
            // 根据用户查询手机号
            userIds.forEach(userId -> {
                try {
                    // 查询手机号
                    DataPermissionUtils.executeIgnore(() -> {
                        AdminUserDO user = adminUserService.getUser(userId);
                        if (user == null || StrUtil.isEmpty(user.getMobile())) {
                            log.error("用户手机号未找到 user:{}", JSONUtil.toJsonStr(user));
                            return;
                        }
                        String mobile = user.getMobile();
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("text", noticeDO.getContent());
                        smsSendService.sendSingleSms(mobile, null, null, SmsSceneEnum.SYSTEM_NOTICE.getTemplateCode(), map);
                    });
                } catch (Exception e) {
                    log.error("发送短信失败", e);
                }
            });
        }

    }
}