package com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage;

import com.xinkongan.cloud.framework.common.validation.InIntArray;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * @Description 可飞范围更新请求参数
 * <AUTHOR>
 * @Date 2024/12/30 10:12
 */
@Data
@Schema(description = "更新可飞范围的请求 VO")
public class UpdateDockCoverageReqVO {

    @NotNull(message = "id不能为空")
    @Schema(description = "id")
    private Long id;

    @NotBlank(message = "机场sn不能为空")
    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "几等分")
    @InIntArray(value = "1,2,4,8",message = "uniform必须在指定范围:{value}")
    @NotNull(message = "uniform不能为空")
    private Integer uniform;

    @Schema(description = "倾斜角")
    @Range(min = 0, max = 360,message = "倾斜角必须在指定范围:{min}~{max}")
    private Integer gradient;

    @Schema(description = "半径")
    private List<Integer> radius;
}