package com.xinkongan.cloud.module.system.service.permission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import com.xinkongan.cloud.framework.common.enums.CommonStatusEnum;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.collection.CollectionUtils;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.datapermission.core.util.DataPermissionUtils;
import com.xinkongan.cloud.framework.mybatis.config.IdGenerator;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.DeptTreeInfoVO;
import com.xinkongan.cloud.module.system.controller.admin.permission.vo.role.*;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.RoleStatisticVO;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserRoleVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.dal.mysql.permission.RoleMapper;
import com.xinkongan.cloud.module.system.dal.mysql.permission.UserRoleMapper;
import com.xinkongan.cloud.module.system.enums.common.PostionMoveEnum;
import com.xinkongan.cloud.module.system.enums.common.TreeNodeType;
import com.xinkongan.cloud.module.system.enums.permission.DataScopeEnum;
import com.xinkongan.cloud.module.system.enums.permission.RoleCodeEnum;
import com.xinkongan.cloud.module.system.enums.permission.RoleTypeEnum;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.framework.common.util.collection.CollectionUtils.convertMap;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.*;
import static com.xinkongan.cloud.module.system.enums.LogRecordConstants.*;

/**
 * 角色 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RoleServiceImpl implements RoleService {

    @Resource
    private PermissionService permissionService;

    @Resource
    private DeptService deptService;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private AdminUserService userService;

    @Override
    public Long createRoleForTenantRegister(RoleSaveReqVO createReqVO, Integer type) {
        // 1. 校验角色
        validateRoleDuplicate(createReqVO.getName(), createReqVO.getCode(), null, createReqVO.getTenantId(), createReqVO.getDeptId());

        // 2. 插入到数据库
        RoleDO role = BeanUtils.toBean(createReqVO, RoleDO.class)
                .setType(ObjectUtil.defaultIfNull(type, RoleTypeEnum.CUSTOM.getType()))
                .setStatus(ObjUtil.defaultIfNull(createReqVO.getStatus(), CommonStatusEnum.ENABLE.getStatus()))
                .setDataScope(DataScopeEnum.DEPT_AND_CHILD.getScope()); // 组织管理员角色给部门及其一下权限

        role.setCreator(String.valueOf(createReqVO.getUserId()));
        role.setUpdater(String.valueOf(createReqVO.getUserId()));

        roleMapper.insert(role);
        return role.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_ROLE_TYPE, subType = SYSTEM_ROLE_CREATE_SUB_TYPE, bizNo = "{{#role.id}}",
            success = SYSTEM_ROLE_CREATE_SUCCESS)
    public Long createRole(RoleSaveReqVO createReqVO, Integer type) {
        // 1. 校验角色
        validateRoleDuplicate(createReqVO.getName(), createReqVO.getCode(), null, TenantContextHolder.getTenantId(), createReqVO.getDeptId());

        // 查询本组织的所有角色的sort的最大值
        List<RoleDO> deptRoleList = roleMapper.selectList(new LambdaQueryWrapper<RoleDO>()
                .eq(RoleDO::getDeptId, createReqVO.getDeptId())
                .orderByAsc(RoleDO::getSort)
                .last("limit 1")
        );

        Integer sort = CollectionUtil.isEmpty(deptRoleList) ? RoleDO.ROLE_DEFAULT_SORT : deptRoleList.get(0).getSort() + 100;
        // 2. 插入到数据库
        RoleDO role = BeanUtils.toBean(createReqVO, RoleDO.class)
                .setType(ObjectUtil.defaultIfNull(type, RoleTypeEnum.CUSTOM.getType()))
                .setStatus(ObjUtil.defaultIfNull(createReqVO.getStatus(), CommonStatusEnum.ENABLE.getStatus()))
                .setCode(RoleCodeEnum.COMMON_ROLE.getCode())
                .setSort(sort)
                .setDataScope(createReqVO.getDataScope()); // 默认可查看所有数据。原因是，可能一些项目不需要项目权限
        roleMapper.insert(role);

        // 3. 给角色分配菜单
        Set<Long> menuIds = createReqVO.getMenuIds();
        if (!CollectionUtil.isEmpty(menuIds)) {
            permissionService.assignRoleMenu(role.getId(), menuIds);
        }

        // 4. 记录操作日志上下文
        LogRecordContext.putVariable("role", role);
        return role.getId();
    }

    @Override
    @LogRecord(type = SYSTEM_ROLE_TYPE, subType = SYSTEM_ROLE_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}", success = SYSTEM_ROLE_UPDATE_SUCCESS)
    public void updateAuthRole(RoleAuthSaveReqVO updateReqVO) {

        // 3. 更新到数据库
        RoleDO roleInfo = roleMapper.selectById(updateReqVO.getId());
        if (roleInfo == null) {
            throw new ServiceException(ROLE_NOT_EXISTS);
        }

        // 系统角色不能被编辑
        if (RoleCodeEnum.TENANT_ADMIN.getCode().equals(roleInfo.getCode())) {
            throw new ServiceException(SYSTEM_ADMIN_ROLE_NOT_DEL_UPDATE);
        }

        roleInfo.setDataScope(updateReqVO.getDataScope());
        roleMapper.updateById(roleInfo);

        // 4. 给角色分配菜单
        Set<Long> menuIds = updateReqVO.getMenuIds();
        if (!CollectionUtil.isEmpty(menuIds)) {
            permissionService.assignRoleMenu(roleInfo.getId(), menuIds);
        }

        // 5. 记录操作日志上下文
        LogRecordContext.putVariable("role", roleInfo);
    }


    @Override
    public void updateRoleBaseInfo(RoleBaseSaveReqVO roleBaseSaveReq) {
        RoleDO roleInfo = roleMapper.selectById(roleBaseSaveReq.getRoleId());
        if (roleInfo == null) {
            throw new ServiceException(ROLE_NOT_EXISTS);
        }
        // 系统角色不能被编辑
        if (RoleCodeEnum.TENANT_ADMIN.getCode().equals(roleInfo.getCode())) {
            throw new ServiceException(SYSTEM_ADMIN_ROLE_NOT_DEL_UPDATE);
        }
        // 校验唯一性
        if (this.checkRoleNameRepeat(roleInfo.getId(), roleBaseSaveReq.getRoleName(), roleInfo.getDeptId())) {
            throw new ServiceException(ROLE_NAME_DUPLICATE);
        }
        roleInfo.setName(roleBaseSaveReq.getRoleName());
        roleInfo.setRemark(roleBaseSaveReq.getRemark());
        roleMapper.updateById(roleInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_ROLE_TYPE, subType = SYSTEM_ROLE_DELETE_SUB_TYPE, bizNo = "{{#id}}", success = SYSTEM_ROLE_DELETE_SUCCESS)
    public void deleteRole(Long id) {
        RoleDO roleInfo = roleMapper.selectById(id);
        if (roleInfo == null) {
            throw new ServiceException(ROLE_NOT_EXISTS);
        }

        // 角色下绑定人员则不能删除
        List<UserRoleVO> userRoleLists = userRoleMapper.selectUserRoleInfosListByRoleIds(Collections.singleton(id));
        if (userRoleLists.size() > 0) {
            throw new ServiceException(ROLE_HAS_BIND_USERS);
        }

        // 系统角色不能被删除
        if (RoleCodeEnum.TENANT_ADMIN.getCode().equals(roleInfo.getCode())) {
            throw new ServiceException(SYSTEM_ADMIN_ROLE_NOT_DEL_UPDATE);
        }

        // 删除角色数据
        roleMapper.deleteById(id);

        // 删除关联数据
        permissionService.processRoleDeleted(id);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(roleInfo, RoleSaveReqVO.class));
        LogRecordContext.putVariable("role", roleInfo);
    }


    @Override
    public void deleteRoleByDeptId(Long deptId) {
        log.info("[删除组织角色]，组织id：{}", deptId);
        // 1. 查询所有角色信息
        List<RoleDO> roleInfos = roleMapper.selectList(RoleDO::getDeptId, deptId);
        if (CollectionUtil.isEmpty(roleInfos)) {
            return;
        }
        // 2.循环删除角色数据，及其关联数据
        for (RoleDO roleInfo : roleInfos) {
            try {
                // 删除角色数据
                roleMapper.deleteById(roleInfo.getId());
                // 删除关联数据
                permissionService.processRoleDeleted(roleInfo.getId());
            } catch (Exception e) {
                log.error("[删除角色是出现错误]，错误信息为：{}", e.getMessage());
                log.error(ExceptionUtils.getStackTrace(e));
            }
        }
    }

    /**
     * 校验角色的唯一字段是否重复
     * <p>
     * 1. 是否存在相同名字的角色
     * 2. 是否存在相同编码的角色
     *
     * @param name 角色名字
     * @param code 角色额编码
     * @param id   角色编号
     */
    private void validateRoleDuplicate(String name, String code, Long id, Long tenant, Long deptId) {
        // 0. 超级管理员，不允许创建
        if (RoleCodeEnum.isSuperAdmin(code)) {
            throw exception(ROLE_ADMIN_CODE_ERROR, code);
        }
        RoleDO role = roleMapper.selectByNameAndTenant(name, tenant, deptId);
        if (role != null && !role.getId().equals(id)) {
            throw exception(ROLE_NAME_DUPLICATE, name);
        }
    }

    /**
     * 校验角色是否可以被更新
     *
     * @param id 角色编号
     */
    private RoleDO validateRoleForUpdate(Long id) {
        RoleDO role = roleMapper.selectById(id);
        if (role == null) {
            throw exception(ROLE_NOT_EXISTS);
        }
        // 内置角色，不允许删除
        if (RoleTypeEnum.SYSTEM.getType().equals(role.getType())) {
            throw exception(ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE);
        }
        return role;
    }

    @Override
    public RoleDO getRole(Long id) {
        return roleMapper.selectById(id);
    }

    @Override
    public RoleRespVO getRoleDetailById(Long id) {
        RoleDO roleInfo = roleMapper.selectById(id);
        if (roleInfo == null) {
            throw new ServiceException(ROLE_NOT_EXISTS);
        }
        RoleRespVO roleRespInfo = BeanUtils.toBean(roleInfo, RoleRespVO.class);

        // 查询角色的菜单权限
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(id);
        roleRespInfo.setMenuIds(menuIds);

        // 查询创建人
        AdminUserDO creatorInfo = userService.getUser(Long.valueOf(roleInfo.getCreator()));
        roleRespInfo.setCreateName(creatorInfo != null ? creatorInfo.getNickname() : "--");
        return roleRespInfo;
    }

    @Override
    public RoleDO getRoleById(Long id) {
        return roleMapper.selectById(id);
    }


    @Override
    public List<RoleDO> getRoleListByStatus(Collection<Integer> statuses, Long deptId) {
        return roleMapper.selectListByStatus(statuses, deptId);
    }

    @Override
    public List<RoleDO> getRoleListByRoleCode(String roleCode) {
        LambdaQueryWrapper<RoleDO> wrapper = new LambdaQueryWrapper<RoleDO>()
                .eq(RoleDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .eq(RoleDO::getCode, roleCode);
        return roleMapper.selectList(wrapper);
    }

    @Override
    public List<RoleDO> getRoleListLikeRoleName(String roleName) {
        LambdaQueryWrapper<RoleDO> wrapper = new LambdaQueryWrapper<RoleDO>()
                .eq(RoleDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .like(StringUtils.hasText(roleName), RoleDO::getName, roleName);
        return roleMapper.selectList(wrapper);
    }

    @Override
    public List<RoleDO> getRoleList(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return roleMapper.selectBatchIds(ids);
    }

    @Override
    public List<RoleDO> getRoleListByIds(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return roleMapper.selectByIds(ids);
    }


    @Override
    public boolean hasAnySuperAdmin(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return false;
        }
        List<RoleDO> roleLists = this.getRoleListByIds(ids);
        for (RoleDO roleInfo : roleLists) {
            if (RoleCodeEnum.isSuperAdmin(roleInfo.getCode())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<BaseTreeNode> getDeptRoleTreeInfo(RoleSearchReqVO roleSearchParams) {

        List<RoleDO> deptRoleList = this.getRoleListLikeRoleName(roleSearchParams.getRoleName());
        if (CollectionUtil.isEmpty(deptRoleList)) {
            return new ArrayList<>();
        }

        // 1 先查询组织树信息
        List<BaseTreeNode> deptTreeInfo = deptService.getDeptTreeInfo(null);

        // 2 组织角色映射表
        Map<Long, List<RoleDO>> deptRoleMaps = deptRoleList.stream().collect(Collectors.groupingBy(RoleDO::getDeptId));
        for (Map.Entry<Long, List<RoleDO>> entry : deptRoleMaps.entrySet()) {
            List<RoleDO> value = entry.getValue();
            value.sort(Comparator.comparingInt(RoleDO::getSort));
        }

        // 3 查询角色人员映射关系 (若租户内用户数目太多，可能会出现内存溢出问题)
        Map<Long, List<UserRoleVO>> roleUserListMaps = permissionService.getUserRoleMapsByRoleIds(deptRoleList.stream().map(RoleDO::getId).toList());

        // 4 递归构建树信息
        for (BaseTreeNode baseTreeNode : deptTreeInfo) {
            this.recurBuildDeptRoleTreeInfo(baseTreeNode, deptRoleMaps, roleSearchParams.getRoleName(), roleUserListMaps);
        }

        return deptTreeInfo;
    }

    private void recurBuildDeptRoleTreeInfo(BaseTreeNode deptTreeNode, Map<Long, List<RoleDO>> deptRoleMaps, String roleName, Map<Long, List<UserRoleVO>> roleUserListMaps) {

        List<BaseTreeNode> children = deptTreeNode.getChildrenNodes();
        // 查询是否有角色信息
        List<RoleDO> roleList = deptRoleMaps.get(deptTreeNode.getId());
        if (!CollectionUtil.isEmpty(roleList)) {
            // 角色列表非空，添加子节点
            List<RoleTreeInfoVO> roleInfos = roleList.stream().map(r -> this.convert(r, roleUserListMaps)).toList();
            // 组织列表
            ArrayList<BaseTreeNode> baseDeptChildTreeNodes = new ArrayList<>(children);
            // 先添加角色数据，然后添加组织数据
            children.clear();
            children.addAll(roleInfos);
            children.addAll(baseDeptChildTreeNodes);
        }
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        Iterator<BaseTreeNode> iterator = children.iterator();
        while (iterator.hasNext()) {
            BaseTreeNode baseTreeNode = iterator.next();
            if (!Objects.equals(baseTreeNode.getNodeType(), TreeNodeType.DEPT_NODE_TYPE.getType())) {
                // 非组织节点不进行递归
                continue;
            }
            DeptTreeInfoVO deptTreeInfoVO = (DeptTreeInfoVO) baseTreeNode;
            this.recurBuildDeptRoleTreeInfo(deptTreeInfoVO, deptRoleMaps, roleName, roleUserListMaps);
            if (StringUtils.hasText(roleName)
                    && CollectionUtil.isEmpty(deptTreeInfoVO.getChildrenNodes())) {
                // 搜索状态下，需要移除到空组织节点
                iterator.remove();
            }
        }
    }

    private RoleTreeInfoVO convert(RoleDO roleInfo, Map<Long, List<UserRoleVO>> roleUserListMaps) {

        RoleTreeInfoVO roleTreeInfo = new RoleTreeInfoVO();
        roleTreeInfo.setId(roleInfo.getId());
        roleTreeInfo.setNodeName(roleInfo.getName());
        roleTreeInfo.setNodeType(TreeNodeType.ROLE_NODE_TYPE.getType());
        roleTreeInfo.setParentId(roleInfo.getDeptId());
        roleTreeInfo.setCode(roleInfo.getCode());

        List<UserRoleVO> roleUserInfos = roleUserListMaps.get(roleInfo.getId());
        if (CollectionUtil.isEmpty(roleUserInfos)) {
            roleUserInfos = new ArrayList<>();
        }
        roleTreeInfo.setUserCount(roleUserInfos.size());
        return roleTreeInfo;
    }

    @Override
    public void createDefaultDeptRole(DeptDO deptInfo) {
        // 1、 创建组织管理员
        RoleDO deptAdminRole = new RoleDO();
        deptAdminRole.setId(IdGenerator.generateId());
        deptAdminRole.setName(RoleCodeEnum.DEPT_ADMIN.getName());
        deptAdminRole.setCode(RoleCodeEnum.DEPT_ADMIN.getCode());
        deptAdminRole.setStatus(CommonStatusEnum.ENABLE.getStatus());
        deptAdminRole.setType(RoleTypeEnum.SYSTEM.getType());
        deptAdminRole.setRemark(RoleCodeEnum.DEPT_ADMIN.getName());
        deptAdminRole.setDataScope(DataScopeEnum.DEPT_AND_CHILD.getScope());
        deptAdminRole.setDataScopeDeptIds(new HashSet<>());
        deptAdminRole.setDeptId(deptInfo.getId());
        deptAdminRole.setSort(RoleDO.ROLE_DEFAULT_SORT);
        roleMapper.insert(deptAdminRole);

        // 赋予该角色拥有租户管理员角色菜单权限，忽略数据权限执行
        DataPermissionUtils.executeIgnore(() -> {
            Set<Long> adminMenus = permissionService.getRoleMenuListByRoleCode(RoleCodeEnum.TENANT_ADMIN.getCode());
            permissionService.assignRoleMenu(deptAdminRole.getId(), adminMenus);
        });

        // 2、 创建协同人员
        RoleDO collaboratorRole = new RoleDO();
        collaboratorRole.setId(IdGenerator.generateId());
        collaboratorRole.setName(RoleCodeEnum.COLLABORATOR_ROLE.getName());
        collaboratorRole.setCode(RoleCodeEnum.COLLABORATOR_ROLE.getCode());
        collaboratorRole.setStatus(CommonStatusEnum.ENABLE.getStatus());
        collaboratorRole.setType(RoleTypeEnum.SYSTEM.getType());
        collaboratorRole.setRemark(RoleCodeEnum.COLLABORATOR_ROLE.getName());
        collaboratorRole.setDataScope(DataScopeEnum.DEPT_AND_CHILD.getScope());
        collaboratorRole.setDataScopeDeptIds(new HashSet<>());
        collaboratorRole.setDeptId(deptInfo.getId());
        collaboratorRole.setSort(RoleDO.ROLE_DEFAULT_SORT);

        roleMapper.insert(collaboratorRole);
        // TODO 2024/12/23 协同人员菜单权限暂时没有
    }

    @Override
    public Boolean checkRoleNameRepeat(Long roleId, String roleName, Long deptId) {
        Long count = roleMapper.selectCount(
                new LambdaQueryWrapper<RoleDO>()
                        .eq(RoleDO::getDeptId, deptId)
                        .ne(roleId != null, RoleDO::getId, roleId)
                        .eq(RoleDO::getName, roleName));
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void roleMove(RoleMoveReqVO roleMoveReq) {
        PostionMoveEnum moveCommand = PostionMoveEnum.findByMove(roleMoveReq.getMove());
        if (moveCommand == null) {
            throw new ServiceException(DEPT_MOVE_COMMAND_ERROR);
        }
        RoleDO roleInfo = roleMapper.selectById(roleMoveReq.getRoleId());
        if (roleInfo == null) {
            throw new ServiceException(ROLE_NOT_EXISTS);
        }
        // 查询该组织下的所有角色信息
        List<RoleDO> roleLists = roleMapper.selectList(
                new LambdaQueryWrapper<RoleDO>()
                        .eq(RoleDO::getDeptId, roleInfo.getDeptId())
                        .eq(RoleDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                        .orderByAsc(RoleDO::getSort));
        if (CollectionUtil.isEmpty(roleLists)) {
            return;
        }
        int position = 0;
        for (int i = 0; i < roleLists.size(); i++) {
            RoleDO roleDO = roleLists.get(i);
            if (Objects.equals(roleDO.getId(), roleMoveReq.getRoleId())) {
                position = i;
                break;
            }
        }
        int swapPosition = position;
        switch (moveCommand) {
            case UP -> swapPosition = (position > 0) ? (position - 1) : position;
            case DOWN -> swapPosition = (position < roleLists.size() - 1) ? (position + 1) : position;
            case TOP -> swapPosition = 0;
            case BOTTOM -> swapPosition = roleLists.size() - 1;
        }
        // 交换数组位置
        Collections.swap(roleLists, position, swapPosition);
        // 重新设置sort 数据然后更新到数据库中
        final int[] defaultSort = {RoleDO.ROLE_DEFAULT_SORT};
        roleLists = roleLists.stream().map(r -> {
            RoleDO role = new RoleDO();
            role.setId(r.getId());
            role.setSort(defaultSort[0]);
            defaultSort[0] = defaultSort[0] + 10;
            return role;
        }).toList();
        // 批量更新数据
        roleMapper.updateBatch(roleLists);
    }

    @Override
    public List<RoleDO> getTenantAdminRole(Long tenant) {
        List<RoleDO> tenantAdminRoleInfos = roleMapper.selectList(new LambdaQueryWrapperX<RoleDO>()
                .eq(RoleDO::getTenantId, tenant)
                .eq(RoleDO::getCode, RoleCodeEnum.TENANT_ADMIN.getCode()));
        return tenantAdminRoleInfos;
    }

    @Override
    public List<RoleDO> getRoleList() {
        return roleMapper.selectList();
    }

    @Override
    public List<RoleStatisticVO> getRoleStatisticInfo() {
        return roleMapper.getRoleStatisticInfoByDataPermission();
    }
}
