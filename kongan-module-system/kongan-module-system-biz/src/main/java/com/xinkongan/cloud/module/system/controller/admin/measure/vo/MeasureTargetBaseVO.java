package com.xinkongan.cloud.module.system.controller.admin.measure.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MeasureTargetBaseVO implements Serializable {

    private static final long serialVersionUID = 1999L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "绝对高度")
    private Double height;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "任务ID")
    private Long taskId;

    @Schema(description = "飞行记录ID")
    private Long flyRecordId;

    @Schema(description = "飞行记录截图文件ID")
    private Long flyRecordFileId;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "创建时间")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
