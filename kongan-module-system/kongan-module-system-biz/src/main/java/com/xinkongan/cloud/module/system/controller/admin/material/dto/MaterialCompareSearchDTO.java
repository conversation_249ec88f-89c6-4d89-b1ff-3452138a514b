package com.xinkongan.cloud.module.system.controller.admin.material.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class MaterialCompareSearchDTO {

    @NotNull(message = "素材id不能为空")
    @Schema(description = "素材id")
    private Long materialId;

    @Schema(description = "待更換素材id")
    private Long replaceId;

    @Schema(description = "搜索关键字")
    private String searchKey;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "组织ID")
    private List<Long> deptIds;

    @NotNull(message = "素材类型不能为空")
    @Schema(description = "类型")
    private Integer type;

    @Schema(description = "左上经度")
    private Double leftUpLon;

    @Schema(description = "左上纬度")
    private Double leftUpLat;

    @Schema(description = "右下经度")
    private Double rightDownLon;

    @Schema(description = "右下纬度")
    private Double rightDownLat;

    @Schema(description = "中心点经度")
    private Double centerLon;

    @Schema(description = "右下纬度")
    private Double centerLat;

    @Schema(description = "三维素材对比规则 半径 目前1000")
    private Integer radius;
}
