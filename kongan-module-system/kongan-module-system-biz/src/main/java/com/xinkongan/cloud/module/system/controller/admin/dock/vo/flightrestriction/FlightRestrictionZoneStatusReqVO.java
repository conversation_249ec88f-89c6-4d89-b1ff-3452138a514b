package com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 限飞区状态更新请求VO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
@Schema(description = "管理后台 - 限飞区状态更新请求VO")
public class FlightRestrictionZoneStatusReqVO {

    @Schema(description = "限飞区ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "限飞区ID不能为空")
    private Long id;

    @Schema(description = "启用状态：true-启用，false-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "启用状态不能为空")
    private Boolean status;
}
