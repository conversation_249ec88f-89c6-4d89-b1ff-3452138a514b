package com.xinkongan.cloud.module.system.controller.admin.user.vo.user;

import com.xinkongan.cloud.framework.common.validation.Mobile;
import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import com.xinkongan.cloud.module.system.framework.operatelog.core.DeptParseFunction;
import com.xinkongan.cloud.module.system.framework.operatelog.core.PostParseFunction;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
@Schema(description = "管理后台 - 用户创建/修改 Request VO")
public class UserSaveReqVO {

    @Schema(description = "用户编号", example = "1024")
    private Long id;

    @DiffLogField(name = "用户账号")
    @NotBlank(message = "用户账号不能为空")
    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "kongan")
    private String username;

    @DiffLogField(name = "用户昵称")
    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String nickname;

    @DiffLogField(name = "备注")
    @Schema(description = "备注", example = "我是一个用户")
    private String remark;

    @NotNull
    @Schema(description = "角色编号", example = "2")
    private Long roleId;

    @NotNull
    @Schema(description = "部门编号", example = "1")
    @DiffLogField(name = "部门", function = DeptParseFunction.NAME)
    private Long deptId;

    @Schema(description = "岗位编号数组", example = "1")
    @DiffLogField(name = "岗位", function = PostParseFunction.NAME)
    private Set<Long> postIds;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    @DiffLogField(name = "用户邮箱")
    private String email;

    @Schema(description = "手机号码", example = "15601691300")
    @Mobile
    @NotBlank(message = "手机号不能为空")
    @DiffLogField(name = "手机号码")
    private String mobile;

    @Schema(description = "用户性别，参见 SexEnum 枚举类", example = "1")
    private Integer sex;

    @Schema(description = "首图文件信息")
    private FileSaveInfoDTO logoFileInfo;

    @Schema(description = "证书数据", example = "[UTC、CAAC]")
    private List<String> certificates;

    @Schema(description = "标签数据", example = "[xxx,xxx]")
    private List<String> tags;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private String password;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private Long tenantId;
}
