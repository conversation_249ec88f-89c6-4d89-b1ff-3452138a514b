package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz.*;
import com.xinkongan.cloud.module.system.service.dock.nfz.INfzSyncService;
import com.xinkongan.cloud.module.system.service.dock.nfz.ISystemDockNfzService;
import com.xinkongan.cloud.module.system.service.dock.nfz.ISystemDockNfzSyncStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 禁飞区管理Controller
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 禁飞区管理")
@RequestMapping("/system/dock/nfz")
public class SystemDockNfzController {

    @Resource
    private ISystemDockNfzService nfzService;
    @Resource
    private ISystemDockNfzSyncStatusService nfzSyncStatusService;
    @Resource
    private INfzSyncService nfzSyncService;

    @GetMapping("/list")
    @Operation(summary = "查询禁飞区全部列表", description = "查询所有禁飞区，不分页")
//    @PreAuthorize("@ss.hasPermission('system:dock:nfz:query')")
    public CommonResult<List<NfzRespVO>> getNfzList(Boolean status) {
        List<NfzRespVO> nfzList = nfzService.getNfzList(status);
        return success(nfzList);
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "根据ID查询禁飞区详情")
    @Parameter(name = "id", description = "禁飞区ID", required = true, example = "1")
//    @PreAuthorize("@ss.hasPermission('system:dock:nfz:query')")
    public CommonResult<NfzRespVO> getNfzById(@PathVariable("id") Long id) {
        NfzRespVO nfzRespVO = nfzService.getNfzById(id);
        return success(nfzRespVO);
    }

    @PostMapping("/create")
    @Operation(summary = "创建禁飞区")
//    @PreAuthorize("@ss.hasPermission('system:dock:nfz:create')")
    public CommonResult<Long> createNfz(@Valid @RequestBody NfzSaveReqVO saveReqVO) {
        Long nfzId = nfzService.createNfz(saveReqVO);
        return success(nfzId);
    }

    @PutMapping("/update")
    @Operation(summary = "修改禁飞区")
//    @PreAuthorize("@ss.hasPermission('system:dock:nfz:update')")
    public CommonResult<Boolean> updateNfz(@Valid @RequestBody NfzUpdateReqVO updateReqVO) {
        nfzService.updateNfz(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除禁飞区", description = "删除禁飞区时会级联删除关联的禁飞区文件")
    @Parameter(name = "id", description = "禁飞区ID", required = true, example = "1")
//    @PreAuthorize("@ss.hasPermission('system:dock:nfz:delete')")
    public CommonResult<Boolean> deleteNfz(@PathVariable("id") Long id) {
        nfzService.deleteNfz(id);
        return success(true);
    }

    @PutMapping("/status/{id}")
    @Operation(summary = "修改禁飞区状态", description = "启用或禁用指定的禁飞区")
    @Parameter(name = "id", description = "禁飞区ID", required = true, example = "1")
//    @PreAuthorize("@ss.hasPermission('system:dock:nfz:update')")
    public CommonResult<Boolean> updateNfzStatus(
            @PathVariable("id") Long id,
            @RequestParam("status") @NotNull(message = "状态不能为空") Boolean status) {
        nfzService.updateNfzStatus(id, status);
        return success(true);
    }

    @PostMapping("/sync")
    @Operation(summary = "机场禁飞区同步", description = "为指定的机场触发禁飞区数据同步")
//    @PreAuthorize("@ss.hasPermission('system:dock:nfz:sync')")
    public CommonResult<Boolean> syncDockNfz(@Valid @RequestBody DockNfzSyncReqVO syncReqVO) {
        nfzSyncService.syncNfzToDock(syncReqVO.getDockSn(), TenantContextHolder.getTenantId());
        return success(true);
    }

    @GetMapping("/sync-status")
    @Operation(summary = "机场同步状态查询", description = "查询所有机场的禁飞区同步状态列表")
//    @PreAuthorize("@ss.hasPermission('system:dock:nfz:query')")
    public CommonResult<List<DockNfzSyncStatusVO>> getDockSyncStatusList() {
        List<DockNfzSyncStatusVO> syncStatusList = nfzSyncStatusService.getDockSyncStatusList();
        return success(syncStatusList);
    }

}
