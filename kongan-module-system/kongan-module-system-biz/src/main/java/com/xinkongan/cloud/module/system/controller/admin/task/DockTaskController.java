package com.xinkongan.cloud.module.system.controller.admin.task;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask.CreateDockTaskReqVO;
import com.xinkongan.cloud.module.system.service.task.IDockTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 机场计划管理
 * <AUTHOR>
 * @Date 2025/1/7 19:42
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 机场计划管理")
@RequestMapping("/system/task")
public class DockTaskController {

    @Resource
    private IDockTaskService dockTaskService;

    /**
     * 创建计划接口
     *
     * <AUTHOR>
     * @date 2025/1/7 19:43
     **/
    @PreAuthorize("@ss.hasAnyPermissions('system:job:add','system:job:copy')")
    @PostMapping("/create")
    @Operation(summary = "创建计划接口", description = "创建计划接口，支持巡检、建模等多种场景")
    CommonResult<Boolean> createTask(@Valid @RequestBody CreateDockTaskReqVO reqVO) {
        Boolean create = dockTaskService.createTask(reqVO);
        return success(create);
    }

}