package com.xinkongan.cloud.module.system.service.tenant;


import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.TenantRegisterReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.tenant.TenantDO;
import com.xinkongan.cloud.module.system.service.tenant.handler.TenantInfoHandler;
import com.xinkongan.cloud.module.system.service.tenant.handler.TenantMenuHandler;

import java.util.List;

/**
 * 租户 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantService {

    /**
     * 检查租户名称是否重复
     * @param id  租户ID
     * @param tenantName 租户名称
     * @return true 表示重复，false 表示不重复
     */
    Boolean checkTenantNameRepeat(Long id, String tenantName);

    /**
     * 租户注册接口
     *
     * @param tenantRegisterInfo 租户注册信息
     */
    Long registerTenant(TenantRegisterReqVO tenantRegisterInfo);


    /**
     * 获得租户
     *
     * @param id 编号
     * @return 租户
     */
    TenantDO getTenant(Long id);

    /**
     * 进行租户的信息处理逻辑
     * 其中，租户编号从 {@link TenantContextHolder} 上下文中获取
     *
     * @param handler 处理器
     */
    void handleTenantInfo(TenantInfoHandler handler);

    /**
     * 进行租户的菜单处理逻辑
     * 其中，租户编号从 {@link TenantContextHolder} 上下文中获取
     *
     * @param handler 处理器
     */
    void handleTenantMenu(TenantMenuHandler handler);

    /**
     * 获得所有租户
     *
     * @return 租户编号数组
     */
    List<Long> getTenantIdList();

    /**
     * 校验租户是否合法
     *
     * @param id 租户编号
     */
    void validTenant(Long id);

    /**
     * 创建租户是构建 租户 连接 EMQX 的用户名
     *
     * @return 用户名 XKA0001
     */
    String buildEmqxUsername();

    /**
     * 设置 EMQX 连接信息
     *
     * @param username EMQX 用户名
     * @param password EMQX 密码
     * @param tenantId 租户ID
     * @param salt     EMQX 密码的盐
     */
    void setEmqxInfoToRedis(String username, String password, String salt, Long tenantId);

    /**
     * 获取租户列表
     *
     * @return 租户列表
     */
    List<TenantDO> getTenantList();


    /**
     * 更新租户信息
     * @param tenantInfo
     */
    void updateTenantInfo(TenantDO tenantInfo);

}
