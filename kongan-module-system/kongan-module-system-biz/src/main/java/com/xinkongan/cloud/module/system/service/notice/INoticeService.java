package com.xinkongan.cloud.module.system.service.notice;

import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.LoginPopNoticeVO;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticePageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeSaveVO;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeVO;
import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeDO;
import com.xinkongan.cloud.module.system.enums.notice.NoticeSendTypeEnum;
import com.xinkongan.cloud.module.system.enums.notice.NoticeTypeEnum;

import java.util.List;
import java.util.Set;

/**
 * @Description 消息通知Service
 * <AUTHOR>
 * @Date 2025/3/6 11:15
 */
public interface INoticeService {

    /**
     * 保存消息
     **/
    Boolean saveNotice(NoticeDO noticeDO);

    /**
     * 发送消息
     * @param sendTypeEnums 通知形式列表
     * @param noticeTypeEnum 通知类型
     * @param userIds 用户ID列表
     **/
    Boolean noticeToUsers(Set<NoticeSendTypeEnum> sendTypeEnums, NoticeTypeEnum noticeTypeEnum, Set<Long> userIds, NoticeSaveVO noticeSaveVO);

    /**
     * 发送消息
     * @param sendTypeEnums 通知形式列表
     * @param noticeTypeEnum 通知类型
     * @param tenantId 租户ID
     **/
    Boolean noticeToTenant(Set<NoticeSendTypeEnum> sendTypeEnums, NoticeTypeEnum noticeTypeEnum, Long tenantId, NoticeSaveVO noticeSaveVO);

    /**
     * 消息通知分页
     * <AUTHOR>
     * @date 2025/3/10 11:31
     **/
    PageResult<NoticeVO> page(NoticePageReqVO noticePageReqVO);

    /**
     * 批量删除
     * <AUTHOR>
     * @date 2025/3/10 11:31
     **/
    Boolean deleteBatch(List<Long> ids);

    /**
     * 登录后弹窗消息
     **/
    LoginPopNoticeVO loginPopNoticeVO(Long userId);

    /**
     * 根据id获取页码
     **/
    Integer getPageNumById(Long id, Integer pageSize);
}
