package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MultipleDeviceVO {

    @Schema(description = "机场设备列表")
    private List<PostureDeviceVO> dockList;

}
