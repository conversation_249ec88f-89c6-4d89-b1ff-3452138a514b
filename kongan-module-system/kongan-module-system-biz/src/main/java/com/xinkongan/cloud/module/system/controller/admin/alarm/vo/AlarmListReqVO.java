package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xinkongan.cloud.module.system.enums.task.JobStatusTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 管理后台 - 警情列表 Request VO
 * <AUTHOR>
 * @Date 2025/3/24 20:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "管理后台 - 警情列表 Request VO")
public class AlarmListReqVO {

    /**
     * {@link JobStatusTypeEnum}
     **/
    @Schema(description = "警情任务状态 1待执行 2执行中 3未完成 5已执行")
    @JsonIgnore
    private List<Integer> status;

    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "结束时间")
    @JsonIgnore
    private LocalDateTime endTime;
}