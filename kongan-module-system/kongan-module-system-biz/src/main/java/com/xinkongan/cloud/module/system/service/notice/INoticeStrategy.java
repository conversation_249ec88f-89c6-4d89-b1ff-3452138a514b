package com.xinkongan.cloud.module.system.service.notice;

import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeDO;
import com.xinkongan.cloud.module.system.enums.notice.NoticeSendTypeEnum;

import java.util.Set;

/**
 * @Description 发送消息策略
 * <AUTHOR>
 * @Date 2025/3/6 14:43
 */
public interface INoticeStrategy {

    /**
     * 发送消息
     **/
    void sendNotice(Set<NoticeSendTypeEnum> noticeSendTypeEnumSet, Set<Long> userIds, NoticeDO noticeDO);
}
