package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @Description 接警飞行记录绑定任务ReqVO
 * <AUTHOR>
 * @Date 2025/3/24 19:24
 */
@Schema(description = "接警飞行记录绑定任务ReqVO")
@Data
public class AlarmBindJobReqVO {

    @Schema(description = "飞行记录id")
    @NotNull(message = "飞行记录id不能为空")
    private Long flyRecordId;

    @Schema(description = "任务id")
    @NotEmpty(message = "任务id不能为空")
    private List<Long> jobId;
}