package com.xinkongan.cloud.module.system.controller.admin.notice;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.*;
import com.xinkongan.cloud.module.system.service.notice.INoticeReadService;
import com.xinkongan.cloud.module.system.service.notice.INoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 消息通知接口
 * <AUTHOR>
 * @Date 2025/3/10 11:08
 */
@Tag(name = "消息通知")
@Validated
@RestController
@RequestMapping("/system/notice")
public class NoticeController {

    @Resource
    private INoticeService noticeService;
    @Resource
    private INoticeReadService noticeReadService;

    @GetMapping("/page")
    @Operation(summary = "消息通知列表", description = "消息通知列表")
    CommonResult<PageResult<NoticeVO>> page(@Valid NoticePageReqVO noticePageReqVO) {
        PageResult<NoticeVO> page = noticeService.page(noticePageReqVO);
        return success(page);
    }

    @GetMapping("/unreadCount")
    @Operation(summary = "未读消息数量", description = "未读消息数量")
    CommonResult<Long> unreadCount() {
        Long unreadCount = noticeReadService.unreadCount(SecurityFrameworkUtils.getLoginUserId());
        return success(unreadCount);
    }

    @GetMapping("/loginPopNoticeVO")
    @Operation(summary = "登录后弹窗接口", description = "登录后弹窗接口")
    CommonResult<LoginPopNoticeVO> loginPopNoticeVO() {
        LoginPopNoticeVO loginPopNoticeVO = noticeService.loginPopNoticeVO(SecurityFrameworkUtils.getLoginUserId());
        return success(loginPopNoticeVO);
    }

    @GetMapping("/noticeStatistics")
    @Operation(summary = "消息统计", description = "消息统计")
    CommonResult<NoticeStatisticsVO> noticeStatistics() {
        NoticeStatisticsVO noticeStatisticsVO = noticeReadService.noticeStatistics(SecurityFrameworkUtils.getLoginUserId());
        return success(noticeStatisticsVO);
    }

    @PostMapping("/markAsRead")
    @Operation(summary = "标记已读", description = "标记已读")
    CommonResult<Boolean> markAsRead(@RequestBody @Validated MarkAsReadReqVO reqVO) {
        Boolean aBoolean = noticeReadService.markAsRead(SecurityFrameworkUtils.getLoginUserId(), reqVO.getIds());
        return success(aBoolean);
    }

    @PostMapping("/deleteBatch")
    @Operation(summary = "批量删除", description = "批量删除")
    CommonResult<Boolean> deleteBatch(@RequestBody @Validated NoticeDeleteBatchReqVO reqVO) {
        Boolean aBoolean = noticeService.deleteBatch(reqVO.getIds());
        return success(aBoolean);
    }

    @GetMapping("/getPageNumById")
    @Operation(summary = "根据id获取页码", description = "根据id获取页码")
    public CommonResult<Integer> getPageNumById(@RequestParam("id") Long id, @RequestParam("pageSize") Integer pageSize) {
        Integer pageNum = noticeService.getPageNumById(id,pageSize);
        return CommonResult.success(pageNum);
    }
}