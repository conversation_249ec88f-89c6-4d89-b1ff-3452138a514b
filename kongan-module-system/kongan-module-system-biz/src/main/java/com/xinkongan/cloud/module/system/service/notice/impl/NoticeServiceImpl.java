package com.xinkongan.cloud.module.system.service.notice.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.mybatis.core.util.MyBatisUtils;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.LoginPopNoticeVO;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticePageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeSaveVO;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeVO;
import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeDO;
import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeReadDO;
import com.xinkongan.cloud.module.system.dal.mysql.notice.NoticeMapper;
import com.xinkongan.cloud.module.system.enums.notice.NoticeSendTypeEnum;
import com.xinkongan.cloud.module.system.enums.notice.NoticeTypeEnum;
import com.xinkongan.cloud.module.system.service.notice.INoticeReadService;
import com.xinkongan.cloud.module.system.service.notice.INoticeService;
import com.xinkongan.cloud.module.system.service.notice.INoticeStrategy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.NOTICE_NOT_FOUND;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.NOTICE_SAVE_FAIL;

/**
 * @Description 消息通知ServiceImpl
 * <AUTHOR>
 * @Date 2025/3/6 11:40
 */
@Slf4j
@Service
public class NoticeServiceImpl implements INoticeService {

    @Resource
    private NoticeMapper noticeMapper;
    @Resource
    private INoticeReadService noticeReadService;
    @Resource
    private List<INoticeStrategy> noticeStrategyList;

    @Override
    public Boolean saveNotice(NoticeDO noticeDO) {
        int insert = noticeMapper.insert(noticeDO);
        return insert > 0;
    }

    @Override
    public Boolean noticeToUsers(Set<NoticeSendTypeEnum> sendTypeEnums, NoticeTypeEnum noticeTypeEnum, Set<Long> userIds, NoticeSaveVO noticeSaveVO) {
        // 保存消息
        NoticeDO noticeDO = NoticeDO.builder()
                .type(noticeTypeEnum.getType())
                .typeDesc(noticeTypeEnum.getDesc())
                .needPop(sendTypeEnums.contains(NoticeSendTypeEnum.POP) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode())
                .module(noticeTypeEnum.getModuleEnum().getModule())
                .build();
        BeanUtils.copyProperties(noticeSaveVO, noticeDO);
        Boolean saveNotice = saveNotice(noticeDO);
        if (!saveNotice) {
            throw exception(NOTICE_SAVE_FAIL);
        }
        // 保存已读关联关系
        List<NoticeReadDO> noticeReadDOS = userIds.stream().map(id -> NoticeReadDO.builder()
                .userId(id)
                .noticeId(noticeDO.getId())
                .readFlag(Boolean.FALSE)
                .build()).toList();
        noticeReadService.saveNoticeRead(noticeReadDOS);
        // TODO 查询第三方通知方式
        // 根据发送策略发送消息
        noticeStrategyList.forEach(noticeStrategy -> noticeStrategy.sendNotice(sendTypeEnums, userIds, noticeDO));
        return Boolean.TRUE;
    }

    @Override
    public Boolean noticeToTenant(Set<NoticeSendTypeEnum> sendTypeEnums, NoticeTypeEnum noticeTypeEnum, Long tenantId, NoticeSaveVO noticeSaveVO) {
        // TODO 根据租户查询用户列表
        Set<Long> userIds = new HashSet<>();
        return noticeToUsers(sendTypeEnums, noticeTypeEnum, userIds, noticeSaveVO);
    }

    @Override
    public PageResult<NoticeVO> page(NoticePageReqVO reqVO) {
        // 查询本账号的消息列表分页
        IPage<NoticeVO> mpPage = MyBatisUtils.buildPage(reqVO);
        IPage<NoticeVO> page = noticeMapper.page(mpPage, reqVO, SecurityFrameworkUtils.getLoginUserId());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteBatch(List<Long> ids) {
        // 批量删除消息
        noticeMapper.deleteByIds(ids);
        // 批量删除已读关联关系
        noticeReadService.deleteBatchByNoticeIds(ids);
        return true;
    }

    @Override
    public LoginPopNoticeVO loginPopNoticeVO(Long userId) {
        // 查询最新未读消息
        NoticeVO noticeVO = noticeMapper.selectNewestNotice(userId);
        if (noticeVO == null) {
            return LoginPopNoticeVO.builder().popNoticeList(List.of()).build();
        }
        // 查询需要弹窗的未读消息
        List<NoticeVO> list = noticeMapper.selectPopNotice(userId);
        if (CollUtil.isEmpty(list)) {
            list = List.of();
        }
        return LoginPopNoticeVO.builder().newestNotice(noticeVO).popNoticeList(list).build();
    }

    @Override
    public Integer getPageNumById(Long id, Integer pageSize) {

        NoticeDO noticeDO = noticeMapper.selectById(id);
        if (noticeDO == null) {
            throw exception(NOTICE_NOT_FOUND);
        }
        // 当前记录的创建时间
        LocalDateTime createTime = noticeDO.getCreateTime();
        // 查询比当前记录更早的数量
        Integer count = noticeMapper.selectCountBefore(SecurityFrameworkUtils.getLoginUserId(), createTime);
        return (count + pageSize) / pageSize;
    }
}