package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 修改重要程度
 * <AUTHOR>
 * @Date 2025/2/17 9:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateImportantReqVO {

    @Schema(description = "飞行记录id", required = true)
    @NotNull(message = "飞行记录id不能为空")
    private Long id;

    @Schema(description = "重要程度 1重要0普通", required = true)
    @NotNull(message = "重要程度不能为空")
    private Integer importantLevel;
}