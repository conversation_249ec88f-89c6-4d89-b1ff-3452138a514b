package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 禁飞区文件响应VO
 */
@Data
@Schema(description = "管理后台 - 禁飞区文件响应VO")
public class NfzFileRespVO {

    @Schema(description = "文件ID", example = "1")
    private Long id;

    @Schema(description = "文件名称", example = "nfz_file.json")
    private String name;

    @Schema(description = "文件地址", example = "http://example.com/file.json")
    private String fileUrl;

    @Schema(description = "文件SHA256签名")
    private String checksum;

    @Schema(description = "文件大小", example = "1024")
    private Integer size;

    @Schema(description = "文件关联表ID", example = "1")
    private Long fileId;

    @Schema(description = "创建人ID", example = "1")
    private Long creator;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "修改人ID", example = "1")
    private Long updater;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;
}
