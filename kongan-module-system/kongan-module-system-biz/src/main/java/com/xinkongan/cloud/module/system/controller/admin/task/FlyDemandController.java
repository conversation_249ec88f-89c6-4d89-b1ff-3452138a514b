package com.xinkongan.cloud.module.system.controller.admin.task;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.demand.*;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask.CreateDockTaskReqVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobDetailRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobPageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobPageRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobStatisticCountVO;
import com.xinkongan.cloud.module.system.service.task.IDemandService;
import com.xinkongan.cloud.module.system.service.task.IDockTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 机场计划管理
 * <AUTHOR>
 * @Date 2025/08/21 21:42
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 机场计划管理")
@RequestMapping("/system/demand")
public class FlyDemandController {

    @Resource
    private IDemandService demandService;

    /**
     * 需求分页
     **/
    @PreAuthorize("@ss.hasAnyPermissions('system:demand:query')")
    @GetMapping("/page")
    @Operation(summary = "需求分页", description = "需求分页")
    CommonResult<PageResult<FlyDemandPageRespVO>> page(@Valid FlyDemandPageReqVO reqVO) {
        PageResult<FlyDemandPageRespVO> page = demandService.page(reqVO);
        return success(page);
    }


    /**
     * 保存飞行需求
     **/
    @PostMapping("/save")
    @Operation(summary = "保存/提交飞行需求", description = "保存/提交飞行需求")
    @PreAuthorize("@ss.hasAnyPermissions('system:demand:add')")
    CommonResult<Long> saveDemand(@Valid @RequestBody FlyDemandReqVO reqVO) {
        Long id = demandService.createDemand(reqVO);
        return success(id);
    }

    /**
     * 更新飞行需求
     **/
    @PostMapping("/edit")
    @Operation(summary = "更新飞行需求", description = "更新飞行需求")
    @PreAuthorize("@ss.hasAnyPermissions('system:demand:edit')")
    CommonResult<Boolean> editDemand(@Valid @RequestBody FlyDemandUpdateReqVO reqVO) {
        Boolean flag= demandService.updateDemand(reqVO) != null;
        return success(flag);
    }

    /**
     * 获取需求详情
     **/
    @PreAuthorize("@ss.hasAnyPermissions('system:job:query')")
    @GetMapping("/getByDemandId")
    @Operation(summary = "根据需求id获取需求详情", description = "根据需求id获取需求详情")
    CommonResult<FlyDemandRespVO> getByDemandId(@RequestParam Long demandId) {
        FlyDemandRespVO flyDemandRespVO = demandService.getById(demandId);
        return success(flyDemandRespVO);
    }

    /**
     * 删除需求
     **/
    @PreAuthorize("@ss.hasPermission('system:demand:delete')")
    @PostMapping("/deleteByDemandId")
    @Operation(summary = "删除需求", description = "删除需求")
    CommonResult<Boolean> deleteByDemandId(@RequestParam Long demandId) {
        Boolean delete = demandService.deleteDemand(demandId);
        return success(delete);
    }

    /**
     * 获取需求总数
     **/
    @PreAuthorize("@ss.hasAnyPermissions('system:job:query')")
    @GetMapping("/getDemandCount")
    @Operation(summary = "获取需求总数", description = "获取需求总数")
    CommonResult<Long> getDemandCount() {
        Long totalCount = demandService.getDemandCount();
        return success(totalCount);
    }


    @Operation(summary = "需求状态数量统计")
    @GetMapping("/demandStatisticCount")
    public CommonResult<DemandStatisticCountVO> demandStatisticCount() {
        return CommonResult.success(demandService.statisticCount());
    }
}