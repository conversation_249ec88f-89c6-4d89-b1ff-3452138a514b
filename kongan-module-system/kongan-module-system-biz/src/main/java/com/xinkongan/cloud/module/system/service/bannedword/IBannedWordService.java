package com.xinkongan.cloud.module.system.service.bannedword;

import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.BannedWordPagePageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.BannedWordVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.CreateBannedWordReqVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.UpdateBannedWordReqVO;

/**
 * @Description 违禁词校验Service
 * <AUTHOR>
 * @Date 2024/11/5 9:23
 */
public interface IBannedWordService {

    /**
     * 启动时加载违禁词Tire树
     * <AUTHOR>
     * @date 2024/11/5 9:59
     **/
    void loadBannedWords();

    /**
     * 校验文本中是否包含违禁词，并返回违禁词
     * <AUTHOR>
     * @date 2024/11/5 10:11
     **/
    String getBannedWord(String text);

    /**
     * 违禁词分页
     * <AUTHOR>
     * @date 2024/11/5 10:35
     **/
    PageResult<BannedWordVO> getBannedWordPage(BannedWordPagePageReqVO pageReqVO);

    /**
     * 创建违禁词
     * <AUTHOR>
     * @date 2024/11/5 10:26
     **/
    Long createBannedWord(CreateBannedWordReqVO createBannedWordReqVO);

    /**
     * 修改违禁词
     * <AUTHOR>
     * @date 2024/11/5 10:53
     **/
    Boolean updateBannedWord(UpdateBannedWordReqVO updateReqVO);

    /**
     * 删除违禁词
     * <AUTHOR>
     * @date 2024/11/5 10:59
     **/
    Boolean deleteBannedWord(Long id);
}
