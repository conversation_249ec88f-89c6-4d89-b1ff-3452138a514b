package com.xinkongan.cloud.module.system.controller.admin.measure.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * 激光打点截图文件 VO
 */
@Data
@ToString
@Schema(description = "激光打点截图文件 VO")
public class MeasureTargetFileVO {

    /**
     * id
     */
    @Schema(description = "飞行记录图片id")
    private Long id;

    /**
     * 文件url
     */
    @Schema(description = "文件url")
    private String url;

    /**
     * 无人机序列号
     */
    @Schema(description = "无人机序列号sn", required = true)
    private String sn;

    /**
     * 飞行记录ID
     */
    @Schema(description = "飞行记录ID")
    private Long flyRecordId;

    /**
     * 类型（0：图片，1：全景图，2：视频）文件类型 {@link RecordFileEnum}
     */
    @Schema(description = "类型（0：图片，1：全景图，2：视频）", required = true)
    private Integer type;

    /**
     * 文件阿里云路径
     */
    @Schema(description = "文件阿里云路径", required = true)
    private String objectName;

    /**
     * 文件大小
     */
    @Schema(description = "文件大小", required = true)
    private Double size;

    /**
     * 是否为原图
     */
    @Schema(description = "是否为原图")
    private Boolean original;

    /**
     * 经度
     */
    @Schema(description = "经度", required = true)
    private String longitude;

    /**
     * 纬度
     */
    @Schema(description = "纬度", required = true)
    private String latitude;

    /**
     * 绝对高度
     */
    @Schema(description = "绝对高度", required = true)
    private String absoluteAltitude;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID", required = true)
    private Long jobId;

    /**
     * 拍照位置
     */
    @Schema(description = "拍照位置")
    private String location;

    /**
     * 镜头类型
     */
    @Schema(description = "镜头类型")
    private String lensType;

    /**
     * 文件来源类型 {@link SourceTypeEnum}
     */
    @Schema(description = "文件来源 1巡检记录素材上传 2联合行动记录素材上传", example = "1")
    private Integer source;

    @Schema(description = "宽度")
    private Integer width;

    @Schema(description = "高度")
    private Integer height;

    @Schema(description = "原图url")
    private String originalUrl;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件标签 0截图 1人工 2 ai 3 ai+人工 4 拍照")
    private Integer labelType;

    @Schema(description = "对应的异常id")
    private Long exceptionId;

    @Schema(description = "文件表id")
    private Long fileId;

    @Schema(description = "异常oss文件id")
    private Long exceptionFileId;
}
