package com.xinkongan.cloud.module.system.service.route;

import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteSaveDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteShareDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteUpdateDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.*;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteDO;

public interface IRouteService {


    Long routePageSearchCount(RouteSearchDTO routeSearchParams);


    /**
     * 航线分页查询
     *
     * @param routeSearchParams 航线查询参数
     * @return 分页结果
     */
    PageResult<RouteRespVO> routePageSearch(RouteSearchDTO routeSearchParams);


    /**
     * 根据航线id查询航线详情
     *
     * @param routeId 航线id
     * @return 航线详情
     */
    RouteDetailRespVO getRouteDetailById(Long routeId);


    /**
     * 根据航线id查询航线基本信息
     *
     * @param routeId 航线id
     * @return 航线基本信息
     */
    RouteBaseRespVO getRouteBaseById(Long routeId);


    /**
     * 航线保存
     *
     * @param routeSaveInfo 航线信息
     * @return 航线id
     */
    Long routeSave(RouteSaveDTO routeSaveInfo);


    /**
     * 删除航线
     *
     * @param routeId 航线id
     * @param forcedDeletion 是否强制删除
     */
    void delRouteById(Long routeId, Boolean forcedDeletion);


    /**
     * 航线更新
     *
     * @param routeUpdateInfo 航线信息
     * @return 航线id
     */
    Long routeUpdate(RouteUpdateDTO routeUpdateInfo);


    /**
     * 航线分享
     *
     * @param routeShareInfo 分享参数
     */
    void routeShare(RouteShareDTO routeShareInfo);


    /**
     * 查询航线分享的组织信息
     *
     * @param routeId 航线id
     * @return 分享信息
     */
    RouteShareVO getRouteShareDept(Long routeId);


    /**
     * 根据id锁定航线
     *
     * @param routeId 航线id
     */
    void lockRouteById(Long routeId);


    /**
     * 解锁航线
     *
     * @param routeId 航线id
     */
    void unlockRouteById(Long routeId);


    /**
     * 检查航线名称是否重复
     *
     * @param routeId   航线id
     * @param deptId    组织id
     * @param routeName 航线名称
     * @return true 重复，false 不重复
     */
    boolean checkRouteNameRepeat(Long routeId, Long deptId, String routeName);


    /**
     * 添加航线复制次数
     *
     * @param routeId 航线id
     */
    void addRouteCopyCount(Long routeId);

    /**
     * 根据id获取航线
     *
     * @param routeId 航线id
     * @return 航线
     */
    RouteDO getById(Long routeId);

    /**
     * 查询当前机场已执行的航线
     *
     * @param param 分页参数
     * @return 航线列表
     */
    PageResult<ExecRouteVO> getExecRouteListByDockSn(DockExecRoutePageParam param);

    /**
     * 删除组织下的航线
     *
     * @param deptId 组织id
     */
    void deleteRouteByDeptId(Long deptId);
}
