package com.xinkongan.cloud.module.system.service.psdk;

import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.job.core.memory.IMemoryTaskService;
import com.xinkongan.cloud.framework.lock.service.RedisLockService;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.PSdkWidgetValueDTO;
import com.xinkongan.cloud.module.system.controller.admin.psdk.dto.*;
import com.xinkongan.cloud.module.system.controller.admin.psdk.vo.VoiceSendToDockVO;
import com.xinkongan.cloud.module.system.dto.SpeakerStatusDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.FlyIncidentTypeEnum;
import com.xinkongan.cloud.module.system.enums.PSDKMethodEnum;
import com.xinkongan.cloud.module.system.enums.psdk.PsdkConstant;
import com.xinkongan.cloud.module.system.service.task.IJobFlyProgressService;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.dto.SpeakValues;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.PsdkWidgetValues;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesPublish;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Slf4j
@Service
public class PsdkServiceImpl implements IPsdkService {

    @Resource
    private ServicesPublish servicesPublish;

    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private WebSocketSendApi webSocketSendApi;

    @Resource
    private IMemoryTaskService memoryTaskService;

    @Resource
    private IJobFlyProgressService progressService;

    private final Map<String, Runnable> cbCacheMap = new ConcurrentHashMap<>();

    @Override
    public void removeCb(String dockSn) {
        cbCacheMap.remove(dockSn);
    }

    @Override
    public void execCb(String dockSn) {
        Runnable runnable = cbCacheMap.get(dockSn);
        if (runnable != null) {
            runnable.run();
        }
    }

    @Override
    public void execShout(VoiceSendToDockVO voiceSendToDockVo, Runnable cb) {
        // 下发播放
        String text = voiceSendToDockVo.getText();
        if (!StringUtils.isEmpty(text)) {
            this.sendTextToDock(voiceSendToDockVo);
        }
        String url = voiceSendToDockVo.getUrl();
        if (!StringUtils.isEmpty(url)) {
            this.sendVoiceToDock(voiceSendToDockVo);
        }
        // 添加回调
        cbCacheMap.put(voiceSendToDockVo.getDockSn(), cb);
    }

    @Override
    public Boolean setWidgetValue(String dockSn, PSdkWidgetValueDTO pSdkWidgetValueParams) {
        CommonTopicResponse<ServicesReplyData> response =
                servicesPublish.publish(
                        dockSn,
                        PSDKMethodEnum.PSDK_WIDGET_VALUE_SET.getMethod(),
                        pSdkWidgetValueParams);
        return response.getData().getResult().isSuccess();
    }

    @Override
    public Boolean sendTextToDock(VoiceSendToDockVO voiceSendToDockVo) {
        String dockSn = voiceSendToDockVo.getDockSn();
        String key = String.format(RedisKeyConstants.DOCK_SPEAKER_LOCK, dockSn);
        return redisLockService.executeWithLockAwait(key, 2, () -> {
            // 判断喊话器是否在播放中
            String speakerStatusKey = String.format(RedisKeyConstants.DOCK_SPEAKER_STATUS, dockSn);
            SpeakerStatusDTO status = (SpeakerStatusDTO) redisCacheService.get(speakerStatusKey);
            if (status != null && status.getIsPlaying()) {
//                throw new ServiceException(ErrorCodeConstants.SPEAKER_IS_PLAYING);
                // 停止播放
                this.speakerPlayStop(dockSn);
            }
            //设置播放模式
            this.setSpeakerPlayMode(dockSn, voiceSendToDockVo.getMode(), PsdkConstant.PSDK_DEFAULT_INDEX);
            //设置播放音量
            this.setSpeakerPlayVolume(dockSn, voiceSendToDockVo.getVoice(), PsdkConstant.PSDK_DEFAULT_INDEX);
            // 播放TTS文本
            Tts tts = Tts.builder()
                    .text(voiceSendToDockVo.getText())
                    .md5(voiceSendToDockVo.getFingerprint())
                    .name(voiceSendToDockVo.getName())
                    .build();
            TextMessage textMessage = TextMessage.builder()
                    .psdkIndex(2)
                    .tts(tts)
                    .build();
            CommonTopicResponse<ServicesReplyData> response = servicesPublish.publish(dockSn, PSDKMethodEnum.SPEAKER_TTS_PLAY_START.getMethod(), textMessage);
            if (!response.getData().getResult().isSuccess()) {
                throw new ServiceException(ErrorCodeConstants.SPEAKER_TTS_PLAY_ERROR);
            }
            // 喊话发送成功，更新缓存，发送喊话状态
            SpeakerStatusDTO speakerStatus = new SpeakerStatusDTO();
            speakerStatus.setDockSn(dockSn);
            speakerStatus.setIsPlaying(true);
            speakerStatus.setType(0);
            speakerStatus.setMd5(voiceSendToDockVo.getFingerprint());
            speakerStatus.setVoice(voiceSendToDockVo.getVoice());
            speakerStatus.setMode(voiceSendToDockVo.getMode());
            redisCacheService.put(speakerStatusKey, speakerStatus, 30L, TimeUnit.MINUTES);
            // 发送给前端当前喊话器状态, 直接表明喊话器当前模式为播放中
            this.sendSpeakerStatus(speakerStatus);
            return Boolean.TRUE;
        });
    }

    @Override
    public void sendSpeakerStatus(SpeakerStatusDTO status) {
        WebSocketMessageDTO<SpeakerStatusDTO> webSocketMessageDTO = WebSocketMessageDTO.<SpeakerStatusDTO>builder()
                .tenantId(TenantContextHolder.getTenantId())
                .message(CustomWebSocketMessage.<SpeakerStatusDTO>builder()
                        .bizCode(BizCodeEnum.SPAKER_STATUS_UPDATE.getCode())
                        .data(status)
                        .build())
                .build();
        webSocketSendApi.sendByTenant(webSocketMessageDTO);
        // 保存飞行记录关键动作 喊话器开始播放
        FlyIncidentTypeEnum voiceType = status.getIsPlaying() ? FlyIncidentTypeEnum.VOICE_START : FlyIncidentTypeEnum.VOICE_OVER;
        processFlyAction(status.getDockSn(), voiceType);
    }


    @Override
    public Boolean sendVoiceToDock(VoiceSendToDockVO voiceSendToDockVo) {

        String dockSn = voiceSendToDockVo.getDockSn();
        String key = String.format(RedisKeyConstants.DOCK_SPEAKER_LOCK, dockSn);
        return redisLockService.executeWithLockAwait(key, 2, () -> {
            // 判断喊话器是否在播放中
            String speakerStatusKey = String.format(RedisKeyConstants.DOCK_SPEAKER_STATUS, dockSn);
            SpeakerStatusDTO status = (SpeakerStatusDTO) redisCacheService.get(speakerStatusKey);
            if (status != null && status.getIsPlaying()) {
//                throw new ServiceException(ErrorCodeConstants.SPEAKER_IS_PLAYING);
                // 停止播放
                this.speakerPlayStop(dockSn);
            }
            //设置播放模式
            this.setSpeakerPlayMode(dockSn, voiceSendToDockVo.getMode(), PsdkConstant.PSDK_DEFAULT_INDEX);

            //设置播放音量
            this.setSpeakerPlayVolume(dockSn, voiceSendToDockVo.getVoice(), PsdkConstant.PSDK_DEFAULT_INDEX);

            //播放音频
            VoiceFileDTO file = VoiceFileDTO.builder()
                    .format("pcm")
                    .url(voiceSendToDockVo.getUrl())
                    .md5(voiceSendToDockVo.getFingerprint())
                    .name(voiceSendToDockVo.getName())
                    .build();
            VoiceMessage voiceMessage = VoiceMessage.builder()
                    .file(file)
                    .psdkIndex(2)
                    .build();
            CommonTopicResponse<ServicesReplyData> response = servicesPublish.publish(dockSn, PSDKMethodEnum.SPEAKER_AUDIO_PLAY_START.getMethod(), voiceMessage);
            if (!response.getData().getResult().isSuccess()) {
                throw new ServiceException(ErrorCodeConstants.SPEAKER_AUDIO_PLAY_START_ERROR);
            }
            // 喊话发送成功，更新缓存，发送喊话状态
            SpeakerStatusDTO speakerStatus = new SpeakerStatusDTO();
            speakerStatus.setDockSn(dockSn);
            speakerStatus.setIsPlaying(true);
            speakerStatus.setType(1);
            speakerStatus.setMd5(voiceSendToDockVo.getFingerprint());
            speakerStatus.setVoice(voiceSendToDockVo.getVoice());
            speakerStatus.setMode(voiceSendToDockVo.getMode());
            redisCacheService.put(speakerStatusKey, speakerStatus, 30L, TimeUnit.MINUTES);
            // 发送给前端当前喊话器状态
            this.sendSpeakerStatus(speakerStatus);
            return Boolean.TRUE;
        });
    }

    /**
     * 记录飞行记录关键动作 喊话器开始/结束播放
     *
     * <AUTHOR>
     * @date 2025/3/17 17:00
     **/
    private void processFlyAction(String dockSn, FlyIncidentTypeEnum flyIncidentTypeEnum) {
        try {
            progressService.saveDetail(dockSn, flyIncidentTypeEnum, null, null);
        } catch (Exception e) {
            log.error("保存开始喊话关键动作失败", e);
        }
    }

    @Override
    public Boolean speakerPlayStop(String dockSn) {
        //播放停止
        ReplyAndStopDTO replyAndStopDTO = ReplyAndStopDTO.builder().psdkIndex(PsdkConstant.PSDK_DEFAULT_INDEX).build();
        CommonTopicResponse<ServicesReplyData> response =
                servicesPublish.publish(
                        dockSn,
                        PSDKMethodEnum.SPEAKER_PLAY_STOP.getMethod(),
                        replyAndStopDTO
                );
        if (!response.getData().getResult().isSuccess()) {
            throw new ServiceException(ErrorCodeConstants.SPEAKER_PLAY_STOP_ERROR);
        }
        String speakerStatusKey = String.format(RedisKeyConstants.DOCK_SPEAKER_STATUS, dockSn);
        SpeakerStatusDTO status = (SpeakerStatusDTO) redisCacheService.get(speakerStatusKey);
        if (status != null) {
            status.setIsPlaying(Boolean.FALSE);
            redisCacheService.put(speakerStatusKey, status, 30L, TimeUnit.MINUTES);
            this.sendSpeakerStatus(status);
        }
        // 延迟 一下删除缓存
        String speakerPlayStatusKey = String.format(RedisKeyConstants.DOCK_SPEAKER_PLAY_STATUS, dockSn);
        memoryTaskService.createDelayTask("speaker_cache_remove_" + dockSn, () -> {
            redisCacheService.invalidate(speakerStatusKey);
            redisCacheService.invalidate(speakerPlayStatusKey);

            this.execCb(dockSn);
        }, 2 * 1000);
        // 移除回调
        cbCacheMap.remove(dockSn);
        return Boolean.TRUE;
    }

    @Override
    public Boolean haveSpeaker(String sn) {
        String speakerKey = String.format(RedisKeyConstants.DRONE_SPEAKER, sn);
        Object o = redisCacheService.get(speakerKey);
        return o != null;
    }

    @Override
    public String speakerType(String sn) {
        String speakerKey = String.format(RedisKeyConstants.DRONE_SPEAKER, sn);
        PsdkWidgetValues psdkWidgetValue = (PsdkWidgetValues) redisCacheService.get(speakerKey);
        if (psdkWidgetValue == null) {
            return null;
        }
        return psdkWidgetValue.getPsdkName();
    }

    @Override
    public List<SpeakValues> speakerStatus(String sn) {
        String key = String.format(RedisKeyConstants.DRONE_SPEAKER, sn);
        PsdkWidgetValues psdkWidgetValue = (PsdkWidgetValues) redisCacheService.get(key);
        if (psdkWidgetValue == null) {
            return new ArrayList<>();
        }
        return psdkWidgetValue.getValues();
    }


    @Override
    public SpeakerStatusDTO getSpeakerStatus(String dockSn) {
        // 1. 先判断该机场是否有喊话器
        Boolean haveSpeaker = this.haveSpeaker(dockSn);
        if (!haveSpeaker) {
            throw new ServiceException(ErrorCodeConstants.VOICE_NOT_EXISTS);
        }
        // 2. 获取喊话器状态
        String key = String.format(RedisKeyConstants.DOCK_SPEAKER_STATUS, dockSn);
        SpeakerStatusDTO speakerStatusInfo = (SpeakerStatusDTO) redisCacheService.get(key);
        if (speakerStatusInfo == null) {
            speakerStatusInfo = new SpeakerStatusDTO();
            speakerStatusInfo.setDockSn(dockSn);
            speakerStatusInfo.setIsPlaying(false);
        }
        return speakerStatusInfo;
    }

    private void setSpeakerPlayMode(String dockSn, Integer playMode, Integer psdkIndex) {
        //设置播放模式
        CommonTopicResponse<ServicesReplyData> response =
                servicesPublish.publish(
                        dockSn,
                        PSDKMethodEnum.SPEAKER_PLAY_MODE_SET.getMethod(),
                        new PlayModeDTO(playMode, psdkIndex)
                );
        if (!response.getData().getResult().isSuccess()) {
            throw new ServiceException(ErrorCodeConstants.PSDK_PLAY_MODE_SET_ERROR);
        }
    }

    private void setSpeakerPlayVolume(String dockSn, Integer playVolume, Integer psdkIndex) {
        //设置播放模式
        CommonTopicResponse<ServicesReplyData> response =
                servicesPublish.publish(
                        dockSn,
                        PSDKMethodEnum.SPEAKER_PLAY_VOLUME_SET.getMethod(),
                        new PlayVolumDTO(playVolume, psdkIndex)
                );
        if (!response.getData().getResult().isSuccess()) {
            throw new ServiceException(ErrorCodeConstants.SPEAKER_PLAY_VOLUME_SET_ERROR);
        }
    }
}
