package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 计划分页Resp VO
 * <AUTHOR>
 * @Date 2025/1/13 18:56
 */
@Data
@Schema(description = "计划分页Resp VO")
public class JobPageRespVO {

    @Schema(description = "计划id")
    private Long taskId;

    @Schema(description = "任务id")
    private Long jobId;
    
    @Schema(description = "任务名称")
    private String jobName;

    @Schema(description = "任务执行模式")
    private Integer execMode;

    @Schema(description = "任务所属组织id")
    private Long deptId;

    @Schema(description = "任务所属组织名称")
    private String deptName;

    @Schema(description = "任务执行时间")
    private LocalDateTime executeTime;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "接警任务是否过期 1是0否")
    private Integer alarmExpiredFlag;

    @Schema(description = "失败原因")
    private String reason;

    @Schema(description = "接警id")
    private Long alarmId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否有断点")
    private Boolean hasBreakpoint;
}