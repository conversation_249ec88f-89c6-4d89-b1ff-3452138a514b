package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/27 10:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "绑定任务VO")
public class BindJobVO {

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "任务名称")
    private String jobName;
}