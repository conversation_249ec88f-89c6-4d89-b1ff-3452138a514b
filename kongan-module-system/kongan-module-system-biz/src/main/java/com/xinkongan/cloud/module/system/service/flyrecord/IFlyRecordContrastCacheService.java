package com.xinkongan.cloud.module.system.service.flyrecord;

import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;

import java.util.List;

/**
 * @Description 飞行记录对比缓存Service
 * <AUTHOR>
 * @Date 2025/2/19 9:38
 */
public interface IFlyRecordContrastCacheService {

    /**
     * 添加飞行记录图片位置缓存
     * <AUTHOR>
     * @date 2025/2/19 9:39
     **/
    void addContrastFileCache(FlyRecordFileDO flyRecordFileDO);

    /**
     * 根据距离获取附近的图片
     * <AUTHOR>
     * @date 2025/2/19 9:39
     **/
    List<Long> getFileByRadius(Long tenantId, Double lon, Double lat, Double radius);
}
