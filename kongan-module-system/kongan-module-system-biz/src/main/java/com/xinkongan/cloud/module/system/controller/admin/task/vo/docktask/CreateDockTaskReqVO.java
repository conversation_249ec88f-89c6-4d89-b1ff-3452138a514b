package com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.xinkongan.cloud.framework.common.util.json.databind.StringLocalDateTimeDeserializer;
import com.xinkongan.cloud.framework.common.util.json.databind.StringLocalDateTimeListDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 创建计划 ReqVO
 * <AUTHOR>
 * @Date 2025/1/7 19:44
 */
@Data
public class CreateDockTaskReqVO {


    @Schema(description = "机场sn")
    @NotNull(message = "机场sn不能为空")
    private String dockSn;

    @Schema(description = "航线id")
    @NotNull(message = "航线id不能为空")
    private Long routeId;

    @Schema(description = "计划名称")
    @NotBlank(message = "计划名称不能为空")
    private String name;

    @Schema(description = "任务预计耗时(单位:秒)")
    @NotNull(message = "任务预计耗时不能为空")
    private Integer predictTime;

    @Schema(description = "计划描述")
    private String description;

    @Schema(description = "场景类型 0巡检1建模")
    private Integer scene = 0;// 默认巡检任务

    @Schema(description = "计划模式  0立即执行1定时任务2循环任务")
    @NotNull(message = "计划模式不能为空")
    private Integer taskMode;

    @Schema(description = "循环模式(循环任务) 0按天循环1按周循环2按月循环")
    private Integer cycleMode;

    @Schema(description = "紧急程度(共享机场) 1紧急0不紧急")
    private Integer urgencyType;

    @Schema(description = "申请原因")
    private String applyReason;

    @Schema(description = "开始时间(循环任务)")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonDeserialize(using = StringLocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    @Schema(description = "结束时间(循环任务)")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonDeserialize(using = StringLocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @Schema(description = "执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonDeserialize(using = StringLocalDateTimeDeserializer.class)
    private LocalDateTime executeTime;

    @Schema(description = "执行时间列表(循环任务)")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonDeserialize(using = StringLocalDateTimeListDeserializer.class)
    private List<LocalDateTime> executeTimeList;

    @Schema(description = "是否自动断点续飞 0否 1是")
    @NotNull(message = "是否自动断点续飞不能为空")
    private Integer autoBreakPoint;

    @Schema(description = "航线任务精度 0:GPS 1高精度RTK")
    @NotNull(message = "航线任务精度不能为空")
    private Integer waylinePrecisionType;

    @Schema(description = "开启模拟飞行")
    @NotNull(message = "是否开启模拟飞行不能为空")
    private Integer enableSimulateMission;

}