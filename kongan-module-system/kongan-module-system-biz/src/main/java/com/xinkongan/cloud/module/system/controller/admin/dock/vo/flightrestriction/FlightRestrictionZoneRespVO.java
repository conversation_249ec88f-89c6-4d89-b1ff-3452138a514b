package com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 限飞区响应VO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
@Schema(description = "管理后台 - 限飞区响应VO")
public class FlightRestrictionZoneRespVO {

    @Schema(description = "限飞区ID", example = "1")
    private Long id;

    @Schema(description = "限飞区名称", example = "测试限飞区")
    private String name;

    @Schema(description = "几何形状类型：1-多边体，2-圆柱体", example = "1")
    private Integer shapeType;

    @Schema(description = "几何形状类型名称", example = "多边体")
    private String shapeTypeName;

    @Schema(description = "区域类型：1-限高区，2-限低区，3-自定义禁飞区域", example = "1")
    private Integer zoneType;

    @Schema(description = "区域类型名称", example = "限高区")
    private String zoneTypeName;

    @Schema(description = "最小高度(米)", example = "0")
    private BigDecimal minHeight;

    @Schema(description = "最大高度(米)", example = "120")
    private BigDecimal maxHeight;

    @Schema(description = "多边形数据(GeoJSON格式)")
    private String polygonData;

    @Schema(description = "圆心经度", example = "116.123456")
    private BigDecimal centerLongitude;

    @Schema(description = "圆心纬度", example = "39.123456")
    private BigDecimal centerLatitude;

    @Schema(description = "半径(米)", example = "1000")
    private BigDecimal radius;

    @Schema(description = "面积(平方米)", example = "1000000")
    private BigDecimal area;

    @Schema(description = "启用状态：true-启用，false-禁用", example = "true")
    private Boolean status;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    @Schema(description = "创建者", example = "admin")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者", example = "admin")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "修改人名称", example = "张三")
    private String updateUserName;
}
