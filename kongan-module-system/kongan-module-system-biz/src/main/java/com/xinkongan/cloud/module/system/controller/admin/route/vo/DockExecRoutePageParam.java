package com.xinkongan.cloud.module.system.controller.admin.route.vo;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
@Data
public class DockExecRoutePageParam extends PageParam {

    @Schema(description = "机场sn")
    @NotEmpty(message = "机场sn不能为空")
    private String dockSn;

}
