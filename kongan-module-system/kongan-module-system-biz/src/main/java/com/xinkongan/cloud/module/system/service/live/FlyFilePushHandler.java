package com.xinkongan.cloud.module.system.service.live;

import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.common.constant.BusinessTopicConstant;
import com.xinkongan.cloud.framework.mq.core.consume.BaseRocketMQListener;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.FlyFileDTO;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Service;

/**
 * @Description 飞行产生的图片推送
 * <AUTHOR>
 * @Date 2025/4/17 19:12
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = BusinessTopicConstant.FLY_FILE_PUSH,
        consumerGroup = "fly-file-push-consumer-group",
        messageModel = MessageModel.CLUSTERING)
public class FlyFilePushHandler extends BaseRocketMQListener<FlyFileDTO> {

    @Resource
    private WebSocketSendApi webSocketSendApi;

    @Override
    public void handlerMessage(FlyFileDTO flyFileDTO) {
        sendWebsocket(BizCodeEnum.FLY_RECORD_FILE_GENERATED, flyFileDTO);
    }

    private <T> void sendWebsocket(BizCodeEnum bizCodeEnum, T data) {
        try {
            // 发送直播开启成功消息给前端
            WebSocketMessageDTO<T> webSocketMessageDTO = WebSocketMessageDTO.<T>builder().tenantId(TenantContextHolder.getTenantId()).message(CustomWebSocketMessage.<T>builder().bizCode(bizCodeEnum.getCode()).data(data).build()).build();
            log.info("发送飞行记录文件消息 webSocketMessageDTO:{}", JSONUtil.toJsonStr(webSocketMessageDTO));
            webSocketSendApi.sendByTenant(webSocketMessageDTO);

        } catch (Exception e) {
            log.error("发送直播推流变更消息给前端失败", e);
        }
    }
}