package com.xinkongan.cloud.module.system.controller.admin.report;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.AuthDataReportVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.TaskTopStatisticVO;
import com.xinkongan.cloud.module.system.dto.TaskTopReportDTO;
import com.xinkongan.cloud.module.system.service.report.IReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Validated
@RestController
@Tag(name = "数据报表-组织人员")
@RequestMapping("/system/report")
public class BaseReportController {

    @Resource
    private IReportService reportService;


    @GetMapping(value = "/auth")
    @Operation(summary = "组织权限部分基础的数据报表")
    @PreAuthorize("@ss.hasPermission('system:report:query')")
    public CommonResult<AuthDataReportVO> authReport() {
        AuthDataReportVO basicAuthReport = reportService.getBasicAuthReport();
        return CommonResult.success(basicAuthReport);
    }

    @PostMapping(value = "/task/top")
    @Operation(summary = "组织权限部分任务排行报表")
    @PreAuthorize("@ss.hasPermission('system:report:query')")
    public CommonResult<List<TaskTopStatisticVO>> taskTopReport(@RequestBody TaskTopReportDTO taskTopReportDTO) {
        List<TaskTopStatisticVO> taskTopStatisticReport = reportService.getTaskTopStatisticReport(taskTopReportDTO);
        return CommonResult.success(taskTopStatisticReport);
    }
}
