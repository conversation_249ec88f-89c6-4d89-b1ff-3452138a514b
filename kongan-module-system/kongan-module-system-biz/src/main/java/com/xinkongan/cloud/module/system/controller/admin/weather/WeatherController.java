package com.xinkongan.cloud.module.system.controller.admin.weather;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.weather.vo.DailyWeatherVO;
import com.xinkongan.cloud.module.system.dto.DailyWeatherParamDTO;
import com.xinkongan.cloud.module.system.service.weather.IWeatherService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@Tag(name = "飞控系统 - 天气查询")
@RequestMapping("/system/weather")
public class WeatherController {

    @Resource
    private IWeatherService weatherService;


    @GetMapping(value = "/get")
    @Operation(summary = "查询天气信息")
    public CommonResult<DailyWeatherVO> getDailyWeatherInfo(DailyWeatherParamDTO dailyWeatherParamInfo) {
        DailyWeatherVO dailyWeatherInfo = weatherService.getDailyWeatherInfo(dailyWeatherParamInfo);
        return CommonResult.success(dailyWeatherInfo);
    }
}
