package com.xinkongan.cloud.module.system.controller.admin.route.dto;


import com.xinkongan.cloud.module.system.api.route.vo.AreaInfoCreateVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RouteAlgorithmSaveDTO implements Serializable  {

    @Schema(description = "航线ID")
    private Long routeId;

    @Schema(description = "航点ID")
    private Long pointId;

    @Schema(description = "算法ID")
    private Long algorithmId;

    @Schema(description = "实例ID")
    private Long exampleId;

    @Schema(description = "航点动作类型: 0:关, 1:开")
    private Integer type;

    @Schema(description = "算法动作顺序")
    private Integer number;

    @Schema(description = "开启算法的航点序号")
    private Integer startIndex;

    @Schema(description = "关闭算法的航点序号")
    private Integer endIndex;

    @Schema(description = "算法批次号id")
    private Long batchId;

    @Schema(description = "算法关联的地图标注ids")
    private List<Long> mapLabelIds;

    @Schema(description = "算法关联的地图标记信息")
    private List<AreaInfoCreateVO> areaInfo;

    public List<Long> getMapLabelIds() {
        if (mapLabelIds == null) {
            return List.of();
        }
        return mapLabelIds;
    }

    public List<AreaInfoCreateVO> getAreaInfo() {
        if (areaInfo == null) {
            return List.of();
        }
        return areaInfo;
    }
}
