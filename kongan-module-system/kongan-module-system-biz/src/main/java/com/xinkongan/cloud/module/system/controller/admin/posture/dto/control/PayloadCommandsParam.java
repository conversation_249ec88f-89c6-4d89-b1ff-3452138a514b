package com.xinkongan.cloud.module.system.controller.admin.posture.dto.control;

import com.xinkongan.cloud.module.system.enums.PayloadCommandsEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.enums.CameraTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.4
 * @date 2023/3/2
 */
@Data
public class PayloadCommandsParam {

    @Schema(description = "设备sn")
    private String dockSn;

    @NotNull
    @Schema(description = "命令")
    private PayloadCommandsEnum cmd;

    @Valid
    @NotNull
    @Schema(description = "命令参数")
    private DronePayloadParam data;

    public Boolean valid() {
        if (Objects.isNull(cmd)) {
            return false;
        }
        return switch (cmd) {
            case CAMERA_MODE_SWitCH -> validCameraModeSwitch();
            case CAMERA_AIM -> validCameraAim();
            case CAMERA_SCREEN_DRAG -> validCameraScreenDrag();
            case CAMERA_FOCAL_LENGTH_SET -> validCameraFocalLengthSet();
            case GIMBAL_RESET -> validGimbalReset();
            case CAMERA_FRAME_ZOOM -> validCameraFrameZoom();
            default -> Boolean.TRUE;
        };
    }

    private Boolean validCameraModeSwitch() {
        return Objects.nonNull(data.getCameraMode());
    }

    private Boolean validCameraAim() {
        return Objects.nonNull(data.getX()) && Objects.nonNull(data.getY())
                && Objects.nonNull(data.getLocked()) && Objects.nonNull(data.getCameraType());
    }

    private Boolean validCameraScreenDrag() {
        return Objects.nonNull(data.getLocked()) && Objects.nonNull(data.getPitchSpeed())
                && Objects.nonNull(data.getYawSpeed());
    }

    private Boolean validCameraFocalLengthSet() {
        return Objects.nonNull(data.getCameraType()) && Objects.nonNull(data.getZoomFactor())
                && (CameraTypeEnum.ZOOM == data.getCameraType()
                || CameraTypeEnum.IR == data.getCameraType());
    }

    private Boolean validGimbalReset() {
        return Objects.nonNull(data.getResetMode());
    }

    private Boolean validCameraFrameZoom() {
        return Objects.nonNull(data.getX()) && Objects.nonNull(data.getY())
                && Objects.nonNull(data.getLocked()) && Objects.nonNull(data.getCameraType())
                && Objects.nonNull(data.getWidth()) && Objects.nonNull(data.getHeight());
    }
}
