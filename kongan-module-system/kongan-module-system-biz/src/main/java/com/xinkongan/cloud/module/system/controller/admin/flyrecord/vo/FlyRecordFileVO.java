package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
public class FlyRecordFileVO extends FlyRecordFileDO {

    @Schema(description = "是否为数据权限内")
    private Boolean isDataScope;

}
