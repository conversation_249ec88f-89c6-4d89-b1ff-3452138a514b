package com.xinkongan.cloud.module.system.controller.admin.material.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class MaterialViewShareVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "素材id")
    private Long materialId;

    @Schema(description = "分享链接")
    private String linkUrl;

    @Schema(description = "分享类型 0 公开分享 1 加密分享")
    private Integer shareType;

    @Schema(description = "分享密码")
    private String password;

    @Schema(description = "有效时间：单位（小时）,expireIn=0 永久有效,否则为有效时间")
    private Integer expireIn;

    @Schema(description = "创建时间")
    private Date createTime;
}
