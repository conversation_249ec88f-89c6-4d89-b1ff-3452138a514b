package com.xinkongan.cloud.module.system.controller.admin.material.vo;

import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import com.xinkongan.cloud.module.system.enums.material.MaterialTypeEnums;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MaterialLoadVO {

    @Schema(description = "素材名称")
    private String name;

    /**
     * {@link MaterialTypeEnums}
     */
    @Schema(description = "素材类型:(2:二维素材，3:三维素材，4:全景素材)")
    private Integer type;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "清晰度")
    private Integer clarityLevel;

    @Schema(description = "素材文件信息")
    @NotNull(message = "素材文件信息不能为空")
    private FileSaveInfoDTO materialFileInfo;

}
