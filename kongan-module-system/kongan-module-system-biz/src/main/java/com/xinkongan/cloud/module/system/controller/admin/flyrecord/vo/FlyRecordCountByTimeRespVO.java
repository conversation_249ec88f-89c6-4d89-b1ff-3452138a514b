package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description 根据时间统计飞行记录
 * <AUTHOR>
 * @Date 2025/2/24 10:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "根据时间统计飞行记录")
public class FlyRecordCountByTimeRespVO {

    /**
     * 日期
     **/
    @Schema(description = "日期")
    private LocalDateTime date;

    /**
     * 巡检任务飞行记录总数
     **/
    @Schema(description = "巡检任务飞行记录总数")
    private Integer inspectionCount;

    /**
     * 接警任务飞行记录总数
     **/
    @Schema(description = "接警任务飞行记录总数")
    private Integer alarmCount;

    /**
     * 建模任务飞行记录总数
     **/
    @Schema(description = "建模任务飞行记录总数")
    private Integer modelingCount;
}