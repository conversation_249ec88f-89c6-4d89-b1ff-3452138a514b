package com.xinkongan.cloud.module.system.controller.admin.label.dto;

import com.xinkongan.cloud.framework.common.dto.SearchDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LabelReferSearchDTO extends SearchDTO {

    @Schema(description = "标注id")
    private Long labelId;

    @Schema(description = "分享标志（1：分享，0：不是分享，2：全部）", example = "1")
    private Integer shareFlag;

}
