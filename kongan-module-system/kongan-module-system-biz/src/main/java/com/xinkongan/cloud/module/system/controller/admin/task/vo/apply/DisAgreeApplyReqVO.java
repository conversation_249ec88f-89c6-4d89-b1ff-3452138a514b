package com.xinkongan.cloud.module.system.controller.admin.task.vo.apply;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description 不同意审批请求参数
 * <AUTHOR>
 * @Date 2024/12/26 14:05
 */
@Schema(description = "不同意审批请求参数")
@Data
public class DisAgreeApplyReqVO {

    @Schema(description = "申请id")
    @NotNull(message = "申请id不能为空")
    private Long applyId;

    @Schema(description = "审批意见")
    private String approveComments;
}