package com.xinkongan.cloud.module.system.controller.admin.measure.vo;

import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 巡检监控图片列表
 */
@Data
@Schema(description = "巡检监控图片列表")
public class FileAddRespVO {


    @Schema(description = "图片信息")
    private FlyRecordFileDO flyRecordFileDO;


}
