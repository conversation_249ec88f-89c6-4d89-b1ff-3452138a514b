package com.xinkongan.cloud.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 用戶id名称vo
 * <AUTHOR>
 * @Date 2025/4/10 18:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "用户id名称vo")
public class UserIdNicknameVO {

    @Schema(description = "用户id")
    private Long id;

    @Schema(description = "用户名称")
    private String nickname;
}