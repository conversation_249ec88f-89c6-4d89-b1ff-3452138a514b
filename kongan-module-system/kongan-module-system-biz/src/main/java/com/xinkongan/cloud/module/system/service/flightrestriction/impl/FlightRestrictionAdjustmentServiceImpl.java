package com.xinkongan.cloud.module.system.service.flightrestriction.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction.FlightRestrictionZoneRespVO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.WayPointDTO;
import com.xinkongan.cloud.module.system.dto.flightrestriction.FlightSegmentDTO;
import com.xinkongan.cloud.module.system.dto.flightrestriction.RestrictionZoneConflictDTO;
import com.xinkongan.cloud.module.system.enums.FlightRestrictionShapeTypeEnum;
import com.xinkongan.cloud.module.system.enums.FlightRestrictionZoneTypeEnum;
import com.xinkongan.cloud.module.system.service.dock.flightrestriction.ISystemFlightRestrictionZoneService;
import com.xinkongan.cloud.module.system.service.flightrestriction.IFlightRestrictionAdjustmentService;
import com.xinkongan.cloud.sdk.geo.wgs84.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 限飞区高度调整服务实现
 *
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Service
public class FlightRestrictionAdjustmentServiceImpl implements IFlightRestrictionAdjustmentService {

    @Resource
    private ISystemFlightRestrictionZoneService flightRestrictionZoneService;

    @Override
    public List<WayPointDTO> adjustWaypointHeights(List<WayPointDTO> wayPoints) {
        log.info("开始调整航点高度以符合限飞区要求，航点数量: {}", wayPoints.size());
        try {
            if (CollUtil.isEmpty(wayPoints)) {
                return wayPoints;
            }
            // 获取启用的限飞区
            List<FlightRestrictionZoneRespVO> enabledZones = flightRestrictionZoneService.getEnabledFlightRestrictionZones();
            if (CollUtil.isEmpty(enabledZones)) {
                log.info("当前租户没有启用的限飞区，无需调整");
                return wayPoints;
            }

            // 生成航段
            List<FlightSegmentDTO> segments = generateFlightSegments(wayPoints);

            // 计算高度调整
            Map<Integer, BigDecimal> heightAdjustments = calculateHeightAdjustments(segments, enabledZones);

            // 构建调整后的航点列表
            return applyHeightAdjustments(wayPoints, heightAdjustments);


        } catch (Exception e) {
            log.error("调整航点高度时发生异常 wayPoints:{}", JSONUtil.toJsonStr(wayPoints), e);
            return wayPoints;
        }
    }

    @Override
    public boolean hasFlightRestrictionConflicts(List<WayPointDTO> wayPoints) {
        try {
            if (CollUtil.isEmpty(wayPoints)) {
                return false;
            }
            List<FlightRestrictionZoneRespVO> enabledZones = flightRestrictionZoneService.getEnabledFlightRestrictionZones();
            if (CollUtil.isEmpty(enabledZones)) {
                return false;
            }

            List<FlightSegmentDTO> segments = generateFlightSegments(wayPoints);
            List<RestrictionZoneConflictDTO> conflicts = detectConflicts(segments, enabledZones);

            return conflicts.stream().anyMatch(RestrictionZoneConflictDTO::getHasConflict);
        } catch (Exception e) {
            log.error("检测限飞区冲突时发生异常", e);
            return false;
        }
    }

    /**
     * 生成航段列表
     */
    private List<FlightSegmentDTO> generateFlightSegments(List<WayPointDTO> wayPoints) {
        List<FlightSegmentDTO> segments = new ArrayList<>();

        for (int i = 0; i < wayPoints.size() - 1; i++) {
            WayPointDTO start = wayPoints.get(i);
            WayPointDTO end = wayPoints.get(i + 1);

            FlightSegmentDTO segment = FlightSegmentDTO.builder()
                    .startLongitude(BigDecimal.valueOf(start.getLongitude()))
                    .startLatitude(BigDecimal.valueOf(start.getLatitude()))
                    .startHeight(BigDecimal.valueOf(start.getExecuteHeight()))
                    .endLongitude(BigDecimal.valueOf(end.getLongitude()))
                    .endLatitude(BigDecimal.valueOf(end.getLatitude()))
                    .endHeight(BigDecimal.valueOf(end.getExecuteHeight()))
                    .startPointIndex(start.getPointIndex())
                    .endPointIndex(end.getPointIndex())
                    .build();

            segments.add(segment);
        }

        return segments;
    }

    /**
     * 检测冲突
     */
    private List<RestrictionZoneConflictDTO> detectConflicts(List<FlightSegmentDTO> segments,
                                                             List<FlightRestrictionZoneRespVO> zones) {
        List<RestrictionZoneConflictDTO> conflicts = new ArrayList<>();

        for (FlightSegmentDTO segment : segments) {
            // 获取与该航段相交的限飞区
            List<FlightRestrictionZoneRespVO> intersectingZones = getIntersectingZones(segment, zones);

            if (CollUtil.isEmpty(intersectingZones)) {
                continue;
            }

            // 检查是否存在冲突（同时有限高和限低要求且无法满足）
            RestrictionZoneConflictDTO conflict = checkSegmentConflict(segment, intersectingZones);
            if (conflict.getHasConflict()) {
                conflicts.add(conflict);
            }
        }

        return conflicts;
    }

    /**
     * 获取与航段相交的限飞区
     */
    private List<FlightRestrictionZoneRespVO> getIntersectingZones(FlightSegmentDTO segment,
                                                                   List<FlightRestrictionZoneRespVO> zones) {
        return zones.stream()
                .filter(zone -> isSegmentIntersectZone(segment, zone))
                .collect(Collectors.toList());
    }

    /**
     * 判断航段是否与限飞区相交（3D空间判断）
     */
    private boolean isSegmentIntersectZone(FlightSegmentDTO segment, FlightRestrictionZoneRespVO zone) {
        try {
            double startLon = segment.getStartLongitude().doubleValue();
            double startLat = segment.getStartLatitude().doubleValue();
            double startHeight = segment.getStartHeight().doubleValue();
            double endLon = segment.getEndLongitude().doubleValue();
            double endLat = segment.getEndLatitude().doubleValue();
            double endHeight = segment.getEndHeight().doubleValue();
            double minHeight = zone.getMinHeight().doubleValue();
            double maxHeight = zone.getMaxHeight().doubleValue();

            log.debug("判断航段与限飞区相交: 航段[({},{},{}) -> ({},{},{})] vs 限飞区[ID:{}, 类型:{}, 高度:{}-{}]",
                    startLon, startLat, startHeight, endLon, endLat, endHeight,
                    zone.getId(), zone.getShapeType(), minHeight, maxHeight);

            if (FlightRestrictionShapeTypeEnum.POLYGON.getType().equals(zone.getShapeType())) {
                // 多边形限飞区 - 使用3D判断
                List<PointWGS84> polygonPoints = parsePolygonFromGeoJSON(zone.getPolygonData(), minHeight);
                if (polygonPoints == null || polygonPoints.isEmpty()) {
                    log.warn("无法解析多边形限飞区[ID:{}]的坐标数据", zone.getId());
                    return false;
                }

                // 创建多边形棱柱体
                double prismHeight = maxHeight - minHeight;
                PolygonalPrism prism = new PolygonalPrism(polygonPoints, prismHeight);

                // 创建线段
                PointWGS84 startPoint = new PointWGS84(startLat, startLon, startHeight);
                PointWGS84 endPoint = new PointWGS84(endLat, endLon, endHeight);
                LineSegmentWGS84 lineSegment = new LineSegmentWGS84(startPoint, endPoint);

                // 判断相交
                boolean intersects = IntersectionUtils.intersects(lineSegment, prism);
                log.info("航段与多边形限飞区[ID:{}]相交判断结果: {}", zone.getId(), intersects);
                return intersects;

            } else if (FlightRestrictionShapeTypeEnum.CYLINDER.getType().equals(zone.getShapeType())) {
                // 圆形限飞区 - 使用3D判断
                if (zone.getCenterLongitude() == null || zone.getCenterLatitude() == null || zone.getRadius() == null) {
                    log.warn("圆形限飞区[ID:{}]缺少必要的几何参数", zone.getId());
                    return false;
                }

                // 创建圆柱体中心点（使用最小高度作为底面高度）
                PointWGS84 centerPoint = new PointWGS84(
                        zone.getCenterLatitude().doubleValue(),
                        zone.getCenterLongitude().doubleValue(),
                        minHeight
                );

                // 创建圆柱体
                double cylinderHeight = maxHeight - minHeight;
                double radius = zone.getRadius().doubleValue();
                CircularCylinder cylinder = new CircularCylinder(centerPoint, radius, cylinderHeight);

                // 创建线段
                PointWGS84 startPoint = new PointWGS84(startLat, startLon, startHeight);
                PointWGS84 endPoint = new PointWGS84(endLat, endLon, endHeight);
                LineSegmentWGS84 lineSegment = new LineSegmentWGS84(startPoint, endPoint);

                // 判断相交
                boolean intersects = IntersectionUtils.intersects(lineSegment, cylinder);
                log.info("航段与圆形限飞区[ID:{}]相交判断结果: {}", zone.getId(), intersects);
                return intersects;

            } else {
                log.warn("未知的限飞区几何形状类型: {}", zone.getShapeType());
                return false;
            }

        } catch (Exception e) {
            log.error("判断航段与限飞区3D相交时发生异常: segment={}, zone={}", segment, zone.getId(), e);
            return false;
        }
    }

    /**
     * 检查航段冲突
     */
    private RestrictionZoneConflictDTO checkSegmentConflict(FlightSegmentDTO segment,
                                                            List<FlightRestrictionZoneRespVO> intersectingZones) {
        BigDecimal maxHeightLimit = null;  // 最高的限高
        BigDecimal minHeightLimit = null;  // 最低的限低
        List<Long> conflictZoneIds = new ArrayList<>();

        // 把最高的限高和最低的限低找出来
        for (FlightRestrictionZoneRespVO zone : intersectingZones) {
            conflictZoneIds.add(zone.getId());

            switch (FlightRestrictionZoneTypeEnum.getByType(zone.getZoneType())) {
                case LOW_LIMIT -> {
                    // 限低区
                    if (minHeightLimit == null || zone.getMinHeight().compareTo(minHeightLimit) > 0) {
                        minHeightLimit = zone.getMinHeight();
                    }
                }
                case HEIGHT_LIMIT -> {
                    // 限高区
                    if (maxHeightLimit == null || zone.getMaxHeight().compareTo(maxHeightLimit) < 0) {
                        maxHeightLimit = zone.getMaxHeight();
                    }
                }
                case CUSTOM_RESTRICTION -> {
                    // 自定义限飞区(限高也限低)
                    if (maxHeightLimit == null || zone.getMaxHeight().compareTo(maxHeightLimit) < 0) {
                        maxHeightLimit = zone.getMaxHeight();
                    }
                    if (minHeightLimit == null || zone.getMinHeight().compareTo(minHeightLimit) > 0) {
                        minHeightLimit = zone.getMinHeight();
                    }
                }
            }
        }

        // 检查是否存在冲突
        boolean hasConflict = false;
        String conflictDescription = "";

        if (maxHeightLimit != null && minHeightLimit != null) {
            // 同时存在限高和限低要求 而且 限低比限高还高 则 存在冲突
            if (minHeightLimit.compareTo(maxHeightLimit) > 0) {
                hasConflict = true;
                conflictDescription = String.format("限飞区冲突：要求最低高度%.2fm，但最高高度限制为%.2fm",
                        minHeightLimit.doubleValue(), maxHeightLimit.doubleValue());
            }
        }

        return RestrictionZoneConflictDTO.builder()
                .hasConflict(hasConflict)
                .conflictSegment(segment)
                .conflictZoneIds(conflictZoneIds)
                .conflictDescription(conflictDescription)
                .maxRequiredHeight(maxHeightLimit)
                .minRequiredHeight(minHeightLimit)
                .build();
    }

    /**
     * 计算高度调整
     */
    private Map<Integer, BigDecimal> calculateHeightAdjustments(List<FlightSegmentDTO> segments,
                                                                List<FlightRestrictionZoneRespVO> zones) {
        Map<Integer, BigDecimal> adjustments = new HashMap<>();

        for (FlightSegmentDTO segment : segments) {
            List<FlightRestrictionZoneRespVO> intersectingZones = getIntersectingZones(segment, zones);

            if (CollUtil.isEmpty(intersectingZones)) {
                continue;
            }

            // 判断是否冲突
            RestrictionZoneConflictDTO conflict = checkSegmentConflict(segment, intersectingZones);

            if (conflict.getHasConflict()) {
                // 存在冲突本航段不调整
                continue;
            }

            // 计算该航段需要的高度调整
            BigDecimal requiredHeight = calculateRequiredHeight(segment, intersectingZones);

            if (requiredHeight != null) {
                // 调整起点和终点高度
                adjustments.put(segment.getStartPointIndex(), requiredHeight);
                adjustments.put(segment.getEndPointIndex(), requiredHeight);
            }
        }

        return adjustments;
    }

    /**
     * 计算航段所需高度
     */
    private BigDecimal calculateRequiredHeight(FlightSegmentDTO segment,
                                               List<FlightRestrictionZoneRespVO> intersectingZones) {
        BigDecimal maxHeightLimit = null;  // 最高的限高
        BigDecimal minHeightLimit = null;  // 最低的限低

        for (FlightRestrictionZoneRespVO zone : intersectingZones) {

            switch (FlightRestrictionZoneTypeEnum.getByType(zone.getZoneType())) {
                case LOW_LIMIT -> {
                    // 限低区
                    if (minHeightLimit == null || zone.getMinHeight().compareTo(minHeightLimit) > 0) {
                        minHeightLimit = zone.getMinHeight();
                    }
                }
                case HEIGHT_LIMIT -> {
                    // 限高区
                    if (maxHeightLimit == null || zone.getMaxHeight().compareTo(maxHeightLimit) < 0) {
                        maxHeightLimit = zone.getMaxHeight();
                    }
                }
                case CUSTOM_RESTRICTION -> {
                    // 限低区
                    if (minHeightLimit == null || zone.getMinHeight().compareTo(minHeightLimit) > 0) {
                        minHeightLimit = zone.getMinHeight();
                    }
                    // 限高区
                    if (maxHeightLimit == null || zone.getMaxHeight().compareTo(maxHeightLimit) < 0) {
                        maxHeightLimit = zone.getMaxHeight();
                    }
                }
            }
        }

        // 确定最终高度
        BigDecimal currentStartHeight = segment.getStartHeight();
        BigDecimal currentEndHeight = segment.getEndHeight();
        BigDecimal currentMaxHeight = currentStartHeight.max(currentEndHeight);
        BigDecimal currentMinHeight = currentStartHeight.min(currentEndHeight);

        BigDecimal targetHeight = null;

        if (maxHeightLimit != null && currentMaxHeight.compareTo(maxHeightLimit) > 0) {
            // 当前高度超过限高，需要降低
            targetHeight = maxHeightLimit;
        }

        if (minHeightLimit != null && currentMinHeight.compareTo(minHeightLimit) < 0) {
            // 当前高度低于限低，需要提高
            if (targetHeight == null) {
                targetHeight = minHeightLimit;
            }
        }

        return targetHeight;
    }

    /**
     * 应用高度调整到航点
     */
    private List<WayPointDTO> applyHeightAdjustments(List<WayPointDTO> wayPoints, Map<Integer, BigDecimal> heightAdjustments) {
        List<WayPointDTO> adjustments = new ArrayList<>();
        for (WayPointDTO wayPoint : wayPoints) {
            // 深拷贝
            WayPointDTO wayPointCopy = JSONUtil.toBean(JSONUtil.toJsonStr(wayPoint), WayPointDTO.class);
            // 修改高度
            BigDecimal adjustedHeight = heightAdjustments.get(wayPointCopy.getPointIndex());
            if (adjustedHeight != null) {
                Float originalHeight = wayPointCopy.getExecuteHeight();
                wayPointCopy.setExecuteHeight(adjustedHeight.floatValue());
                log.info("航点{}高度调整: {}m -> {}m", wayPointCopy.getPointIndex(), originalHeight, adjustedHeight);
            }
            adjustments.add(wayPointCopy);
        }
        return adjustments;
    }

    /**
     * 从GeoJSON数据解析多边形坐标点
     *
     * @param geoJsonData GeoJSON格式的多边形数据
     * @param baseHeight  底面高度
     * @return PointWGS84列表，解析失败返回null
     */
    private List<PointWGS84> parsePolygonFromGeoJSON(String geoJsonData, double baseHeight) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(geoJsonData);

            // 检查是否为POLYGON类型
            if (!rootNode.has("type") || !"POLYGON".equalsIgnoreCase(rootNode.get("type").asText())) {
                log.warn("GeoJSON数据不是POLYGON类型");
                return null;
            }

            // 获取坐标数组
            JsonNode coordinatesNode = rootNode.get("coordinates");
            if (coordinatesNode == null || !coordinatesNode.isArray()) {
                log.warn("GeoJSON数据缺少coordinates字段或格式错误");
                return null;
            }

            // 解析外环坐标（第一个数组）
            JsonNode outerRingNode = coordinatesNode.get(0);
            if (outerRingNode == null || !outerRingNode.isArray()) {
                log.warn("GeoJSON数据外环坐标格式错误");
                return null;
            }

            // 构建PointWGS84列表
            List<PointWGS84> points = new ArrayList<>();
            for (JsonNode coordNode : outerRingNode) {
                if (coordNode.isArray() && coordNode.size() >= 2) {
                    double lon = coordNode.get(0).asDouble();
                    double lat = coordNode.get(1).asDouble();
                    points.add(new PointWGS84(lat, lon, baseHeight));
                }
            }

            if (points.size() < 3) { // 多边形至少需要3个点
                log.warn("多边形坐标点数量不足");
                return null;
            }

            log.debug("成功解析多边形坐标点，数量: {}", points.size());
            return points;

        } catch (Exception e) {
            log.error("解析GeoJSON多边形数据时发生异常: {}", geoJsonData, e);
            return null;
        }
    }
}
