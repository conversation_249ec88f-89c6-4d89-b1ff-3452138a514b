package com.xinkongan.cloud.module.system.service.route;

import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteFolderDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteFolderRespVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointRespVO;

import java.util.List;

public interface IWayPointService {


    /**
     * 根据航线id和航点序号查询航点信息
     *
     * @param routeId 航线id
     * @param index   航点序号
     * @return 航点信息
     */
    WayPointRespVO getWayPointInfoByRouteIdAndIndex(Long routeId, Integer index);


    /**
     * 根据航线id删除航点信息
     *
     * @param routeId 航线id
     */
    void deletePointByRouteId(Long routeId);


    /**
     * 保存航线信息
     *
     * @param routeId   航线id
     * @param folders 航线列表
     */
    void saveRouteWayPointInfo(Long routeId, List<RouteFolderDTO> folders);


    /**
     * 根据航线id查询航点信息
     *
     * @param routeId 航线id
     * @return 航点数据
     */
    List<RouteFolderRespVO> getWayPointInfoByRouteId(Long routeId);

    /**
     * 根据航线id获取最后一个航点的序号
     *
     * @param routeId 航线id
     * @return 最后一个航点的序号
     */
    Long getLastIndexByRouteId(Long routeId);

}
