package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 飞行记录统计
 * <AUTHOR>
 * @Date 2025/2/24 9:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlyRecordCountRespVO {

    /**
     * 飞行记录总数
     */
    private Integer totalCount;

    /**
     * 巡检任务飞行记录总数
     **/
    private Integer inspectionCount;

    /**
     * 接警任务飞行记录总数
     **/
    private Integer alarmCount;

    /**
     * 建模任务飞行记录总数
     **/
    private Integer modelingCount;


}