package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteAlgorithmSaveDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.WayPointActionInfoDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteAlgorithmVO;
import com.xinkongan.cloud.module.system.dto.WayPointShoutDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 接警配置RespVO
 * <AUTHOR>
 * @Date 2025/3/19 14:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "接警配置RespVO")
public class AlarmConfigRespVO {

    @Schema(description = "警情场景id")
    private Long id;

    @Schema(description = "警情场景")
    private String alarmScene;

    @Schema(description = "是否自动执行")
    private Integer autoExecute;

    @Schema(description = "默认配置")
    private Integer defaultFlag;

    @Schema(description = "过期时间")
    private Integer expireTime;

    /**
     * 接警图片格式
     */
    @TableField(value = "image_format", typeHandler = JacksonTypeHandler.class)
    private List<String> imageFormat;

    /**
     * 算法开启距离
     */
    @TableField("algorithm_radius")
    private Integer algorithmRadius;

    /**
     * 算法开启后飞行高度
     */
    @TableField("algorithm_height")
    private Integer algorithmHeight;

    /**
     * 算法开启后飞行速度
     */
    @TableField("algorithm_speed")
    private Integer algorithmSpeed;

    /**
     * 算法开启后云台俯仰角
     */
    @TableField("algorithm_pitch_angle")
    private Integer algorithmPitchAngle;

    @Schema(description = "接警动作")
    private List<WayPointActionInfoDTO> action;

    /**
     * 航点喊话信息
     */
    @Schema(description = "航点喊话信息")
    private List<WayPointShoutDTO> wayPointShouts;


    @Schema(description = "路由算法")
    private List<AlarmConfigAlgorithmInfos> routeAlgorithmInfos;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;
}