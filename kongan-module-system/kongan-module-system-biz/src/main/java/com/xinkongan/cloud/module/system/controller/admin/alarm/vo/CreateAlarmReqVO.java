package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * @Description 新建警情ReqVO
 * <AUTHOR>
 * @Date 2025/3/18 17:16
 */
@Schema(description = "管理后台 - 新建警情ReqVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateAlarmReqVO {

    @Schema(description = "所属组织")
    private Long deptId;

    @Schema(description = "任务名称")
    @Length(min = 1, max = 50, message = "任务名称长度为1-50个字符")
    @NotBlank(message = "任务名称不能为空")
    private String name;

    @Schema(description = "接警场景Id")
    private Long alarmSceneId;

    @Schema(description = "警情描述")
    @Length(max = 200, message = "警情描述长度上限为200个字符")
    private String description;

    @Schema(description = "警情位置")
    @NotBlank(message = "警情位置不能为空")
    private String address;

    @Schema(description = "经度")
    @NotBlank(message = "经度不能为空")
    private String longitude;

    @Schema(description = "纬度")
    @NotBlank(message = "纬度不能为空")
    private String latitude;

    @Schema(description = "任务紧急程度 1紧急0普通")
    private Integer taskUrgency;
}