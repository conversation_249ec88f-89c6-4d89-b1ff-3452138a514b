package com.xinkongan.cloud.module.system.service.feedback;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.feedback.vo.FeedbackCreateReqVO;
import com.xinkongan.cloud.module.system.controller.admin.file.vo.file.FileCreateReqVO;
import com.xinkongan.cloud.module.system.convert.feedback.FeedbackConvert;
import com.xinkongan.cloud.module.system.dal.dataobject.feedback.FeedbackDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.dal.mysql.feedback.FeedBackMapper;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/05/26 10:12
 */
@Service
@Slf4j
public class FeedbackServiceImpl implements FeedbackService {

    @Resource
    private FeedBackMapper feedBackMapper;
    @Resource
    private FileService fileService;
    @Resource
    private AdminUserService userService;

    @Override
    public Long createFeedBack(FeedbackCreateReqVO reqVO) {
        Set<Long> ids = new HashSet<>();
        if (CollectionUtil.isNotEmpty(reqVO.getFileInfos())) {
            reqVO.getFileInfos().forEach(fileInfo -> {
                FileCreateReqVO fileCreateReqVO = BeanUtil.toBean(fileInfo, FileCreateReqVO.class);
                fileCreateReqVO.setDeptId(reqVO.getDeptId() != null ? reqVO.getDeptId() : SecurityFrameworkUtils.getLoginUserDeptId());
                Long fileId = fileService.createFile(fileCreateReqVO);
                ids.add(fileId);
            });
        }
        FeedbackDO feedbackDO = FeedbackConvert.INSTANCE.convert(reqVO);
        feedbackDO.setFileIds(ids);
        AdminUserDO userDO = userService.getUser(SecurityFrameworkUtils.getLoginUserId());
        if (userDO != null) {
            feedbackDO.setContactWay(userDO.getMobile());
        }
        feedbackDO.setDeptId(SecurityFrameworkUtils.getLoginUserDeptId());
        feedbackDO.setTenantId(TenantContextHolder.getTenantId());
        feedBackMapper.insert(feedbackDO);
        return feedbackDO.getId();
    }
}