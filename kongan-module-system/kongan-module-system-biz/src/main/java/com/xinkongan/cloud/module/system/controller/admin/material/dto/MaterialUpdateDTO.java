package com.xinkongan.cloud.module.system.controller.admin.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MaterialUpdateDTO {

    @NotNull
    @Schema(description = "素材id")
    private Long materialId;

    @Schema(description = "素材名称")
    private String materialName;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "素材清晰度")
    private Integer clarityLevel;
}
