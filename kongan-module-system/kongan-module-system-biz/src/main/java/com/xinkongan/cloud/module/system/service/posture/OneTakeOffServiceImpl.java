package com.xinkongan.cloud.module.system.service.posture;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.redis.enums.RedisKeyExpireEnums;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceVO;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.OneKeyTakeoffTakeoffReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyDO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.task.TaskSceneTypeEnum;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.task.IJobFlyService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.api.DockCloudApiControlService;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.request.TakeoffToPointRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.OsdDock;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DockModeCodeEnum;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/2/13
 */
@Slf4j
@Service
public class OneTakeOffServiceImpl implements IOneTakeOffService {

    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private DockCloudApiControlService dockCloudApiControlService;

    @Resource
    private IJobFlyService jobFlyService;

    @Resource
    private DockDeviceService dockDeviceService;

    @Resource
    private IJobService jobService;

    @Override
    public Boolean takeOff(OneKeyTakeoffTakeoffReqVO reqVO) {
        log.info("一键起飞-起飞请求参数：{}", JSONUtil.toJsonStr(reqVO));
        // TODO 机场权限校验
        // 创建飞行信息
        JobFlyDO jobFlyDO = JobFlyDO.builder()
                .status(0)
                .dockSn(reqVO.getDockSn())
                .name("一键起飞")
                .scene(TaskSceneTypeEnum.ONE_CLICK_TAKE_OFF.getScene())
                .execMode(0)
                .execTime(LocalDateTime.now())
                .execUserId(SecurityFrameworkUtils.getLoginUserId())
                .build();
        Long flyId = jobFlyService.createJobFly(jobFlyDO);
        // 查询机场
        DockDeviceVO dockDeviceVO = dockDeviceService.getByDockSn(reqVO.getDockSn(), jobFlyDO.getTenantId());
        // 创建任务缓存
        JobFlyInfo jobInfo = BeanUtil.copyProperties(jobFlyDO, JobFlyInfo.class);
        jobInfo.setJobId(jobFlyDO.getId());
        jobInfo.setFlyId(jobFlyDO.getId());
        // 一键起飞不属于任务 没有任务名称
        // jobInfo.setJobName(jobFlyDO.getName());
        jobInfo.setDockSn(dockDeviceVO.getDeviceSn());
        jobInfo.setDroneSn(dockDeviceVO.getChildSn());
        jobService.setExecTaskCacheByDockSn(reqVO.getDockSn(), jobInfo);
        // 飞行器检查
        if (!checkTakeoffCondition(reqVO.getDockSn())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_STATUS_DOES_NOT_SUPPORT_TAKEOFF);
        }
        log.info("一键起飞-飞行前检查完成");
        // 构建起飞参数
        TakeoffToPointRequest request = BeanUtil.toBean(reqVO, TakeoffToPointRequest.class);
        request.setFlightId(flyId.toString());
        // 起飞
        CommonTopicResponse<ServicesReplyData> response = dockCloudApiControlService.takeoffToPoint(request, reqVO.getDockSn());
        return response.getData().getResult().isSuccess();
    }

    private Boolean checkTakeoffCondition(String dockSn) {
        OsdDock osdDock = redisCacheService.get(RedisKeyExpireEnums.OSD_DOCK.getKey(dockSn), OsdDock.class);
        if (osdDock == null) {
            return false;
        }
        return osdDock.getModeCode().equals(DockModeCodeEnum.IDLE);
    }

}
