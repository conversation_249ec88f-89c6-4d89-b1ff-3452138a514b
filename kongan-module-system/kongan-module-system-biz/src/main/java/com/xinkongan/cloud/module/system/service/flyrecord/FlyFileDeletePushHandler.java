package com.xinkongan.cloud.module.system.service.flyrecord;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.common.constant.BusinessTopicConstant;
import com.xinkongan.cloud.framework.mq.core.consume.BaseRocketMQListener;
import com.xinkongan.cloud.framework.mq.core.message.RocketMQMessage;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.FlyFileDTO;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 飞行产生的图片推送
 * <AUTHOR>
 * @Date 2025/4/17 19:12
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = BusinessTopicConstant.FLY_FILE_DELETE_PUSH,
        consumerGroup = "fly-file-delete-push-consumer-group",
        messageModel = MessageModel.CLUSTERING)
public class FlyFileDeletePushHandler extends BaseRocketMQListener<List<Long>> {

    @Resource
    private WebSocketSendApi webSocketSendApi;

    @Override
    public void handlerMessage(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        ids.forEach(this::sendWebsocket);
    }

    private <T> void sendWebsocket(T data) {
        try {
            // 发送直播开启成功消息给前端
            WebSocketMessageDTO<T> webSocketMessageDTO = WebSocketMessageDTO.<T>builder()
                    .tenantId(TenantContextHolder.getTenantId()).message(CustomWebSocketMessage.<T>builder().bizCode(BizCodeEnum.FLY_RECORD_FILE_DELETE.getCode()).data(data).build()).build();
            log.info("发送飞行记录文件消息 webSocketMessageDTO:{}", JSONUtil.toJsonStr(webSocketMessageDTO));
            webSocketSendApi.sendByTenant(webSocketMessageDTO);

        } catch (Exception e) {
            log.error("发送直播推流变更消息给前端失败", e);
        }
    }
}