package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteAlgorithmSaveDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.WayPointActionInfoDTO;
import com.xinkongan.cloud.module.system.dto.WayPointShoutDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 创建接警场景ReqVO
 * <AUTHOR>
 * @Date 2025/3/19 13:45
 */
@Schema(description = "创建接警配置ReqVO")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CreateAlarmConfigReqVO {

    @Schema(description = "接警场景")
    @NotBlank(message = "接警场景不能为空")
    private String alarmScene;

    @Schema(description = "是否自动下发 1是0否")
    private Integer autoExecute;

    @Schema(description = "过期时间/h")
    private Integer expireTime;

    @Schema(description = "到达警情半径距离")
    private Integer algorithmRadius;

    @Schema(description = "算法开启后飞行高度")
    private Integer algorithmHeight;

    @Schema(description = "算法开启后飞行速度")
    private Integer algorithmSpeed;

    @Schema(description = "算法开启后云台俯仰角")
    private Integer algorithmPitchAngle;

    @Schema(description = "照片存储类型", example = "[\"wide\", \"zoom\", \"ir\"]")
    @NotEmpty(message = "照片存储类型不能为空")
    private List<String> imageFormat;

    @Schema(description = "接警动作")
    private List<WayPointActionInfoDTO> action;

    @Schema(description = "航点喊话信息")
    private List<WayPointShoutDTO> wayPointShouts;

    @Schema(description = "航点中配置的算法信息")
    private List<RouteAlgorithmSaveDTO> routeAlgorithmInfos;
}