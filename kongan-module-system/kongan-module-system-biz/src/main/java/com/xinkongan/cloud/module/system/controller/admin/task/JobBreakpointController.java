package com.xinkongan.cloud.module.system.controller.admin.task;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.breakpoint.BreakpointResumeReqVO;
import com.xinkongan.cloud.module.system.service.task.IJobBreakpointService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 任务断点管理Controller
 * <AUTHOR>
 * @Date 2025/01/20
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 任务断点管理")
@RequestMapping("/system/job/breakpoint")
public class JobBreakpointController {

    @Resource
    private IJobBreakpointService breakpointService;


    /**
     * 手动断点续飞
     */
    @PostMapping("/resume")
    @Operation(summary = "断点续飞", description = "手动断点续飞")
//    @PreAuthorize("@ss.hasPermission('system:job:breakpoint:resume')")
    public CommonResult<Long> resumeFromBreakpoint(@Valid @RequestBody BreakpointResumeReqVO reqVO) {
        Long result = breakpointService.resumeFromBreakpoint(reqVO);
        return success(result);
    }

}
