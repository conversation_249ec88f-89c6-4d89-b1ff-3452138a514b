package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobSceneNumberVO {

    @Schema(description = "巡检")
    private Integer inspection;

    @Schema(description = "建模")
    private Integer modeling;

    @Schema(description = "接警")
    private Integer alarmResponse;

}
