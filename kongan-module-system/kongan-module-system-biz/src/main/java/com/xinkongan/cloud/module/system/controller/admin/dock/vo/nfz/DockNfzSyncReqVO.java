package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 机场禁飞区同步请求VO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
@Schema(description = "管理后台 - 机场禁飞区同步请求VO")
public class DockNfzSyncReqVO {

    @Schema(description = "机场SN", requiredMode = Schema.RequiredMode.REQUIRED, example = "DOCK001")
    @NotBlank(message = "机场SN不能为空")
    private String dockSn;

}
