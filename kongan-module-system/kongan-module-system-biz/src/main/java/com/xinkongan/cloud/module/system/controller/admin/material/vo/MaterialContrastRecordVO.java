package com.xinkongan.cloud.module.system.controller.admin.material.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class MaterialContrastRecordVO {

    @Schema(description = "素材对比主键id")
    private Long id;

    @Schema(description = "原始素材主键id")
    private Long orgId;

    @Schema(description = "原始素材名称")
    private String orgMaterialName;

    @Schema(description = "对比素材主键id")
    private Long contrastId;

    @Schema(description = "对比素材名称")
    private String contrastMaterialName;

    @Schema(description = "对比名称")
    private String contrastName;

    @Schema(description = "缩略图地址")
    private String thumbnail;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2024-12-31 14:50:00")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2024-12-31 14:50:00")
    private Date updateTime;

    @Schema(description = "组织名称")
    private String deptName;

    @Schema(description = "组织id")
    private Long deptId;
}
