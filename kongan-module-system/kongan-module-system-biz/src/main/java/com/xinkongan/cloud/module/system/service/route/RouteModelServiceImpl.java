package com.xinkongan.cloud.module.system.service.route;

import cn.hutool.core.bean.BeanUtil;
import com.xinkongan.cloud.framework.kmz.dto.Overlap;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.ModelRouteDTO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteModelDO;
import com.xinkongan.cloud.module.system.dal.mysql.route.RouteModelMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/6/20 11:45
 */
@Service
public class RouteModelServiceImpl implements IRouteModelService{

    @Resource
    private RouteModelMapper routeModelMapper;

    @Override
    public void saveRouteModelInfo(ModelRouteDTO modelRouteDTO) {
        RouteModelDO routeModelDO = BeanUtil.copyProperties(modelRouteDTO, RouteModelDO.class);
        routeModelMapper.insert(routeModelDO);
    }

    @Override
    public ModelRouteDTO getRouteModelInfoByRouteId(Long routeId) {
        RouteModelDO routeModelDO = routeModelMapper.selectOne(new LambdaQueryWrapperX<RouteModelDO>().
                eq(RouteModelDO::getRouteId, routeId));
    return BeanUtil.copyProperties(routeModelDO, ModelRouteDTO.class);
    }

    @Override
    public void updateRouteModelInfo(ModelRouteDTO modelRouteDTO) {
        RouteModelDO routeModelDO = BeanUtil.copyProperties(modelRouteDTO, RouteModelDO.class);
        routeModelMapper.update(routeModelDO,new LambdaQueryWrapperX<RouteModelDO>().
                eq(RouteModelDO::getRouteId, modelRouteDTO.getRouteId()));
    }

    @Override
    public void delRouteModelInfoByRouteId(Long routeId) {
        routeModelMapper.delete(new LambdaQueryWrapperX<RouteModelDO>().
                eq(RouteModelDO::getRouteId, routeId));
    }
}
