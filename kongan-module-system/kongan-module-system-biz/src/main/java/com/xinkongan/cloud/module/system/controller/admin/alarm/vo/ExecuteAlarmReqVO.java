package com.xinkongan.cloud.module.system.controller.admin.alarm.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 执行警情ReqVO
 * <AUTHOR>
 * @Date 2025/3/20 14:29
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "执行警情ReqVO")
@Builder
public class ExecuteAlarmReqVO {

    @Schema(description = "警情id")
    @NotNull(message = "警情id不能为空")
    private Long jobId;

    @Schema(description = "机场sn")
    @NotBlank(message = "机场sn不能为空")
    private String dockSn;
}