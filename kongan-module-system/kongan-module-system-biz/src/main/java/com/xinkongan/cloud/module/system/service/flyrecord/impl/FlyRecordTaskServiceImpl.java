package com.xinkongan.cloud.module.system.service.flyrecord.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ScheduleDeleteSetReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordTaskDO;
import com.xinkongan.cloud.module.system.dal.mysql.flyrecord.FlyRecordTaskMapper;
import com.xinkongan.cloud.module.system.enums.flyrecord.DeleteType;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordFileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordTaskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.powerjob.worker.log.OmsLogger;

import java.time.LocalDateTime;
import java.util.List;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.FLY_RECORD_DELETE_PARAM_ERROR;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.FLY_RECORD_DELETE_TYPE_ERROR;

/**
 * @Description 飞行记录删除任务ServiceImpl
 * <AUTHOR>
 * @Date 2025/2/17 10:21
 */
@Slf4j
@Service
public class FlyRecordTaskServiceImpl implements IFlyRecordTaskService {

    @Resource
    private FlyRecordTaskMapper flyRecordTaskMapper;// 飞行记录删除任务Mapper
    @Resource
    private IFlyRecordService flyRecordService;// 飞行记录Service
    @Resource
    private IFlyRecordFileService flyRecordFileService;// 飞行记录文件Service

    @Transactional(rollbackFor = Exception.class)
    @DataPermission(enable = false)
    @TenantIgnore
    @Override
    public void scheduledDelete(OmsLogger omsLogger) {
        // 查询所有配置
        List<FlyRecordTaskDO> flyRecordTaskList = flyRecordTaskMapper.selectList();
        omsLogger.info("[定期删除飞行记录定时任务] 查询所有配置:{}", JSONUtil.toJsonStr(flyRecordTaskList));
        log.info("定时删除飞行记录 flyRecordTaskList:{}", JSONUtil.toJsonStr(flyRecordTaskList));
        for (FlyRecordTaskDO flyRecordTaskDO : flyRecordTaskList) {
            omsLogger.info("当前配置信息:{}", JSONUtil.toJsonStr(flyRecordTaskDO));
            // 要删除的组织id
            Long deptId = flyRecordTaskDO.getDeptId();
            Long tenantId = flyRecordTaskDO.getTenantId();
            try {
                TenantContextHolder.setTenantId(tenantId);
                DeleteType deleteType = DeleteType.find(flyRecordTaskDO.getDeleteType());
                // 组织配置的多少天删除
                Integer days = flyRecordTaskDO.getDays();
                // 结束时间为当前时间减去配置的天数 超过这个时间就删除
                LocalDateTime endTime = LocalDateTime.now().minusDays(days);
                if (DeleteType.FLY_RECORD.equals(deleteType)) {
                    // 删除飞行记录
                    flyRecordService.deleteUnImportantFlyRecord(deptId, endTime, omsLogger);
                } else {
                    // 删除飞行记录图片或视频
                    flyRecordFileService.deleteUnImportantFile(deptId, endTime, deleteType, omsLogger);
                }
            } catch (Exception e) {
                log.error("组织:{} 定时删除飞行记录失败", deptId, e);
                omsLogger.error("[定期删除飞行记录定时任务] 组织:{} 定时删除飞行记录失败", deptId);
            } finally {
                TenantContextHolder.clear();
            }
        }
    }

    /**
     * 定期删除飞行记录设置
     *
     * <AUTHOR>
     * @date 2025/2/17 11:14
     **/
    @Override
    public Boolean scheduleDeleteSet(ScheduleDeleteSetReqVO reqVO) {

        Integer cycleType = reqVO.getCycleType();
        if (cycleType == 1) {
            // 永不删除
            flyRecordTaskMapper.delete(Wrappers.<FlyRecordTaskDO>lambdaQuery()
                    .eq(FlyRecordTaskDO::getDeptId, SecurityFrameworkUtils.getLoginUserDeptId()));
            return true;
        } else {
            // 定期删除
            Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
            Integer days = reqVO.getDays();
            if (days == null || days < 1 || days > 3650) {
                throw exception(FLY_RECORD_DELETE_PARAM_ERROR, "删除周期必须在1-2650之间");
            }
            List<Integer> deleteTypes = reqVO.getDeleteTypeArr();
            if (CollUtil.isEmpty(deleteTypes)) {
                throw exception(FLY_RECORD_DELETE_PARAM_ERROR, "删除内容不能为空");
            }
            // 删除类型处理
            DeleteType deleteType = null;
            if (deleteTypes.contains(0) && deleteTypes.contains(1) && deleteTypes.contains(2)) {
                deleteType = DeleteType.FLY_RECORD;
            } else if (deleteTypes.contains(1) && deleteTypes.contains(2)) {
                deleteType = DeleteType.IMAGE_VIDEO;
            } else if (deleteTypes.contains(1)) {
                deleteType = DeleteType.IMAGE;
            } else if (deleteTypes.contains(2)) {
                deleteType = DeleteType.VIDEO;
            }
            if (deleteType == null) {
                throw exception(FLY_RECORD_DELETE_TYPE_ERROR);
            }
            // 保存删除任务
            FlyRecordTaskDO fileRecordTaskDO = FlyRecordTaskDO.builder().days(days).deleteType(deleteType.getType()).deleteTypeArr(reqVO.getDeleteTypeArr()).build();
            fileRecordTaskDO.setDeptId(deptId);
            FlyRecordTaskDO scheduleDeleteSet = getScheduleDeleteSet();
            if (scheduleDeleteSet != null) {
                fileRecordTaskDO.setId(scheduleDeleteSet.getId());
                int update = flyRecordTaskMapper.updateById(fileRecordTaskDO);
                return update > 0;
            } else {
                int insert = flyRecordTaskMapper.insert(fileRecordTaskDO);
                return insert > 0;
            }
        }
    }

    @Override
    public FlyRecordTaskDO getScheduleDeleteSet() {
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        FlyRecordTaskDO flyRecordTaskDO = flyRecordTaskMapper.selectOne(Wrappers.<FlyRecordTaskDO>lambdaQuery()
                .eq(FlyRecordTaskDO::getDeptId, deptId));
        return flyRecordTaskDO;
    }
}