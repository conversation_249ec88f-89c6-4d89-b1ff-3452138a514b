package com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 机场接警配置
 * <AUTHOR>
 * @Date 2025/3/19 16:59
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "机场接警配置")
public class UpdateDockAlarmReqVO {

    @Schema(description = "机场sn")
    @NotBlank(message = "机场sn不能为空")
    private String dockSn;

    @Schema(description = "是否自动出警 1是0否")
    private Integer autoExecute;

    @Schema(description = "飞行高度/m")
    private Integer flyHeight;

    @Schema(description = "飞行速度m/s")
    private Integer flySpeed;

    @Schema(description = "返航高度/m")
    private Integer returnHeight;
}