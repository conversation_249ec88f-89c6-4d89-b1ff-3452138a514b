package com.xinkongan.cloud.module.system.service.dept;

import com.xinkongan.cloud.module.system.api.setting.dto.UserMapSettingReqVO;
import com.xinkongan.cloud.module.system.api.setting.dto.SettingRespVO;

/**
 * 组织地图设置 Service 接口
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-27
 */
public interface DeptMapSettingService {

    /**
     * 更新组织地图设置
     *
     * @param deptId 组织ID
     * @param reqVO 用户地图设置请求VO
     */
    void updateDeptMapSetting(Long deptId, UserMapSettingReqVO reqVO);

    /**
     * 获取组织地图设置
     *
     * @param deptId 组织ID
     * @return 用户设置响应VO
     */
    SettingRespVO getDeptMapSetting(Long deptId);

    /**
     * 检查组织地图信息是否手动修改过
     * 
     * @param deptId 组织ID
     * @return true:已手动修改 false:未手动修改
     */
    Boolean checkMapManualModified(Long deptId);

    /**
     * 机场接入时自动更新组织地图信息
     * 
     * @param deptId 组织ID
     * @param longitude 机场经度
     * @param latitude 机场纬度
     * @param address 机场地址
     */
    void autoUpdateMapFromAirport(Long deptId, Double longitude, Double latitude, String address);

    /**
     * 为新创建的组织设置默认地图信息
     * 
     * @param deptId 组织ID
     */
    void setDefaultMapSetting(Long deptId);

}
