package com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RouteDynamicStateRespVO {

    private String dockSn;
    private Integer currentWaypoint;
    private Boolean lastWaypoint;
    private List<DynamicActionRespVO> pointActions;
    private List<DynamicActionRespVO> legActions;

}
