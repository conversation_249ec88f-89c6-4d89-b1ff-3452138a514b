package com.xinkongan.cloud.module.system.service.route;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.module.algorithm.dto.ArriveWaypointDTO;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.controller.admin.psdk.vo.VoiceRespVO;
import com.xinkongan.cloud.module.system.controller.admin.psdk.vo.VoiceSendToDockVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointRespVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointShoutVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.live.LiveService;
import com.xinkongan.cloud.module.system.service.psdk.IPsdkService;
import com.xinkongan.cloud.module.system.service.psdk.IVoiceService;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.enums.VideoTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants.DOCK_SN_JOB_INFO;

@Slf4j
@Service
public class WayPointActionExecServiceImpl implements IWayPointActionExecService {

    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private IWayPointService wayPointService;

    @Resource
    private LiveService liveService;

    @Resource
    private DockDeviceService dockDeviceService;

    @Resource
    private IPsdkService psdkService;

    @Resource
    private IVoiceService voiceService;

    @Override
    public void onArriveWaypoint(ArriveWaypointDTO arriveWaypointInfo) {
        // 查询航点信息
        // 1. 查询执行的航线信息
        JobFlyInfo jobFlyInfo = redisCacheService.get(String.format(DOCK_SN_JOB_INFO, arriveWaypointInfo.getDockSn()), JobFlyInfo.class);
        if (jobFlyInfo == null) {
            log.error("[无人机到达航点通知]，执行信息不存在，航点信息为：{}", JSONObject.toJSONString(arriveWaypointInfo));
            return;
        }
        WayPointRespVO wayPointInfo = wayPointService.getWayPointInfoByRouteIdAndIndex(jobFlyInfo.getRouteId(), arriveWaypointInfo.getCurrentWaypointIndex());
        if (wayPointInfo == null) {
            log.error("[无人机到达航点通知]，航点信息不存在，航点信息为：{}", JSONObject.toJSONString(arriveWaypointInfo));
            return;
        }
        DockDeviceDO deviceInfo = dockDeviceService.getByCacheDeviceSn(arriveWaypointInfo.getDockSn());
        if (deviceInfo == null) {
            log.error("[无人机到达航点通知]，设备信息不存在，航点信息为：{}", JSONObject.toJSONString(arriveWaypointInfo));
            return;
        }
        // 2. 执行镜头模式切换
        this.execSwitchCamera(arriveWaypointInfo, wayPointInfo, deviceInfo.getChildSn());

        // 3. 执行航点喊话逻辑
        this.shout(arriveWaypointInfo, wayPointInfo);
    }

    private void execSwitchCamera(ArriveWaypointDTO arriveWaypointInfo, WayPointRespVO wayPointInfo, String droneSn) {
        // 执行镜头模式切换
        String lensMode = wayPointInfo.getLensMode();
        if (StringUtils.isEmpty(lensMode)) {
            return;
        }
        liveService.setLiveVideoType(arriveWaypointInfo.getDockSn(), droneSn, VideoTypeEnum.find(lensMode));
    }


    private void shout(ArriveWaypointDTO arriveWaypointInfo, WayPointRespVO wayPointInfo) {
        List<WayPointShoutVO> shouts = wayPointInfo.getWayPointShouts();
        if (CollectionUtil.isEmpty(shouts)) return;

        // 按类型降序排序（先执行关闭操作）
        shouts = new ArrayList<>(shouts);
        shouts.sort(Comparator.comparingInt(WayPointShoutVO::getType).reversed());

        shouts.forEach(shout -> {
            if (shout.getType() == 0) {
                handleStopShout(arriveWaypointInfo);
                return;
            }
            handleActiveShout(arriveWaypointInfo, shout);
        });
    }

    // 处理停止喊话
    private void handleStopShout(ArriveWaypointDTO dto) {
        try {
            psdkService.speakerPlayStop(dto.getDockSn());
        } catch (Exception e) {
            log.error("[喊话停止异常] dockSn:{}", dto.getDockSn(), e);
            log.error(ExceptionUtils.getStackTrace(e));
        }
    }

    // 处理有效喊话
    private void handleActiveShout(ArriveWaypointDTO dto, WayPointShoutVO shout) {
        try {
            VoiceRespVO voice = voiceService.getVoiceInfoById(shout.getVoiceId());
            VoiceSendToDockVO payload = buildVoicePayload(dto, shout, voice);

            if (shout.getMode() == 0) {
                executeSingleShout(shout, payload);
            } else if (shout.getMode() == 1) {
                executeLoopShout(shout, payload);
            }
        } catch (Exception e) {
            log.error("[喊话处理异常] shoutId:{}", shout.getVoiceId(), e);
            log.error(ExceptionUtils.getStackTrace(e));
        }
    }

    // 构建喊话参数对象
    private VoiceSendToDockVO buildVoicePayload(ArriveWaypointDTO dto, WayPointShoutVO shout, VoiceRespVO voice) {
        VoiceSendToDockVO voiceSendToDockInfo = VoiceSendToDockVO.builder()
                .dockSn(dto.getDockSn())
                .mode(shout.getMode())
                .voice(shout.getVolume())
                .build();
        voiceSendToDockInfo.setFingerprint(voice.getFingerprint());
        voiceSendToDockInfo.setName(voice.getName());
        voiceSendToDockInfo.setUrl(voice.getUrl());
        voiceSendToDockInfo.setText(voice.getText());

        return voiceSendToDockInfo;
    }

    // 执行单次喊话
    private void executeSingleShout(WayPointShoutVO shout, VoiceSendToDockVO payload) {
        if (shout.getVoiceType() == 0) {
            psdkService.sendTextToDock(payload);
        } else {
            psdkService.sendVoiceToDock(payload);
        }
    }

    // 执行循环喊话
    private void executeLoopShout(WayPointShoutVO shout, VoiceSendToDockVO payload) {
        if (shout.getIsLeg() == 0) {
            handleLegLoop(shout, payload);
        } else {
            handleCountLoop(shout, payload);
        }
    }

    // 航段循环处理
    private void handleLegLoop(WayPointShoutVO shout, VoiceSendToDockVO payload) {
        // 设置循环模式并发送
        payload.setMode(1);
        if (shout.getVoiceType() == 0) {
            psdkService.sendTextToDock(payload);
        } else {
            psdkService.sendVoiceToDock(payload);
        }
    }

    // 次数循环处理
    public void handleCountLoop(WayPointShoutVO shout, VoiceSendToDockVO payload) {
        AtomicInteger counter = new AtomicInteger(shout.getLoopNum());
        payload.setMode(0);
        Runnable executor = new Runnable() {
            @Override
            public void run() {
                log.info("[执行循环喊话]：机场sn：{},执行循环喊话，剩余次数：{}", payload.getDockSn(), counter.get());
                psdkService.execShout(payload, () -> {
                    if (counter.decrementAndGet() > 0) {
                        // 停顿 2s，防止出现问题
                        try {
                            TimeUnit.SECONDS.sleep(2);
                            log.info("[执行循环喊话结束回调]：机场sn：{},执行循环喊话，剩余次数：{}", payload.getDockSn(), counter.get());
                            this.run();
                        } catch (Exception e) {
                            log.error("[执行循环喊话结束回调异常]：机场sn：{},执行循环喊话，剩余次数：{}", payload.getDockSn(), counter.get(), e);
                            log.error(ExceptionUtils.getStackTrace(e));
                        }
                    } else {
                        log.info("[喊话结束]：机场sn：{}。", payload.getDockSn());
                        psdkService.removeCb(payload.getDockSn());
                    }
                });
            }
        };
        executor.run();
    }
}
