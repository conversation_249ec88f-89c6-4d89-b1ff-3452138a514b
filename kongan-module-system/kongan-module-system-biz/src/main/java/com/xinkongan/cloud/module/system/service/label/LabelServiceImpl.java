package com.xinkongan.cloud.module.system.service.label;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.datapermission.core.plugins.SharePluginParam;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.label.dto.*;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelDetailRespVO;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelRespVO;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelShareVO;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.TabRespVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.LabelStatisticVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.label.LabelDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.dal.mysql.label.LabelMapper;
import com.xinkongan.cloud.module.system.dto.ResourceShareDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.label.LabelReferenceEnums;
import com.xinkongan.cloud.module.system.enums.material.MaterialReferenceEnums;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.module.system.service.material.IMaterialManageService;
import com.xinkongan.cloud.module.system.service.material.IMaterialReferenceService;
import com.xinkongan.cloud.module.system.service.share.IShareService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


@Slf4j
@Service
public class LabelServiceImpl implements ILabelService {

    @Resource
    private LabelMapper labelMapper;

    @Resource
    private FileService fileService;

    @Resource
    private ITabService tabService;

    @Resource
    private IShareService shareService;

    @Resource
    private IMaterialReferenceService materialReferenceService;

    @Resource
    private IMaterialManageService materialManageService;

    @Resource
    private ILabelReferenceService labelReferenceService;

    @Resource
    private DeptService deptService;

    @Resource
    private AdminUserService adminUserService;


    @Override
    @DataPermission(enable = false)
    public LabelDetailRespVO getLabelDetailById(Long labelId) {
        LabelDO labelDO = labelMapper.selectById(labelId);
        if (labelDO == null) {
            throw new ServiceException(ErrorCodeConstants.LABEL_ID_ERROR);
        }
        LabelDetailRespVO labelDetailRespVO = this.convert(labelDO);
        // 查询标记数据
        List<TabRespVO> tabInfos = tabService.getTabInfosByLabelId(labelId);
        labelDetailRespVO.setTabInfos(tabInfos);

        // 补充分享字段
        boolean resourceShare = shareService.checkResourceShare(labelId, ResourceShareTypeEnum.LABEL_RESOURCE.getResourceType());
        labelDetailRespVO.setShareFlag(resourceShare ? 1 : 0);
        // 分享的数据创建人和创建组织显示 --
        if (resourceShare) {
            labelDetailRespVO.setCreateName("--");
            labelDetailRespVO.setDeptName("--");
        }
        // 查询素材引用数据
        List<Long> materialIds = materialReferenceService.getMaterialIdsByReferenceKey(String.valueOf(labelId), MaterialReferenceEnums.LABEL_REFERENCE);
        List<MaterialInfoVO> materialInfos = materialManageService.getMaterialListByIds(materialIds);
        labelDetailRespVO.setMaterialInfos(materialInfos);
        return labelDetailRespVO;
    }


    @Override
    public List<LabelDetailRespVO> getLabelDetailByIds(List<Long> labelIds) {
        if (CollectionUtil.isEmpty(labelIds)) {
            return new ArrayList<>();
        }
        List<LabelDO> labelInfos = labelMapper.selectByIds(labelIds);
        if (CollectionUtil.isEmpty(labelInfos)) {
            return new ArrayList<>();
        }
        Map<Long, List<TabRespVO>> labelTabMaps = tabService.getTabInfosByLabelIds(labelIds);
        Map<Long, Boolean> labelShareMaps = shareService.checkResourceShare(labelIds, ResourceShareTypeEnum.LABEL_RESOURCE.getResourceType());

        List<LabelDetailRespVO> labelDetailRespInfos = labelInfos
                .stream()
                .map(l -> {
                    LabelDetailRespVO labelDetailRespVO = this.convert(l);
                    labelDetailRespVO.setTabInfos(labelTabMaps.get(l.getId()));
                    labelDetailRespVO.setShareFlag(labelShareMaps.get(l.getId()) ? 1 : 0);
                    // 查询素材引用数据
                    List<Long> materialIds = materialReferenceService.getMaterialIdsByReferenceKey(String.valueOf(l.getId()), MaterialReferenceEnums.LABEL_REFERENCE);
                    List<MaterialInfoVO> materialInfos = materialManageService.getMaterialListByIds(materialIds);
                    labelDetailRespVO.setMaterialInfos(materialInfos);
                    return labelDetailRespVO;
                }).toList();
        return labelDetailRespInfos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateLabelById(LabelUpdateDTO labelUpdateInfo) {
        LabelDO labelDO = labelMapper.selectById(labelUpdateInfo.getId());
        if (labelDO == null) {
            throw new ServiceException(ErrorCodeConstants.LABEL_ID_ERROR);
        }
        if (!StringUtils.isEmpty(labelUpdateInfo.getUrl())) {
            // 需要更新缩略图
            labelDO.setThumbnailUrl(labelUpdateInfo.getUrl());
            List<Long> fileIds = labelDO.getFileIds();
            fileIds.add(labelUpdateInfo.getFileId());
        }

        BeanUtils.copyProperties(labelUpdateInfo, labelDO);
        labelMapper.updateById(labelDO);
        // 移除之前的标记信息
        tabService.delTabByLabelId(labelUpdateInfo.getId());
        // 保存标记数据
        tabService.saveTabInfos(labelUpdateInfo.getId(), labelUpdateInfo.getTabSaveInfos());

        // 移除之前的素材引用信息
        materialReferenceService.deleteMaterialReferenceByReferenceKey(String.valueOf(labelUpdateInfo.getId()), MaterialReferenceEnums.LABEL_REFERENCE);
        // 保存素材引用数据
        materialReferenceService.saveMaterialReference(String.valueOf(labelUpdateInfo.getId()), MaterialReferenceEnums.LABEL_REFERENCE, labelUpdateInfo.getMaterialIds());
        return labelDO.getId();
    }

    @Override
    public void delLabelById(Long labelId, Boolean forcedDeletion) {
        LabelDO labelDO = labelMapper.selectById(labelId);
        if (labelDO == null) {
            throw new ServiceException(ErrorCodeConstants.LABEL_ID_ERROR);
        }
        if (labelReferenceService.checkLabelReference(labelId, LabelReferenceEnums.ROUTE_REFERENCE) && !forcedDeletion) {
            throw new ServiceException(ErrorCodeConstants.LABEL_HAS_REFERENCE);
        }
        // 删除标注记录
        labelMapper.deleteById(labelId);
        // 删除文件数据
        fileService.deleteFileByIds(labelDO.getFileIds());
        // 删除标记数据
        tabService.delTabByLabelId(labelId);
        // 删除引用数据
        materialReferenceService.deleteMaterialReferenceByReferenceKey(String.valueOf(labelId), MaterialReferenceEnums.LABEL_REFERENCE);
    }

    @Override
    public Long saveLabel(LabelSaveDTO labelSaveInfo) {
        LabelDO labelDO = BeanUtils.toBean(labelSaveInfo, LabelDO.class);
        labelDO.setThumbnailUrl(labelSaveInfo.getUrl());
        List<Long> fileIds = labelDO.getFileIds();
        fileIds.add(labelSaveInfo.getFileId());
        labelMapper.insert(labelDO);
        // 保存标记数据
        tabService.saveTabInfos(labelDO.getId(), labelSaveInfo.getTabSaveInfos());
        // 保存素材引用数据
        String labelId = String.valueOf(labelDO.getId());
        materialReferenceService.saveMaterialReference(labelId, MaterialReferenceEnums.LABEL_REFERENCE, labelSaveInfo.getMaterialIds());
        return labelDO.getId();
    }

    @Override
    public PageResult<LabelRespVO> getLabelByPage(LabelSearchDTO searchParams) {
        Page<LabelRespVO> page = new Page<>(searchParams.getPage(), searchParams.getOffset());
        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.LABEL_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.LABEL_RESOURCE.getResourceType())
                .shareFlag(searchParams.getShareFlag())
                .build();

        List<LabelRespVO> records = labelMapper.getLabelInfosByPageWithShare(page, sharePluginParam, searchParams);
        return new PageResult<>(records, page.getTotal());
    }


    @Override
    public void saveLabelShare(LabelShareDTO labelShareInfo) {
        LabelDO labelDO = labelMapper.selectById(labelShareInfo.getLabelId());
        if (labelDO == null) {
            throw new ServiceException(ErrorCodeConstants.LABEL_ID_ERROR);
        }
        ResourceShareDTO resourceShareParam = ResourceShareDTO
                .builder()
                .resourceId(labelShareInfo.getLabelId())
                .resourceType(ResourceShareTypeEnum.LABEL_RESOURCE.getResourceType())
                .shareDeptIds(labelShareInfo.getDeptIds())
                .oriDeptId(labelDO.getDeptId())
                .build();
        shareService.resourceShareDeptIds(resourceShareParam);
    }


    @Override
    public LabelShareVO getLabelShareDept(Long labelId) {
        List<Long> deptIds = shareService.getResourceSharesDept(labelId);
        return LabelShareVO.builder().deptIds(deptIds).labelId(labelId).build();
    }

    private LabelDetailRespVO convert(LabelDO labelDO) {
        LabelDetailRespVO labelDetailRespVO = BeanUtils.toBean(labelDO, LabelDetailRespVO.class);
        if (labelDetailRespVO.getDeptId() != null) {
            DeptDO deptInfo = deptService.getDept(labelDetailRespVO.getDeptId());
            labelDetailRespVO.setDeptName(deptInfo.getName());
        }
        if (!StringUtils.isEmpty(labelDO.getCreator())) {
            AdminUserDO userInfo = adminUserService.getUser(Long.parseLong(labelDO.getCreator()));
            if (userInfo != null) {
                labelDetailRespVO.setCreateName(userInfo.getNickname());
            }
        }
        return labelDetailRespVO;
    }

    @Override
    public void lockLabel(Long lableId) {
        LabelDO labelDO = labelMapper.selectById(lableId);
        if (labelDO == null) {
            throw new ServiceException(ErrorCodeConstants.LABEL_ID_ERROR);
        }
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        if (!Objects.equals(String.valueOf(currentUserId), labelDO.getCreator())) {
            throw new ServiceException(ErrorCodeConstants.LABEL_LOCK_ONLY_BY_CREATOR);
        }
        labelDO.setLockFlag(true);
        labelMapper.updateById(labelDO);
    }

    @Override
    public void unLockLabel(Long lableId) {
        LabelDO labelDO = labelMapper.selectById(lableId);
        if (labelDO == null) {
            throw new ServiceException(ErrorCodeConstants.LABEL_ID_ERROR);
        }
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        if (!Objects.equals(String.valueOf(currentUserId), labelDO.getCreator())) {
            throw new ServiceException(ErrorCodeConstants.LABEL_LOCK_ONLY_BY_CREATOR);
        }
        labelDO.setLockFlag(false);
        labelMapper.updateById(labelDO);
    }

    @Override
    public LabelStatisticVO getLabelStatistic() {
        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.LABEL_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.LABEL_RESOURCE.getResourceType())
                .shareFlag(null)
                .build();

        List<LabelRespVO> labelStatisticWithShare = labelMapper.getLabelStatisticWithShare(sharePluginParam);
        int labelTotal = labelStatisticWithShare.size();
        AtomicInteger labelInnerTotal = new AtomicInteger();
        AtomicInteger labelOuterTotal = new AtomicInteger();
        labelStatisticWithShare.forEach(v -> (v.getShareFlag() == 1 ? labelOuterTotal : labelInnerTotal).getAndIncrement());
        LabelStatisticVO labelStatisticVO = new LabelStatisticVO();
        labelStatisticVO.setLabelTotal(labelTotal);
        labelStatisticVO.setLabelInnerTotal(labelInnerTotal.get());
        labelStatisticVO.setLabelOuterTotal(labelOuterTotal.get());
        // 查询航线引用
        Long labelReferenceRouteCount = labelReferenceService.getLabelReferenceRouteCount();
        labelStatisticVO.setRouteReferTotal(labelReferenceRouteCount);
        return labelStatisticVO;
    }

    @Override
    public Integer getPageNumById(LabelSearchByIdDTO search) {
        LabelDO labelDO = labelMapper.selectById(search.getId());
        if (labelDO == null) {
            throw new ServiceException(ErrorCodeConstants.LABEL_ID_ERROR);
        }
        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.LABEL_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.LABEL_RESOURCE.getResourceType())
                .shareFlag(search.getShareFlag())
                .build();
        Integer count = labelMapper.labelByTimeWithShare_mpCount(sharePluginParam, search, labelDO.getCreateTime());
        log.info("查询异常页码 id:{},count:{}", search.getId(), count);
        // 计算当前任务的页码
        return (count + search.getPageSize())/ search.getPageSize();
    }

    @Override
    public PageResult<LabelRespVO> getLabelPageByDeptId(LabelSearchDTO search) {
        Page<LabelRespVO> page = new Page<>(search.getPage(), search.getOffset());
        Set<Long> deptIds = deptService.getChildDeptByDeptId(search.getDeptId());
        List<LabelRespVO> records = labelMapper.getLabelInfosByPageDeptId(page, search, deptIds);
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DataPermission(enable = false)
    public void deleteLabelByDeptId(Long deptId) {
        log.info("[删除组织下的标注] 组织ID：{}", deptId);
        try {
            // 查询当前组织下的所有标注
            List<LabelDO> labelList = labelMapper.selectList(
                    new LambdaQueryWrapperX<LabelDO>()
                            .eq(LabelDO::getDeptId, deptId)
            );

            if (CollectionUtil.isEmpty(labelList)) {
                log.info("[删除组织下的标注] 组织ID：{} 下没有标注数据", deptId);
                return;
            }

            // 逐个删除标注（调用现有的删除方法，确保完整删除逻辑）
            for (LabelDO label : labelList) {
                try {
                    delLabelById(label.getId(), true);
                    log.info("[删除组织下的标注] 成功删除标注ID：{}", label.getId());
                } catch (Exception e) {
                    log.error("[删除组织下的标注] 删除标注ID：{} 失败", label.getId(), e);
                }
            }

            log.info("[删除组织下的标注] 组织ID：{} 删除完成，共删除 {} 个标注", deptId, labelList.size());
        } catch (Exception e) {
            log.error("[删除组织下的标注] 组织ID：{} 删除失败", deptId, e);
            throw e;
        }
    }
}
