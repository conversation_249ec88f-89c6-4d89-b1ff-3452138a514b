package com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DeptTreeInfoVO extends BaseTreeNode {

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "父部门id")
    private Long parentId;

    @Schema(description = "部门显示顺序")
    private Integer sort;

    @Schema(description = "负责人用户id")
    private Long leaderUserId;

    @Schema(description = "负责人电话")
    private String phone;

    @Schema(description = "负责人邮箱")
    private String email;

    @Schema(description = "绑定码")
    private String bindCode;

    @Schema(description = "部门状态")
    private Integer status;

    @Schema(description = "组织层级")
    private Integer deptLevel;

    @Schema(description = "组织类型的code码")
    private String deptTypeCode;
}
