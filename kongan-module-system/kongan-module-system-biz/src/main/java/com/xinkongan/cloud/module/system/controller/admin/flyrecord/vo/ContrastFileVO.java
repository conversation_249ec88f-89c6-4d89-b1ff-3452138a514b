package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/20 10:29
 */
@Schema(description = "对比文件VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ContrastFileVO {

    @Schema(description = "文件id")
    private Long id;

    @Schema(description = "文件url")
    private String url;

    @Schema(description = "对比文件类型 0人工截图1标注异常2对比异常3拍摄照片")
    private Integer fileType;

    @Schema(description = "经度")
    private String lon;

    @Schema(description = "纬度")
    private String lat;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "图片名称")
    private String name;

    @Schema(description = "图片宽/像素")
    private Integer width;

    @Schema(description = "图片高/像素")
    private Integer height;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "拍照时间")
    private LocalDateTime actionTime;

    @Schema(description = "飞行记录id")
    private Long flyRecordId;

    @Schema(description = "异常id")
    private Long exceptionId;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "左图id")
    private Long leftId;

    @Schema(description = "右图id")
    private Long rightId;
}