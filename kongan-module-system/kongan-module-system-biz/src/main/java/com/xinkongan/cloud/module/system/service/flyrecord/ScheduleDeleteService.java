package com.xinkongan.cloud.module.system.service.flyrecord;

import com.xinkongan.cloud.framework.job.core.powerjob.handler.JobHandler;
import com.xinkongan.cloud.framework.job.core.powerjob.handler.PowerJobLoggerContext;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.log.OmsLogger;

/**
 * @Description 定期删除定时任务
 * <AUTHOR>
 * @Date 2025/3/3 9:00
 */
@Slf4j
@Service(ScheduleDeleteService.HANDLER_BEAN_NAME)
public class ScheduleDeleteService implements JobHandler {

    // 定时删除任务处理器bean名称
    public static final String HANDLER_BEAN_NAME = "ScheduleDeleteService";

    @Resource
    private IFlyRecordTaskService flyRecordTaskService;

    @Override
    public void execute(String param) {
        // powerJob日志上下文
        OmsLogger omsLogger = PowerJobLoggerContext.getOmsLogger();
        omsLogger.info("[定期删除飞行记录定时任务] 执行定时任务");
        TenantUtils.executeIgnore(()-> flyRecordTaskService.scheduledDelete(omsLogger));
    }
}