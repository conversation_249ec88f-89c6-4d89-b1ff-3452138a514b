package com.xinkongan.cloud.module.system.controller.admin.psdk.dto;

import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class VoiceCreateDTO extends FileSaveInfoDTO {

    @Schema(description = "语音或文本名称", example = "李四")
    private String name;

    @Schema(description = "喊话文本")
    private String text;

    @Schema(description = "喊话类型 （0：文本类型   1：语音类型）", example = "2")
    private Integer type;

    @Schema(description = "创建文件类型（0：pcm文件  1：mp3文件）")
    @Min(value = 0, message = "文件类型不能小于0")
    @Max(value = 1, message = "文件类型不能大于1")
    private Integer voiceType;
}
