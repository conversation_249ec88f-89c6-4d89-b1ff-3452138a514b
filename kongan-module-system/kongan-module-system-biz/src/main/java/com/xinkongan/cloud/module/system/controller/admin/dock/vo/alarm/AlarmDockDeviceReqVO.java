package com.xinkongan.cloud.module.system.controller.admin.dock.vo.alarm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 接警机场列表
 * <AUTHOR>
 * @Date 2025/3/20 11:15
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "接警机场列表ReqVO")
public class AlarmDockDeviceReqVO {

    @Schema(description = "经度")
    @NotNull(message = "警情经度不能为空")
    private Double longitude;

    @Schema(description = "纬度")
    @NotNull(message = "警情纬度不能为空")
    private Double latitude;

    @JsonIgnore
    private Long userId;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "关键字模糊查询")
    private String searchKey;
}