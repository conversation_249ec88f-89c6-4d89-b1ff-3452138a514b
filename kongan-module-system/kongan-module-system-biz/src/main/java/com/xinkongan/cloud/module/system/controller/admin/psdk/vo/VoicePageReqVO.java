package com.xinkongan.cloud.module.system.controller.admin.psdk.vo;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 喊话器语音分页 Request VO")
@Data
@ToString(callSuper = true)
public class VoicePageReqVO extends PageParam {

    @Schema(description = "语音或文本名称", example = "李四")
    private String name;

    @Schema(description = "喊话文本")
    private String text;

    @Schema(description = "喊话语音阿里云地址", example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "创建人id")
    private String createBy;

    @Schema(description = "喊话类型 （0：文本类型   1：语音类型）", example = "2")
    private Integer type;

    @Schema(description = "行动单位id", example = "15854")
    private Long deptId;

    @Schema(description = "文件id（表infra_file）", example = "8245")
    private Long fileId;

}
