package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 禁飞区同步请求VO
 */
@Data
@Schema(description = "管理后台 - 禁飞区同步请求VO")
public class NfzSyncReqVO {

    @Schema(description = "机场SN", requiredMode = Schema.RequiredMode.REQUIRED, example = "DOCK001")
    @NotBlank(message = "机场SN不能为空")
    private String dockSn;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "是否强制同步", example = "false")
    private Boolean forceSync = false;

    @Schema(description = "同步备注", example = "手动同步禁飞区")
    private String remark;
}
