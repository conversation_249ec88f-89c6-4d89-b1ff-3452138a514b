package com.xinkongan.cloud.module.system.service.callback.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import com.xinkongan.cloud.module.system.api.upgrade.dto.UpgradeNoticeDTO;
import com.xinkongan.cloud.module.system.enums.callback.SystemCallbackTypeEnum;
import com.xinkongan.cloud.module.system.service.callback.ICallbackHandle;
import com.xinkongan.cloud.module.system.service.upgrade.IUpgradeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description 系统升级回调
 * <AUTHOR>
 * @Date 2025/5/22 16:42
 */
@Slf4j
@Service
public class SystemUpgradeNoticeCallbackStrategy implements ICallbackHandle<Boolean> {

    @Resource
    private IUpgradeService upgradeService;

    @Override
    public SystemCallbackTypeEnum getCallbackType() {
        return SystemCallbackTypeEnum.SYSTEM_UPGRADE_NOTICE;
    }

    @Override
    public Boolean handle(JSONObject callbackParam) {
        log.info("系统升级回调参数:{}", callbackParam);
        UpgradeNoticeDTO upgradeNoticeDTO = BeanUtil.toBean(callbackParam, UpgradeNoticeDTO.class);
        if(upgradeNoticeDTO == null) {
            return false;
        }
        upgradeService.pushUpgradeNotice(upgradeNoticeDTO);
        return true;
    }
}