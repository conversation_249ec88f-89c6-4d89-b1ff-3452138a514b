package com.xinkongan.cloud.module.system.service.flyrecord.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.mybatis.core.util.MyBatisUtils;
import com.xinkongan.cloud.framework.security.core.LoginUser;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ContrastFileVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ContrastPageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ContrastPicLeftListReqVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.ContrastSaveReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordContrastDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import com.xinkongan.cloud.module.system.dal.mysql.flyrecord.FlyRecordContrastMapper;
import com.xinkongan.cloud.module.system.dal.mysql.flyrecord.FlyRecordFileMapper;
import com.xinkongan.cloud.module.system.enums.flyrecord.ContrastFileTypeEnum;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileSourceEnum;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileTypeEnum;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordContrastCacheService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordContrastService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordFileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import com.xinkongan.cloud.module.system.service.permission.PermissionService;
import com.xinkongan.cloud.module.system.service.share.IShareService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.xinkongan.cloud.framework.common.exception.enums.GlobalErrorCodeConstants.NO_PERMISSION;
import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.FLY_RECORD_NOT_FOUND;

/**
 * @Description 飞行记录对比ServiceImpl
 * <AUTHOR>
 * @Date 2025/2/19 10:23
 */
@Service
@Slf4j
public class FlyRecordContrastServiceImpl implements IFlyRecordContrastService {

    @Resource
    private FlyRecordFileMapper flyRecordFileMapper;
    @Resource
    private IFlyRecordContrastCacheService flyRecordContrastCacheService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private IShareService shareService;
    @Resource
    private FlyRecordContrastMapper flyRecordContrastMapper;
    @Resource
    private IFlyRecordService flyRecordService;
    @Resource
    private IFlyRecordFileService flyRecordFileService;

    @DataPermission(enable = false)
    @Override
    public List<ContrastFileVO> radiusFiles(Double longitude, Double latitude, Integer radius, LocalDateTime startTime, LocalDateTime endTime) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            log.info("对比记录 未登录");
            return List.of();
        }
        Long tenantId = loginUser.getTenantId();
        Long deptId = loginUser.getDeptId();
        Long userId = loginUser.getId();

        List<Long> fileByRadius = flyRecordContrastCacheService.getFileByRadius(tenantId, longitude, latitude, Double.valueOf(radius));
        if (CollUtil.isEmpty(fileByRadius)) {
            return List.of();
        }

        // 获取当前账号的角色数据权限范围
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(userId);
        Set<Long> dataPermissionDeptIds = deptDataPermission.getDeptIds();
        // 查询共享给当前用户的飞行记录
        List<Long> shareFlyrecordIsList = shareService.getDeptShareIds(deptId, ResourceShareTypeEnum.FLY_RECORD_RESOURCE);

        // 查询数据权限范围内+共享给当前用户范围内的+不是自己组织内+筛序时间的
        List<FlyRecordFileDO> flyRecordFileDOS = flyRecordFileMapper.selectList(Wrappers.<FlyRecordFileDO>lambdaQuery()
                .in(FlyRecordFileDO::getId, fileByRadius)
                .and(i -> i.in(FlyRecordFileDO::getDeptId, dataPermissionDeptIds)
                        .or()
                        .in(CollUtil.isNotEmpty(shareFlyrecordIsList), FlyRecordFileDO::getFlyRecordId, shareFlyrecordIsList))
                .ge(startTime != null, FlyRecordFileDO::getActionTime, startTime)
                .le(endTime != null, FlyRecordFileDO::getActionTime, endTime)
                .orderByDesc(FlyRecordFileDO::getActionTime));
        // TODO 查询异常图片

        // 合并成对比记录附近图片列表
        List<ContrastFileVO> contrastFileVOList = new ArrayList<>();
        for (FlyRecordFileDO flyRecordFileDO : flyRecordFileDOS) {
            ContrastFileVO contrastFileVO = BeanUtil.toBean(flyRecordFileDO, ContrastFileVO.class);
            contrastFileVOList.add(contrastFileVO);
        }
        return contrastFileVOList;
    }

    /**
     * 保存对比记录
     **/
    @Override
    public Boolean save(ContrastSaveReqVO contrastSaveReqVO) {
        // 查询当前飞行记录
        FlyRecordDO flyRecordDO = flyRecordService.getById(contrastSaveReqVO.getLeftFlyRecordId());
        if (flyRecordDO == null) {
            throw exception(FLY_RECORD_NOT_FOUND);
        }
        FlyRecordContrastDO recordContrastDO = BeanUtil.toBean(contrastSaveReqVO, FlyRecordContrastDO.class);
        recordContrastDO.setDeptId(SecurityFrameworkUtils.getLoginUserDeptId());
        // TODO 保存异常记录并将异常id设置到对比记录中
        // 保存对比记录
        int insert = flyRecordContrastMapper.insert(recordContrastDO);
        return insert > 0;
    }

    @DataPermission(enable = false)
    @Override
    public PageResult<FlyRecordContrastDO> page(ContrastPageReqVO contrastPageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            log.info("对比记录 未登录");
            return PageResult.empty();
        }
        Long deptId = loginUser.getDeptId();
        Long userId = loginUser.getId();
        // 查询分享给当前用户的飞行记录列表
        List<Long> deptShareFlyRecordIds = shareService.getDeptShareIds(deptId, ResourceShareTypeEnum.FLY_RECORD_RESOURCE);
        // 查询当前角色的数据权限
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(userId);
        Set<Long> dataPermissionDeptIds = deptDataPermission.getDeptIds();
        // 查询当前飞行记录
        FlyRecordDO flyRecordDO = flyRecordService.getById(contrastPageReqVO.getFlyRecordId());
        if (flyRecordDO == null) {
            throw exception(FLY_RECORD_NOT_FOUND);
        }
        boolean isShare = deptShareFlyRecordIds.contains(flyRecordDO.getId());
        if (dataPermissionDeptIds.contains(flyRecordDO.getDeptId()) || isShare) { // 有数据权限
            IPage<FlyRecordContrastDO> mpPage = MyBatisUtils.buildPage(contrastPageReqVO);
            List<FlyRecordContrastDO> list = flyRecordContrastMapper.page(mpPage, contrastPageReqVO, dataPermissionDeptIds, isShare);
            if (CollUtil.isEmpty(list)) {
                return PageResult.empty();
            }
            return new PageResult<>(list, mpPage.getTotal());
        } else { // 没有权限的
            throw exception(NO_PERMISSION);
        }
    }

    @Override
    public List<ContrastFileVO> picLeftList(ContrastPicLeftListReqVO reqVO) {
        Long flyRecordId = reqVO.getFlyRecordId();
        List<ContrastFileVO> contrastFileVOList = new ArrayList<>();
        List<FlyRecordFileDO> flyRecordFileDOS = flyRecordFileService.listByFlyRecordId(flyRecordId);
        // TODO 查询异常图片

        // TODO for 判断异常图片是不是对比记录的二合一图片

        // TODO 是二合一 查询对比记录填充左图id、右图id

        for (FlyRecordFileDO flyRecordFileDO : flyRecordFileDOS) {
            FileTypeEnum fileTypeEnum = FileTypeEnum.find(flyRecordFileDO.getFileType());

            if (fileTypeEnum.equals(FileTypeEnum.IMAGE)) {
                ContrastFileVO contrastFileVO = BeanUtil.toBean(flyRecordFileDO, ContrastFileVO.class);
                FileSourceEnum fileSourceEnum = FileSourceEnum.find(flyRecordFileDO.getFileSource());

                // 设置对比记录图片的类型
                if (fileSourceEnum.equals(FileSourceEnum.SCREENSHOT)) {
                    contrastFileVO.setFileType(ContrastFileTypeEnum.SCREENSHOT.getValue());
                } else if (fileSourceEnum.equals(FileSourceEnum.PHOTOGRAPH)) {
                    contrastFileVO.setFileType(ContrastFileTypeEnum.PHOTOGRAPH.getValue());
                }
                contrastFileVOList.add(contrastFileVO);
            }
        }
        return contrastFileVOList;
    }
}