package com.xinkongan.cloud.module.system.service.route;

import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteAlgorithmSaveDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteAlgorithmVO;
import com.xinkongan.cloud.module.system.dto.WayPointActionDTO;

import java.util.List;
import java.util.Map;

public interface IRouteAlgorithmService {

    void deleteRouteAlgorithmInfoByRouteIdAndPointId(Long routeId, Long pointId);

    /**
     * 保存航线算法信息
     *
     * @param routeId             航线ID
     * @param pointId             航点ID
     * @param routeAlgorithmInfos 航线算法信息
     */
    List<Long> saveRouteAlgorithmInfo(Long routeId, Long pointId,
                                      List<RouteAlgorithmSaveDTO> routeAlgorithmInfos,
                                      List<WayPointActionDTO> wayPointActions);


    /**
     * 查询整条航线中配置的算法信息
     *
     * @param routeId 航线ID
     * @return 航线算法信息
     */
    Map<Long, List<RouteAlgorithmVO>> getRouteAlgorithmInfoByRouteId(Long routeId);


    /**
     * 根据航点id查询算法信息
     *
     * @param pointId 航点ID
     * @return 算法信息
     */
    List<RouteAlgorithmVO> getRouteAlgorithmInfoByPointId(Long pointId);

    /**
     * 实例是否被使用
     *
     * @param id 实例id
     * @return true: 被使用 false: 未被使用
     */
    Boolean isAlgorithmExampleUsed(Long id);

}
