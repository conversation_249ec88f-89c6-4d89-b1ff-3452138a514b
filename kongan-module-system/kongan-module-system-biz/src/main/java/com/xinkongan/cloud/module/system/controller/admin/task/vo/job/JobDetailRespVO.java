package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage.DockCoverageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 任务详情 Resp VO
 * <AUTHOR>
 * @Date 2025/1/14 10:43
 */
@Schema(description = "任务详情Resp VO")
@Data
public class JobDetailRespVO {

    @Schema(description = "计划id")
    private Long taskId;
    
    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "任务所属组织id")
    private Long deptId;

    @Schema(description = "任务所属组织名称")
    private String deptName;

    @Schema(description = "任务统计信息")
    private JobDetailStatistics jobDetailStatistics;

    @Schema(description = "任务创建人id")
    private Long userId;

    @Schema(description = "任务创建人名称")
    private String nickname;

    @Schema(description = "任务名称")
    private String jobName;

    @Schema(description = "任务描述")
    private String description;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "执行机场sn")
    private String dockSn;

    @Schema(description = "机场名称")
    private String dockName;

    @Schema(description = "执行航线id")
    private Long routeId;

    @Schema(description = "执行航线名称")
    private String routeName;

    @Schema(description = "任务执行模式")
    private Integer execMode;

    @Schema(description = "任务精度 航线任务精度 0:GPS 1高精度RTK")
    private Integer waylinePrecisionType;

    @Schema(description = "是否开启自动断点续飞")
    private Integer autoBreakPoint;

    /**
     * 可显示多个；若进行断点续飞，则添加显示第二次任务的执行时间；列表中显示最新的执行时间；排序也以最新执行时间排序
     * <AUTHOR>
     * @date 2025/1/14 10:58
     **/
    @Schema(description = "执行时间列表")
    private List<LocalDateTime> executeTimeList;

    @Schema(description = "任务预计耗时（单位:秒）")
    private Integer predictTime;

    @Schema(description = "失败原因")
    private String reason;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "任务租户id")
    private Long tenantId;

    @Schema(description = "机场可飞范围")
    private DockCoverageVO dockCoverageVO;

}