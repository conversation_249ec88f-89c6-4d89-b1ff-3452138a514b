package com.xinkongan.cloud.module.system.controller.admin.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserSettingDTO {

    @Schema(description = "用户ID", example = "123")
    private Long userId;

    @Schema(description = "X轴速度", example = "1.0")
    private Double drcSpeedX;

    @Schema(description = "W轴速度", example = "1.0")
    private Double drcSpeedW;

    @Schema(description = "机场接入是否已经引导", example = "true")
    private Boolean dockGuide;

}
