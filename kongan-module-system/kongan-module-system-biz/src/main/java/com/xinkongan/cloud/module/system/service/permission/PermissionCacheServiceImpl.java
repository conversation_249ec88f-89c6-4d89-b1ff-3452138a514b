package com.xinkongan.cloud.module.system.service.permission;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.module.system.controller.admin.auth.vo.AuthPermissionInfoRespVO;
import com.xinkongan.cloud.module.system.controller.admin.permission.vo.role.RoleRespVO;
import com.xinkongan.cloud.module.system.convert.auth.AuthConvert;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.MenuDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleMenuDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.UserRoleDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.dal.mysql.permission.MenuMapper;
import com.xinkongan.cloud.module.system.dal.mysql.permission.RoleMapper;
import com.xinkongan.cloud.module.system.dal.mysql.permission.RoleMenuMapper;
import com.xinkongan.cloud.module.system.dal.mysql.permission.UserRoleMapper;
import com.xinkongan.cloud.module.system.dal.mysql.user.AdminUserMapper;
import com.xinkongan.cloud.module.system.enums.permission.AuthCacheEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Service
public class PermissionCacheServiceImpl implements IPermissionCacheService, IAuthRefreshCacheService {

    // 用户权限缓存有效期为 5 分钟
    public static Long CACHE_EXPIRE_TIME = 60 * 5L;


    // 用户权限缓存的前缀
    public static String USER_AUTH_CACHE_PREFIX = "user:auth:cache:prefix:";

    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private RoleMenuMapper roleMenuMapper;

    @Resource
    private MenuMapper menuMapper;

    @Resource
    private AdminUserMapper adminUserMapper;


    @Override
    public List<RoleDO> getUserRoleDosById(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        String authKey = USER_AUTH_CACHE_PREFIX + userId;
        String value = redisCacheService.hashGet(authKey, AuthCacheEnum.AUTH_USER_ROLE.getAuthField(), String.class);
        List<RoleDO> roleList = JSONObject.parseArray(value, RoleDO.class);
        if (roleList == null) {
            // 去数据库中查询，并存放到缓存中
            List<UserRoleDO> userRoles = userRoleMapper.selectListByUserId(userId);
            if (CollectionUtil.isEmpty(userRoles)) {
                return new ArrayList<>();
            }
            roleList = roleMapper.selectByIds(userRoles.stream().map(UserRoleDO::getRoleId).collect(Collectors.toSet()));
            redisCacheService.hashPut(authKey, AuthCacheEnum.AUTH_USER_ROLE.getAuthField(), JSONObject.toJSONString(roleList), CACHE_EXPIRE_TIME);
        }
        return roleList;
    }

    @Override
    public List<RoleRespVO> getUserRoleRespById(Long userId) {
        List<RoleDO> roleLists = this.getUserRoleDosById(userId);
        return roleLists.stream().map(this::roleConvert).toList();
    }

    private RoleRespVO roleConvert(RoleDO roleInfo) {
        return BeanUtils.toBean(roleInfo, RoleRespVO.class);
    }

    @Override
    public Set<Long> getRoleIdsByUserId(Long userId) {
        List<RoleDO> roleLists = this.getUserRoleDosById(userId);
        return roleLists.stream().map(RoleDO::getId).collect(Collectors.toSet());
    }

    @Override
    public List<AuthPermissionInfoRespVO.MenuVO> getUserMenusByUserId(Long userId) {
        return this.getUserMenuInfosByUserId(userId).stream().map(AuthConvert.INSTANCE::convertTreeNode).toList();
    }

    @Override
    public List<MenuDO> getUserMenuInfosByUserId(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        String authKey = USER_AUTH_CACHE_PREFIX + userId;
        String value = redisCacheService.hashGet(authKey, AuthCacheEnum.AUTH_USER_MENU.getAuthField(), String.class);
        List<MenuDO> menuList = JSONObject.parseArray(value, MenuDO.class);
        if (menuList == null) {
            // 从数据库中查询菜单并更新到缓存中
            Set<Long> roleIds = this.getRoleIdsByUserId(userId);
            if (CollectionUtil.isEmpty(roleIds)) {
                return new ArrayList<>();
            }
            List<RoleMenuDO> roleMenus = roleMenuMapper.selectListByRoleId(roleIds);
            if (CollectionUtil.isEmpty(roleMenus)) {
                return new ArrayList<>();
            }
            menuList = menuMapper.selectByMenuIds(
                    roleMenus.stream()
                            .map(RoleMenuDO::getMenuId)
                            .collect(Collectors.toSet())
            ).stream().toList();
            redisCacheService.hashPut(authKey, AuthCacheEnum.AUTH_USER_MENU.getAuthField(), JSONObject.toJSONString(menuList), CACHE_EXPIRE_TIME);
        }
        return menuList;
    }

    @Override
    public Set<String> getUserPermission(Long userId) {
        List<AuthPermissionInfoRespVO.MenuVO> menus = this.getUserMenusByUserId(userId);
        return menus.stream().map(AuthPermissionInfoRespVO.MenuVO::getPermission).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getUserRolePermission(Long userId) {
        List<RoleDO> roles = this.getUserRoleDosById(userId);
        return roles.stream().map(RoleDO::getCode).collect(Collectors.toSet());
    }

    @Override
    public void invalidUserAuthCacheById(Long userId, String auth) {
        String authKey = USER_AUTH_CACHE_PREFIX + userId;
        redisCacheService.hashDel(authKey, new String[]{auth});
    }

    @Override
    public void invalidUserAuth(Long userId) {
        String authKey = USER_AUTH_CACHE_PREFIX + userId;
        redisCacheService.invalidate(authKey);
    }

    @Override
    public void refreshSystemUserAuthCache(Long tenantId) {
        log.info("[刷新系统用户的权限缓存]，tenantId:{}", tenantId);
        // 查询租户下的所有用户
        List<AdminUserDO> adminUserInfos = adminUserMapper.selectList(
                new LambdaQueryWrapperX<AdminUserDO>()
                        .eq(AdminUserDO::getTenantId, tenantId)
        );
        if (CollectionUtil.isEmpty(adminUserInfos)) {
            return;
        }
        // 遍历用户，删除用户的缓存
        for (AdminUserDO adminUserInfo : adminUserInfos) {
            this.invalidUserAuth(adminUserInfo.getId());
        }
        log.info("[刷新系统用户的权限缓存]，完成，tenantId:{}", tenantId);
    }
}
