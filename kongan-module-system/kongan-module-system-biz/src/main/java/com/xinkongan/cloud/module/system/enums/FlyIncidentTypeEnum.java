package com.xinkongan.cloud.module.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.xinkongan.cloud.module.system.enums.FlyProgressDetailTypeEnum.*;

/**
 * <AUTHOR>
 * @date 2025/2/17
 */
@Getter
@AllArgsConstructor
public enum FlyIncidentTypeEnum {

    JOB_ISSUED(JOB_PROGRESS, "任务已下发"),
    HAVE_TAKEN_OFF(JOB_PROGRESS, "无人机已起飞"),
    ARRIVAL_WAYPOINT(JOB_PROGRESS, "无人机已到达第%s个航点"),
    ARRIVAL_ALARM_WAYPOINT(JOB_PROGRESS, "无人机已到达警情点"),
    PAUSE_WAYLINE(JOB_PROGRESS, "%s暂停航线"),
    RESUME_WAYLINE(JOB_PROGRESS, "%s恢复航线"),
    DRC_ENTER_MODE(JOB_PROGRESS, "%s进入远程控制"),
    DRC_ENTER_MODE_RETURN(JOB_PROGRESS, "%s退出远程控制"),
    RETURN_FLIGHT(JOB_PROGRESS, "无人机返航"),
    LANDED(JOB_PROGRESS, "无人机降落"),
    TASK_OVER(JOB_PROGRESS, "任务已完成"),
    REMOTE_SHOUTING_START(JOB_PROGRESS, "开启喊话"),
    REMOTE_SHOUTING_OVER(JOB_PROGRESS, "停止喊话"),
    LIVE_RECORD_START(ACTION, "开启录制"),
    LIVE_RECORD_OVER(ACTION, "停止录制"),
    PANO_TAKE(ACTION,"全景图拍照"),
    VOICE_START(ACTION,"开启喊话"),
    VOICE_OVER(ACTION,"停止喊话"),
    HMS(HMS_INFO,"%s"),
    ;

    private final FlyProgressDetailTypeEnum type;
    private final String incident;

    public String getIncident(Object... params) {
        return String.format(incident, params);
    }

}
