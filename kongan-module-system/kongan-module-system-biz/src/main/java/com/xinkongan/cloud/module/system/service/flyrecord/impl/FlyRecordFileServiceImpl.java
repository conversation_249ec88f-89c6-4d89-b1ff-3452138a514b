package com.xinkongan.cloud.module.system.service.flyrecord.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.common.constant.BusinessTopicConstant;
import com.xinkongan.cloud.framework.common.constant.SystemEnvConst;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.common.util.io.FileUtils;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.datapermission.core.util.DataPermissionUtils;
import com.xinkongan.cloud.framework.env.config.SystemEnv;
import com.xinkongan.cloud.framework.mq.core.send.IRocketMQSendService;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.algorithm.api.exception.ExceptionApi;
import com.xinkongan.cloud.module.algorithm.dto.ExceptionDTO;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.FlyFileDTO;
import com.xinkongan.cloud.module.system.api.live.dto.record.AgoraRecordPayloadDTO;
import com.xinkongan.cloud.module.system.api.live.dto.record.LiveRecordCacheDTO;
import com.xinkongan.cloud.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import com.xinkongan.cloud.module.system.controller.admin.file.vo.file.FileCreateReqVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordFileVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.UploadFileReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.dal.dataobject.file.FileDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyDO;
import com.xinkongan.cloud.module.system.dal.mysql.flyrecord.FlyRecordFileMapper;
import com.xinkongan.cloud.module.system.enums.flyrecord.DeleteType;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileSourceEnum;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileTypeEnum;
import com.xinkongan.cloud.module.system.framework.file.core.client.s3.S3FileClientConfig;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordContrastCacheService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordFileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import com.xinkongan.cloud.module.system.service.live.ILiveCacheService;
import com.xinkongan.cloud.module.system.service.material.IMaterialManageService;
import com.xinkongan.cloud.module.system.service.permission.PermissionService;
import com.xinkongan.cloud.module.system.service.task.IJobFlyService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.module.system.util.file.AliOssUtils;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.media.dto.FileUploadCallback;
import com.xinkongan.cloud.sdk.dock.cloudapi.media.dto.FileUploadCallbackFile;
import com.xinkongan.cloud.sdk.dock.cloudapi.media.dto.FlightTask;
import com.xinkongan.cloud.sdk.dock.cloudapi.media.dto.UploadCallbackFileMetadata;
import com.xinkongan.cloud.sdk.geo.utils.LocationUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.powerjob.worker.log.OmsLogger;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.xinkongan.cloud.framework.common.exception.enums.GlobalErrorCodeConstants.ID_NOT_EXISTS;
import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.FLY_RECORD_FILE_NOT_EXISTS;

/**
 * @Description 飞行记录文件Service
 * <AUTHOR>
 * @Date 2025/2/12 14:36
 */
@Slf4j
@Service
public class FlyRecordFileServiceImpl implements IFlyRecordFileService {

    @Resource
    private IFlyRecordService flyRecordService;// 飞行记录Service
    @Resource
    private FileService fileService;// 文件Service
    @Resource
    private FlyRecordFileMapper flyRecordFileMapper;// 飞行记录文件Mapper
    @Resource
    private SystemEnv systemEnv;
    @Resource
    private IRedisCacheService redisCacheService;// 缓存Service
    @Resource
    private LocationUtil locationUtil;// 地理位置工具类
    @Resource
    private IMaterialManageService materialManageService;
    @Resource
    private DockDeviceService dockDeviceService;// 机场设备Service
    @Resource
    private ILiveCacheService liveCacheService;// 直播缓存Service
    @Resource
    private IFlyRecordContrastCacheService flyRecordContrastCacheService;
    @Resource
    private WebSocketSendApi webSocketSendApi;
    @Resource
    private IJobService jobService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private IRocketMQSendService rocketMQSendService;
    @Resource
    private ExceptionApi exceptionApi;
    @Resource
    private IJobFlyService jobFlyService;


    // 需要保存的文件后缀
    private static final String[] FILE_SUFFIX_LIST = new String[]{"MP4", "JPG", "JPEG", "PNG", "mp4", "jpg", "jpeg", "png"};

    private static final String CODE = "fly-record-file-code";
    private static final String VIDEO_CODE = "fly-record-video-code";

    @Override
    public void processDockFileCallback(String dockSn, Long timestamp, FileUploadCallback fileUploadCallback) {
        FileUploadCallbackFile file = fileUploadCallback.getFile();
        // 查询当前飞行记录
        // 飞行id
        Long flyId = file.getExt().getFlightId() == null ? null : Long.parseLong(file.getExt().getFlightId());
        // 根据飞行id查询飞行记录
        FlyRecordDO flyRecordDO = flyRecordService.getByFlyId(flyId);
        if (flyRecordDO == null) {
            log.error("飞行记录不存在:{}", flyId);
            return;
        }
        Integer uploadedFileCount = flyRecordDO.getUploadedFileCount();
        FlightTask flightTask = fileUploadCallback.getFlightTask();
        if (flightTask != null) {
            if (uploadedFileCount == null || uploadedFileCount < flightTask.getUploadedFileCount()) {
                flyRecordDO.setUploadedFileCount(flightTask.getUploadedFileCount());
            }
            flyRecordDO.setExpectedFileCount(flightTask.getExpectedFileCount());
            // 发送websocket通知前端更新进度
            sendWebsocket(BizCodeEnum.FLY_RECORD_FILE_MEDIA_UPDATE, flyRecordDO);
            flyRecordService.updateById(FlyRecordDO.builder().id(flyRecordDO.getId()).uploadedFileCount(flyRecordDO.getUploadedFileCount()).expectedFileCount(flyRecordDO.getExpectedFileCount()).build());
        }
        // 只处理需要处理的文件类型
        String fileSuffix = file.getName().substring(file.getName().lastIndexOf(".") + 1);
        if (Arrays.asList(FILE_SUFFIX_LIST).contains(fileSuffix)) {
            // 文件完整url
            String url = fileService.getUrlByObjectKey(file.getObjectKey());
            // 图片文件的metaData
            JSONObject metaData = null;
            if (fileSuffix.equalsIgnoreCase("jpeg")) {
                metaData = FileUtils.parsePicMetaData(url);
            }
            // 解析文件类型
            FileTypeEnum fileType = parseFileType(fileSuffix, metaData);
            if (fileSuffix.equals("MP4") || fileSuffix.equals("mp4")) {
                processDJIMp4BugActionTime(file.getMetadata());
            }
            FlyRecordFileDO flyRecordFileDO = FlyRecordFileDO.builder().dockSn(dockSn).flyRecordId(flyRecordDO.getId()).fileType(fileType.getCode()).url(url).lat(String.valueOf(file.getMetadata().getShootPosition().getLat())).lon(String.valueOf(file.getMetadata().getShootPosition().getLng())).actionTime(file.getMetadata().getCreatedTime()).lensType(getLensType(file.getName())).path(file.getObjectKey()).fileSource(FileSourceEnum.RECORD.getCode()).absoluteAltitude(String.valueOf(file.getMetadata().getAbsoluteAltitude())).relativeAltitude(String.valueOf(file.getMetadata().getRelativeAltitude())).name(file.getName()).build();
            flyRecordFileDO.setDeptId(flyRecordDO.getDeptId());
            flyRecordFileDO.setTenantId(flyRecordDO.getTenantId());

            // 机场上报文件根据jobId
            JobFlyDO jobFlyDO = jobFlyService.getJobFlyById(flyId);
            if (jobFlyDO != null) {
                flyRecordFileDO.setCreator(jobFlyDO.getCreator());
            }

            // 如果是oss查询文件大小
            if (SystemEnvConst.FileStorageTypeEnum.ALI_OSS.equals(systemEnv.getFileStorageType())) {
                Long fileSize = AliOssUtils.getSize((S3FileClientConfig) fileService.getFileClientConfig(), file.getObjectKey());
                if (fileSize != null) {
                    flyRecordFileDO.setFileSize(fileSize);
                }
                // 地理位置逆解析
                flyRecordFileDO.setAddress(getFileAddress(flyRecordFileDO));
            }
            Long fileId = saveFileRecord(flyRecordFileDO);
            // 保存文件id
            if (fileId != null) {
                flyRecordFileDO.setFileId(fileId);
            }
            // 如果是图片，设置图片宽高
            if (FileTypeEnum.IMAGE.equals(fileType) && metaData != null) {
                setPicWidthAndHeight(flyRecordFileDO, metaData);
                // 截取图片名称
                flyRecordFileDO.setName(getName(file.getObjectKey()));
                flyRecordFileDO.setFileSource(FileSourceEnum.PHOTOGRAPH.getCode());
            } else if (FileTypeEnum.VIDEO.equals(fileType)) {
                updateVideoName(flyRecordFileDO);
            }
            log.info("保存飞行记录文件:{}", JSONUtil.toJsonStr(flyRecordFileDO));
            saveFlyRecordFileDO(flyRecordFileDO);

            if (Objects.equals(fileType, FileTypeEnum.PANO_IMAGE)) {
                // 全景图直接走内部解析逻辑
                materialManageService.loadRoutePointPanorama(flyRecordDO.getTenantId(), flyRecordDO.getDeptId(), fileId, flyRecordDO.getJobId());
            }
        }
    }

    /**
     * 补偿处理大疆录制视频的时间回调BUG +8 注意：每次升级固件需要检查大疆是否已修复
     *
     * <AUTHOR>
     * @date 2025/3/19 10:52
     **/
    private void processDJIMp4BugActionTime(UploadCallbackFileMetadata metadata) {
        // 目前为止 直到固件3205的视频录制的时间都是少八个小时的 补偿处理+8
        // 比当前时间的时间差大于8小时的 认为是大疆的BUG
        if (metadata.getCreatedTime().isBefore(LocalDateTime.now().minusHours(8)) || metadata.getCreatedTime().isAfter(LocalDateTime.now().plusHours(8))) {
            log.info("补偿处理大疆录制视频的时间回调BUG +8");
            metadata.setCreatedTime(metadata.getCreatedTime().isBefore(LocalDateTime.now().minusHours(8)) ?
                    metadata.getCreatedTime().plusHours(8) : metadata.getCreatedTime().minusHours(8));
        }
    }

    /**
     * 处理声网回调 保存录制文件 {"cname":"1581F6Q8D241P00CM55E","details":{"fileList":"video/349a51625f4f194e3c170da859832a3f_1581F6Q8D241P00CM55E_0.mp4","msgName":"cloud_recording_file_infos"},"sendts":1739848430561,"sequence":17,"serviceScene":"rtc_record","serviceType":0,"uid":12296006}
     *
     * <AUTHOR>
     * @date 2025/2/18 11:34
     **/
    @Override
    public void processAgoraCallback(AgoraRecordPayloadDTO payload) {
        log.info("保存声网回调视频录制 {}", JSONUtil.toJsonStr(payload));
        String deviceSn = payload.getCname();
        if (deviceSn == null) {
            log.info("cname为空");
            return;
        }
        AgoraRecordPayloadDTO.Details details = payload.getDetails();
        if (details == null) {
            log.info("details为空");
            return;
        }
        String fileList = details.getFileList();
        if (fileList == null) {
            log.info("fileList为空");
            return;
        }
        if (!fileList.contains("mp4")) {
            log.info("fileList不包含mp4");
            return;
        }
        // 根据sn获取设备信息
        DockDeviceDO byDBDeviceSn = dockDeviceService.getByDBDeviceSn(deviceSn);
        if (byDBDeviceSn == null) {
            log.info("设备信息为空");
            return;
        }
        LiveRecordCacheDTO liveRecordCache = liveCacheService.getLiveRecordCache(byDBDeviceSn.getDeviceSn(), byDBDeviceSn.getTenantId());
        if (liveRecordCache == null) {
            log.info("直播录制缓存为空");
            return;
        }
        // 根据flyId获取飞行记录id
        FlyRecordDO flyRecordDO = flyRecordService.getByFlyId(liveRecordCache.getFlyId());
        if (flyRecordDO == null) {
            log.info("飞行记录为空");
            return;
        }

        FlyRecordFileDO flyRecordFileDO = FlyRecordFileDO.builder().flyRecordId(flyRecordDO.getId()).fileType(FileTypeEnum.VIDEO.getCode()).fileSource(FileSourceEnum.RECORD.getCode()).actionTime(DateUtil.toLocalDateTime(new Date(liveRecordCache.getTs() * 1000))).path(fileList).url(fileService.getUrlByObjectKey(fileList)).build();
        flyRecordFileDO.setTenantId(flyRecordDO.getTenantId());
        flyRecordFileDO.setDeptId(flyRecordDO.getDeptId());
        TenantContextHolder.setTenantId(flyRecordFileDO.getTenantId());
        // 如果是oss查询文件大小
        if (SystemEnvConst.FileStorageTypeEnum.ALI_OSS.equals(systemEnv.getFileStorageType())) {
            Long fileSize = AliOssUtils.getSize((S3FileClientConfig) fileService.getFileClientConfig(), fileList);
            if (fileSize != null) {
                flyRecordFileDO.setFileSize(fileSize);
            }
        }
        Long fileId = saveFileRecord(flyRecordFileDO);
        // 保存文件id
        if (fileId != null) {
            flyRecordFileDO.setFileId(fileId);
        }
        updateVideoName(flyRecordFileDO);
        log.info("保存飞行记录文件:{}", JSONUtil.toJsonStr(flyRecordFileDO));
        saveFlyRecordFileDO(flyRecordFileDO);
        // 删除直播录制缓存
        liveCacheService.removeLiveRecordCache(liveRecordCache.getDroneSn(), liveRecordCache.getTenantId());
        try {
            // 10秒后删除此文件夹除了mp4外外其他文件
            redisCacheService.put(RedisKeyConstants.AGORA_RECORD_FILE_DELETE + flyRecordDO.getId(), 1, 10L, () -> {
                fileService.deleteAssociatedNonMp4Files(fileList);
            });
        } catch (Exception e) {
            log.error("声网删除除了mp4外此文件夹外其他文件失败， url:{}", fileList, e);
        }

    }

    /**
     * 查询飞行记录下的所有文件
     *
     * <AUTHOR>
     * @date 2025/2/13 15:17
     **/
    @Override
    public List<FlyRecordFileDO> listByFlyRecordId(Long flyRecordId) {
        if (flyRecordId == null) {
            throw exception(ID_NOT_EXISTS);
        }
        List<FlyRecordFileDO> flyRecordFileDOS = flyRecordFileMapper.selectList(Wrappers.<FlyRecordFileDO>lambdaQuery().eq(FlyRecordFileDO::getFlyRecordId, flyRecordId));
        if (CollUtil.isEmpty(flyRecordFileDOS)) {
            return List.of();
        }
        return flyRecordFileDOS;
    }

    @Override
    public FlyRecordFileVO getFlyRecordFileVO(Long id) {
        AtomicReference<FlyRecordFileDO> atomicReference = new AtomicReference<>();
        DataPermissionUtils.executeIgnore(() -> atomicReference.set(getById(id)));
        FlyRecordFileDO flyRecordFileDO = atomicReference.get();
        if (flyRecordFileDO == null) {
            throw exception(FLY_RECORD_FILE_NOT_EXISTS);
        }
        FlyRecordFileVO flyRecordFileVO = BeanUtil.toBean(flyRecordFileDO, FlyRecordFileVO.class);
        FlyRecordFileDO fileDO = flyRecordFileMapper.selectById(id);
        flyRecordFileVO.setIsDataScope(fileDO != null);
        return flyRecordFileVO;
    }

    @DataPermission(enable = false)
    @Override
    public FlyRecordFileDO getById(Long id) {
        FlyRecordFileDO flyRecordFileDO = flyRecordFileMapper.selectById(id);
        if (flyRecordFileDO != null) {
            Long flyRecordId = flyRecordFileDO.getFlyRecordId();
            DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(SecurityFrameworkUtils.getLoginUserId());
            if (deptDataPermission != null && deptDataPermission.getDeptIds() != null) {
                Boolean hasFlyRecordPermission = flyRecordService.hasFlyRecordPermission(flyRecordId, SecurityFrameworkUtils.getLoginUser().getTenantId(), deptDataPermission.getDeptIds());
                if (hasFlyRecordPermission) {
                    return flyRecordFileDO;
                }
            }
        }
        // 没有权限、数据 返回空
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteById(Long id) {
        FlyRecordFileDO flyRecordFileDO = flyRecordFileMapper.selectById(id);
        ExceptionDTO exceptionDTO = exceptionApi.getById(id).getCheckedData();
        if (flyRecordFileDO == null && exceptionDTO == null) {
            throw exception(FLY_RECORD_FILE_NOT_EXISTS);
        }
        if (flyRecordFileDO != null && flyRecordFileDO.getExceptionId() == null) {
            // 删除飞行记录文件
            // 删除文件记录
            int i = flyRecordFileMapper.deleteById(id);
            FileTypeEnum fileTypeEnum = FileTypeEnum.find(flyRecordFileDO.getFileType());
            // 删除桶文件
            if (!FileTypeEnum.PANO_IMAGE.equals(fileTypeEnum)) {
                fileService.deleteBatchIds(List.of(flyRecordFileDO.getFileId()));
            }
            // 关联删除异常
            if (flyRecordFileDO.getExceptionId() != null) {
                exceptionApi.deleteById(flyRecordFileDO.getExceptionId()).getCheckedData();
            }
            // 删除图片消息推送
            rocketMQSendService.syncSend(BusinessTopicConstant.FLY_FILE_DELETE_PUSH, List.of(id));
            return i > 0;
        } else {
            // 删除异常
            return exceptionApi.deleteByIds(List.of(id)).getCheckedData();
        }
    }

    @Override
    public void deleteByUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return;
        }
        flyRecordFileMapper.delete(
                new LambdaQueryWrapperX<FlyRecordFileDO>()
                        .eq(FlyRecordFileDO::getUrl, url)
        );
    }

    /**
     * 导入文件/截图
     *
     * <AUTHOR>
     * @date 2025/2/14 9:55
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long uploadFile(UploadFileReqVO uploadFileReqVO) {
        Long flyRecordId = uploadFileReqVO.getFlyRecordId();
        Long fileId = uploadFileReqVO.getFileId();
        FlyRecordDO flyRecordDO = flyRecordService.getById(flyRecordId);
        FileDO fileRecordById = fileService.getFileRecordById(fileId);
        FlyRecordFileDO recordFileDO = BeanUtil.toBean(uploadFileReqVO, FlyRecordFileDO.class);
        recordFileDO.setUrl(fileRecordById.getUrl());
        recordFileDO.setPath(fileRecordById.getPath());
        recordFileDO.setFileSize(fileRecordById.getSize());
        FileSourceEnum fileSourceEnum = FileSourceEnum.find(uploadFileReqVO.getFileSource());
        recordFileDO.setFlyRecordId(flyRecordDO.getId());
        recordFileDO.setFileId(uploadFileReqVO.getFileId());
        recordFileDO.setDockSn(flyRecordDO.getDockSn());
        // 处理图片名称
        if (FileSourceEnum.SCREENSHOT.equals(fileSourceEnum)) {
            // 截图
            // 查询飞行记录文件里找已存在的截图数量
            boolean hasSerialNumCache = redisCacheService.hasSerialNumCache(CODE, String.valueOf(flyRecordId));
            int startNum = 1;
            if (!hasSerialNumCache) {
                Long count = flyRecordFileMapper.selectCount(Wrappers.<FlyRecordFileDO>lambdaQuery().eq(FlyRecordFileDO::getFlyRecordId, flyRecordId).eq(FlyRecordFileDO::getFileType, FileSourceEnum.SCREENSHOT.getCode()));
                startNum = count == null || count == 0 ? 1 : count.intValue() + 1;
            }
            // 获取最新流水号
            Integer serialNum = redisCacheService.getSerialNum(CODE, String.valueOf(flyRecordId), startNum);
            recordFileDO.setName("截图" + serialNum);
        } else if (FileSourceEnum.IMPORT.equals(fileSourceEnum)) {
            // 导入
            recordFileDO.setName(getName(fileRecordById.getUrl()));
        }
        recordFileDO.setDeptId(flyRecordDO.getDeptId());

        // 地理位置逆解析
        recordFileDO.setAddress(getFileAddress(recordFileDO));
        saveFlyRecordFileDO(recordFileDO);
        return recordFileDO.getId();
    }

    @Override
    public void deleteByFlyRecord(Long id) {
        // 查询飞行记录下的所有文件
        List<FlyRecordFileDO> flyRecordFileList = flyRecordFileMapper.selectList(Wrappers.<FlyRecordFileDO>lambdaQuery().eq(FlyRecordFileDO::getFlyRecordId, id));
        for (FlyRecordFileDO flyRecordFileDO : flyRecordFileList) {
            deleteById(flyRecordFileDO.getId());
        }
    }

    /**
     * 删除未被标记为重要的飞行记录文件
     *
     * <AUTHOR>
     * @date 2025/2/17 10:20
     **/
    @Transactional(rollbackFor = Exception.class)
    @DataPermission(enable = false)
    @Override
    public void deleteUnImportantFile(Long deptId, LocalDateTime endTime, DeleteType deleteType, OmsLogger omsLogger) {
        omsLogger.info("删除未被标记为重要的飞行记录文件, deptId:{}, endTime:{}, deleteType:{}", deptId, endTime, deleteType);
        // 查询指定类型的飞行记录文件
        List<FlyRecordFileDO> fileDOList = flyRecordFileMapper.selectUnImportantFile(deptId, endTime, deleteType.getType());
        for (FlyRecordFileDO fileDO : fileDOList) {
            omsLogger.info("删除飞行记录文件:{}", JSONUtil.toJsonStr(fileDO));
            TenantUtils.executeIgnore(() -> deleteById(fileDO.getId()));
        }
    }

    @Override
    public List<FlyRecordFileDO> listByIds(Set<Long> fileIds) {
        List<FlyRecordFileDO> flyRecordFileDOS = flyRecordFileMapper.selectList(Wrappers.<FlyRecordFileDO>lambdaQuery().in(FlyRecordFileDO::getId, fileIds));
        if (CollUtil.isEmpty(flyRecordFileDOS)) {
            return List.of();
        }
        return flyRecordFileDOS;
    }

    @Override
    public Map<Long, FlyRecordFileDO> mapByIds(Set<Long> fileIds) {
        List<FlyRecordFileDO> flyRecordFileDOS = listByIds(fileIds);
        return flyRecordFileDOS.stream().collect(Collectors.toMap(FlyRecordFileDO::getId, f -> f));
    }

    @Override
    public List<FlyFileDTO> listByDockSn(String dockSn) {
        JobFlyInfo jobFlyInfo = jobService.getExecTaskCacheByDockSn(dockSn);
        if (jobFlyInfo != null && jobFlyInfo.getFlyRecordId() != null) {
            List<FlyRecordFileDO> flyRecordFileDOS = listByFlyRecordId(jobFlyInfo.getFlyRecordId());
            List<FlyFileDTO> fileDTOList = new ArrayList<>();
            List<FlyFileDTO> noExceptionPics = flyRecordFileDOS.stream()
                    .filter(flyRecordFileDO -> flyRecordFileDO.getExceptionId() == null)
                    .map(flyRecordFileDO -> BeanUtil.toBean(flyRecordFileDO, FlyFileDTO.class)).toList();
            if (CollUtil.isNotEmpty(noExceptionPics)) {
                fileDTOList.addAll(noExceptionPics);
            }
            try {
                // 查询异常列表
                List<ExceptionDTO> exceptionDTOList = exceptionApi.getExceptionListByFlyId(jobFlyInfo.getFlyId()).getCheckedData();
                if (CollUtil.isNotEmpty(exceptionDTOList)) {
                    List<FlyFileDTO> exceptionPics = exceptionDTOList.stream().map(exceptionDTO -> {
                        FlyFileDTO flyFileDTO = FlyFileDTO.builder().id(exceptionDTO.getId()).exceptionId(exceptionDTO.getId())
                                .flyRecordId(jobFlyInfo.getFlyRecordId())
                                .fileType(FileTypeEnum.IMAGE.getCode()).fileSource(FileSourceEnum.ALGORITHM.getCode())
                                .dockSn(exceptionDTO.getDockSn()).url(exceptionDTO.getUrl())
                                .build();
                        BeanUtil.copyProperties(exceptionDTO, flyFileDTO);
                        return flyFileDTO;
                    }).toList();
                    if (CollUtil.isNotEmpty(exceptionPics)) {
                        fileDTOList.addAll(exceptionPics);
                    }
                }
            } catch (Exception e) {
                log.error("查询异常列表失败", e);
            }
            // 根据时间排序
            fileDTOList.forEach(flyFileDTO -> {
                if (flyFileDTO.getActionTime() == null) {
                    flyFileDTO.setActionTime(flyFileDTO.getCreateTime());
                }
            });
            fileDTOList.sort(Comparator.comparing(FlyFileDTO::getActionTime));
            return fileDTOList;
        }
        return List.of();
    }


    @Override
    public Long createFileRecord(FlyRecordFileDO flyRecordFileDO) {
        flyRecordFileMapper.insert(flyRecordFileDO);
        return flyRecordFileDO.getId();
    }

    @Override
    public Integer countByFlyIdWithoutException(List<Long> flyId) {
        Integer count = flyRecordFileMapper.countByFlyIdWithoutException(flyId);
        return count == null ? 0 : count;
    }

    /**
     * 统计飞行记录下的完整素材数量
     * 修改说明：扩展统计逻辑，同时统计文件素材和异常记录
     * 数据来源：system_fly_record_file表 + algorithm_exception表
     * 关联逻辑：通过fly_record_id字段关联
     */
    @Override
    public Map<Long, Integer> flyRecordMaterialCountMap(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Map.of();
        }
        // 使用UNION查询同时统计文件素材和异常记录数量
        List<Map<String, Object>> list = flyRecordFileMapper.flyRecordMaterialCountMap(ids);
        if (CollUtil.isEmpty(list)) {
            return Map.of();
        }
        Map<Long, Integer> result = new HashMap<>();
        list.forEach(map -> {
            Object key = map.get("key");
            Object value = map.get("value");
            if (key != null) {
                result.put((Long) key, value == null ? 0 : Integer.parseInt(value.toString()));
            }
        });
        return result;
    }

    @DataPermission(enable = false)
    @Override
    public FlyRecordFileDO getByIdIgnorePermission(Long id) {
        return flyRecordFileMapper.selectById(id);
    }

    @Override
    public void updateById(FlyRecordFileDO flyRecordFileDO) {
        flyRecordFileMapper.updateById(flyRecordFileDO);
    }

    @Override
    public Boolean deleteBatchByExceptionIds(List<Long> exceptionIds) {
        List<FlyRecordFileDO> flyRecordFileDOS = flyRecordFileMapper.selectList(Wrappers.<FlyRecordFileDO>lambdaQuery().in(FlyRecordFileDO::getExceptionId, exceptionIds));
        if (CollUtil.isEmpty(flyRecordFileDOS)) {
            return true;
        }
        List<Long> fileIds = flyRecordFileDOS.stream().map(FlyRecordFileDO::getFileId).toList();
        List<Long> ids = flyRecordFileDOS.stream().map(FlyRecordFileDO::getId).toList();
        flyRecordFileMapper.deleteByIds(ids);
        // 删除图片
        fileService.deleteBatchIds(fileIds);
        return true;
    }

    @Override
    public Long countPicByJobId(Long jobId) {
        return flyRecordFileMapper.selectCount(Wrappers.<FlyRecordFileDO>lambdaQuery()
                .eq(FlyRecordFileDO::getFlyRecordId, jobId)// 当前任务
                .in(FlyRecordFileDO::getFileType, FileTypeEnum.IMAGE.getCode())// 图片
                .isNull(FlyRecordFileDO::getExceptionId));// 不是异常
    }

    @Override
    public Long countMediaCountByJobId(Long flyrecordId) {
        Long count = flyRecordFileMapper.selectCount(
                Wrappers.<FlyRecordFileDO>lambdaQuery()
                        .eq(FlyRecordFileDO::getFlyRecordId, flyrecordId)
                        .isNull(FlyRecordFileDO::getExceptionId)
        );
        Long exceptionCount = flyRecordFileMapper.countExceptionMediaByFlyRecordId(flyrecordId);
        return count + exceptionCount;
    }


    @Override
    public Long countPhotographByJobIdOptimized(Long jobId) {
        return flyRecordFileMapper.countPhotographByJobIdOptimized(jobId);
    }

    /**
     * 截取图片名称
     *
     * <AUTHOR>
     * @date 2025年2月14日15:53:48
     **/
    private String getFileAddress(FlyRecordFileDO recordFileDO) {
        try {
            String lat = recordFileDO.getLat();
            String lon = recordFileDO.getLon();
            if (lat != null && lon != null && !"0.0".equals(lat) && !"0.0".equals(lon)) {
                return locationUtil.getLocationByWg84(lon, lat);
            }
        } catch (Exception e) {
            log.error("获取地址失败", e);
        }
        return null;
    }

    /**
     * 保存文件记录
     *
     * <AUTHOR>
     * @date 2025/2/12 17:04
     **/
    private Long saveFileRecord(FlyRecordFileDO flyRecordFileDO) {
        FileCreateReqVO createReqVO = FileCreateReqVO.builder()
                .url(flyRecordFileDO.getUrl())
                .name(flyRecordFileDO.getPath())
                .path(flyRecordFileDO.getPath())
                .name(flyRecordFileDO.getName())
                .size(flyRecordFileDO.getFileSize())
                .deptId(flyRecordFileDO.getDeptId())
                .tenantId(flyRecordFileDO.getTenantId()).build();
        return fileService.createFile(createReqVO);
    }


    public static void main(String[] args) {
        FlyRecordFileServiceImpl flyRecordFileService = new FlyRecordFileServiceImpl();
        String url = "https://v3-test.oss-cn-hangzhou.aliyuncs.com/dock_media/1392536385093632/2025/3/20/1427417517817856/DJI_202503201435_009_1427417517817856/DJI_20250320144449_0002_V.jpeg";

        JSONObject metaData = FileUtils.parsePicMetaData(url);
        FileTypeEnum fileType = flyRecordFileService.parseFileType("jpeg", metaData);
    }

    /**
     * 解析文件类型
     *
     * <AUTHOR>
     * @date 2025/2/13 9:32
     **/
    private FileTypeEnum parseFileType(String fileSuffix, JSONObject metaData) {
        if ("mp4".equalsIgnoreCase(fileSuffix)) {
            return FileTypeEnum.VIDEO;
        } else {
            // 图片查看是否是全景图
            JSONObject xPKeywords = (JSONObject) metaData.get("XPKeywords");
            if (xPKeywords == null) {
                return FileTypeEnum.IMAGE;
            }
            Object xpKeywordsValue = xPKeywords.get("value");
            if (xpKeywordsValue == null) {
                return FileTypeEnum.IMAGE;
            }
            // 检查值是否为 "pano"
            if ("pano".equals(decodeXPKeywords((String) xpKeywordsValue))) {
                return FileTypeEnum.PANO_IMAGE;
            } else {
                return FileTypeEnum.IMAGE;
            }
        }
    }

    // 解码 XPKeywords 的值（UTF-16 LE 编码）
    private String decodeXPKeywords(String encodedValue) {
        String[] parts = encodedValue.split(" ");
        StringBuilder decoded = new StringBuilder();
        for (int i = 0; i < parts.length; i += 2) {
            decoded.append((char) Integer.parseInt(parts[i]));
        }
        return decoded.toString();
    }

    /**
     * 设置图片宽高
     *
     * <AUTHOR>
     * @date 2025/2/13 9:57
     **/
    private void setPicWidthAndHeight(FlyRecordFileDO flyRecordFileDO, JSONObject metaData) {
        // 宽
        JSONObject imageWidthObj = (JSONObject) metaData.get("ImageWidth");
        if (imageWidthObj != null) {
            String imageWidth = (String) imageWidthObj.get("value");
            flyRecordFileDO.setWidth(Integer.valueOf(imageWidth));
        }
        // 高
        JSONObject imageHeightObj = (JSONObject) metaData.get("ImageHeight");
        if (imageHeightObj != null) {
            String imageWidth = (String) imageHeightObj.get("value");
            flyRecordFileDO.setHeight(Integer.valueOf(imageWidth));
        }
    }

    /**
     * 保存文件记录
     *
     * @param flyRecordFileDO 飞行记录文件
     * @return 文件id
     */
    private void saveFlyRecordFileDO(FlyRecordFileDO flyRecordFileDO) {
        flyRecordFileMapper.insert(flyRecordFileDO);
        if (FileTypeEnum.IMAGE.equals(FileTypeEnum.find(flyRecordFileDO.getFileType()))) {
            // 保存位置信息
            flyRecordContrastCacheService.addContrastFileCache(flyRecordFileDO);
        }
        FlyRecordFileDO fileDO = flyRecordFileMapper.selectById(flyRecordFileDO.getId());
        FlyFileDTO flyFileDTO = BeanUtil.copyProperties(fileDO, FlyFileDTO.class);
        // 发送飞行记录图片生成消息
        rocketMQSendService.syncSend(BusinessTopicConstant.FLY_FILE_PUSH, flyFileDTO);
    }

    private String getLensType(String name) {
        // TODO 修改成字符匹配
        String[] split = name.split("\\.");
        return split[0].substring(split[0].length() - 1);
    }

    /**
     * 从文件名中提取名称（不包括路径和后缀）
     *
     * <AUTHOR>
     * @date 2025/2/13 15:44
     **/
    private String getName(String fileName) {
        // 直接返回文件名（不包括路径和后缀）
        int lastIndex = fileName.lastIndexOf('/');
        if (lastIndex != -1) {
            return fileName.substring(lastIndex + 1, fileName.lastIndexOf('.'));
        }
        return fileName;
    }

    private <T> void sendWebsocket(BizCodeEnum bizCodeEnum, T data) {
        try {
            // 发送直播开启成功消息给前端
            WebSocketMessageDTO<T> webSocketMessageDTO = WebSocketMessageDTO.<T>builder().tenantId(TenantContextHolder.getTenantId()).message(CustomWebSocketMessage.<T>builder().bizCode(bizCodeEnum.getCode()).data(data).build()).build();
            log.info("发送飞行记录文件消息 webSocketMessageDTO:{}", JSONUtil.toJsonStr(webSocketMessageDTO));
            webSocketSendApi.sendByTenant(webSocketMessageDTO);

        } catch (Exception e) {
            log.error("发送直播推流变更消息给前端失败", e);
        }
    }

    private void updateVideoName(FlyRecordFileDO flyRecordFileDO) {
        // 查询飞行记录文件里找已存在的截图数量
        boolean hasSerialNumCache = redisCacheService.hasSerialNumCache(VIDEO_CODE, String.valueOf(flyRecordFileDO.getFlyRecordId()));
        int startNum = 1;
        if (!hasSerialNumCache) {
            Long count = flyRecordFileMapper.selectCount(Wrappers.<FlyRecordFileDO>lambdaQuery().eq(FlyRecordFileDO::getFlyRecordId, flyRecordFileDO.getFlyRecordId()).eq(FlyRecordFileDO::getFileType, FileSourceEnum.RECORD.getCode()));
            startNum = count == null || count == 0 ? 1 : count.intValue() + 1;
        }
        // 获取最新流水号
        Integer serialNum = redisCacheService.getSerialNum(VIDEO_CODE, String.valueOf(flyRecordFileDO.getFlyRecordId()), startNum);
        flyRecordFileDO.setName("录制视频" + serialNum);
    }
}