package com.xinkongan.cloud.module.system.controller.admin.psdk;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.PSdkWidgetValueDTO;
import com.xinkongan.cloud.module.system.controller.admin.psdk.vo.VoiceSendToDockVO;
import com.xinkongan.cloud.module.system.dto.SpeakerStatusDTO;
import com.xinkongan.cloud.module.system.service.psdk.IPsdkService;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.dto.SpeakValues;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RestController
@Tag(name = "管理后台 - psdk控制")
@RequestMapping("/system/psdk")
public class PsdkController {


    @Resource
    private IPsdkService psdkService;


    @Operation(summary = "psdk控制-控件值设置")
    @PostMapping("/widget/set/{dockSn}")
    public CommonResult<Boolean> psdkWidgetValueSet(@PathVariable String dockSn,
                                                    @RequestBody PSdkWidgetValueDTO pSdkWidgetValueParams) {
        Boolean res = psdkService.setWidgetValue(dockSn, pSdkWidgetValueParams);
        return CommonResult.success(res);
    }

    @Operation(summary = "psdk控制-机场喊话")
    @PostMapping("/sendTextToDock")
    public CommonResult<Boolean> sendTextMessage(@Valid @RequestBody VoiceSendToDockVO voiceSendToDockVo) {
        Boolean res = psdkService.sendTextToDock(voiceSendToDockVo);
        return CommonResult.success(res);
    }

    @Operation(summary = "psdk控制-发送喊话文件")
    @PostMapping("/sendVoiceToDock")
    public CommonResult<Boolean> sendVoiceMessage(@Valid @RequestBody VoiceSendToDockVO voiceSendToDockVo) {
        Boolean res = psdkService.sendVoiceToDock(voiceSendToDockVo);
        return CommonResult.success(res);
    }

    @Operation(summary = "psdk控制-停止播放")
    @GetMapping("/speakerPlayStop")
    public CommonResult<Boolean> speakerPlayStop(@RequestParam("dockSn") @NotNull String dockSn) {
        Boolean res = psdkService.speakerPlayStop(dockSn);
        return CommonResult.success(res);
    }

    @Operation(summary = "psdk控制-是否负载喊话器")
    @GetMapping("/haveSpeaker")
    public CommonResult<Boolean> deviceList(@RequestParam("sn") @NotNull String sn) {
        Boolean res = psdkService.haveSpeaker(sn);
        return CommonResult.success(res);
    }


    @Operation(summary = "psdk控制-获取喊话器类型")
    @GetMapping("/payload/type/{dockSn}")
    public CommonResult<String> getPayloadType(@PathVariable String dockSn) {
        String type = psdkService.speakerType(dockSn);
        return CommonResult.success(type);
    }


    @Operation(summary = "psdk控制-获取喊话器负载值")
    @GetMapping("/payload/values/{dockSn}")
    public CommonResult<List<SpeakValues>> getPayloadValues(@PathVariable String dockSn) {
        List<SpeakValues> speakValues = psdkService.speakerStatus(dockSn);
        return CommonResult.success(speakValues);
    }


    @Operation(summary = "psdk控制-喊话器播放状态")
    @GetMapping("/status/{dockSn}")
    public CommonResult<SpeakerStatusDTO> getSpeakerStatus(@PathVariable String dockSn) {
        SpeakerStatusDTO speakerStatus = psdkService.getSpeakerStatus(dockSn);
        return CommonResult.success(speakerStatus);
    }
}
