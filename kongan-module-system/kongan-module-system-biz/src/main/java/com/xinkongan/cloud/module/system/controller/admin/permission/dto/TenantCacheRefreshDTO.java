package com.xinkongan.cloud.module.system.controller.admin.permission.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class TenantCacheRefreshDTO {

    @NotEmpty(message = "访问密钥不能为空")
    @Schema(description = "访问密钥")
    private String accessKey;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID")
    private Long tenantId;
}
