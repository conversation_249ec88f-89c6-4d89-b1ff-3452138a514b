package com.xinkongan.cloud.module.system.service.notice.stragy;

import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeDO;
import com.xinkongan.cloud.module.system.enums.notice.NoticeSendTypeEnum;
import com.xinkongan.cloud.module.system.service.notice.INoticeStrategy;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * @Description 第三方通知方式
 * <AUTHOR>
 * @Date 2025/3/7 9:25
 */
@Service
public class ThirdNoticeStrategy implements INoticeStrategy {

    private final NoticeSendTypeEnum noticeSendTypeEnum = NoticeSendTypeEnum.THIRD;

    @Override
    public void sendNotice(Set<NoticeSendTypeEnum> noticeSendTypeEnumSet, Set<Long> userIds, NoticeDO noticeDO) {
        if (noticeSendTypeEnumSet.contains(noticeSendTypeEnum)) {

        }
    }
}