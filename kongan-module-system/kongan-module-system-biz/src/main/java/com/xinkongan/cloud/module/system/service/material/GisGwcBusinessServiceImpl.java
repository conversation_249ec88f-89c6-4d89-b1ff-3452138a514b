package com.xinkongan.cloud.module.system.service.material;

import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.MaterialGisDealCallbackDTO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialMapper;
import com.xinkongan.cloud.module.system.dto.MaterialCompleteDTO;
import com.xinkongan.cloud.module.system.dto.MaterialParseSuccessNotifyDTO;
import com.xinkongan.cloud.module.system.enums.material.MaterialParseStatus;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GisGwcBusinessServiceImpl extends DefaultParseHandler implements IGisBusinessService {

    @Resource
    private MaterialMapper materialMapper;

    @Override
    public void dealGisCallback(MaterialGisDealCallbackDTO downloadCallbackDTO) {
        log.info("[GWC缓存完成回调]，参数为：{}", downloadCallbackDTO);
        String jsonString = JSONObject.toJSONString(downloadCallbackDTO.getData());
        MaterialCompleteDTO materialCompleteInfo = JSONObject.parseObject(jsonString, MaterialCompleteDTO.class);
        String layerId = materialCompleteInfo.getLayerId();
        String[] fields = layerId.split("_");
        if (fields.length != 2) {
            log.error("[GWC回调layerId错误]....");
            throw new RuntimeException("GWC回调layerId错误");
        }
        Long materialId = Long.parseLong(fields[1]);
        try {
            MaterialDO materialInfo = materialMapper.selectById(materialId);
            if (materialInfo == null) {
                throw new RuntimeException("二维素材未找到");
            }
            materialInfo.setTileUrl(materialCompleteInfo.getTileUrl());
            materialInfo.setStatus(MaterialParseStatus.SUCCESS.getCode());
            materialInfo.setProcess(100);
            materialMapper.updateById(materialInfo);
            // 通知前端进度 100 %
            this.sendMaterialParseProcessNotify(materialId, 100);

            MaterialParseSuccessNotifyDTO payload = MaterialParseSuccessNotifyDTO.builder()
                    .tileUrl(materialInfo.getTileUrl())
                    .thumbnailUrl(materialInfo.getJpgUrl())
                    .materialInfo(materialInfo)
                    .build();
            // 通知前端解析成功
            this.sendMaterialParseStatusNotify(materialId, MaterialParseStatus.SUCCESS, payload);
        } catch (Exception e) {
            log.error("[GWC缓存完成回调]，异常信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
            // 通知前端解析失败
            this.sendMaterialParseStatusNotify(materialId, MaterialParseStatus.FAILED);
        }
    }
}
