package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * @Description 飞行记录航线信息
 * <AUTHOR>
 * @Date 2025/2/13 14:51
 */
@Schema(description = "飞行记录航线信息")
@Data
@Builder
public class FlyRecordRouteInfo {

    @Schema(description = "航线id")
    private Long id;

    @Schema(description = "航线名称")
    private String name;
}