package com.xinkongan.cloud.module.system.controller.admin.label.dto;

import com.xinkongan.cloud.module.system.dto.PointInfoDTO;
import com.xinkongan.cloud.module.system.enums.label.TabLineTypeEnum;
import com.xinkongan.cloud.module.system.enums.label.TabTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class TabSaveDTO implements Serializable {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "标注名称")
    private String name;

    /**
     * {@link TabTypeEnum}
     */
    @Schema(description = "标注类型:(Point 点，Line 线，Polygon 面，Circle 圆)")
    private String type;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "颜色")
    private String color;

    @Schema(description = "是否显示： 1 显示，0 不显示")
    private Integer view;

    @Schema(description = "标点经度")
    private Double longitude;

    @Schema(description = "标点维度")
    private Double latitude;

    @Schema(description = "图标")
    private Integer icon;

    @Schema(description = "线距离/米")
    private Double distance;

    @Schema(description = "面积/平方米")
    private Double area;

    @Schema(description = "圆半径/米")
    private Double radius;

    /**
     * {@link TabLineTypeEnum}
     */
    @Schema(description = "线类型：(solidLine 实线，dottedLine 虚线，arrowLine 箭头线)")
    private String lineType;

    @Schema(description = "标注id")
    private Long labelId;

    @Schema(description = "航线id")
    private Long routeId;

    @Schema(description = "保存点的集合")
    private List<PointInfoDTO> pointInfo;

    public List<PointInfoDTO> getPointInfo() {
        if (pointInfo == null) {
            pointInfo = new ArrayList<>();
        }
        return pointInfo;
    }
}
