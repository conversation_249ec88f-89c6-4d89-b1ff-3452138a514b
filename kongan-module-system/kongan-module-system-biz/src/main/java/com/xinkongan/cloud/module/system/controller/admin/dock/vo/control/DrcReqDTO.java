package com.xinkongan.cloud.module.system.controller.admin.dock.vo.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DrcReqDTO {

    @NotBlank
    @JsonProperty(value = "dockSn")
    @Schema(description = "机场sn")
    private String dockSn;

    @Valid
    @Schema(description = "授权打断低电量返航")
    private Boolean authorizedToInterruptLowPowerReentry = false;

}
