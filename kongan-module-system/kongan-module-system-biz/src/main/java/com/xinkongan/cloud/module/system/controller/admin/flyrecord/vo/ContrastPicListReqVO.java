package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description 对比图片列表ReqVO
 * <AUTHOR>
 * @Date 2025/2/19 11:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "对比图片列表ReqVO")
public class ContrastPicListReqVO {

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "半径")
    private Integer radius;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}