package com.xinkongan.cloud.module.system.dal.mysql.task;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinkongan.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.xinkongan.cloud.module.system.controller.admin.alarm.vo.AlarmListReqVO;
import com.xinkongan.cloud.module.system.controller.admin.alarm.vo.AlarmListRespVO;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.HistogramVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.TaskTopStatisticVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.demand.DemandStatisticCountVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.demand.FlyDemandPageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.demand.FlyDemandPageRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.*;
import com.xinkongan.cloud.module.system.dal.dataobject.task.DemandDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @Description 飞行需求mapper接口类
 * <AUTHOR>
 * @Date 2025/08/21
 */
@Mapper
public interface DemandMapper extends BaseMapperX<DemandDO> {

    /**
     * 飞行需求分页
     **/
    IPage<FlyDemandPageRespVO> page(@Param("page") IPage<FlyDemandPageRespVO> mpPage, @Param("reqVO") FlyDemandPageReqVO reqVO);



    /**
     * 查询需求详情
     **/
    JobDetailRespVO selectByDemandId(@Param("jobId") Long jobId);


    /**
     * 数量统计
     **/
    DemandStatisticCountVO statisticCount();




    /**
     * 查询数据权限范围内任务数目排行榜
     *
     * @param timeRange 时间范围
     * @return top5的结果
     */
    List<TaskTopStatisticVO> getTaskTopStatisticInfo(@Param("timeRange") LocalDateTime[] timeRange);

    /**
     * 任务排期列表
     */
    List<ScheduleFlyInfo> selectScheduleList(@Param("reqVO") ScheduleFlyReqVO scheduleFlyReqVO);

    /**
     * 查询任务页码
     */
    Integer selectAfterCount(@Param("execTime") LocalDateTime execTime, @Param("scene") Integer scene,
                             @Param("statusSet") Set<Integer> statusSet, @Param("status") Integer status);

    List<AlarmListRespVO> listAlarmJob(@Param("reqVO") AlarmListReqVO reqVO);



    /**
     * 任务类型数量统计
     **/
    JobSceneStatisticCountVO statisticCountByType(@Param("reqVO") JobStatisticByTimeRangeReqVO reqVO);


    List<HistogramVO> computeHistogramData(@Param("deptId") Long deptId,
                                           @Param("userId") Long userId,
                                           @Param("timeFormat") String timeFormat,
                                           @Param("timeRange") LocalDateTime[] timeRange);

    /**
     * 查询接警任务页码
     **/
    Integer selectAfterCountForAlarmScene(@Param("createTime") LocalDateTime createTime, @Param("statusSet") Set<Integer> set, @Param("status") Integer status);
}
