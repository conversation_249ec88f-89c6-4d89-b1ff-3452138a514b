package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.DockTypeFeature;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.CameraTypeFeature;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DeviceDictionaryDO;
import com.xinkongan.cloud.module.system.service.dock.dict.DeviceDictionaryService;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DeviceEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Validated
@RestController
@Tag(name = "管理后台 - 设备字典管理")
@RequestMapping("/system/deviceDictionary")
public class DeviceDictionaryController {

    @Resource
    private DeviceDictionaryService deviceDictionaryService;

    @Resource
    private DockDeviceService dockDeviceService;

    @GetMapping("/getDeviceDictionaryByEnum/{domain}/{type}/{subType}")
    @Operation(summary = "根据设备枚举值查询设备字典信息", description = "根据设备枚举值查询设备字典信息")
    public CommonResult<DeviceDictionaryDO> getDeviceDictionaryByEnum(@PathVariable Integer domain, @PathVariable Integer type, @PathVariable Integer subType) {
        DeviceDictionaryDO deviceDictionaryDO = deviceDictionaryService.getByDeviceEnum(DeviceEnum.find(domain, type, subType));
        return CommonResult.success(deviceDictionaryDO);
    }

    @GetMapping("/getCameraInfoByDroneSn/{droneSn}")
    @Operation(summary = "根据无人机SN查询相机信息")
    public CommonResult<CameraTypeFeature> getCameraInfoByDroneSn(@PathVariable String droneSn) {
        return CommonResult.success(dockDeviceService.getCameraInfoByDroneSn(droneSn));
    }


    @Operation(summary = "根据机场SN查询机场特性")
    @PostMapping("/getDockTypeFeatureByDockSn/{dockSn}")
    public CommonResult<DockTypeFeature> getDockTypeFeatureByDockSn(@PathVariable @NotNull String dockSn) {
        DockTypeFeature dockTypeFeature = dockDeviceService.getDockTypeFeatureByDockSn(dockSn);
        return CommonResult.success(dockTypeFeature);
    }
}
