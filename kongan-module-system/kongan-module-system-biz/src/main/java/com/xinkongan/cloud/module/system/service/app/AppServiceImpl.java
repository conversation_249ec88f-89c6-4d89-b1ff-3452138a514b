package com.xinkongan.cloud.module.system.service.app;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xinkongan.cloud.framework.common.enums.CommonStatusEnum;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.dict.vo.DictInfoVO;
import com.xinkongan.cloud.module.system.dal.dataobject.app.AppInfoDO;
import com.xinkongan.cloud.module.system.dal.dataobject.app.AppVersionDO;
import com.xinkongan.cloud.module.system.dal.mysql.app.AppInfoMapper;
import com.xinkongan.cloud.module.system.dal.mysql.app.AppVersionMapper;
import com.xinkongan.cloud.module.system.enums.dict.DictType;
import com.xinkongan.cloud.module.system.service.dict.IDictService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import java.util.List;



/**
 * 指挥端-app管理 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AppServiceImpl implements AppService {


    @Resource
    private AppInfoMapper appInfoMapper;
    @Resource
    private AppVersionMapper appVersionMapper;
    @Resource
    private IDictService dictService;


    @Override
    public String getLatestAppUrl() {
        String code = "secret";
        List<DictInfoVO> dictListByType = dictService.getDictListByType(DictType.MANAGE_APP_CODE);
        if (!CollUtil.isEmpty(dictListByType)) {
            code = dictListByType.get(0).getDictValue();
        }
        AppInfoDO appInfoDO = appInfoMapper.selectOne(new LambdaQueryWrapperX<AppInfoDO>().
                eq(AppInfoDO::getCode, code));
        if (appInfoDO == null) {
            return null;
        }
        List<AppVersionDO> list = appVersionMapper.selectList(new LambdaQueryWrapperX<AppVersionDO>()
                .eq(AppVersionDO::getAppId, appInfoDO.getId())
                .eq(AppVersionDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .orderByDesc(AppVersionDO::getSoftwareVersionCode, AppVersionDO::getId));
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        // 默认取app关联的最新一个版本
        return list.get(0).getInstallPackage();
    }

}
