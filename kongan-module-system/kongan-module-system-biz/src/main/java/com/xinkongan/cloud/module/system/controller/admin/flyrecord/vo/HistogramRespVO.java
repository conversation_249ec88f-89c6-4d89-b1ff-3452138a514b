package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class HistogramRespVO {

    @Schema(description = "接警记录数")
    private Long alarmCount;

    @Schema(description = "智能巡检")
    private Long inspectCount;

    @Schema(description = "其他飞行")
    private Long otherCount;

    @Schema(description = "时间")
    private String timePeriod;
}
