package com.xinkongan.cloud.module.system.controller.admin.label;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.label.dto.LabelShareDTO;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelShareVO;
import com.xinkongan.cloud.module.system.service.label.ILabelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@RestController
@Tag(name = "飞控平台 - 标注分享")
@RequestMapping("/system/label/share")
public class LabelShareController {

    @Resource
    private ILabelService labelService;


    @PostMapping(value = "/save")
    @Operation(summary = "标注分享-保存分享")
    @PreAuthorize("@ss.hasPermission('system:label:share:save')")
    public CommonResult<Void> labelShare(@RequestBody @Valid LabelShareDTO labelShare) {
        labelService.saveLabelShare(labelShare);
        return CommonResult.success();
    }


    @GetMapping(value = "/list/{labelId}")
    @Operation(summary = "标注分享-分享列表")
    @PreAuthorize("@ss.hasPermission('system:label:share:query')")
    public CommonResult<LabelShareVO> getLabelShareDept(@PathVariable Long labelId) {
        LabelShareVO labelShareVO = labelService.getLabelShareDept(labelId);
        return CommonResult.success(labelShareVO);
    }
}
