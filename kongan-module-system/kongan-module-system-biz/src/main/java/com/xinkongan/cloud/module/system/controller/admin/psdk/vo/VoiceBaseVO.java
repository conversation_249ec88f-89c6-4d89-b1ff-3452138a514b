package com.xinkongan.cloud.module.system.controller.admin.psdk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class VoiceBaseVO {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "语音或文本名称", example = "李四")
    private String name;

    @Schema(description = "喊话文本")
    private String text;

    @Schema(description = "喊话类型 （0：文本类型   1：语音类型）", example = "2")
    private Integer type;

    @Schema(description = "行动单位id", example = "15854")
    private Long deptId;

    @Schema(description = "音频文件时长")
    private Long duration;

    @Schema(description = "语音喊话地址")
    private String url;

    @Schema(description = "mp3文件的url")
    private String mp3Url;

    @Schema(description = "md5值")
    private String fingerprint;
}
