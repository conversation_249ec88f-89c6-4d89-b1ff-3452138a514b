package com.xinkongan.cloud.module.system.controller.admin.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MaterialPublicShareDTO {

    @NotNull(message = "素材id不能为空")
    @Schema(description = "素材id")
    private Long materialId;

    @NotNull(message = "分享类型不能为空")
    @Schema(description = "是否是公开分享，0：公开分享，1：加密分享")
    private Integer shareType;

    @NotNull(message = "有效时长不能为空")
    @Schema(description = "有效时长，(单位：小时),0：永久有效")
    private Integer expireIn;

    @Schema(description = "加密密码")
    private String password;
}
