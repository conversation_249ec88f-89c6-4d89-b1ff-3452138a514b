package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import com.xinkongan.cloud.module.algorithm.dto.AlgorithmTargetDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 飞行记录文件和异常图片VO
 * <AUTHOR>
 * @Date 2025/4/11 15:26
 */
@Schema(description = "飞行记录文件和异常图片VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlyRecordFileAndExceptionVO {

    @Schema(description = "飞行记录文件id")
    private Long id;

    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "飞行记录id")
    private Long flyRecordId;

    @Schema(description = "文件表id")
    private Long fileId;

    @Schema(description = "文件url")
    private String url;

    @Schema(description = "图片/异常名称")
    private String name;

    @Schema(description = "动作执行时间")
    private LocalDateTime actionTime;

    @Schema(description = "文件来源0拍摄1截图2导入3录制")
    private Integer fileSource;

    @Schema(description = "文件类型0视频1图片2全景图")
    private Integer fileType;

    @Schema(description = "高/像素")
    private Integer height;

    @Schema(description = "长/像素")
    private Integer width;

    @Schema(description = "文件大小/byte")
    private Long fileSize;

    @Schema(description = "纬度")
    private String lat;

    @Schema(description = "经度")
    private String lon;

    @Schema(description = "拍摄地点")
    private String address;

    @Schema(description = "相对高度")
    private String relativeAltitude;

    @Schema(description = "绝对高度", required = true)
    private String absoluteAltitude;

    @Schema(description = "镜头模式")
    private String lensType;

    @Schema(description = "异常id")
    private Long exceptionId;

    @Schema(description = "素材id")
    private Long materialId;

    @Schema(description = "文件路径")
    private String path;

    @Schema(description = "是否是分享来的")
    private Boolean isShare;

    @Schema(description = "异常类型")
    private String type;

    @Schema(description = "算法目标列表(图例)")
    List<AlgorithmTargetDTO> targetList;
}