package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/25 10:44
 */
@Data
@Schema(description = "任务详情Resp VO")
public class AlarmJobDetailRespVO {

    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "所属组织id")
    private Long deptId;

    @Schema(description = "所属组织名称")
    private String deptName;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "接警名称")
    private String name;

    @Schema(description = "接警描述")
    private String description;

    @Schema(description = "警情位置")
    private String address;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "接警场景")
    private String alarmScene;

    @Schema(description = "接警类型 0手动 1第三方")
    private Integer source;

    @Schema(description = "生成时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人id")
    private Long creator;

    @Schema(description = "创建人名称")
    private String nickname;

    @Schema(description = "执行机场列表")
    private List<DockDeviceVO> dockList;

    @Schema(description = "执行时间列表")
    private List<LocalDateTime> executeTimeList;

    @Schema(description = "任务统计")
    private JobDetailStatistics jobDetailStatistics;

    public List<DockDeviceVO> getDockList() {
        return dockList == null ? List.of() : dockList;
    }
}