package com.xinkongan.cloud.module.system.controller.admin.label.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xinkongan.cloud.module.system.dto.PointInfoDTO;
import com.xinkongan.cloud.module.system.enums.label.TabLineTypeEnum;
import com.xinkongan.cloud.module.system.enums.label.TabTypeEnum;
import com.xinkongan.cloud.module.system.service.label.PointInfoHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TabRespVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "标注名称")
    private String name;

    /**
     * {@link TabTypeEnum}
     */
    @Schema(description = "标注类型")
    private String type;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "颜色")
    private String color;

    @Schema(description = "是否显示： 1 显示，0 不显示")
    private Integer view;

    @Schema(description = "标点经度")
    private String longitude;

    @Schema(description = "标点维度")
    private String latitude;

    @Schema(description = "图标")
    private Integer icon;

    @Schema(description = "线距离/米")
    private Double distance;

    @Schema(description = "面积/平方米")
    private Double area;

    @Schema(description = "圆半径/米")
    private Double radius;

    /**
     * {@link TabLineTypeEnum}
     */
    @Schema(description = "线类型")
    private String lineType;

    @Schema(description = "标注id")
    private Long labelId;

    @Schema(description = "保存点的集合")
    @TableField(typeHandler = PointInfoHandler.class)
    private List<PointInfoDTO> pointInfo;

    public List<PointInfoDTO> getPointInfo() {
        if (pointInfo == null) {
            pointInfo = new ArrayList<>();
        }
        return pointInfo;
    }
}
