package com.xinkongan.cloud.module.system.controller.admin.weather.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DailyWeatherVO {

    @Schema(description = "预报日期")
    private String fxDate;

    @Schema(description = "日出时间")
    private String sunrise;

    @Schema(description = "日落时间")
    private String sunset;

    @Schema(description = "月出时间")
    private String moonrise;

    @Schema(description = "月落时间")
    private String moonset;

    @Schema(description = "月相名称")
    private String moonPhase;

    @Schema(description = "月相图标")
    private String moonPhaseIcon;

    @Schema(description = "最高温度")
    private String tempMax;

    @Schema(description = "最低温度")
    private String tempMin;

    @Schema(description = "白天气象图标")
    private String iconDay;

    @Schema(description = "白天气象描述")
    private String textDay;

    @Schema(description = "夜间气象图标")
    private String iconNight;

    @Schema(description = "夜间气象描述")
    private String textNight;

    @Schema(description = "白天风向角度")
    private String wind360Day;

    @Schema(description = "白天风向")
    private String windDirDay;

    @Schema(description = "白天风力等级")
    private String windScaleDay;

    @Schema(description = "白天风速")
    private String windSpeedDay;

    @Schema(description = "夜间风向角度")
    private String wind360Night;

    @Schema(description = "夜间风向")
    private String windDirNight;

    @Schema(description = "夜间风力等级")
    private String windScaleNight;

    @Schema(description = "夜间风速")
    private String windSpeedNight;

    @Schema(description = "湿度")
    private String humidity;

    @Schema(description = "降水量")
    private String precip;

    @Schema(description = "气压")
    private String pressure;

    @Schema(description = "能见度")
    private String vis;

    @Schema(description = "云量")
    private String cloud;

    @Schema(description = "紫外线指数")
    private String uvIndex;
}
