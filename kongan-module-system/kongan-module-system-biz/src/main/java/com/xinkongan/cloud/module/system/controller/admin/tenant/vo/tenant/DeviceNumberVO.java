package com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceNumberVO {

    @Schema(description = "当前设备数量")
    private Integer currentDeviceNumber;

    @Schema(description = "最大设备数量")
    private Integer maxDeviceNumber;

}
