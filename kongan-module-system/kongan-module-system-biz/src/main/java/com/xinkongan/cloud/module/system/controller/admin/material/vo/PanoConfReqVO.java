package com.xinkongan.cloud.module.system.controller.admin.material.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PanoConfReqVO {

    @Schema(description = "素材ID")
    private Long materialId;

    @Schema(description = "可视类型 0 全部 1 可视范围")
    private Integer type;

    @Schema(description = "可视范围，单位km")
    private Integer visualRange;
}
