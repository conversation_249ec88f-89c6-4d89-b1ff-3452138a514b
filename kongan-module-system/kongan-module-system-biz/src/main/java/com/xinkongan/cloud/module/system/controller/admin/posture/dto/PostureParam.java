package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "态势页面公共参数")
public class PostureParam {

    /**
     * 态势页面所选择的组织树信息
     */
    private Set<Long> deptIds;

}
