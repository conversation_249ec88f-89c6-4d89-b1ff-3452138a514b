package com.xinkongan.cloud.module.system.service.posture;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.posture.PostureDateParam;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
public interface IPostureService {

    /**
     * 校验组织列表是否在权限内
     *
     * @param deptIdSet 需要查询的组织ID列表
     * @return 是否合法
     */
    boolean verifyOrganizationList(Set<Long> deptIdSet);

    /**
     * 获取混合任务
     *
     * @param page 分页信息
     * @return 任务数据
     */
    PageResult<MixedJobPageResp> mixedJob(PostureParamPage page);

    /**
     * 查询不同类型任务的数量
     *
     * @param param 查询参数
     * @return 任务数量
     */
    JobSceneNumberVO jobSceneNumber(PostureParam param);

    /**
     * 按月统计任务数量走势
     *
     * @param param 组织列表参数
     * @return 折线信息
     */
    List<JobNumberTrendVO> jobNumberTrend(PostureParam param);

    /**
     * 获取多设备列表
     *
     * @param param 查询参数
     * @return 设备列表
     */
    MultipleDeviceVO multipleDeviceList(PostureParam param);

    /**
     * 根据机场SN查询无人机正在执行的航线和飞行轨迹
     *
     * @param dockSn 机场SN
     * @return 航线和飞行轨迹
     */
    DockRouteAndFlyRecordRespVO getDockRouteAndFlyRecord(String dockSn);

    /**
     * 统计设备飞行记录排名
     *
     * @param param 查询参数
     * @return 设备排名列表
     */
    List<DeviceRankVO> deviceRank(FlightRankParam param);

    /**
     * 设备飞行统计
     *
     * @param param 查询参数
     * @return 飞行统计信息
     */
    DeviceRankVO deviceFlyStatistics(PostureDateParam param);

    /**
     * 警情列表分页查询
     *
     * @param param 查询参数
     * @return 警情信息
     */
    PageResult<PostureAlarmListVO> alertListPage(PostureParamPage param);

    /**
     * 消息通知分页查询
     *
     * @param param 查询参数
     * @return 消息列表
     */
    PageResult<PostureNoticeListVO> noticeListPage(PageParam param);
}
