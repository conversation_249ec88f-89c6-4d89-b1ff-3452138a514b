package com.xinkongan.cloud.module.system.controller.admin.dock.vo.flightrestriction;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 限飞区创建请求VO
 *
 * <AUTHOR> 4.0 sonnet
 */
@Data
@Schema(description = "管理后台 - 限飞区创建请求VO")
public class FlightRestrictionZoneSaveReqVO {

    @Schema(description = "限飞区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试限飞区")
    @NotBlank(message = "限飞区名称不能为空")
    @Size(max = 100, message = "限飞区名称长度不能超过100个字符")
    private String name;

    @Schema(description = "几何形状类型：1-多边体，2-圆柱体", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "几何形状类型不能为空")
    private Integer shapeType;

    @Schema(description = "区域类型：1-限高区，2-限低区，3-自定义禁飞区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "区域类型不能为空")
    private Integer zoneType;

    @Schema(description = "最小高度(米)", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "最小高度不能为空")
    @DecimalMin(value = "0", message = "最小高度不能小于0")
    private BigDecimal minHeight;

    @Schema(description = "最大高度(米)", requiredMode = Schema.RequiredMode.REQUIRED, example = "120")
    @NotNull(message = "最大高度不能为空")
    @DecimalMin(value = "0", message = "最大高度不能小于0")
    private BigDecimal maxHeight;

    @Schema(description = "多边形数据(GeoJSON格式)，多边体类型时必填", example = "{\"type\":\"Polygon\",\"coordinates\":[[[116.1,39.1],[116.2,39.1],[116.2,39.2],[116.1,39.2],[116.1,39.1]]]}")
    private String polygonData;

    @Schema(description = "圆心经度，圆柱体类型时必填", example = "116.123456")
    @DecimalMin(value = "-180", message = "经度范围为-180到180")
    @DecimalMax(value = "180", message = "经度范围为-180到180")
    private BigDecimal centerLongitude;

    @Schema(description = "圆心纬度，圆柱体类型时必填", example = "39.123456")
    @DecimalMin(value = "-90", message = "纬度范围为-90到90")
    @DecimalMax(value = "90", message = "纬度范围为-90到90")
    private BigDecimal centerLatitude;

    @Schema(description = "半径(米)，圆柱体类型时必填", example = "1000")
    @DecimalMin(value = "0", message = "半径不能小于0", inclusive = false)
    private BigDecimal radius;

    @Schema(description = "面积(平方米)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000000")
    @NotNull(message = "面积不能为空")
    @DecimalMin(value = "0", message = "面积不能小于0", inclusive = false)
    private BigDecimal area;

    @Schema(description = "启用状态：true-启用，false-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "启用状态不能为空")
    private Boolean status;
}
