package com.xinkongan.cloud.module.system.dal.dataobject.permission;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinkongan.cloud.framework.mybatis.core.dataobject.TenantBaseDO;
import lombok.*;

/**
 * 用户和角色关联
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("system_user_role")
@KeySequence("system_user_role_seq")
@EqualsAndHashCode(callSuper = true)
public class UserRoleDO extends TenantBaseDO {

    /**
     * 自增主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户 ID
     */
    private Long userId;
    /**
     * 角色 ID
     */
    private Long roleId;

}
