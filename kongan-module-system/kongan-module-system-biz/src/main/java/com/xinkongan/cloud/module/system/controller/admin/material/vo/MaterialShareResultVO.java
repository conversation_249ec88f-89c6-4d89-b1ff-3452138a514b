package com.xinkongan.cloud.module.system.controller.admin.material.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialShareResultVO {

    @Schema(description = "是否是公开分享")
    private Boolean isPublic;

    @Schema(description = "分享者")
    private String shareCreator;
}
