package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.common.annotation.DeviceControlCheck;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.control.ApproveReqDTO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.control.DrcReqDTO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.control.DrcRespDTO;
import com.xinkongan.cloud.module.system.enums.FlyIncidentTypeEnum;
import com.xinkongan.cloud.module.system.service.dock.control.IDockControlService;
import com.xinkongan.cloud.module.system.service.dock.mqtt.progress.FlightTaskService;
import com.xinkongan.cloud.module.system.service.task.IJobFlyProgressService;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.request.FlyToPointRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */
@Validated
@RestController
@Tag(name = "管理后台 - 飞行控制管理")
@RequestMapping("/system/fly-control")
public class DockControlController {

    @Resource
    private IDockControlService dockControlService;

    @Resource
    private FlightTaskService flightTaskService;

    @Resource
    private IJobFlyProgressService progressService;

    @Operation(summary = "航线暂停")
    @PostMapping("/pauseWayline/{dockSn}")
    @DeviceControlCheck(sn = "#dockSn")
    public CommonResult<Boolean> pauseWayline(@PathVariable String dockSn) {
        dockControlService.pauseWayline(dockSn);
        progressService.saveDetail(dockSn, FlyIncidentTypeEnum.PAUSE_WAYLINE, null, null, SecurityFrameworkUtils.getLoginUserNickname());
        return CommonResult.success(true);
    }

    @Operation(summary = "航线恢复")
    @PostMapping("/resumeWayline/{dockSn}")
    @DeviceControlCheck(sn = "#dockSn")
    public CommonResult<Boolean> resumeWayline(@PathVariable String dockSn) {
        dockControlService.resumeWayline(dockSn);
        progressService.saveDetail(dockSn, FlyIncidentTypeEnum.RESUME_WAYLINE, null, null, SecurityFrameworkUtils.getLoginUserNickname());
        return CommonResult.success(true);
    }

    @Operation(summary = "进入远程控制前检查")
    @PostMapping("/checkRemoteControl")
    @DeviceControlCheck(sn = "#reqDTO.dockSn")
    public CommonResult<DrcRespDTO> checkRemoteControl(@RequestBody DrcReqDTO reqDTO) {
        DrcRespDTO respDTO = dockControlService.checkRemoteControl(reqDTO);
        return CommonResult.success(respDTO);
    }

    @Operation(summary = "进入远程控制")
    @PostMapping("/remoteControl")
    @DeviceControlCheck(sn = "#reqDTO.dockSn")
    public CommonResult<DrcRespDTO> remoteControl(@RequestBody DrcReqDTO reqDTO) {
        DrcRespDTO respDTO = dockControlService.remoteControl(reqDTO);
        progressService.saveDetail(reqDTO.getDockSn(), FlyIncidentTypeEnum.DRC_ENTER_MODE, null, null, SecurityFrameworkUtils.getLoginUserNickname());
        return CommonResult.success(respDTO);
    }

    @Operation(summary = "申请进行远程控制")
    @PostMapping("/requestRemoteControl/{dockSn}")
    @DeviceControlCheck(sn = "#dockSn")
    public CommonResult<Boolean> requestRemoteControl(@PathVariable String dockSn) {
        Boolean control = dockControlService.requestRemoteControl(dockSn);
        return CommonResult.success(control);
    }

    @Operation(summary = "审批远程控制")
    @PostMapping("/approveRemoteControl")
    @DeviceControlCheck(sn = "#reqDTO.dockSn")
    public CommonResult<Boolean> approveRemoteControl(@RequestBody ApproveReqDTO reqDTO) {
        Boolean control = dockControlService.approveRemoteControl(reqDTO);
        return CommonResult.success(control);
    }

    @Operation(summary = "查询是否存在可以恢复的航线")
    @GetMapping("/existResumeWayline/{dockSn}")
    @DeviceControlCheck(sn = "#dockSn")
    public CommonResult<Boolean> existWayline(@PathVariable String dockSn) {
        Boolean existed = flightTaskService.existSuspendTask(dockSn);
        return CommonResult.success(existed);
    }

    @Operation(summary = "退出远程控制并恢复航线")
    @PostMapping("/exitRemoteControl/{dockSn}")
    @DeviceControlCheck(sn = "#dockSn")
    public CommonResult<Boolean> exitRemoteControl(@PathVariable String dockSn) {
        Boolean control = dockControlService.exitRemoteControl(dockSn);
        progressService.saveDetail(dockSn, FlyIncidentTypeEnum.DRC_ENTER_MODE_RETURN, null, null, SecurityFrameworkUtils.getLoginUserNickname());
        return CommonResult.success(control);
    }

    @Operation(summary = "flyTo指点飞行")
    @PostMapping("/flyToPoint/{dockSn}")
    @DeviceControlCheck(sn = "#dockSn")
    public CommonResult<Boolean> flyToPoint(@RequestBody FlyToPointRequest request, @PathVariable String dockSn) {
        Boolean flyToPoint = dockControlService.flyToPoint(request, dockSn);
        return CommonResult.success(flyToPoint);
    }

}
