package com.xinkongan.cloud.module.system.controller.admin.upgrade;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.api.upgrade.dto.UpgradeNoticeDTO;
import com.xinkongan.cloud.module.system.service.upgrade.IUpgradeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 系统升级通知
 * <AUTHOR>
 * @Date 2025/5/22 16:15
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 系统升级通知")
@RequestMapping("/system/upgrade")
public class SystemUpgradeController {

    @Resource
    private IUpgradeService upgradeService;

    /**
     * 获取系统通知
     **/
    @GetMapping("/getUpgradeNotice")
    @Operation(summary = "获取系统通知", description = "获取系统通知")
    CommonResult<UpgradeNoticeDTO> getUpgradeNotice() {
        UpgradeNoticeDTO upgradeNotice = upgradeService.getUpgradeNotice();
        return success(upgradeNotice);
    }

    /**
     * 不再提醒
     **/
    @GetMapping("/noRemind")
    @Operation(summary = "不再提醒", description = "不再提醒")
    CommonResult<Boolean> noRemind(@RequestParam("id") Long id) {
        upgradeService.notRemind(id);
        return success(true);
    }
}