package com.xinkongan.cloud.module.system.framework.file.core.client;

import com.xinkongan.cloud.module.system.api.file.dto.file.StsCredentialsDTO;
import com.xinkongan.cloud.module.system.enums.file.S3FileDirPrefixEnum;
import com.xinkongan.cloud.module.system.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import io.minio.Result;
import io.minio.messages.DeleteError;

import java.io.InputStream;
import java.util.List;

/**
 * 文件客户端
 *
 * <AUTHOR>
 */
public interface FileClient {

    /**
     * 获得客户端编号
     *
     * @return 客户端编号
     */
    Long getId();

    /**
     * 上传文件
     *
     * @param content 文件流
     * @param path    相对路径
     * @return 完整路径，即 HTTP 访问地址
     * @throws Exception 上传文件时，抛出 Exception 异常
     */
    String upload(byte[] content, String path, String type) throws Exception;

    /**
     * 桶内复制文件
     *
     * <AUTHOR>
     * @date 2025/1/15 17:30
     **/
    String copyObject(String url, String newObjectName) throws Exception;

    /**
     * 使用文件流上传
     *
     * <AUTHOR>
     * @date 2024/11/15 17:53
     **/
    String upload(InputStream inputStream, String path, String type) throws Exception;

    /**
     * 删除文件
     *
     * @param path 相对路径
     * @throws Exception 删除文件时，抛出 Exception 异常
     */
    void delete(String path) throws Exception;

    /**
     * 递归删除子目录及其文件
     *
     * @param path 目录的路径
     */
    void deleteDir(String path);

    /**
     * 获得文件的内容
     *
     * @param path 相对路径
     * @return 文件的内容
     */
    byte[] getContent(String path) throws Exception;

    /**
     * 获得文件预签名地址
     *
     * @param path 相对路径
     * @return 文件预签名地址
     */
    default FilePresignedUrlRespDTO getPresignedObjectUrl(String path) throws Exception {
        throw new UnsupportedOperationException("不支持的操作");
    }

    /**
     * 获取STS
     *
     * @param tenantId      租户ID
     * @param dirPrefixEnum 文件分类枚举
     * @return STS
     */
    default StsCredentialsDTO getSts(Long tenantId, S3FileDirPrefixEnum dirPrefixEnum) {
        throw new UnsupportedOperationException("不支持的操作");
    }

    default String getUrlByObjectKey(String objectKey) {
        throw new UnsupportedOperationException("不支持的操作");
    }

    default String getObjectKeyByUrl(String url) {
        throw new UnsupportedOperationException("不支持的操作");
    }

    default FileClientConfig getFileClientConfig() {
        throw new UnsupportedOperationException("不支持的操作");
    }

    default StsCredentialsDTO getPySts(String string, S3FileDirPrefixEnum dirPrefixEnum) {
        return null;
    }

    default void deleteAssociatedNonMp4Files(String mp4url) throws Exception {
        throw new UnsupportedOperationException("不支持的操作");
    }

    default Iterable<Result<DeleteError>> deleteBatchObjectKeys(List<String> objectKeys) {
        throw new UnsupportedOperationException("不支持的操作");
    }
}
