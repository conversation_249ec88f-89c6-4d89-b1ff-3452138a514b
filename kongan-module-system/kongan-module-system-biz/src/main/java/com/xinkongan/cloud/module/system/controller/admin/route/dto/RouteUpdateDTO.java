package com.xinkongan.cloud.module.system.controller.admin.route.dto;

import com.xinkongan.cloud.framework.kmz.enums.TemplateTypeEnum;
import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class RouteUpdateDTO extends RouteBaseInfoDTO{

    @Schema(description = "航线id", example = "11")
    private Long id;

    @Schema(description = "航线名称", example = "xx巡检航线")
    @Size(max = 50, message = "航线名称不能超过50个字符")
    @NotEmpty(message = "航线名称不能为空")
    private String routeName;

    @Schema(description = "航线类型", example = "1（航点航线）")
    @NotNull(message = "航线类型不能为空")
    private Integer routeType;

    @Schema(description = "组织id", example = "12")
    @NotNull(message = "组织id不能为空")
    private Long deptId;

    @Schema(description = "机场SN", example = "6QCDL870020104")
    private String dockSn;

    @Schema(description = "无人机SN", example = "1581F6QAD23BM00ABBMW")
    private String droneSn;

    @Schema(description = "相机负载index", example = "53-0-0")
    private String cameraPayload;

    @Schema(description = "首图文件信息")
    @NotNull(message = "首图信息不能为空")
    private FileSaveInfoDTO snapFileInfo;

    @Schema(description = "白模状态（1 开启，0 关闭）")
    private Integer whiteMold;

}
