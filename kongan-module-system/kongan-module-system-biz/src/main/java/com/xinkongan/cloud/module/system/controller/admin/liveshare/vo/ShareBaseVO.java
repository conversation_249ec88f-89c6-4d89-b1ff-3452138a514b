package com.xinkongan.cloud.module.system.controller.admin.liveshare.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShareBaseVO {

    @Schema(description = "分享直播的用户")
    private String username;

    @Schema(description = "直播分享是否加密")
    private Integer encipher;

    @Schema(description = "分享的直播有效期，单位为分钟")
    private Integer validity;

    @Schema(description = "直播观看信息")
    private WatchLiveVO watchLive;

}
