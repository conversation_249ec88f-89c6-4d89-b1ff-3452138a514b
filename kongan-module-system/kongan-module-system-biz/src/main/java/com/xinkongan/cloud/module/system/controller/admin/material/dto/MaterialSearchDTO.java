package com.xinkongan.cloud.module.system.controller.admin.material.dto;

import com.xinkongan.cloud.framework.common.dto.SearchDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class MaterialSearchDTO extends SearchDTO {

    @Schema(description = "素材类型(2:二维素材;3:三维素材;4:全景图)")
    private Integer type;

    @Schema(description = "素材类型(2:二维素材;3:三维素材;4:全景图)")
    private List<Integer> types;

    @Schema(description = "分享标志（1：分享，0：不是分享，2：全部）", example = "1")
    private Integer shareFlag;

    @Schema(description = "组织列表", example = "[1,2]")
    private List<Long> deptIds;

    @Schema(description = "组织ID")
    private Long deptId;

    @Schema(description = "创建时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "解析状态：（解析中:parseing，解析成功：success，解析失败：failed）")
    private String parseStatus;

    public List<Long> getDeptIds() {
        if (deptIds == null) {
            deptIds = new ArrayList<>();
        }
        return deptIds;
    }

    public List<Integer> getTypes() {
        if (types == null) {
            types = new ArrayList<>();
        }
        return types;
    }
}
