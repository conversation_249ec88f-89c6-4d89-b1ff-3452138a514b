package com.xinkongan.cloud.module.system.service.permission;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import com.xinkongan.cloud.module.system.controller.admin.permission.vo.role.*;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.RoleStatisticVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;

import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 角色 Service 接口
 *
 * <AUTHOR>
 */
public interface RoleService {

    /**
     * 创建部门角色
     *
     * @return 角色id
     */
    Long createRoleForTenantRegister(RoleSaveReqVO createReqVO, Integer type);


    /**
     * 创建角色
     *
     * @param createReqVO 创建角色信息
     * @param type        角色类型
     * @return 角色编号
     */
    Long createRole(@Valid RoleSaveReqVO createReqVO, Integer type);


    /**
     * 更新角色的权限信息
     *
     * @param roleAuthSaveReq 更新角色权限信息
     */
    void updateAuthRole(@Valid RoleAuthSaveReqVO roleAuthSaveReq);


    /**
     * 更新角色基础信息
     *
     * @param roleBaseSaveReq 角色基础信息
     */
    void updateRoleBaseInfo(RoleBaseSaveReqVO roleBaseSaveReq);


    /**
     * 删除角色
     *
     * @param id 角色编号
     */
    void deleteRole(Long id);


    /**
     * 删除组织下的所有角色信息
     *
     * @param deptId 组织Id
     */
    void deleteRoleByDeptId(Long deptId);


    /**
     * 获得角色
     *
     * @param id 角色编号
     * @return 角色
     */
    RoleDO getRole(Long id);


    /**
     * 根据id查询角色详情信息
     *
     * @param id 角色id
     * @return 角色详情
     */
    RoleRespVO getRoleDetailById(Long id);


    /**
     * 获得角色，从缓存中
     *
     * @param id 角色编号
     * @return 角色
     */
    RoleDO getRoleById(Long id);


    /**
     * 获得角色列表
     *
     * @param ids 角色编号数组
     * @return 角色列表
     */
    List<RoleDO> getRoleList(Collection<Long> ids);


    /**
     * 获得角色数组，从缓存中
     *
     * @param ids 角色编号数组
     * @return 角色数组
     */
    List<RoleDO> getRoleListByIds(Collection<Long> ids);


    /**
     * 获得角色列表
     *
     * @param statuses 筛选的状态
     * @return 角色列表
     */
    List<RoleDO> getRoleListByStatus(Collection<Integer> statuses, Long deptId);


    /**
     * 根据角色编码获取角色列表
     *
     * @return 角色列表
     */
    List<RoleDO> getRoleListByRoleCode(String roleCode);


    /**
     * 模糊查询角色列表
     *
     * @param roleName 角色名称
     * @return 角色列表
     */
    List<RoleDO> getRoleListLikeRoleName(String roleName);


    /**
     * 判断角色编号数组中，是否有超级管理员
     *
     * @param ids 角色编号数组
     * @return 是否有管理员
     */
    boolean hasAnySuperAdmin(Collection<Long> ids);


    /**
     * 获取组织角色树信息
     *
     * @param roleSearchParams 搜索信息
     * @return 角色树结构信息
     */
    List<BaseTreeNode> getDeptRoleTreeInfo(RoleSearchReqVO roleSearchParams);


    /**
     * 创建默认的组织管理角色
     *
     * @param deptInfo 组织信息
     */
    void createDefaultDeptRole(DeptDO deptInfo);


    /**
     * 组织内角色名称重复性检查
     *
     * @param roleId   角色id
     * @param roleName 角色名称
     * @param deptId   组织id
     * @return true 重复，false 不重复
     */
    Boolean checkRoleNameRepeat(Long roleId, String roleName, Long deptId);


    /**
     * 角色移动操作
     *
     * @param roleMoveReq 移动参数
     */
    void roleMove(RoleMoveReqVO roleMoveReq);


    /**
     * 查询租户管理员角色
     *
     * @param tenant 租户id
     * @return 租户管理员角色
     */
    List<RoleDO> getTenantAdminRole(Long tenant);


    /**
     * 获取数据权限范围内的角色列表
     *
     * @return 角色列表
     */
    List<RoleDO> getRoleList();


    /**
     * 获取数据权限范围内的角色统计数据信息
     *
     * @return 角色统计列表
     */
    List<RoleStatisticVO> getRoleStatisticInfo();
}
