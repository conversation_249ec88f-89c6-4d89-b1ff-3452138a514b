package com.xinkongan.cloud.module.system.framework.file.core.client.s3;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.google.common.collect.Iterables;
import com.xinkongan.cloud.framework.common.constant.SystemEnvConst;
import com.xinkongan.cloud.module.system.api.file.dto.file.CredentialsDTO;
import com.xinkongan.cloud.module.system.api.file.dto.file.StsCredentialsDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.file.S3FileDirPrefixEnum;
import com.xinkongan.cloud.module.system.framework.file.core.client.AbstractFileClient;
import com.xinkongan.cloud.module.system.framework.file.core.client.FileClientConfig;
import io.minio.*;
import io.minio.credentials.AssumeRoleProvider;
import io.minio.http.Method;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 基于 S3 协议的文件客户端，实现 MinIO、阿里云、腾讯云、七牛云、华为云等云服务
 * <p>
 * S3 协议的客户端，采用亚马逊提供的 software.amazon.awssdk.s3 库
 *
 * <AUTHOR>
 */
@Slf4j
public class S3FileClient extends AbstractFileClient<S3FileClientConfig> {

    private MinioClient client;

    public S3FileClient(Long id, S3FileClientConfig config) {
        super(id, config);
    }

    public S3FileClient() {
        super(1L, null);

    }

    @Override
    protected void doInit() {
        // 补全 domain
        if (StrUtil.isEmpty(config.getDomain())) {
            config.setDomain(buildDomain());
        }
        // 初始化客户端
        client = MinioClient.builder()
                .endpoint(buildEndpointURL()) // Endpoint URL
                .region(buildRegion()) // Region
                .credentials(config.getAccessKey(), config.getAccessSecret()) // 认证密钥
                .build();
    }

    /**
     * 基于 endpoint 构建调用云服务的 URL 地址
     *
     * @return URI 地址
     */
    private String buildEndpointURL() {
        // 如果已经是 http 或者 https，则不进行拼接.主要适配 MinIO
        if (HttpUtil.isHttp(config.getEndpoint()) || HttpUtil.isHttps(config.getEndpoint())) {
            return config.getEndpoint();
        }
        return StrUtil.format("https://{}", config.getEndpoint());
    }

    /**
     * 基于 bucket + endpoint 构建访问的 Domain 地址
     *
     * @return Domain 地址
     */
    private String buildDomain() {
        // 如果已经是 http 或者 https，则不进行拼接.主要适配 MinIO
        if (HttpUtil.isHttp(config.getEndpoint()) || HttpUtil.isHttps(config.getEndpoint())) {
            return StrUtil.format("{}/{}", config.getEndpoint(), config.getBucket());
        }
        // 阿里云、腾讯云、华为云都适合。七牛云比较特殊，必须有自定义域名
        return StrUtil.format("https://{}.{}", config.getBucket(), config.getEndpoint());
    }

    /**
     * 基于 bucket 构建 region 地区
     *
     * @return region 地区
     */
    private String buildRegion() {
        // 阿里云必须有 region，否则会报错
        String provider = config.getProvider();
        if (SystemEnvConst.FileStorageTypeEnum.ALI_OSS.getValue().equals(provider)) {
            return StrUtil.subBefore(config.getEndpoint(), '.', false)
                    .replaceAll("-internal", "")// 去除内网 Endpoint 的后缀
                    .replaceAll("https://", "");
        }
        // 天翼云
        if (SystemEnvConst.FileStorageTypeEnum.CT_YUN.getValue().equals(provider)) {
            return StrUtil.subBefore(config.getEndpoint(), ".zos", false)
                    .replaceAll("-internal", "")
                    .replaceAll("https://", "");
        }
        return null;
    }

    @Override
    public String upload(byte[] content, String path, String type) throws Exception {
        // 执行上传
        client.putObject(PutObjectArgs.builder()
                .bucket(config.getBucket()) // bucket 必须传递
                .contentType(type)
                .object(path) // 相对路径作为 key
                .stream(new ByteArrayInputStream(content), content.length, -1) // 文件内容
                .build());
        // 拼接返回路径
        return config.getDomain() + "/" + path;
    }

    /**
     * 桶内复制文件
     *
     * <AUTHOR>
     * @date 2025/1/15 17:30
     **/
    @Override
    public String copyObject(String url, String newObjectName) throws Exception {
        String objectName = parseObjectName(url);
        client.copyObject(
                CopyObjectArgs.builder()
                        .bucket(config.getBucket())
                        .object(newObjectName)
                        .source(CopySource.builder().bucket(config.getBucket()).object(objectName).build())
                        .build());
        return config.getDomain() + "/" + newObjectName;
    }


    /**
     * 解析对象名称
     *
     * <AUTHOR>
     * @date 2025/1/15 17:23
     **/
    private String parseObjectName(String url) {
        if (StrUtil.isEmpty(url)) {
            throw exception(ErrorCodeConstants.FILE_URL_NOT_EMPTY);
        }
        // 是否存在domain
        if (StrUtil.contains(url, config.getDomain())) {
            String replace = url.replace(config.getDomain(), "");
            if (replace.startsWith("/")) {
                replace = replace.substring(1);
            }
            return replace;
        } else {
            return url;
        }
    }

    /**
     * 直接使用文件流上传
     *
     * <AUTHOR>
     * @date 2024/11/15 17:59
     **/
    @Override
    public String upload(InputStream inputStream, String path, String type) throws Exception {

        client.putObject(PutObjectArgs.builder()
                .bucket(config.getBucket())
                .contentType(type)
                .object(path)
                .stream(inputStream, inputStream.available(), -1)
                .build());

        return config.getDomain() + "/" + path;
    }

    @Override
    public void delete(String path) throws Exception {
        client.removeObject(RemoveObjectArgs.builder()
                .bucket(config.getBucket()) // bucket 必须传递
                .object(path) // 相对路径作为 key
                .build());
    }

    @Override
    public void deleteDir(String path) {
        try {
            List<DeleteObject> deleteObjects = new ArrayList<>();
            // 递归获取文件
            this.getFileListByRecur(path, deleteObjects);
            // 删除文件
            Iterable<Result<DeleteError>> results = client.removeObjects(RemoveObjectsArgs.builder().bucket(config.getBucket()).objects(deleteObjects).build());
            for (Result<DeleteError> result : results) {
                DeleteError error = result.get();
                log.error("Error in deleting object " + error.objectName() + "; " + error.message());
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            log.error("删除文件出现异常，异常信息为：{}", e.getMessage());
        }
    }


    /**
     * 递归获取所有的文件
     *
     * @param object
     * @param paths
     */
    private void getFileListByRecur(String object, List<DeleteObject> paths) throws Exception {
        Iterable<Result<Item>> results = client.listObjects(ListObjectsArgs.builder().prefix(object).bucket(config.getBucket()).build());
        for (Result<Item> r : results) {
            if (r.get().isDir()) {
                this.getFileListByRecur(r.get().objectName(), paths);
                continue;
            }
            DeleteObject deleteObject = new DeleteObject(r.get().objectName());
            paths.add(deleteObject);
        }
    }


    @Override
    public byte[] getContent(String path) throws Exception {
        GetObjectResponse response = client.getObject(GetObjectArgs.builder()
                .bucket(config.getBucket()) // bucket 必须传递
                .object(path) // 相对路径作为 key
                .build());
        return IoUtil.readBytes(response);
    }

    @Override
    public FilePresignedUrlRespDTO getPresignedObjectUrl(String path) throws Exception {
        String uploadUrl = client.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                .method(Method.PUT)
                .bucket(config.getBucket())
                .object(path)
                .expiry(10, TimeUnit.MINUTES) // 过期时间（秒数）取值范围：1 秒 ~ 7 天
                .build()
        );
        return new FilePresignedUrlRespDTO(uploadUrl, config.getDomain() + "/" + path);
    }

    /**
     * 获取STS
     *
     * @param tenantId      租户ID
     * @param dirPrefixEnum 文件分类枚举
     * @return STS
     */
    @Override
    public StsCredentialsDTO getSts(Long tenantId, S3FileDirPrefixEnum dirPrefixEnum) {
        String endpoint = config.getEndpoint();
        String domain = config.getDomain();
        String resultEndPoint = endpoint;
        // MINIO 需要把端口号去掉 使用nginx代理到/${Bucket}上去
        if (config.getProvider().equals("minio")
                && ObjectUtil.isNotEmpty(domain) && domain.contains("https://")) {
            //去除端口号
            int slashIndex = domain.lastIndexOf("/");
            resultEndPoint = domain.substring(0, slashIndex);
        }
        return StsCredentialsDTO.builder()
                .endpoint(resultEndPoint)
                .bucket(config.getBucket())
                .credentials(this.getCredentials())
                .objectKeyPrefix(dirPrefixEnum.getDir(String.valueOf(tenantId)))
                .region(buildStsRegion(config.getProvider()))
                .provider(config.getProvider())
                .domain(config.getDomain())
                .build();
    }

    @Override
    public String getUrlByObjectKey(String objectKey) {
        return config.getDomain() + "/" + objectKey;
    }

    @Override
    public String getObjectKeyByUrl(String url) {
        String prefix = config.getDomain() + "/";
        if (!url.startsWith(prefix)) {
            log.info("当前文件非本系统文件，不做处理，url为：{} prefix：{}", url, prefix);
            return url;
        }
        return url.substring((config.getDomain() + "/").length());
    }

    public static void main(String[] args) throws Exception {
        String url = "tenantResource/special/1431173483470848/liveRecord/2025/6/13/5b903f1f5e4dc0cbbbd3d4970fa6a55b_1581F5BMD22C7001986G_0.mp4";
        new S3FileClient().deleteAssociatedNonMp4Files(url);
    }

    /**
     * 获取临时凭证
     * 不同云存储获取sts的方式不同，根据每家的sdk进行获取
     * 获取不到返回空
     *
     * <AUTHOR>
     * @date 2024/11/18 10:32
     **/
    private CredentialsDTO getCredentials() {
        String provider = config.getProvider();
        String accessKey = config.getAccessKey();
        String accessSecret = config.getAccessSecret();
        try {
            if (SystemEnvConst.FileStorageTypeEnum.ALI_OSS.getValue().equals(provider)) {
                // 阿里云
                DefaultProfile profile = DefaultProfile.getProfile(buildStsRegion(provider), accessKey, accessSecret);
                IAcsClient client = new DefaultAcsClient(profile);
                AssumeRoleRequest request = new AssumeRoleRequest();
                request.setDurationSeconds(config.getExpire());
                request.setRoleArn(config.getRoleArn());
                request.setRoleSessionName(config.getRoleSessionName());
                AssumeRoleResponse response = client.getAcsResponse(request);
                return new CredentialsDTO(response.getCredentials(), config.getExpire());
            } else if (SystemEnvConst.FileStorageTypeEnum.MINIO.getValue().equals(provider)) {
                // minio
                AssumeRoleProvider assumeRoleProvider = new AssumeRoleProvider(config.getEndpoint(), config.getAccessKey(), config.getAccessSecret(), Math.toIntExact(config.getExpire()), null, buildRegion(), null, null, null, null);
                return new CredentialsDTO(assumeRoleProvider.fetch(), config.getExpire());
            } else if (SystemEnvConst.FileStorageTypeEnum.CT_YUN.getValue().equals(provider)) {
                // 天翼云
            }
        } catch (ClientException | NoSuchAlgorithmException e) {
            log.error("获取sts失败!", e);
        }
        return null;
    }

    /**
     * 获取sts时的region
     *
     * <AUTHOR>
     * @date 2024/11/18 10:32
     **/
    private String buildStsRegion(String provider) {
        String region = buildRegion();
        if (SystemEnvConst.FileStorageTypeEnum.ALI_OSS.getValue().equals(provider)) {
            return region == null ? null : region.replaceAll("oss-", "");
        }
        return region;
    }

    @Override
    public FileClientConfig getFileClientConfig() {
        return config;
    }

    @Override
    public StsCredentialsDTO getPySts(String tenantId, S3FileDirPrefixEnum dirPrefixEnum) {
        return StsCredentialsDTO.builder()
                .endpoint(config.getEndpoint())
                .domain(config.getDomain())
                .bucket(config.getBucket())
                .credentials(this.getCredentials())
                .provider(config.getProvider())
                .objectKeyPrefix(dirPrefixEnum.getDir(tenantId))
                .region(buildRegionNoOss())
                .build();
    }

    @Override
    public void deleteAssociatedNonMp4Files(String mp4Path) throws Exception {
        // 去掉路径最前面的斜杠
        String normalizedPath = mp4Path.startsWith("/") ? mp4Path.substring(1) : mp4Path;

        // 提取目录和文件名
        int lastSlashIndex = normalizedPath.lastIndexOf("/");
        if (lastSlashIndex == -1) {
            throw new IllegalArgumentException("非法路径，缺少目录结构: " + mp4Path);
        }

        String dirPrefix = normalizedPath.substring(0, lastSlashIndex + 1); // 包含末尾斜杠
        String fileName = normalizedPath.substring(lastSlashIndex + 1);

        if (!fileName.endsWith(".mp4")) {
            throw new IllegalArgumentException("传入的文件路径不是mp4: " + mp4Path);
        }

        log.info("dirPrefix: {}", dirPrefix);
        // 列出该路径下所有对象
        Iterable<Result<Item>> results = client.listObjects(
                ListObjectsArgs.builder()
                        .bucket(config.getBucket())
                        .prefix(dirPrefix)
                        .recursive(true)
                        .build()
        );

        List<DeleteObject> objectsToDelete = new ArrayList<>();

        for (Result<Item> result : results) {
            Item item = result.get();
            String objectName = item.objectName();

            // 跳过当前 mp4 文件
            if (objectName.equals(normalizedPath)) continue;

            // 判断不是 mp4 则需要删除
            if (!objectName.endsWith(".mp4")) {
                objectsToDelete.add(new DeleteObject(objectName));
            }
        }

        if (!objectsToDelete.isEmpty()) {
            log.info("待删除文件数量:{}", objectsToDelete.size());
            Iterable<Result<DeleteError>> resultsError = client.removeObjects(
                    RemoveObjectsArgs.builder()
                            .bucket(config.getBucket())
                            .objects(objectsToDelete)
                            .build()
            );
            while (resultsError.iterator().hasNext()) {
                Result<DeleteError> result = resultsError.iterator().next();
                if (result.get() != null) {
                    log.error("删除文件失败:{}", JSONUtil.toJsonStr(result.get()));
                }
            }
        } else {
            log.info("未找到需要删除的非mp4文件");
        }
    }

    @Override
    public Iterable<Result<DeleteError>> deleteBatchObjectKeys(List<String> objectKeys) {
        List<Iterable<Result<DeleteError>>> allResults = new ArrayList<>();

        // 分批处理（每批1000个）
        int batchSize = 1000;
        for (int i = 0; i < objectKeys.size(); i += batchSize) {
            int end = Math.min(i + batchSize, objectKeys.size());
            List<DeleteObject> batch = objectKeys.subList(i, end).stream()
                    .map(DeleteObject::new)
                    .toList();

            // 执行批量删除并保存结果
            Iterable<Result<DeleteError>> batchResults = client.removeObjects(
                    RemoveObjectsArgs.builder()
                            .bucket(config.getBucket())
                            .objects(batch)
                            .build()
            );
            allResults.add(batchResults);
        }

        // 合并所有批次的结果
        return Iterables.concat(allResults);
    }

    /**
     * 基于 bucket 构建 region 地区 --- 获取Sts时为 cn-hangzhou 不需要 前缀 “oss-”
     *
     * @return region 地区
     */
    private String buildRegionNoOss() {
        String region = buildRegion();
        return region == null ? null : region.replaceAll("oss-", "");
    }
}
