package com.xinkongan.cloud.module.system.controller.admin.label.dto;

import com.xinkongan.cloud.framework.common.dto.SearchDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LabelSearchDTO extends SearchDTO {

    @Schema(description = "分享标志（1：分享，0：不是分享，2：全部）", example = "1")
    private Integer shareFlag;

    @Schema(description = "创建时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "组织ID")
    private Long deptId;

}
