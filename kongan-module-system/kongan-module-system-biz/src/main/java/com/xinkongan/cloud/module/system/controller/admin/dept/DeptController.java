package com.xinkongan.cloud.module.system.controller.admin.dept;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import com.xinkongan.cloud.framework.common.enums.CommonStatusEnum;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.xinkongan.cloud.module.system.controller.admin.dept.dto.DeptUserSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.*;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.enums.sms.SmsSceneEnum;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.sms.SmsCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.checkerframework.checker.units.qual.C;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;
import static com.xinkongan.cloud.framework.common.util.servlet.ServletUtils.getClientIP;


@Validated
@RestController
@Tag(name = "管理后台 - 部门")
@RequestMapping("/system/dept")
public class DeptController {

    @Resource
    private DeptService deptService;

    @Resource
    private SmsCodeService smsCodeService;


    @PostMapping("/create")
    @Operation(summary = "创建部门")
    @PreAuthorize("@ss.hasPermission('system:dept:create')")
    public CommonResult<DeptRespVO> createDept(@Valid @RequestBody DeptSaveReqVO createReqVO) {
        DeptRespVO deptInfo = deptService.createDept(createReqVO);
        return success(deptInfo);
    }

    @PutMapping("/update")
    @Operation(summary = "更新部门")
    @PreAuthorize("@ss.hasPermission('system:dept:update')")
    public CommonResult<Boolean> updateDept(@Valid @RequestBody DeptSaveReqVO updateReqVO) {
        deptService.updateDept(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除部门")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:dept:delete')")
    public CommonResult<Boolean> deleteDept(@RequestBody DeptDelReqVO deptDelReqInfo) {
        // 验证手机验证码忽略租户
        TenantUtils.executeIgnore(() ->
                // 校验验证码
                smsCodeService.useSmsCode(
                        SmsCodeUseReqDTO.builder()
                                .mobile(deptDelReqInfo.getMobile())
                                .code(deptDelReqInfo.getCode())
                                .scene(SmsSceneEnum.DEPT_DEL_AUTH.getScene())
                                .usedIp(getClientIP())
                                .build()
                )
        );
        deptService.deleteDept(deptDelReqInfo.getDeptId());
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获取部门列表")
    @PreAuthorize("@ss.hasPermission('system:dept:query')")
    public CommonResult<List<DeptRespVO>> getDeptList(DeptListReqVO reqVO) {
        List<DeptDO> list = deptService.getDeptList(reqVO);
        return success(BeanUtils.toBean(list, DeptRespVO.class));
    }

    @GetMapping(value = {"/list-all-simple"})
    @PreAuthorize("@ss.hasPermission('system:dept:query')")
    @Operation(summary = "获取部门精简信息列表", description = "只包含被开启的部门，主要用于前端的下拉选项")
    public CommonResult<List<DeptSimpleRespVO>> getSimpleDeptList() {
        List<DeptDO> list = deptService.getDeptList(
                new DeptListReqVO().setStatus(CommonStatusEnum.ENABLE.getStatus()));
        return success(BeanUtils.toBean(list, DeptSimpleRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得部门信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:dept:query')")
    public CommonResult<DeptRespVO> getDept(@RequestParam("id") Long id) {
        DeptRespVO deptInfo = deptService.getDeptInfoById(id);
        return success(deptInfo);
    }


    @GetMapping("/tree")
    @Operation(summary = "获取部门信息-树结构")
    @PreAuthorize("@ss.hasPermission('system:dept:query')")
    public CommonResult<List<BaseTreeNode>> getDeptTree(@RequestParam(required = true, defaultValue = "", value = "deptName")
                                                        String deptName) {
        List<BaseTreeNode> deptTreeInfo = deptService.getDeptTreeInfo(deptName);
        return success(deptTreeInfo);
    }

    @PostMapping(value = "/move")
    @Operation(summary = "部门列表移动操作-部门移动操作")
    @PreAuthorize("@ss.hasPermission('system:dept:move')")
    public CommonResult<Long> moveDeptPosition(@RequestBody DeptMoveReqVO deptMoveReq) {
        Long positionId = deptService.moveDeptPosition(deptMoveReq.getDeptId(), deptMoveReq.getMove());
        return CommonResult.success(positionId);
    }

    @GetMapping(value = "/del/check/{deptId}")
    @Operation(summary = "部门删除检查")
    @PreAuthorize("@ss.hasPermission('system:dept:delete')")
    public CommonResult<Void> deptDeleteCheck(@PathVariable Long deptId) {
        deptService.deptDelCheck(deptId);
        return CommonResult.success();
    }

    @PostMapping(value = "/user/page")
    @Operation(summary = "组织用户分页")
    @PreAuthorize("@ss.hasPermission('system:dept:query')")
    public CommonResult<PageResult<DeptUserVO>> getDeptUserPage(@RequestBody DeptUserSearchDTO deptUserSearchInfo) {
        PageResult<DeptUserVO> result = deptService.getDeptUserPageByDeptId(deptUserSearchInfo);
        return CommonResult.success(result);
    }
}
