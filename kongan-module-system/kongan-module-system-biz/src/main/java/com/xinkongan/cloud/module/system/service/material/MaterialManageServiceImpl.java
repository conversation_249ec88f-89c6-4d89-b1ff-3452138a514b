package com.xinkongan.cloud.module.system.service.material;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.common.util.map.GeoLocationUtil;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.datapermission.core.plugins.SharePluginParam;
import com.xinkongan.cloud.framework.datapermission.core.util.DataPermissionUtils;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.api.material.MaterialLoadingStatusDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.*;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.*;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialNumStatisticVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialSumVO;
import com.xinkongan.cloud.module.system.convert.material.MaterialConvert;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.file.FileDO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialContrastRecordDO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialDO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.PanoConfigurationDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobDO;
import com.xinkongan.cloud.module.system.dal.dataobject.tenant.TenantDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialContrastRecordMapper;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialMapper;
import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import com.xinkongan.cloud.module.system.dto.MaterialParseDTO;
import com.xinkongan.cloud.module.system.dto.ResourceShareDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.material.MaterialParseStatus;
import com.xinkongan.cloud.module.system.enums.material.MaterialTypeEnums;
import com.xinkongan.cloud.module.system.enums.material.PanoConfTypeEnum;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;
import com.xinkongan.cloud.module.system.framework.file.core.client.s3.S3FileClientConfig;
import com.xinkongan.cloud.module.system.framework.material.SystemMaterialConfig;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordFileService;
import com.xinkongan.cloud.module.system.service.share.IShareService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.module.system.service.tenant.TenantService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import com.xinkongan.cloud.sdk.geo.utils.LocationUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.MATERIAL_IS_NULL;

@Slf4j
@Service
public class MaterialManageServiceImpl implements IMaterialManageService {

    @Resource
    private MaterialMapper materialMapper;

    @Resource
    private MaterialContrastRecordMapper materialContrastRecordMapper;

    @Resource
    private MaterialParseFactory materialParseFactory;

    @Resource
    private IShareService shareService;

    @Resource
    private FileService fileService;

    @Resource
    private DeptService deptService;

    @Resource
    private TenantService tenantService;

    @Resource
    private SystemMaterialConfig systemMaterialConfig;

    @Resource
    private LocationUtil locationUtil;

    @Resource
    private PanoConfigurationService panoConfigurationService;

    @Resource
    private IJobService jobService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private IMaterialReferenceService materialReferenceService;
    @Resource
    private IRedisCacheService redisCacheService;

    @Resource
    private IFlyRecordFileService flyRecordFileService;


    @Override
    public Long materialLoad(MaterialLoadVO materialLoad) {
        FileSaveInfoDTO materialFileInfo = materialLoad.getMaterialFileInfo();
        FileDO materialFIleRecord = fileService.getFileRecordById(materialFileInfo.getFileId());
        if (materialFIleRecord == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        MaterialDO materialInfo = BeanUtils.toBean(materialLoad, MaterialDO.class);
        String name = materialInfo.getName();
        // 去掉.tif 后缀
        if (!StringUtils.isEmpty(name) && name.endsWith(".tif")) {
            materialInfo.setName(name.substring(0, name.lastIndexOf(".tif")));
        }
        materialInfo.setUrl(materialFileInfo.getUrl());
        materialInfo.setCreatorName(SecurityFrameworkUtils.getLoginUserNickname());
        materialInfo.setObjectKey(materialFIleRecord.getPath());
        materialInfo.setIsModel(YesNoEnum.NO.getCode());
        materialInfo.setFileIds(Collections.singletonList(materialFileInfo.getFileId()));
        materialInfo.setStatus(MaterialParseStatus.PARSING.getCode());
        materialInfo.setSize(materialFIleRecord.getSize());
        materialMapper.insert(materialInfo);

        // 素材导入后置处理
        IMaterialParseHandler materialParseHandler = materialParseFactory.getMaterialParseHandlerByType(materialLoad.getType());
        MaterialParseDTO materialParse = BeanUtils.toBean(materialInfo, MaterialParseDTO.class);

        materialParse.setTenantId(TenantContextHolder.getTenantId());
        materialParseHandler.doHandler0(materialParse);
        return materialInfo.getId();
    }


    @Override
    @DataPermission(enable = false)
    public void loadRoutePointPanorama(Long tenantId, Long deptId, Long fileId, Long jobId) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            // 查询文件信息
            FileDO fileInfo = fileService.getFileRecordById(fileId);
            if (fileInfo == null) {
                throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
            }
            String materialName = fileInfo.getName();
            if (materialName.endsWith(".jpeg")) {
                materialName = materialName.substring(0, materialName.lastIndexOf(".jpeg"));
            }
            MaterialDO materialInfo = new MaterialDO();
            materialInfo.setName(materialName);
            materialInfo.setType(MaterialTypeEnums.PANORAMA_MATERIAL.getType());
            materialInfo.setUrl(fileInfo.getUrl());
            materialInfo.setCreatorName(fileInfo.getCreator());
            materialInfo.setObjectKey(fileInfo.getPath());
            materialInfo.setIsModel(YesNoEnum.YES.getCode());
            materialInfo.setFileIds(Collections.singletonList(fileInfo.getId()));
            materialInfo.setStatus(MaterialParseStatus.PARSING.getCode());
            materialInfo.setSize(fileInfo.getSize());
            materialInfo.setTenantId(tenantId);
            materialInfo.setDeptId(deptId);

            // 查询job
            JobDO jobInfo = jobService.getById(jobId);
            if (jobInfo != null) {
                materialInfo.setCreator(jobInfo.getCreator());
                materialInfo.setUpdater(jobInfo.getUpdater());
                // 查询别名
                if (!StringUtils.isEmpty(jobInfo.getCreator())) {
                    AdminUserDO adminUserInfo = adminUserService.getUser(Long.parseLong(jobInfo.getCreator()));
                    if (adminUserInfo != null) {
                        materialInfo.setCreatorName(adminUserInfo.getNickname());
                    }
                }
            }
            materialMapper.insert(materialInfo);

            // 素材导入后置处理
            IMaterialParseHandler materialParseHandler = materialParseFactory.getMaterialParseHandlerByType(materialInfo.getType());
            MaterialParseDTO materialParse = BeanUtils.toBean(materialInfo, MaterialParseDTO.class);

            materialParse.setTenantId(TenantContextHolder.getTenantId());
            materialParseHandler.doHandler0(materialParse);
        } catch (Exception e) {
            log.error("[导入航点全景图失败]，失败原因：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
        } finally {
            TenantContextHolder.clear();
        }
    }

    @Override
    public void delMaterialById(Long materialId) {
        MaterialDO materialInfo = materialMapper.selectById(materialId);
        if (materialInfo == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        // 删除文件相关数据
        List<Long> fileIds = materialInfo.getFileIds();
        fileService.deleteFileByIds(fileIds);
        // 删除素材数据
        materialMapper.deleteById(materialId);
        // 删除标注中的引用数据
        materialReferenceService.deleteMaterialReferenceByMaterialIds(Collections.singletonList(materialId));

        if (Objects.equals(MaterialTypeEnums.PANORAMA_MATERIAL.getType(), materialInfo.getType())) {
            // 全景图同步执行一下删除飞行记录表中的文件记录
            flyRecordFileService.deleteByUrl(materialInfo.getUrl());
        }
        // 删除文件夹类的数据
        MaterialTypeEnums materialEnum = MaterialTypeEnums.getMaterialEnumByType(materialInfo.getType());
        S3FileClientConfig fileClientConfig = (S3FileClientConfig) fileService.getFileClientConfig();
        String tileUrl = materialInfo.getTileUrl();
        log.info("[删除素材目录] 素材ID:{} 类型:{} 原始路径:{}", materialInfo.getId(), materialEnum, tileUrl);

        if (tileUrl != null) {
            String baseDir = tileUrl.replace(fileClientConfig.getDomain() + "/", "");

            assert materialEnum != null;
            String dir = switch (materialEnum) {
                case TWO_GIS_DIMENSION_ORTHOGRAM -> baseDir.replace("{z}/{x}_{reverseY}.png", "");
                case THREE_D_MODEL_MATERIAL -> baseDir.replace("tileset.json", "");
                default -> null;
            };

            if (dir != null && !dir.isBlank()) {
                log.info("[删除素材目录] 素材ID:{} 最终目录:{}", materialInfo.getId(), dir);
                try {
                    fileService.deleteDirRecur(dir);
                } catch (Exception e) {
                    log.error("[删除素材目录异常] 素材ID:{} 目录:{} 错误:{}",
                            materialInfo.getId(), dir, ExceptionUtils.getRootCauseMessage(e));
                }
            }
        }
        // 删除素材对比
        delContrastRecordByMaterialId(materialId);
    }

    public void delContrastRecordByMaterialId(Long materialId) {
        // 1. 查询需要删除的记录（仅获取文件ID字段）
        List<Long> fileIds = materialContrastRecordMapper.selectList(new LambdaQueryWrapperX<MaterialContrastRecordDO>()
                        .eq(MaterialContrastRecordDO::getOrgId, materialId)
                        .select(MaterialContrastRecordDO::getFileIds))
                .stream()
                .filter(record -> CollectionUtil.isNotEmpty(record.getFileIds())) // 空文件过滤
                .flatMap(record -> record.getFileIds().stream())
                .collect(Collectors.toList());

        // 2. 批量删除对比记录
        materialContrastRecordMapper.delete(new LambdaQueryWrapperX<MaterialContrastRecordDO>()
                .eq(MaterialContrastRecordDO::getOrgId, materialId));

        // 3. 批量删除关联文件
        if (CollectionUtil.isNotEmpty(fileIds)) {
            try {
                fileService.deleteFileByIds(fileIds);
                log.info("[删除素材对比记录] 素材ID:{} 成功删除文件:{}个", materialId, fileIds.size());
            } catch (Exception e) {
                log.error("[删除素材对比记录异常] 素材ID:{} 错误:{}", materialId, ExceptionUtils.getRootCauseMessage(e));
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.FILE_DELETE_FAILURE);
            }
        }
    }

    @Override
    public Long updateMaterialInfo(MaterialUpdateDTO materialUpdateInfo) {
        MaterialDO materialDO = materialMapper.selectById(materialUpdateInfo.getMaterialId());
        if (materialDO == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        if (StringUtils.isNotEmpty(materialUpdateInfo.getMaterialName())) {
            materialDO.setName(materialUpdateInfo.getMaterialName());
        }
        if (materialUpdateInfo.getDeptId() != null) {
            materialDO.setDeptId(materialUpdateInfo.getDeptId());
        }
        if (materialUpdateInfo.getClarityLevel() != null) {
            materialDO.setClarityLevel(materialUpdateInfo.getClarityLevel());
        }
        materialMapper.updateById(materialDO);
        return materialDO.getId();
    }


    @Override
    public void updateMaterialThumbnailInfo(MaterialThumbnailDTO materialThumbnail) {
        MaterialDO materialDO = materialMapper.selectById(materialThumbnail.getMaterialId());
        if (materialDO == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        if (StringUtils.isNotEmpty(materialDO.getJpgUrl())) {
            return;
        }
        materialDO.setJpgUrl(materialThumbnail.getUrl());
        materialDO.getFileIds().add(materialThumbnail.getFileId());
        if (materialThumbnail.getLongitude() != null && materialThumbnail.getLatitude() != null) {
            materialDO.setCenterLon(materialThumbnail.getLongitude());
            materialDO.setCenterLat(materialThumbnail.getLatitude());
            String address = locationUtil.getLocationByWg84(materialThumbnail.getLongitude(), materialThumbnail.getLatitude());
            materialDO.setAddress(address);
        }
        materialMapper.updateById(materialDO);
    }

    @Override
    public MaterialDetailVO getMaterialInfoById(Long materialId) {
        AtomicReference<MaterialDetailVO> materialInfo = new AtomicReference<>();
        DataPermissionUtils.executeIgnore(() -> {
            MaterialDO materialDO = materialMapper.selectById(materialId);
            if (materialDO == null) {
                throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
            }
            MaterialDetailVO convert = this.convert(materialDO);
            materialInfo.set(convert);
        });
        return materialInfo.get();
    }


    @Override
    public MaterialDetailVO getMaterialInfoByIdForIgnore(Long materialId) {
        AtomicReference<MaterialDetailVO> materialInfo = new AtomicReference<>();
        DataPermissionUtils.executeIgnore(() -> {
            MaterialDO materialDO = materialMapper.selectById(materialId);
            if (materialDO == null) {
                materialInfo.set(null);
                return;
            }
            MaterialDetailVO convert = this.convert(materialDO);
            materialInfo.set(convert);
        });
        return materialInfo.get();
    }

    @Override
    public PageResult<MaterialInfoVO> getMaterialPage(MaterialSearchDTO searchParams) {
        Page<MaterialInfoVO> page = new Page<>(searchParams.getPage(), searchParams.getOffset());
        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.MATERIAL_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.MATERIAL_RESOURCE.getResourceType())
                .shareFlag(searchParams.getShareFlag())
                .build();
        List<MaterialInfoVO> records = materialMapper.selectMaterialByPageWithShare(page, sharePluginParam, searchParams);
        return new PageResult<>(records, page.getTotal());
    }


    @Override
    public List<MaterialInfoVO> getMaterialList(MaterialSearchDTO searchParams) {

        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.MATERIAL_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.MATERIAL_RESOURCE.getResourceType())
                .shareFlag(searchParams.getShareFlag())
                .build();

        List<MaterialInfoVO> materialInfoVOS = materialMapper.selectMaterialByListWithShare(sharePluginParam, searchParams);
        if (CollUtil.isEmpty(materialInfoVOS)) {
            return Collections.emptyList();
        }

        // 查询缓存中的已加载的素材id列表
        List<MaterialLoadingStatusDTO> loadingStatusByCache = getLoadingStatusByCache(TenantContextHolder.getTenantId(), SecurityFrameworkUtils.getLoginUserId());
        if (CollUtil.isNotEmpty(loadingStatusByCache)) {
            Map<Long, MaterialLoadingStatusDTO> loadingStatusDTOMap = loadingStatusByCache.stream().collect(Collectors.toMap(MaterialLoadingStatusDTO::getMaterialId, v -> v));
            // 遍历结果修改加载状态
            for (MaterialInfoVO materialInfoVO : materialInfoVOS) {
                if (loadingStatusDTOMap.containsKey(materialInfoVO.getId())) {
                    MaterialLoadingStatusDTO materialLoadingStatusDTO = loadingStatusDTOMap.get(materialInfoVO.getId());
                    materialInfoVO.setLoadingStatus(materialLoadingStatusDTO.getLoadingStatus());
                }
            }
        }
        return materialInfoVOS;
    }

    @Override
    public void materialShare(MaterialShareDTO materialShareParams) {
        MaterialDO materialInfo = materialMapper.selectById(materialShareParams.getMaterialId());
        if (materialInfo == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        ResourceShareDTO resourceShareParam = ResourceShareDTO.builder().resourceId(materialShareParams.getMaterialId()).resourceType(ResourceShareTypeEnum.MATERIAL_RESOURCE.getResourceType()).shareDeptIds(materialShareParams.getDeptIds()).oriDeptId(materialInfo.getDeptId()).build();
        shareService.resourceShareDeptIds(resourceShareParam);
    }

    @Override
    public MaterialShareVO getMaterialShareDept(Long materialId) {
        List<Long> deptIds = shareService.getResourceSharesDept(materialId);
        return MaterialShareVO.builder().deptIds(deptIds).materialId(materialId).build();
    }

    private MaterialDetailVO convert(MaterialDO materialInfo) {
        MaterialDetailVO materialDetailVO = BeanUtils.toBean(materialInfo, MaterialDetailVO.class);
        if (materialInfo.getDeptId() != null) {
            DeptDO dept = deptService.getDept(materialInfo.getDeptId());
            if (dept != null) {
                materialDetailVO.setDeptName(dept.getName());
            }
        }
        // 补充分享字段
        boolean resourceShare = shareService.checkResourceShare(materialInfo.getId(), ResourceShareTypeEnum.MATERIAL_RESOURCE.getResourceType());
        materialDetailVO.setShareFlag(resourceShare ? 1 : 0);
        if (resourceShare) {
            materialDetailVO.setDeptName("--");
            materialDetailVO.setCreatorName("--");
        }
        return materialDetailVO;
    }

    @Override
    public MaterialNumStatisticVO getMaterialStatistic() {

        MaterialNumStatisticVO materialNumStatisticVO = new MaterialNumStatisticVO();
        // 使用group by 语句统计 各个type 的总数
        List<MaterialSumVO> materialSumList = materialMapper.selectMaterialSumByType();
        if (CollectionUtil.isEmpty(materialSumList)) {
            return materialNumStatisticVO;
        }
        Map<Integer, MaterialSumVO> materialSumMap = materialSumList.stream().collect(Collectors.toMap(MaterialSumVO::getType, v -> v));
        // 二维素材总数
        MaterialSumVO materialSumVO = materialSumMap.get(MaterialTypeEnums.TWO_GIS_DIMENSION_ORTHOGRAM.getType());
        if (materialSumVO != null) {
            materialNumStatisticVO.setTwoMaterialSum(materialSumVO.getCount());
            materialNumStatisticVO.setTwoMaterialSize(convertToGB(materialSumVO.getStorageSize()));
        }
        // 三维素材总数
        MaterialSumVO threeMaterialSumVO = materialSumMap.get(MaterialTypeEnums.THREE_D_MODEL_MATERIAL.getType());
        if (threeMaterialSumVO != null) {
            materialNumStatisticVO.setThreeMaterialSum(threeMaterialSumVO.getCount());
            materialNumStatisticVO.setThreeMaterialSize(convertToGB(threeMaterialSumVO.getStorageSize()));
        }
        // 全景素材总数
        MaterialSumVO panoramaMaterialSumVO = materialSumMap.get(MaterialTypeEnums.PANORAMA_MATERIAL.getType());
        if (panoramaMaterialSumVO != null) {
            materialNumStatisticVO.setPanoraMaterialSum(panoramaMaterialSumVO.getCount());
            materialNumStatisticVO.setPanoraMaterialSize(convertToGB(panoramaMaterialSumVO.getStorageSize()));
        }
        // 遍历map计算素材总数和素材大小总数
        materialNumStatisticVO.setMaterialSum(materialSumList.stream().map(MaterialSumVO::getCount).reduce(Integer::sum).orElse(0));

        // 计算其他文件大小
        TenantDO tenantInfo = tenantService.getTenant(TenantContextHolder.getTenantId());
        Long useStorageSize = tenantInfo.getUseStorageSize();
        Long materialStorageSize = materialSumList.stream().map(MaterialSumVO::getStorageSize).reduce(Long::sum).orElse(0L);
        materialNumStatisticVO.setOtherFileSize(convertToGB(useStorageSize - materialStorageSize));

        materialNumStatisticVO.setTenantStorageSize(convertToGB(tenantInfo.getStorageSize()));
        return materialNumStatisticVO;
    }


    private Double convertToGB(Long size) {
        if (size == null) {
            return 0.0;
        }
        return size / 1024.0 / 1024.0 / 1024.0;
    }

    @Override
    public PageResult<MaterialContrastRecordVO> getMaterialContrastRecord(MaterialContrastSearchDTO materialContrastRecordSearch) {
        Page<MaterialInfoVO> page = new Page<>(materialContrastRecordSearch.getPage(), materialContrastRecordSearch.getOffset());
        List<MaterialContrastRecordVO> records = materialContrastRecordMapper.getMaterialContrastRecord(page, materialContrastRecordSearch);
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public Long saveMaterialContrastRecord(MaterialContrastSaveDTO materialContrastSaveInfo) {
        FileSaveInfoDTO thumbnailFileInfo = materialContrastSaveInfo.getThumbnailFileInfo();
        MaterialContrastRecordDO materialContrastRecordDO = BeanUtils.toBean(materialContrastSaveInfo, MaterialContrastRecordDO.class);
        materialContrastRecordDO.setFileIds(Collections.singletonList(thumbnailFileInfo.getFileId()));
        materialContrastRecordDO.setThumbnail(thumbnailFileInfo.getUrl());
        materialContrastRecordDO.setName(materialContrastSaveInfo.getContrastName());
        materialContrastRecordMapper.insert(materialContrastRecordDO);
        return materialContrastRecordDO.getId();
    }

    @Override
    public void delMaterialContrastRecord(Long id) {
        MaterialContrastRecordDO materialContrastRecordDO = materialContrastRecordMapper.selectById(id);
        if (materialContrastRecordDO == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_CONTRAST_RECORD_ERROR_NOT_FOUND);
        }
        List<Long> fileIds = materialContrastRecordDO.getFileIds();
        fileService.deleteFileByIds(fileIds);
        materialContrastRecordMapper.deleteById(id);
    }

    @Override
    public Long updateMaterialContrastRecord(MaterialContrastUpdateDTO materialContrastUpdateInfo) {
        MaterialContrastRecordDO materialContrastRecordDO = materialContrastRecordMapper.selectById(materialContrastUpdateInfo.getContrastId());
        if (materialContrastRecordDO == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_CONTRAST_RECORD_ERROR_NOT_FOUND);
        }
        materialContrastRecordDO.setName(materialContrastUpdateInfo.getContrastName());
        materialContrastRecordDO.setUpdateTime(null);
        materialContrastRecordMapper.updateById(materialContrastRecordDO);
        return materialContrastRecordDO.getId();
    }


    @Override
    public List<MaterialInfoVO> getMaterialCompareRecordList(MaterialCompareSearchDTO materialCompareSearch) {
        AtomicReference<MaterialDO> materialDO = new AtomicReference<>();
        DataPermissionUtils.executeIgnore(() -> materialDO.set(materialMapper.selectById(materialCompareSearch.getMaterialId())));
        if (materialDO.get() == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        materialCompareSearch.setLeftUpLon(materialDO.get().getLeftUpLon());
        materialCompareSearch.setLeftUpLat(materialDO.get().getLeftUpLat());
        materialCompareSearch.setRightDownLon(materialDO.get().getRightDownLon());
        materialCompareSearch.setRightDownLat(materialDO.get().getRightDownLat());
        materialCompareSearch.setRadius(systemMaterialConfig.getRadius());
        materialCompareSearch.setCenterLon(materialDO.get().getCenterLon());
        materialCompareSearch.setCenterLat(materialDO.get().getCenterLat());

        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.MATERIAL_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.MATERIAL_RESOURCE.getResourceType())
                .build();
        return materialMapper.selectMaterialContrastByPageWithShare(sharePluginParam, materialCompareSearch);
    }


    @Override
    public MaterialContrastRecordDetailVO getMaterialContrastRecordDetail(Long id) {
        MaterialContrastRecordDO materialContrastRecordDO = materialContrastRecordMapper.selectById(id);
        if (materialContrastRecordDO == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_CONTRAST_RECORD_ERROR_NOT_FOUND);
        }
        MaterialContrastRecordDetailVO materialContrastRecordDetailVO = BeanUtils.toBean(materialContrastRecordDO, MaterialContrastRecordDetailVO.class);
        materialContrastRecordDetailVO.setContrastName(materialContrastRecordDO.getName());
        // 查询原始素材
        if (materialContrastRecordDO.getOrgId() != null) {
            materialContrastRecordDetailVO.setOrgMaterial(this.getMaterialInfoById(materialContrastRecordDO.getOrgId()));
        }
        // 查询对比素材
        if (materialContrastRecordDO.getContrastId() != null) {
            materialContrastRecordDetailVO.setContrastMaterial(this.getMaterialInfoByIdForIgnore(materialContrastRecordDO.getContrastId()));
        }
        return materialContrastRecordDetailVO;
    }


    @Override
    @DataPermission(enable = false)
    public List<MaterialInfoVO> getPanoByConf(Long materialId) {
        List<MaterialInfoVO> materialVOS = new ArrayList<>();
        MaterialDO materialDO = materialMapper.selectById(materialId);
        if (ObjectUtil.isEmpty(materialDO)) {
            throw new ServiceException(MATERIAL_IS_NULL);
        }
        materialVOS.add(MaterialConvert.INSTANCE.convert(materialDO));
        if (ObjectUtil.isEmpty(materialDO.getCenterLat()) || ObjectUtil.isEmpty(materialDO.getCenterLon())) {
            return materialVOS;
        }
        List<MaterialDO> materialDOS = materialMapper.selectList(
                new LambdaQueryWrapperX<MaterialDO>()
                        .ne(MaterialDO::getId, materialId)
                        .eq(MaterialDO::getType, MaterialTypeEnums.PANORAMA_MATERIAL.getType())
                        .eq(MaterialDO::getStatus, MaterialParseStatus.SUCCESS.getCode())
                        .isNotNull(MaterialDO::getCenterLat)
                        .isNotNull(MaterialDO::getCenterLon)
        );
        if (CollUtil.isEmpty(materialDOS)) {
            return materialVOS;
        }
        // 查询全景图配置
        PanoConfigurationDO panoConf = panoConfigurationService.getPanoConf(materialId);
        if (panoConf.getType().equals(PanoConfTypeEnum.ALL.getCode())) {
            materialVOS.addAll(materialDOS.stream().map(MaterialConvert.INSTANCE::convert).toList());
            return materialVOS;
        }
        List<MaterialInfoVO> materialVOList = materialDOS
                .stream()
                .filter(material ->
                        GeoLocationUtil.calculateDistance(
                                materialDO.getCenterLat(),
                                materialDO.getCenterLon(),
                                material.getCenterLat(),
                                material.getCenterLon()
                        ) <= panoConf.getVisualRange() * 1000
                ).map(MaterialConvert.INSTANCE::convert).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(materialVOList)) {
            materialVOS.addAll(materialVOList);
        }
        return materialVOS;
    }

    @Override
    @DataPermission(enable = false)
    public List<MaterialInfoVO> getMaterialListByIds(List<Long> materialIds) {
        if (CollectionUtil.isEmpty(materialIds)) {
            return new ArrayList<>();
        }
        List<MaterialDO> materialInfos = materialMapper.selectList(
                new LambdaQueryWrapperX<MaterialDO>()
                        .in(MaterialDO::getId, materialIds)
        );
        return materialInfos.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void updateLoadingStatus(MaterialLoadingStatusDTO materialLoadingStatusVO) {

        // 查询缓存
        List<MaterialLoadingStatusDTO> list = getLoadingStatusByCache(TenantContextHolder.getTenantId(), SecurityFrameworkUtils.getLoginUserId());

        // 修改变更
        if (CollUtil.isNotEmpty(list)) {
            // 缓存中是否存在当前素材
            boolean hasMaterial = !list.stream().filter(materialLoadingStatusDTO -> materialLoadingStatusDTO.getMaterialId().equals(materialLoadingStatusVO.getMaterialId())).findAny().isEmpty();
            if (hasMaterial) {
                // 找到缓存中此素材并修改加载状态
                for (MaterialLoadingStatusDTO status : list) {
                    if (status.getMaterialId().equals(materialLoadingStatusVO.getMaterialId())) {
                        status.setLoadingStatus(materialLoadingStatusVO.getLoadingStatus());
                        break;
                    }
                }
            } else {
                // 缓存中不存在当前素材 直接添加
                list.add(materialLoadingStatusVO);
            }
        } else {
            // 缓存中没有 直接保存即可
            list.add(materialLoadingStatusVO);
        }

        // 如果是取消加载 直接删除即可
        list.removeIf(status -> Objects.equals(YesNoEnum.NO.getCode(), status.getLoadingStatus()));

        // 如果取消之后 缓存为空 直接删除缓存
        if (CollUtil.isEmpty(list)) {
            redisCacheService.delete(String.format(RedisKeyConstants.USER_MATERIAL_LOADING_STATUS, TenantContextHolder.getTenantId(), SecurityFrameworkUtils.getLoginUserId()));
        } else {
            // 缓存没有空 保存缓存
            String redisKey = String.format(RedisKeyConstants.USER_MATERIAL_LOADING_STATUS, TenantContextHolder.getTenantId(), SecurityFrameworkUtils.getLoginUserId());
            redisCacheService.put(redisKey, JSONUtil.toJsonStr(list));
        }

    }

    private List<MaterialLoadingStatusDTO> getLoadingStatusByCache(Long tenantId, Long userId) {
        String redisKey = String.format(RedisKeyConstants.USER_MATERIAL_LOADING_STATUS, tenantId, userId);
        String loadingStatusJson = redisCacheService.get(redisKey, String.class);
        if (StringUtils.isNotEmpty(loadingStatusJson)) {
            return JSONUtil.toList(loadingStatusJson, MaterialLoadingStatusDTO.class);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public PageResult<MaterialInfoVO> selectMaterialByPageDeptId(MaterialSearchDTO materialSearchInfo) {
        Page<MaterialInfoVO> page = new Page<>(materialSearchInfo.getPage(), materialSearchInfo.getOffset());
        Set<Long> deptIds = deptService.getChildDeptByDeptId(materialSearchInfo.getDeptId());
        materialSearchInfo.setDeptIds(deptIds.stream().toList());
        List<MaterialInfoVO> records = materialMapper.selectMaterialByPageDeptId(page, materialSearchInfo);
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DataPermission(enable = false)
    public void deleteMaterialByDeptId(Long deptId) {
        log.info("[删除组织下的素材] 组织ID：{}", deptId);
        try {
            // 查询当前组织下的所有素材
            List<MaterialDO> materialList = materialMapper.selectList(
                    new LambdaQueryWrapperX<MaterialDO>()
                            .eq(MaterialDO::getDeptId, deptId)
            );

            if (CollectionUtil.isEmpty(materialList)) {
                log.info("[删除组织下的素材] 组织ID：{} 下没有素材数据", deptId);
                return;
            }

            // 逐个删除素材（调用现有的删除方法，确保完整删除逻辑）
            for (MaterialDO material : materialList) {
                try {
                    delMaterialById(material.getId());
                    log.info("[删除组织下的素材] 成功删除素材ID：{}", material.getId());
                } catch (Exception e) {
                    log.error("[删除组织下的素材] 删除素材ID：{} 失败", material.getId(), e);
                }
            }

            log.info("[删除组织下的素材] 组织ID：{} 删除完成，共删除 {} 个素材", deptId, materialList.size());
        } catch (Exception e) {
            log.error("[删除组织下的素材] 组织ID：{} 删除失败", deptId, e);
            throw e;
        }
    }
}
