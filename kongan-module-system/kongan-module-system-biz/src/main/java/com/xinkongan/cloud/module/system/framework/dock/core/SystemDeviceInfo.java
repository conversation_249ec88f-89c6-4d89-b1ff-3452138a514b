package com.xinkongan.cloud.module.system.framework.dock.core;

import com.xinkongan.cloud.sdk.dock.mqtt.config.context.DeviceInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/11/12
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SystemDeviceInfo extends DeviceInfo {

    private Long tenantId;
    private Long deptId;

}
