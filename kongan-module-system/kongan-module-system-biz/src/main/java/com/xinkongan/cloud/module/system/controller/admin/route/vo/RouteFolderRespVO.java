package com.xinkongan.cloud.module.system.controller.admin.route.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RouteFolderRespVO {

    @Schema(description = "航线序号", example = "1")
    private Integer waylineId;

    @Schema(description = "航点列表", example = "[{},{}]")
    private List<WayPointRespVO> points;

    public List<WayPointRespVO> getPoints() {
        if (points == null) {
            this.points = new ArrayList<>();
        }
        return points;
    }
}
