package com.xinkongan.cloud.module.system.controller.admin.task;

import cn.hutool.core.collection.CollUtil;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.*;
import com.xinkongan.cloud.module.system.dto.DockSecurityCheckDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.service.task.IDockSecurityCheckService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.module.system.service.task.IJobStatisticService;
import com.xinkongan.cloud.module.system.vo.DeviceSecurityCheckVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 机场任务管理
 * <AUTHOR>
 * @Date 2025/1/7 19:42
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 机场任务管理")
@RequestMapping("/system/job")
public class DockJobController {
    @Resource
    private IJobService jobService;

    @Resource
    private IDockSecurityCheckService dockSecurityCheckService;

    @Resource
    private IJobStatisticService jobStatisticService;

    /**
     * 任务分页
     *
     * <AUTHOR>
     * @date 2025/1/7 19:43
     **/
    @PreAuthorize("@ss.hasAnyPermissions('system:job:query','system:alarm:query')")
    @GetMapping("/page")
    @Operation(summary = "任务分页", description = "任务分页")
    CommonResult<PageResult<JobPageRespVO>> page(@Valid JobPageReqVO reqVO) {
        PageResult<JobPageRespVO> page = jobService.page(reqVO);
        return success(page);
    }

    /**
     * 获取任务详情
     *
     * <AUTHOR>
     * @date 2025/1/14 10:03
     **/
    @PreAuthorize("@ss.hasAnyPermissions('system:job:query','system:job:info:query','system:alarm:query')")
    @GetMapping("/getByJobId")
    @Operation(summary = "根据任务id获取任务详情", description = "根据任务id获取任务详情")
    CommonResult<JobDetailRespVO> getByJobId(@RequestParam Long jobId) {
        JobDetailRespVO jobDetailRespVO = jobService.getByJobId(jobId);
        return success(jobDetailRespVO);
    }

    /**
     * 删除任务 若任务状态为“执行中”/“待审批”则无法删除
     *
     * <AUTHOR>
     * @date 2025/1/14 14:16
     **/
    @PreAuthorize("@ss.hasPermission('system:job:delete')")
    @PostMapping("/deleteByJobId")
    @Operation(summary = "删除任务", description = "删除任务")
    CommonResult<Boolean> deleteJobByJobId(@RequestParam Long jobId) {
        Boolean delete = jobService.deleteJob(jobId);
        return success(delete);
    }

    /**
     * 批量删除任务接口
     *
     * <AUTHOR>
     * @date 2025/1/14 15:25
     **/
    @PreAuthorize("@ss.hasPermission('system:job:delete')")
    @PostMapping("/deleteBatchByJobIds")
    @Operation(summary = "批量删除任务", description = "批量删除任务")
    CommonResult<Boolean> deleteJobBatchByJobIds(@RequestBody List<Long> ids) {
        Boolean delete = jobService.deleteJobBatch(ids);
        return success(delete);
    }

    /**
     * 取消执行任务
     *
     * <AUTHOR>
     * @date 2025/1/14 16:21
     **/
    @PreAuthorize("@ss.hasPermission('system:job:cancel')")
    @PostMapping("/cancelByJobId")
    @Operation(summary = "取消执行任务", description = "取消执行任务")
    CommonResult<Boolean> cancelJobByJobId(@RequestParam Long jobId) {
        Boolean cancel = jobService.cancelJob(jobId);
        return success(cancel);
    }

    /**
     * 任务下发安全检查
     *
     * @param dockSecurityCheckInfo 安全检查参数
     * @return 检查结果
     */
    @PostMapping("/security/check")
    @Operation(summary = "任务下发安全检查", description = "任务下发安全检查")
    public CommonResult<List<DeviceSecurityCheckVO>> jobSecurityCheck(@RequestBody DockSecurityCheckDTO dockSecurityCheckInfo) {
        List<DeviceSecurityCheckVO> deviceSecurityCheckResult = dockSecurityCheckService.checkSecurityBeforeDeliverTask(dockSecurityCheckInfo);
        return CommonResult.success(deviceSecurityCheckResult);
    }


    /**
     * 一键取消
     *
     * <AUTHOR>
     * @date 2025/1/14 16:51
     **/
    @PreAuthorize("@ss.hasPermission('system:job:cancel')")
    @PostMapping("/cancelByIds")
    @Operation(summary = "一键取消", description = "一键取消")
    CommonResult<Boolean> cancelByIds(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw exception(ErrorCodeConstants.TASK_PARAMS_ERROR, "一键取消任务id列表不能为空");
        }
        Boolean cancelBatch = jobService.cancelJobBatch(ids);
        return success(cancelBatch);
    }

    /**
     * 结束任务 仅对“未完成”的任务支持“结束任务操作”
     *
     * <AUTHOR>
     * @date 2025/1/14 16:51
     **/
    @PreAuthorize("@ss.hasAnyPermissions('system:job:end','system:alarm:end')")
    @PostMapping("/endJobByJobId")
    @Operation(summary = "结束任务", description = "结束任务")
    CommonResult<Boolean> endJobByJobId(@RequestParam Long jobId) {
        Boolean end = jobService.endJobById(jobId);
        return success(end);
    }

    @GetMapping("/getJobFlyInfo/{dockSn}")
    @Operation(summary = "获取任务飞行信息", description = "获取任务飞行信息")
    CommonResult<JobFlyInfo> getJobFlyInfo(@PathVariable String dockSn) {
        JobFlyInfo jobFlyInfo = jobService.getExecTaskCacheByDockSn(dockSn);
        return success(jobFlyInfo);
    }

    @PreAuthorize("@ss.hasPermission('system:schedule:query')")
    @Operation(summary = "任务排期列表")
    @GetMapping("/scheduleList")
    public CommonResult<List<ScheduleFlyInfo>> scheduleList(@Valid ScheduleFlyReqVO scheduleFlyReqVO) {
        List<ScheduleFlyInfo> respVO = jobService.scheduleList(scheduleFlyReqVO);
        return CommonResult.success(respVO);
    }

    @PreAuthorize("@ss.hasPermission('system:dock:schedule:query')")
    @Operation(summary = "设备管理模块任务排期列表")
    @GetMapping("/scheduleListForDevice")
    public CommonResult<List<ScheduleFlyInfo>> scheduleListForJob(@Valid ScheduleFlyReqVO scheduleFlyReqVO) {
        List<ScheduleFlyInfo> respVO = jobService.scheduleList(scheduleFlyReqVO);
        return CommonResult.success(respVO);
    }

    @Operation(summary = "查询任务页码")
    @GetMapping("/getPageNumById")
    public CommonResult<Integer> getPageNumById(@RequestParam("id") Long id, @RequestParam("pageSize") Integer pageSize) {
        Integer pageNum = jobService.getPageNumById(id, pageSize);
        return CommonResult.success(pageNum);
    }

    @Operation(summary = "任务数量统计")
    @GetMapping("/statisticCount")
    public CommonResult<JobStatisticCountVO> statisticCount() {
        return CommonResult.success(jobStatisticService.statisticCount());
    }

    @Operation(summary = "任务类型数量统计")
    @GetMapping("/statisticCountByType")
    public CommonResult<JobSceneStatisticCountVO> statisticCountByType() {
        return CommonResult.success(jobStatisticService.statisticCountByType());
    }

    @Operation(summary = "任务类型时间段统计")
    @GetMapping("/statisticCountByTimeRange")
    public CommonResult<JobSceneStatisticCountVO> statisticCountByTimeRange(@Valid  JobStatisticByTimeRangeReqVO reqVO) {
        return CommonResult.success(jobStatisticService.statisticCountByTimeRange(reqVO));
    }


}