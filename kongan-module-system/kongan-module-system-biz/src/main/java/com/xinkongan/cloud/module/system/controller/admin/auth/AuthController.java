package com.xinkongan.cloud.module.system.controller.admin.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xinkongan.cloud.framework.common.enums.CommonStatusEnum;
import com.xinkongan.cloud.framework.common.enums.UserTypeEnum;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.util.crypto.RSAPasswordEncoder;
import com.xinkongan.cloud.framework.ratelimiter.core.annotation.RateLimiter;
import com.xinkongan.cloud.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import com.xinkongan.cloud.framework.security.config.SecurityProperties;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.api.social.dto.SocialUserBindReqDTO;
import com.xinkongan.cloud.module.system.controller.admin.auth.vo.*;
import com.xinkongan.cloud.module.system.controller.admin.sms.vo.code.SmsCodeSendInfoVO;
import com.xinkongan.cloud.module.system.convert.auth.AuthConvert;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.MenuDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.enums.logger.LoginLogTypeEnum;
import com.xinkongan.cloud.module.system.service.auth.AdminAuthService;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.permission.IPermissionCacheService;
import com.xinkongan.cloud.module.system.service.permission.MenuService;
import com.xinkongan.cloud.module.system.service.sms.SmsCodeService;
import com.xinkongan.cloud.module.system.service.social.SocialClientService;
import com.xinkongan.cloud.module.system.service.social.SocialUserService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;
import static com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 认证相关")
@RequestMapping("/system/auth")
public class AuthController {

    @Resource
    private AdminAuthService authService;
    @Resource
    private AdminUserService userService;
    @Resource
    private MenuService menuService;
    @Resource
    private SocialClientService socialClientService;
    @Resource
    private SecurityProperties securityProperties;
    @Resource
    private RSAPasswordEncoder rsaPasswordEncoder;
    @Resource
    private SmsCodeService smsCodeService;
    @Resource
    private SocialUserService socialUserService;

    @Resource
    private IPermissionCacheService permissionCacheService;
    @Resource
    private DeptService deptService;


    @PermitAll
    @PostMapping("/reset/pwd")
    @Operation(summary = "认证相关-重置密码")
    @RateLimiter(keyResolver = ClientIpRateLimiterKeyResolver.class, time = 1, count = 5)
    public CommonResult<Boolean> resetPassword(@RequestBody AuthResetPwdVO authResetPwd) {
        // 忽略租户，该接口仅用于登录页的忘记密码
        authResetPwd.setPassword(rsaPasswordEncoder.decode(authResetPwd.getPassword()));
        AtomicReference<Boolean> atomicReference = new AtomicReference<>();
        TenantUtils.executeIgnore(() -> atomicReference.set(authService.resetPassword(authResetPwd)));
        return CommonResult.success(atomicReference.get());
    }


    @PermitAll
    @PostMapping("/rsa/public/key")
    @Operation(summary = "认证相关-rsa公钥")
    @RateLimiter(keyResolver = ClientIpRateLimiterKeyResolver.class, time = 1, count = 5)
    public CommonResult<String> getRsaPublicKey() {
        String rsaPublicKey = rsaPasswordEncoder.getRsaPublicKey();
        return CommonResult.success(rsaPublicKey);
    }


    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "认证相关-账号登录")
    @RateLimiter(keyResolver = ClientIpRateLimiterKeyResolver.class, time = 1, count = 5)
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        // 将账号密码使用gm2 进行解密
        reqVO.setUsername(rsaPasswordEncoder.decode(reqVO.getUsername()));
        reqVO.setPassword(rsaPasswordEncoder.decode(reqVO.getPassword()));
        return success(authService.login(reqVO));
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "认证相关-登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "认证相关-刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    public CommonResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    @GetMapping("/get-permission-info")
    @Operation(summary = "认证相关-权限信息")
    public CommonResult<AuthPermissionInfoRespVO> getPermissionInfo() {
        // 1.1 获得用户信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        if (user == null) {
            return success(null);
        }

        // 获得组织信息
        DeptDO dept = deptService.getDept(user.getDeptId());

        // 1.2 获得角色列表
        List<RoleDO> roles = permissionCacheService.getUserRoleDosById(getLoginUserId());
        if (CollUtil.isEmpty(roles)) {
            return success(AuthConvert.INSTANCE.convert(user, Collections.emptyList(), Collections.emptyList(), dept));
        }
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色

        // 1.3 获得菜单列表
        List<MenuDO> menuList = permissionCacheService.getUserMenuInfosByUserId(getLoginUserId());
        menuList = menuService.filterDisableMenus(menuList);

        // 2. 拼接结果返回
        return success(AuthConvert.INSTANCE.convert(user, roles, menuList, dept));
    }

    // ========== 短信登录相关 ==========

    @PostMapping("/sms-login")
    @PermitAll
    @Operation(summary = "认证相关-短信登录")
    public CommonResult<AuthLoginRespVO> smsLogin(@RequestBody @Valid AuthSmsLoginReqVO reqVO) {
        return success(authService.smsLogin(reqVO));
    }

    @PostMapping("/send-sms-code")
    @PermitAll
    @Operation(summary = "认证相关-发送验证码")
    @RateLimiter(keyResolver = ClientIpRateLimiterKeyResolver.class, time = 1, count = 2)
    public CommonResult<SmsCodeSendInfoVO> sendLoginSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        CommonResult<SmsCodeSendInfoVO> smsValidReq = smsCodeService.validSmsSendFrequency(reqVO.getMobile());
        if (smsValidReq != null) {
            return smsValidReq;
        }
        // 忽略租户
        TenantUtils.executeIgnore(() -> authService.sendSmsCode(reqVO));
        return success();
    }

    // ========== 社交登录相关 ==========

    @GetMapping("/social-auth-redirect")
    @PermitAll
    @Operation(summary = "认证相关-社交跳转")
    @Parameters({
            @Parameter(name = "type", description = "社交类型", required = true),
            @Parameter(name = "redirectUri", description = "回调路径")
    })
    public CommonResult<String> socialLogin(@RequestParam("type") Integer type,
                                            @RequestParam("redirectUri") String redirectUri) {
        return success(socialClientService.getAuthorizeUrl(
                type, UserTypeEnum.ADMIN.getValue(), redirectUri));
    }

    @PermitAll
    @PostMapping("/social-login")
    @Operation(summary = "认证相关-社交账号登录", description = "适合未登录的用户，但是社交账号已绑定用户")
    public CommonResult<AuthLoginRespVO> socialQuickLogin(@RequestBody @Valid AuthSocialLoginReqVO reqVO) {
        // 忽略租户
        AtomicReference<AuthLoginRespVO> atomicReference = new AtomicReference<>();
        TenantUtils.executeIgnore(() -> atomicReference.set(authService.socialLogin(reqVO)));
        return success(atomicReference.get());
    }


    @PermitAll
    @PostMapping("/social-login-bind")
    @Operation(summary = "认证相关-社交账号登录(绑定)", description = "适合未登录的用户，并且未绑定的用户")
    public CommonResult<AuthLoginRespVO> socialQuickLoginAndBind(@RequestBody @Valid AuthSocialLoginBindReqVO reqVO) {
        // 先进行手机号登录
        AuthSmsLoginReqVO smsLoginReqVO = AuthSmsLoginReqVO.builder().mobile(reqVO.getMobile()).code(reqVO.getVerificationCode()).build();
        AuthLoginRespVO authLoginRespVO = authService.smsLogin(smsLoginReqVO);

        // 再绑定微信号
        SocialUserBindReqDTO socialUserBindReqInfo = SocialUserBindReqDTO
                .builder()
                .userId(authLoginRespVO.getUserId())
                .userType(UserTypeEnum.MEMBER.getValue())
                .socialType(reqVO.getType())
                .code(reqVO.getCode())
                .state(reqVO.getState())
                .build();
        socialUserService.bindSocialUser(socialUserBindReqInfo);
        return success(authLoginRespVO);
    }


    // =============验证相关接口===============

    @PermitAll
    @GetMapping(value = "/check/mobile/repeat/{mobileNum}")
    @Operation(summary = "认证相关-手机号是否注册", description = "查询手机号是否已经注册")
    public CommonResult<Boolean> mobileCheckRepeat(@PathVariable String mobileNum) {
        // 忽略租户
        AtomicReference<Boolean> atomicReference = new AtomicReference<>();
        TenantUtils.executeIgnore(() -> atomicReference.set(authService.checkMobileRegister(mobileNum)));
        return success(atomicReference.get());
    }
}
