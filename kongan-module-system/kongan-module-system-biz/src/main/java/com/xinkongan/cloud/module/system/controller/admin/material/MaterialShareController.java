package com.xinkongan.cloud.module.system.controller.admin.material;


import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.util.crypto.GmPasswordEncoder;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.MaterialPublicReqDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.MaterialPublicShareDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialDetailVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialShareResultVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialViewShareVO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.service.material.IMaterialManageService;
import com.xinkongan.cloud.module.system.service.material.IMaterialShareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Validated
@RestController
@Tag(name = "飞控平台 - 素材分享")
@RequestMapping("/system/material/{materialType}/share")
public class MaterialShareController {

    @Resource
    private IMaterialShareService materialShareService;

    @Resource
    private IMaterialManageService materialManageService;

    @Resource
    private GmPasswordEncoder gmPasswordEncoder;


    @PostMapping(value = "/create")
    @Operation(summary = "公开分享-创建链接")
    @PreAuthorize("@ss.hasPermission('system:material:share:create:' + #materialType)")
    public CommonResult<MaterialViewShareVO> createMaterialShareLink(@PathVariable String materialType,
                                                                     @Valid @RequestBody MaterialPublicShareDTO materialPublicShare) {
        MaterialViewShareVO materialShareInfo = materialShareService.createMaterialShareLink(materialPublicShare);
        return CommonResult.success(materialShareInfo);
    }


    @PermitAll
    @TenantIgnore
    @DataPermission(enable = false)
    @GetMapping(value = "/isPublic/{shareKey}")
    @Operation(summary = "公开分享-查询状态")
    public CommonResult<MaterialShareResultVO> getMaterialShareType(@PathVariable Long shareKey) {
        MaterialShareResultVO publicResult = materialShareService.isPublic(shareKey);
        return CommonResult.success(publicResult);
    }


    @PermitAll
    @TenantIgnore
    @DataPermission(enable = false)
    @PostMapping(value = "/get/share")
    @Operation(summary = "公开分享-获取资源")
    public CommonResult<MaterialDetailVO> getShareResource(@RequestBody MaterialPublicReqDTO materialPublicReq) {
        MaterialViewShareVO shareInfo = materialShareService.getShareInfoByShareKey(materialPublicReq.getShareKey());
        if (Objects.equals(shareInfo.getShareType(), 1)
                && !gmPasswordEncoder.match(materialPublicReq.getPassword(), shareInfo.getPassword())) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_SHARE_PASSWORD_ERROR);
        }
        if (shareInfo.getExpireIn() == 0) {
            MaterialDetailVO materialInfo = materialManageService.getMaterialInfoById(shareInfo.getMaterialId());
            return CommonResult.success(materialInfo);
        }
        // 判断时间是否过期
        int expireInHours = shareInfo.getExpireIn();
        long expireInMillis = (long) expireInHours * 3600 * 1000;
        Date expirationTime = new Date(shareInfo.getCreateTime().getTime() + expireInMillis);
        if (new Date().after(expirationTime)) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_SHARE_EXPIRE_PASS);
        }
        MaterialDetailVO materialInfo = materialManageService.getMaterialInfoById(shareInfo.getMaterialId());
        return CommonResult.success(materialInfo);
    }
}
