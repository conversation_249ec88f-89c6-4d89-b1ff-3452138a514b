package com.xinkongan.cloud.module.system.framework.config;

import com.xinkongan.cloud.framework.common.config.CustomCallerRunsPolicy;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * @Description 日志专用线程池
 * <AUTHOR>
 * @Date 2025/4/23 13:50
 */
@Slf4j
@Data
@EnableAsync
@Configuration
@ConfigurationProperties(prefix = "kongan.thread.pool.log")
public class LogSaveThreadPoolConfig {

    // 核心线程数
    private int corePoolSize = Runtime.getRuntime().availableProcessors();

    // 最大线程数
    private int maxPoolSize = Runtime.getRuntime().availableProcessors() * 2 + 1;

    // 队列容量
    private int queueCapacity = 100;

    // 线程空闲时间（秒）
    private int keepAliveSeconds = 60;

    // 线程名称前缀
    private String threadNamePrefix = "kongan-log-thread-";

    // 线程池名称
    public static final String THREAD_BEAN_NAME = "logTaskExecutor";

    @Bean(THREAD_BEAN_NAME)
    public ThreadPoolTaskExecutor threadTaskExecutor() {
        ThreadPoolTaskExecutor threadTaskExecutor = new ThreadPoolTaskExecutor();
        threadTaskExecutor.setCorePoolSize(corePoolSize);
        threadTaskExecutor.setMaxPoolSize(maxPoolSize);
        threadTaskExecutor.setQueueCapacity(queueCapacity);
        threadTaskExecutor.setKeepAliveSeconds(keepAliveSeconds);
        threadTaskExecutor.setThreadNamePrefix(threadNamePrefix);
        threadTaskExecutor.setRejectedExecutionHandler(new CustomCallerRunsPolicy());
        threadTaskExecutor.initialize();
        return threadTaskExecutor;
    }


}