package com.xinkongan.cloud.module.system.controller.admin.route.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class WayPointShoutVO {

    @Schema(description = "喊话名称")
    private String name;

    @Schema(description = "0文本 1语音")
    private Integer voiceType;

    @Schema(description = "动作类型：0 关闭， 1开启")
    private Integer type;

    @Schema(description = "播放模式：0 单次，1 循环")
    private Integer mode;

    @Schema(description = "音量大小")
    private Integer volume;

    @Schema(description = "循环方式：0 循环航段  1 循环次数")
    private Integer isLeg;

    @Schema(description = "循环次数")
    private Integer loopNum;

    @Schema(description = "开始航线索引")
    private Integer startIndex;

    @Schema(description = "结束航线索引")
    private Integer endIndex;

    @Schema(description = "喊话语音id")
    private Long voiceId;

    @Schema(description = "批次id")
    private Long batchId;

    @Schema(description = "航线id")
    private Long routeId;

    @Schema(description = "航点id")
    private Long pointId;
}
