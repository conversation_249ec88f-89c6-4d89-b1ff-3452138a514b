package com.xinkongan.cloud.module.system.controller.admin.feedback.vo;

import com.xinkongan.cloud.module.system.api.file.dto.FileDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/05/22 10:03
 */
@Data
public class FeedbackCreateReqVO {

    @Schema(description = "反馈类型 1 问题反馈 2 使用咨询 3 优化意见")
    @NotNull
    private Integer type;

    @Schema(description = "反馈建议")
    @Size(max = 500, message = "反馈建议不能超过500个字符")
    private String feedbackAdvice;

    @Schema(description = "附件列表")
    @Size(max = 3, message = "您上传的附件数量已达上限3个，请调整后重新上传")
    private List<FileDTO> fileInfos;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "租户id")
    private Long tenantId;

}
