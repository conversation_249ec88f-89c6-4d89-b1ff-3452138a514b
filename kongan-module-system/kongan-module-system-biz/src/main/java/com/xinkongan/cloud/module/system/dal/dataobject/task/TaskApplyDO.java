package com.xinkongan.cloud.module.system.dal.dataobject.task;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xinkongan.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.xinkongan.cloud.framework.mybatis.core.dataobject.DeptBaseDO;
import com.xinkongan.cloud.framework.mybatis.core.type.LocalDateTimeListTypeHandler;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage.DockCoverageVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteFolderRespVO;
import com.xinkongan.cloud.module.system.enums.task.TaskApplyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 任务申请表
 * 注意：此表不加入组织数据权限配置，如有数据权限限制则手动拼接dept_id条件
 * <AUTHOR>
 * @Date 2024/12/20 9:28
 */
@TableName(value = "system_task_apply",autoResultMap = true) // 修正表名拼写错误
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskApplyDO extends DeptBaseDO {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    // 机场sn
    @TableField("dock_sn")
    private String dockSn;

    @TableField("dock_name")
    private String dockName;

    /**
     * 审批状态0待审批1已通过2未通过3已取消4已过期 {@link TaskApplyEnum}
     * <AUTHOR>
     * @date 2024/12/26 14:26
     **/
    @TableField("status")
    private Integer status;

    // 申请人id
    @TableField("user_id")
    private Long userId;

    // 申请人名称
    @TableField("user_nickname")
    private String userNickname;

    // 申请人手机号
    @TableField("user_phone_number")
    private String userPhoneNumber;

    // 申请人所属组织id
    @TableField("user_dept_id")
    private Long userDeptId;

    // 申请人组织名称
    @TableField("user_dept_name")
    private String userDeptName;

    // 申请原因
    @TableField("reason")
    private String reason;

    // 计划id
    @TableField("task_id")
    private Long taskId;

    // 执行模式 0立即执行1定时任务2循环任务
    @TableField("task_exec_mode")
    private Integer taskExecMode;

    // 任务id(循环模式为第一个任务的任务id)
    @TableField("job_id")
    private Long jobId;

    // 任务紧急程度
    @TableField("task_urgency")
    private Integer taskUrgency;

    // 任务名称
    @TableField("task_name")
    private String taskName;

    // 任务类型  1接警任务、2临时任务、3巡检任务、4建模任务
    @TableField("scene")
    private Integer scene;

    @TableField("task_fly_time")
    private LocalDateTime taskFlyTime;

    // 任务航线id
    @TableField("task_route_id")
    private Long taskRouteId;

    @TableField("route_thumbnail")
    private String routeThumbnail;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> fileIds;

    // 任务航线名称
    @TableField("task_route_name")
    private String taskRouteName;


    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<RouteFolderRespVO> taskRoutePointInfo;

    /**
     * 可飞范围
     **/
    @TableField(value = "dock_coverage", typeHandler = JacksonTypeHandler.class)
    private DockCoverageVO dockCoverage;

    // 任务预计执行时间(秒)
    @TableField("task_expected_execution_time")
    private Integer taskExpectedExecutionTime;

    // 任务预计执行里程(km)
    @TableField("task_expected_execution_mileage")
    private Float taskExpectedExecutionMileage;

    // 执行次数
    @TableField("task_count")
    private Integer taskCount;

    // 任务周期-开始时间
    @TableField("task_start_time")
    private LocalDateTime taskStartTime;

    // 任务周期-结束时间
    @TableField("task_end_time")
    private LocalDateTime taskEndTime;

    @TableField("task_description")
    private String taskDescription;

    @TableField("wayline_precision_type")
    private Integer waylinePrecisionType;

    @TableField("auto_break_point")
    private Integer autoBreakPoint;
    
    // 审批人id
    @TableField("approve_id")
    private Long approveId;

    // 审批人名称
    @TableField("approve_nickname")
    private String approveNickname;

    // 审批时间
    @TableField("approve_time")
    private LocalDateTime approveTime;

    // 审批人组织id
    @TableField("approve_dept_id")
    private Long approveDeptId;

    @TableField("approve_dept_name")
    private String approveDeptName;

    // 审批人手机号
    @TableField("approve_phone_number")
    private String approvePhoneNumber;

    // 审批意见
    @TableField("approve_comments")
    private String approveComments;

    // 审批过期定时任务id
    @TableField("schedule_id")
    private Long scheduleId;
    
    @TableField("auto_approve")
    private Integer autoApprove;
    
    @TableField(value = "task_fly_time_list",typeHandler = LocalDateTimeListTypeHandler.class)
    private List<LocalDateTime> taskFlyTimeList;

    @TableField("alarm_longitude")
    private Double alarmLongitude;

    @TableField("alarm_latitude")
    private Double alarmLatitude;
}



