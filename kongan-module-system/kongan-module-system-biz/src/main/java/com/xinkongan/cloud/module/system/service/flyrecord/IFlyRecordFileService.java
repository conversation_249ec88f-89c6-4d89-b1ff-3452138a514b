package com.xinkongan.cloud.module.system.service.flyrecord;

import com.xinkongan.cloud.module.system.api.flyrecord.dto.FlyFileDTO;
import com.xinkongan.cloud.module.system.api.live.dto.record.AgoraRecordPayloadDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.FlyRecordFileVO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.UploadFileReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import com.xinkongan.cloud.module.system.enums.flyrecord.DeleteType;
import com.xinkongan.cloud.sdk.dock.cloudapi.media.dto.FileUploadCallback;
import tech.powerjob.worker.log.OmsLogger;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description 飞行记录文件Service
 * <AUTHOR>
 * @Date 2025/2/12 14:33
 */
public interface IFlyRecordFileService {

    /**
     * 处理机场文件回调
     *
     * <AUTHOR>
     * @date 2025/2/12 14:33
     **/
    void processDockFileCallback(String dockSn, Long timestamp, FileUploadCallback fileUploadCallback);

    /**
     * 处理声网回调 保存录制文件
     *
     * <AUTHOR>
     * @date 2025/2/18 11:34
     **/
    void processAgoraCallback(AgoraRecordPayloadDTO payload);

    /**
     * 查询飞行记录下的所有文件
     *
     * <AUTHOR>
     * @date 2025/2/13 15:17
     **/
    List<FlyRecordFileDO> listByFlyRecordId(Long flyRecordId);

    /**
     * 查询飞行记录文件详情并标识是否在权限内
     * @param id 文件id
     * @return 文件详情
     */
    FlyRecordFileVO getFlyRecordFileVO(Long id);

    /**
     * 图片详情
     *
     * <AUTHOR>
     * @date 2025/2/13 19:29
     **/
    FlyRecordFileDO getById(Long id);

    /**
     * 删除文件
     *
     * <AUTHOR>
     * @date 2025/2/13 20:00
     **/
    Boolean deleteById(Long id);


    /**
     * 根据文件的url删除文件
     *
     * @param url 文件url
     */
    void deleteByUrl(String url);

    /**
     * 导入文件
     *
     * <AUTHOR>
     * @date 2025/2/14 9:55
     **/
    Long uploadFile(UploadFileReqVO uploadFileReqVO);

    /**
     * 根据飞行记录id删除文件
     *
     * <AUTHOR>
     * @date 2025/2/17 10:19
     **/
    void deleteByFlyRecord(Long id);

    /**
     * 删除未被标记为重要的飞行记录文件
     *
     * <AUTHOR>
     * @date 2025/2/17 10:20
     **/
    void deleteUnImportantFile(Long deptId, LocalDateTime endTime, DeleteType deleteType, OmsLogger omsLogger);

    List<FlyRecordFileDO> listByIds(Set<Long> fileIds);

    Map<Long, FlyRecordFileDO> mapByIds(Set<Long> fileIds);

    List<FlyFileDTO> listByDockSn(String dockSn);


    /**
     * 创建文件记录
     *
     * @param flyRecordFileDO
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2025/2/26 09:43
     */
    Long createFileRecord(FlyRecordFileDO flyRecordFileDO);

    Integer countByFlyIdWithoutException(List<Long> flyId);

    /**
     * 统计飞行记录下的完整素材数量
     * 包括：飞行记录文件表中的文件素材 + 异常表中的异常记录
     * @param ids 飞行记录ID列表
     * @return Map<飞行记录ID, 素材总数量>
     **/
    Map<Long, Integer> flyRecordMaterialCountMap(List<Long> ids);

    FlyRecordFileDO getByIdIgnorePermission(Long id);

    void updateById(FlyRecordFileDO flyRecordFileDO);

    /**
     * 根据异常id删除文件
     */
    Boolean deleteBatchByExceptionIds(List<Long> exceptionIds);

    /**
     * 根据任务id统计图片数量 拍摄图片和截图 不包含异常图片
     */
    Long countPicByJobId(Long jobId);


    /**
     * 根据任务id统计媒体数量
     *
     * @param jobId
     * @return
     */
    Long countMediaCountByJobId(Long jobId);

    /**
     * 根据任务ID统计拍摄的照片数量（一个SQL查询）
     * 通过Job -> JobFly -> FlyRecord -> FlyRecordFile的关系链查询
     *
     * @param jobId 任务ID
     * @return 拍摄照片数量
     */
    Long countPhotographByJobIdOptimized(Long jobId);
}
