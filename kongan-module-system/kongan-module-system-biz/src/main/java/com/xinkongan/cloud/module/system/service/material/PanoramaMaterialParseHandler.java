package com.xinkongan.cloud.module.system.service.material;

import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.mq.constant.TopicConstant;
import com.xinkongan.cloud.framework.mq.core.message.RocketMQMessage;
import com.xinkongan.cloud.framework.mq.core.send.IRocketMQSendService;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.controller.admin.file.vo.file.FileCreateReqVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialDetailVO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialMapper;
import com.xinkongan.cloud.module.system.dto.*;
import com.xinkongan.cloud.module.system.enums.file.S3FileDirPrefixEnum;
import com.xinkongan.cloud.module.system.enums.material.MaterialParseStatus;
import com.xinkongan.cloud.module.system.framework.material.SystemMaterialConfig;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.sdk.geo.utils.LocationUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
@RocketMQMessageListener(topic = TopicConstant.PANORAMA_MATERIAL_PARSE_RESULT_TOPIC,
        consumerGroup = "material-event-consumer-group",
        messageModel = MessageModel.CLUSTERING)
public class PanoramaMaterialParseHandler extends DefaultParseHandler implements RocketMQListener<String> {

    @Resource
    private IRocketMQSendService rocketMQSendService;

    @Resource
    private SystemMaterialConfig systemMaterialConfig;

    @Resource
    private MaterialMapper materialMapper;

    @Resource
    private FileService fileService;

    @Resource
    private LocationUtil locationUtil;


    @Override
    public void doHandler(MaterialParseDTO materialParse) throws Exception {
        log.info("[开始处理全景图素材]，素材信息为：{}", materialParse);
        PanoramaParseDTO panoramaParseInfo = BeanUtils.toBean(materialParse, PanoramaParseDTO.class);
        panoramaParseInfo.setMaxWh(500);

        String stsUrl = systemMaterialConfig.getStsUrl(S3FileDirPrefixEnum.FULL_VIEW.getDir(), materialParse.getTenantId());
        panoramaParseInfo.setStsUrl(stsUrl);

        RocketMQMessage<PanoramaParseDTO> rocketMQMessage = RocketMQMessage
                .<PanoramaParseDTO>builder()
                .tags(String.valueOf(materialParse.getId()))
                .keys(String.valueOf(materialParse.getId()))
                .body(panoramaParseInfo)
                .build();
        rocketMQSendService.syncSend(TopicConstant.PANORAMA_MATERIAL_PARSE_TOPIC, rocketMQMessage);
        this.sendMaterialParseProcessNotify(materialParse.getId(), 20);
        log.info("[全景图解析消息发送成功].....");
    }

    @Override
    public void onMessage(String message) {
        log.info("[收到全景图处理成功回调]，回调消息为：{}", message);
        PanoramaParseSuccessResultDTO panoramaParseSuccessResult = JSONObject.parseObject(message, PanoramaParseSuccessResultDTO.class);
        AtomicReference<MaterialDO> reference = new AtomicReference<>();
        TenantUtils.executeIgnore(() -> reference.set(materialMapper.selectById(panoramaParseSuccessResult.getId())));
        MaterialDO materialDO = reference.get();
        if (materialDO == null) {
            log.error("[回调异常]，素材信息不存在");
            return;
        }
        try {
            // 设置租户上下文
            TenantContextHolder.setTenantId(materialDO.getTenantId());

            if (Objects.equals(panoramaParseSuccessResult.getFunction(), "process")) {
                materialDO.setProcess(panoramaParseSuccessResult.getProcess());
                materialMapper.updateById(materialDO);
                // 发送通知
                this.sendMaterialParseProcessNotify(materialDO.getId(), panoramaParseSuccessResult.getProcess());
                return;
            }
            PanoramaInfoDTO panoramaInfo = panoramaParseSuccessResult.getPanorama_info();
            materialDO.setCenterLon(panoramaInfo.getLongitude());
            materialDO.setCenterLat(panoramaInfo.getLatitude());
            materialDO.setHeight(panoramaInfo.getHeight());
            materialDO.setGimbalYamDegree(panoramaInfo.getGimbalYamDegree());
            materialDO.setStatus(MaterialParseStatus.SUCCESS.getCode());

            // 进行地理逆编码
            String address = locationUtil.getLocationByWg84(panoramaInfo.getLongitude(), panoramaInfo.getLatitude());
            materialDO.setAddress(address);

            // 保存文件记录
            ThumbnailInfoDTO thumbnailInfo = panoramaParseSuccessResult.getThumbnail_info();
            FileCreateReqVO thumbnailFIle = BeanUtils.toBean(thumbnailInfo, FileCreateReqVO.class);
            Long fileId = fileService.createFile(thumbnailFIle);

            materialDO.setJpgUrl(thumbnailInfo.getUrl());
            List<Long> fileIds = materialDO.getFileIds();
            fileIds.add(fileId);
            materialDO.setProcess(100);
            // 更新数据库
            materialMapper.updateById(materialDO);

            MaterialParseSuccessNotifyDTO payload = MaterialParseSuccessNotifyDTO.builder()
                    .tileUrl(materialDO.getTileUrl())
                    .thumbnailUrl(materialDO.getJpgUrl())
                    .materialInfo(materialDO)
                    .build();

            // 发送解析成功的通知
            this.sendMaterialParseProcessNotify(materialDO.getId(), 100);
            this.sendMaterialParseStatusNotify(materialDO.getId(), MaterialParseStatus.SUCCESS,payload);
        } catch (Exception e) {
            log.error("[全景图解析回调异常]，异常信息为：{}", e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
            // 更新数据库状态
            materialDO.setStatus(MaterialParseStatus.FAILED.getCode());
            materialMapper.updateById(materialDO);
            // 发送解析失败的通知
            this.sendMaterialParseStatusNotify(materialDO.getId(), MaterialParseStatus.FAILED);
        } finally {
            // 移除租户上下文
            TenantContextHolder.clear();
        }
    }
}
