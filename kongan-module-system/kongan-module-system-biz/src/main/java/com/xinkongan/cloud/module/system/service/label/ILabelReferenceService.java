package com.xinkongan.cloud.module.system.service.label;

import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.label.dto.LabelReferSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.LabelTopVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteRespVO;
import com.xinkongan.cloud.module.system.enums.label.LabelReferenceEnums;

import java.util.List;

public interface ILabelReferenceService {

    /**
     * 删除标签引用
     * @param referenceKey 引用key
     * @param labelReference 标签引用类型
     */
    void delLabelReference(String referenceKey, LabelReferenceEnums labelReference);


    /**
     * 添加标签引用
     * @param referenceKey 引用key
     * @param labelIds  标签id
     * @param labelReference 标签引用类型
     */
    void addLabelReference(String referenceKey, List<Long> labelIds, LabelReferenceEnums labelReference);

    /**
     * 校验标签引用
     * @param labelId 标签id
     * @param labelReference 标签引用类型
     * @return 是否引用
     */
    Boolean checkLabelReference(Long labelId, LabelReferenceEnums labelReference);


    /**
     * 分页查询标注引用的航线
     * @param search 查询条件
     * @return 分页结果
     */
    PageResult<RouteRespVO> getLabelReferenceRoutePage(LabelReferSearchDTO search);



    /**
     * 查询标注引用的航线数量
     * @return 数量
     */
    Long getLabelReferenceRouteCount();


    /**
     * 查询标注引用排行榜
     * @return
     */
    List<LabelTopVO> getLabelReferenceTop();

    /**
     * 获取标签引用的id
     * @param referenceKey 引用key
     * @param labelReferenceEnums 标签引用类型
     * @return 标签id
     */
    List<Long> getLabelIdsByReferenceKey(String referenceKey, LabelReferenceEnums labelReferenceEnums);
}
