package com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask;

import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteAlgorithmVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointShoutVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DynamicActionRespVO {

    private String type;

    private String value;

    @Schema(description = "具体的算法动作信息")
    private RouteAlgorithmVO routeAlgorithmInfo;

    @Schema(description = "具体的喊话动作信息")
    private WayPointShoutVO wayPointShoutInfo;

}
