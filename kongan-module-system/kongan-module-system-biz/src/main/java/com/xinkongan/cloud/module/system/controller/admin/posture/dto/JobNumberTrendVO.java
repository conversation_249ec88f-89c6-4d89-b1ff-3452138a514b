package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobNumberTrendVO {

    @Schema(description = "月份")
    private String month;

    @Schema(description = "巡检任务数量")
    private Integer inspectionJobNumber;

    @Schema(description = "建模任务数量")
    private Integer modelingJobNumber;

    @Schema(description = "接警任务数量")
    private Integer alarmResponseJobNumber;

}
