package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.module.algorithm.dto.AlgorithmIdNameVO;
import com.xinkongan.cloud.module.algorithm.dto.FlowSegmentCountDTO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 飞行记录详情
 * <AUTHOR>
 * @Date 2025/2/13 14:00
 */
@Data
@Schema(description = "飞行记录详情")
public class FlyRecordDetailRespVO {

    @Schema(description = "飞行记录id")
    private Long id;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "飞行id")
    private Long flyId;

    @Schema(description = "航线id")
    private Long routeId;

    @Schema(description = "组织名称")
    private String deptName;

    @Schema(description = "起飞时间")
    private LocalDateTime takeOffTime;

    @Schema(description = "记录名称")
    private String name;

    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "机场名称")
    private String dockName;

    @Schema(description = "机场高度")
    private Integer height;

    @Schema(description = "起飞点纬度")
    private Double takeOffLat;

    @Schema(description = "起飞点经度")
    private Double takeOffLon;

    @Schema(description = "终点纬度")
    private Double lastLat;

    @Schema(description = "终点经度")
    private Double lastLon;

    @Schema(description = "降落时间")
    private LocalDateTime landTime;

    @Schema(description = "场景 0巡检1建模2接警")
    private Integer scene;

    @Schema(description = "该飞行架次当前已上传媒体数量")
    private Integer uploadedFileCount;

    @Schema(description = "该飞行架次拍摄媒体总数量")
    private Integer expectedFileCount;

    @Schema(description = "重要程度0普通1重要")
    private Integer importantLevel;

    @Schema(description = "飞行总里程")
    private Float flightMileage;

    @Schema(description = "飞行时长/s")
    private Float flightDuration;

    @Schema(description = "osd实时信息文件地址")
    private String osdUrl;

    @Schema(description = "图片素材(个数)")
    private Integer picCount;

    @Schema(description = "视频素材(个数)")
    private Integer videoCount;

    @Schema(description = "识别异常数")
    private Integer exceptionCount;

    @Schema(description = "是否分享0否1是")
    private Integer shareFlag;

    @Schema(description = "任务信息")
    private FlyRecordJobInfo jobInfo;

    @Schema(description = "航线信息")
    private FlyRecordRouteInfo routeInfo;

    @Schema(description = "文件列表")
    private List<FlyRecordFileAndExceptionVO> fileList;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "绑定任务列表")
    private List<BindJobVO> bindJobList;

    @Schema(description = "算法历史列表")
    @JsonIgnore
    private String algorithmIds;

    @Schema(description = "使用算法列表")
    private List<AlgorithmIdNameVO> algorithmHistoryList;

    @Schema(description = "是否有人流量分段累计统计 1是 0否")
    private Integer hasPersonFlowCount = YesNoEnum.NO.getCode();

    @Schema(description = "是否有车流量分段累计统计 1是 0否")
    private Integer hasCarFlowCount = YesNoEnum.NO.getCode();

    @Schema(description = "人流量算法分段累计统计图")
    private List<FlowSegmentCountDTO> personFlowCountList;

    @Schema(description = "车流算法分段累计统计图")
    private List<FlowSegmentCountDTO> carFlowCountList;

    public List<BindJobVO> getBindJobList() {
        return bindJobList == null ? List.of() : bindJobList;
    }
}