package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * @Description 任务信息
 * <AUTHOR>
 * @Date 2025/2/13 14:48
 */
@Data
@Schema(description = "任务信息")
@Builder
public class FlyRecordJobInfo {

    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "场景(任务类型) 0巡检1建模2接警")
    private Integer scene;
}