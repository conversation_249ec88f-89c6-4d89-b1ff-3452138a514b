package com.xinkongan.cloud.module.system.controller.admin.feedback;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.feedback.vo.FeedbackCreateReqVO;
import com.xinkongan.cloud.module.system.service.feedback.FeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;


/**
 * @Description
 * <AUTHOR>
 * @Date 2025/05/22 10:02
 */
@Tag(name = "帮助中心 - 问题反馈")
@RestController
@RequestMapping("/system/feedback")
@Validated
public class FeedbackController {

    @Resource
    private FeedbackService feedbackService;

    @PostMapping("/create")
    @Operation(summary = "创建问题反馈")
    public CommonResult<Long> createFeedback(@Valid @RequestBody FeedbackCreateReqVO reqVO) {
        Long feedBackId = feedbackService.createFeedBack(reqVO);
        return success(feedBackId);
    }
}
