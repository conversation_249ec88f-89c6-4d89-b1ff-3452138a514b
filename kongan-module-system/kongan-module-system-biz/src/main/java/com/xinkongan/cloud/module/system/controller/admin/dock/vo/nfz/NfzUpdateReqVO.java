package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/12 17:54
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "管理后台 - 禁飞区修改请求VO")
public class NfzUpdateReqVO extends NfzSaveReqVO {

    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private Long id;
}