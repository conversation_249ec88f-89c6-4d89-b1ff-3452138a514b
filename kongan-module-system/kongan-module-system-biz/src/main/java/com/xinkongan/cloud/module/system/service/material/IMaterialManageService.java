package com.xinkongan.cloud.module.system.service.material;

import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.api.material.MaterialLoadingStatusDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.*;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.*;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialNumStatisticVO;

import java.util.List;

public interface IMaterialManageService {


    /**
     * 素材导入,该方法用于页面接口调用导入的素材
     *
     * @param materialLoad 导入的素材信息
     * @return 素材id
     */
    Long materialLoad(MaterialLoadVO materialLoad);


    /**
     * 加载航点拍摄的全景图数据,该方法忽略数据权限
     *
     * @param tenantId 租户id
     * @param deptId   组织id
     * @param fileId   文件id
     */
    void loadRoutePointPanorama(Long tenantId, Long deptId, Long fileId,Long flyRecordId);


    /**
     * 根据素材id删除素材
     *
     * @param materialId 素材id
     * @return 是否删除成功
     */
    void delMaterialById(Long materialId);


    /**
     * 根据素材id查询素材信息
     *
     * @param materialId 素材id
     * @return 素材详情
     */
    MaterialDetailVO getMaterialInfoById(Long materialId);


    /**
     * 根据素材id查询素材信息
     *
     * @param materialId 素材id
     * @return 素材详情,若不能查询到则返回null
     */
    MaterialDetailVO getMaterialInfoByIdForIgnore(Long materialId);


    /**
     * 分页查询素材信息
     *
     * @param searchParams 分页查询参数
     * @return 查询结果
     */
    PageResult<MaterialInfoVO> getMaterialPage(MaterialSearchDTO searchParams);


    /**
     * 查询素材列表
     * @param searchParams
     * @return
     */
    List<MaterialInfoVO> getMaterialList(MaterialSearchDTO searchParams);


    /**
     * 素材分享
     *
     * @param materialShareParams 分享参数
     */
    void materialShare(MaterialShareDTO materialShareParams);


    /**
     * 根据id查询素材分享的组织
     *
     * @param materialId 素材id
     * @return 分享结果
     */
    MaterialShareVO getMaterialShareDept(Long materialId);


    /**
     * 根据用户数据权限进行数据统计
     *
     * @return 数据统计结果
     */
    MaterialNumStatisticVO getMaterialStatistic();


    /**
     * 修改素材信息
     *
     * @param materialUpdateInfo 素材信息
     * @return 素材id
     */
    Long updateMaterialInfo(MaterialUpdateDTO materialUpdateInfo);


    /**
     * 创建缩略图
     * @param materialThumbnail 缩略图信息
     */
    void updateMaterialThumbnailInfo(MaterialThumbnailDTO materialThumbnail);


    /**
     * 根据id查询素材对比记录
     *
     * @return 对比记录
     */
    PageResult<MaterialContrastRecordVO> getMaterialContrastRecord(MaterialContrastSearchDTO materialContrastRecordSearch);


    /**
     * 根据id查询素材对比记录详情
     *
     * @param id 对比记录id
     * @return 对比记录详情
     */
    MaterialContrastRecordDetailVO getMaterialContrastRecordDetail(Long id);


    /**
     * 保存素材对比记录
     *
     * @param materialContrastSaveInfo 对比记录
     */
    Long saveMaterialContrastRecord(MaterialContrastSaveDTO materialContrastSaveInfo);


    /**
     * 删除素材对比记录
     *
     * @param id 对比记录id
     */
    void delMaterialContrastRecord(Long id);


    /**
     * 修改素材对比记录
     *
     * @param materialContrastUpdateInfo 对比记录
     * @return 对比记录id
     */
    Long updateMaterialContrastRecord(MaterialContrastUpdateDTO materialContrastUpdateInfo);


    /**
     * 素材对比目标列表
     * @param materialCompareSearch 对比参数
     * @return 对比目标列表
     */
    List<MaterialInfoVO> getMaterialCompareRecordList(MaterialCompareSearchDTO materialCompareSearch);


    /**
     * 根据素材id查询全景图列表
     * @param materialId 素材id
     * @return 全景图列表
     */
    List<MaterialInfoVO> getPanoByConf(Long materialId);


    /**
     * 根据id查询素材列表
     * @param materialIds 素材id列表
     * @return 素材列表
     */
    List<MaterialInfoVO> getMaterialListByIds(List<Long> materialIds);

    /**
     * 修改加载状态
     * @param materialLoadingStatusDTO 加载状态
     */
    void updateLoadingStatus(MaterialLoadingStatusDTO materialLoadingStatusDTO);

    /**
     * 分页查询素材信息-根据组织ID
     *
     * @param materialSearchInfo 查询条件
     * @return 素材记录
     */
    PageResult<MaterialInfoVO> selectMaterialByPageDeptId(MaterialSearchDTO materialSearchInfo);

    /**
     * 根据组织ID删除素材
     *
     * @param deptId 组织ID
     */
    void deleteMaterialByDeptId(Long deptId);
}
