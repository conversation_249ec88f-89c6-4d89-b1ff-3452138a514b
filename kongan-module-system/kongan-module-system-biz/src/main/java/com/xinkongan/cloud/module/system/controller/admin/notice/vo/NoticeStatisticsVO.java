package com.xinkongan.cloud.module.system.controller.admin.notice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 消息统计
 * <AUTHOR>
 * @Date 2025/3/10 16:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeStatisticsVO {

    @Schema(description = "已读消息数量")
    private Integer unreadCount;

    @Schema(description = "任务模块消息数量")
    private Integer taskModuleCount;

    @Schema(description = "机场模块消息数量")
    private Integer dockModuleCount;

    @Schema(description = "系统模块消息数量")
    private Integer systemModuleCount;

    @Schema(description = "任务执行失败数量")
    private Integer jobExecFailCount;

    @Schema(description = "任务审批数量")
    private Integer jobApproveCount;

    @Schema(description = "机场共享数量")
    private Integer dockShareCount;

    @Schema(description = "机场接入数量")
    private Integer dockAccessCount;

    @Schema(description = "安全告警数量")
    private Integer dockSafeAlarm;

    @Schema(description = "机场通知数量")
    private Integer dockHmsInfo;

    @Schema(description = "机场提醒数量")
    private Integer dockHmsWarn;

    @Schema(description = "机场警告数量")
    private Integer dockHmsError;

    @Schema(description = "系统公告数量")
    private Integer systemAnnouncement;

    @Schema(description = "套餐提醒数量")
    private Integer systemPackageLow;
}