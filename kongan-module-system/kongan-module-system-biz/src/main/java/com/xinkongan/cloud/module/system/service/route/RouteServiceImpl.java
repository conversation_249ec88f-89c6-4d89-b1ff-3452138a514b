package com.xinkongan.cloud.module.system.service.route;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.datapermission.core.plugins.SharePluginParam;
import com.xinkongan.cloud.framework.kmz.dto.WpmlRouteInfo;
import com.xinkongan.cloud.framework.kmz.utils.KmzUtils;
import com.xinkongan.cloud.framework.lock.annotations.RedissonLock;
import com.xinkongan.cloud.framework.mybatis.config.IdGenerator;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelDetailRespVO;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.TabRespVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.*;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.*;
import com.xinkongan.cloud.module.system.dal.dataobject.dept.DeptDO;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteDO;
import com.xinkongan.cloud.module.system.dal.dataobject.user.AdminUserDO;
import com.xinkongan.cloud.module.system.dal.mysql.route.RouteMapper;
import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import com.xinkongan.cloud.module.system.dto.ResourceShareDTO;
import com.xinkongan.cloud.module.system.dto.RouteFileInfo;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.file.S3FileDirPrefixEnum;
import com.xinkongan.cloud.module.system.enums.label.LabelReferenceEnums;
import com.xinkongan.cloud.module.system.enums.material.MaterialReferenceEnums;
import com.xinkongan.cloud.module.system.enums.route.RouteLockFlagEnum;
import com.xinkongan.cloud.module.system.enums.route.RouteTypeEnum;
import com.xinkongan.cloud.module.system.enums.share.ResourceShareTypeEnum;
import com.xinkongan.cloud.module.system.service.dept.DeptService;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.file.FileService;
import com.xinkongan.cloud.module.system.service.label.ILabelReferenceService;
import com.xinkongan.cloud.module.system.service.label.ILabelService;
import com.xinkongan.cloud.module.system.service.label.ITabService;
import com.xinkongan.cloud.module.system.service.material.IMaterialManageService;
import com.xinkongan.cloud.module.system.service.material.IMaterialReferenceService;
import com.xinkongan.cloud.module.system.service.share.IShareService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import com.xinkongan.cloud.module.system.util.RouteConvertUtils;
import com.xinkongan.cloud.module.system.vo.FileUploadVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RouteServiceImpl implements IRouteService {

    @Resource
    private RouteMapper routeMapper;

    @Resource
    private IWayPointService wayPointService;

    @Resource
    private IShareService shareService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private DockDeviceService dockDeviceService;

    @Resource
    private FileService fileService;

    @Resource
    private IJobService jobService;

    @Resource
    private IMaterialReferenceService materialReferenceService;

    @Resource
    private IMaterialManageService materialManageService;

    @Resource
    private ILabelReferenceService labelReferenceService;

    @Resource
    private ILabelService labelService;

    @Resource
    private ITabService tabService;

    @Resource
    private IRouteAlgorithmService routeAlgorithmService;

    @Resource
    private DeptService deptService;

    @Resource
    private IRouteModelService routeModelService;

    @Lazy
    @Resource
    private IRouteService routeService;


    @Override
    public PageResult<RouteRespVO> routePageSearch(RouteSearchDTO routeSearchParams) {
        Page<RouteRespVO> page = new Page<>(routeSearchParams.getPage(), routeSearchParams.getOffset());

        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.ROUTE_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.ROUTE_RESOURCE.getResourceType())
                .shareFlag(routeSearchParams.getShareFlag())
                .build();

        List<RouteRespVO> records = routeMapper.selectRouteByPageWithShare(page, sharePluginParam, routeSearchParams);
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public Long routePageSearchCount(RouteSearchDTO routeSearchParams) {
        RouteDO routeInfo = routeMapper.selectById(routeSearchParams.getRouteId());
        if (routeInfo == null) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NOT_EXISTS);
        }
        SharePluginParam sharePluginParam = SharePluginParam
                .builder()
                .resourceTableName(ResourceShareTypeEnum.ROUTE_RESOURCE.getTableName())
                .resourceType(ResourceShareTypeEnum.ROUTE_RESOURCE.getResourceType())
                .shareFlag(routeSearchParams.getShareFlag())
                .build();
        Long rowNumber = routeMapper.selectRouteRowNumberByPageWithShare_mpCount(sharePluginParam, routeSearchParams, routeInfo.getCreateTime());
        return (rowNumber + routeSearchParams.getOffset() - 1) / routeSearchParams.getOffset();
    }

    @Override
    @DataPermission(enable = false)
    public RouteBaseRespVO getRouteBaseById(Long routeId) {
        RouteDO routeInfo = routeMapper.selectById(routeId);
        if (routeInfo == null) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NOT_EXISTS);
        }
        AdminUserDO adminUserInfo = null;
        if (StrUtil.isNotBlank(routeInfo.getCreator())) {
            adminUserInfo = adminUserService.getUser(Long.parseLong(routeInfo.getCreator()));
        }
        RouteBaseRespVO routeBaseRespVO = BeanUtils.toBean(routeInfo, RouteBaseRespVO.class);
        if (adminUserInfo != null) {
            routeBaseRespVO.setCreateName(adminUserInfo.getNickname());
        }
        boolean resourceShare = shareService.checkResourceShare(routeInfo.getId(), ResourceShareTypeEnum.ROUTE_RESOURCE.getResourceType());
        routeBaseRespVO.setShareFlag(resourceShare ? 1 : 0);

        // 查询设备信息
        if (!StringUtils.isEmpty(routeInfo.getDockSn())) {
            DockDeviceDO dockDeviceInfo = dockDeviceService.getByDBDeviceSn(routeInfo.getDockSn());
            if (dockDeviceInfo != null) {
                routeBaseRespVO.setDockName(dockDeviceInfo.getDeviceName());
                routeBaseRespVO.setDockSn(dockDeviceInfo.getDeviceSn());
            }
        }
        // 查询组织信息
        DeptDO deptInfo = deptService.getDeptById(routeInfo.getDeptId());
        if (deptInfo != null) {
            routeBaseRespVO.setDeptName(deptInfo.getName());
        }
        return routeBaseRespVO;
    }

    @Override
    @DataPermission(enable = false)
    public RouteDetailRespVO getRouteDetailById(Long routeId) {
        RouteBaseRespVO routeBaseInfo = this.getRouteBaseById(routeId);
        RouteDetailRespVO routeDetailRespInfo = BeanUtils.toBean(routeBaseInfo, RouteDetailRespVO.class);
        // 补充航点相关信息
        List<RouteFolderRespVO> wayPointInfo = wayPointService.getWayPointInfoByRouteId(routeId);
        routeDetailRespInfo.setFolders(wayPointInfo);
        // 查询任务引用次数
        Long count = jobService.countJobsByRouteId(routeId);
        routeDetailRespInfo.setTaskRefCount(count.intValue());

        // 查询素材引用
        List<Long> materialIds = materialReferenceService.getMaterialIdsByReferenceKey(String.valueOf(routeId), MaterialReferenceEnums.ROUTE_REFERENCE);
        List<MaterialInfoVO> materialInfos = materialManageService.getMaterialListByIds(materialIds);
        routeDetailRespInfo.setMaterials(materialInfos);

        // 查询标注引用
        List<Long> labelIds = labelReferenceService.getLabelIdsByReferenceKey(String.valueOf(routeId), LabelReferenceEnums.ROUTE_REFERENCE);
        List<LabelDetailRespVO> labelDetailInfos = labelService.getLabelDetailByIds(labelIds);
        routeDetailRespInfo.setLabels(labelDetailInfos);

        // 查询标记数据
        List<TabRespVO> tabInfos = tabService.getTabInfosByRouteId(routeId);
        routeDetailRespInfo.setTabInfos(tabInfos);

        // 补充配置算法信息
        this.updateAlgorithmConf(routeDetailRespInfo);

        //查询建模航线参数信息
        ModelRouteDTO modelRouteDTO = routeModelService.getRouteModelInfoByRouteId(routeId);
        routeDetailRespInfo.setModelRouteDTO(modelRouteDTO);

        return routeDetailRespInfo;
    }

    private void updateAlgorithmConf(RouteDetailRespVO routeDetailRespInfo) {
        Map<Long, List<RouteAlgorithmVO>> algorithmMap = routeAlgorithmService.getRouteAlgorithmInfoByRouteId(routeDetailRespInfo.getId());

        Set<String> algorithmNames = algorithmMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(RouteAlgorithmVO::getAlgorithmName)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(LinkedHashSet::new));
        routeDetailRespInfo.setAlgorithmConf(algorithmNames.isEmpty() ? "" : String.join(",", algorithmNames));
    }

    @Override
    public Long routeSave(RouteSaveDTO routeSaveInfo) {
        if (this.checkRouteNameRepeat(null, routeSaveInfo.getDeptId(), routeSaveInfo.getRouteName())) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NAME_HAS_REPEAT);
        }
        // 1、保存航线基本信息
        RouteDO routeBaseInfo = BeanUtils.toBean(routeSaveInfo, RouteDO.class);
        routeMapper.insert(routeBaseInfo);

        String routeId = String.valueOf(routeBaseInfo.getId());

        // 2、生成航线并更新数据库信息
        this.addOrUpdateRouteInfo(routeBaseInfo, routeSaveInfo);

        // 3、保存航点信息
        wayPointService.saveRouteWayPointInfo(routeBaseInfo.getId(), routeSaveInfo.getFolders());

        // 4、保存素材引用
        materialReferenceService.saveMaterialReference(routeId, MaterialReferenceEnums.ROUTE_REFERENCE, routeSaveInfo.getMaterialIds());

        // 5、保存标注的引用
        labelReferenceService.addLabelReference(routeId, routeSaveInfo.getLabelIds(), LabelReferenceEnums.ROUTE_REFERENCE);

        // 6、保存标记数据
        tabService.saveRouteTabInfos(routeBaseInfo.getId(), routeSaveInfo.getTabSaveInfos());

        // 7、保存建模航线参数信息
        if (ObjectUtil.equals(RouteTypeEnum.MODELING_ROUTE.getType(), routeSaveInfo.getRouteType())) {
            ModelRouteDTO modelRouteDTO = routeSaveInfo.getModelRouteDTO();
            modelRouteDTO.setRouteId(routeBaseInfo.getId());
            routeModelService.saveRouteModelInfo(modelRouteDTO);
        }

        return routeBaseInfo.getId();
    }

    @Override
    @Transactional
    public void delRouteById(Long routeId, Boolean forcedDeletion) {
        RouteDO routeInfo = routeMapper.selectById(routeId);
        if (routeInfo == null) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NOT_EXISTS);
        }
        // 检查航线是否关联任务
        Long count = jobService.countJobsByRouteId(routeId);
        if (count > 0 && !forcedDeletion) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_HAS_BY_JOB_BIND_NOT_DEL);
        }
        // 删除航线信息
        routeMapper.deleteById(routeId);

        // 删除航点信息
        wayPointService.deletePointByRouteId(routeId);

        // 删除文件信息
        fileService.deleteFileByIds(routeInfo.getFileIds());

        // 删除共享数据
        shareService.removeResourceShare(routeId, ResourceShareTypeEnum.ROUTE_RESOURCE.getResourceType());

        // 删除素材引用
        materialReferenceService.deleteMaterialReferenceByReferenceKey(String.valueOf(routeId), MaterialReferenceEnums.ROUTE_REFERENCE);

        // 删除标注的引用
        labelReferenceService.delLabelReference(String.valueOf(routeId), LabelReferenceEnums.ROUTE_REFERENCE);

        // 删除标记数据
        tabService.delTabByRouteId(routeId);

        // 删除建模参数
        routeModelService.delRouteModelInfoByRouteId(routeId);
    }

    @Override
    @RedissonLock(prefixKey = "#routeUpdateInfo.id", message = "航线正在更新中，请勿重复提交")
    public Long routeUpdate(RouteUpdateDTO routeUpdateInfo) {
        RouteDO routeInfo = routeMapper.selectById(routeUpdateInfo.getId());
        if (routeInfo == null) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NOT_EXISTS);
        }
        if (this.checkRouteNameRepeat(routeInfo.getId(), routeUpdateInfo.getDeptId(), routeUpdateInfo.getRouteName())) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NAME_HAS_REPEAT);
        }
        if (RouteLockFlagEnum.isLock(routeInfo.getLockFlag())
                && !Objects.equals(routeInfo.getCreator(), String.valueOf(SecurityFrameworkUtils.getLoginUserId()))) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_IS_LOCK_STATUS);
        }
        // 1、移除之前航点相关数据
        wayPointService.deletePointByRouteId(routeInfo.getId());

        // 2、删除文件相关信息
        fileService.deleteFileByIds(routeInfo.getFileIds());

        // 3、更新航线信息
        routeInfo.getFileIds().clear();
        BeanUtils.copyProperties(routeUpdateInfo, routeInfo);
        this.addOrUpdateRouteInfo(routeInfo, routeUpdateInfo);

        // 4、保存航点信息
        wayPointService.saveRouteWayPointInfo(routeInfo.getId(), routeUpdateInfo.getFolders());

        // 5、保存素材引用
        materialReferenceService.saveMaterialReference(String.valueOf(routeInfo.getId()), MaterialReferenceEnums.ROUTE_REFERENCE, routeUpdateInfo.getMaterialIds());

        // 6. 保存标注的引用
        labelReferenceService.addLabelReference(String.valueOf(routeInfo.getId()), routeUpdateInfo.getLabelIds(), LabelReferenceEnums.ROUTE_REFERENCE);

        // 7. 更新标记数据
        tabService.saveRouteTabInfos(routeInfo.getId(), routeUpdateInfo.getTabSaveInfos());

        // 8. 更新建模航线参数信息
        if (ObjectUtil.equals(RouteTypeEnum.MODELING_ROUTE.getType(), routeUpdateInfo.getRouteType())) {
            ModelRouteDTO modelRouteDTO = routeUpdateInfo.getModelRouteDTO();
            modelRouteDTO.setRouteId(routeInfo.getId());
            routeModelService.updateRouteModelInfo(modelRouteDTO);
        }

        return routeInfo.getId();
    }

    @Override
    public void routeShare(RouteShareDTO routeShareInfo) {
        RouteDO routeInfo = routeMapper.selectById(routeShareInfo.getRouteId());
        if (routeInfo == null) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NOT_EXISTS);
        }

        ResourceShareDTO resourceShareParam = ResourceShareDTO
                .builder()
                .resourceId(routeShareInfo.getRouteId())
                .resourceType(ResourceShareTypeEnum.ROUTE_RESOURCE.getResourceType())
                .shareDeptIds(routeShareInfo.getDeptIds())
                .oriDeptId(routeInfo.getDeptId())
                .build();
        shareService.resourceShareDeptIds(resourceShareParam);
    }

    @Override
    public RouteShareVO getRouteShareDept(Long routeId) {
        List<Long> deptIds = shareService.getResourceSharesDept(routeId);
        return RouteShareVO.builder().deptIds(deptIds).routeId(routeId).build();
    }

    @Override
    public void lockRouteById(Long routeId) {
        RouteDO routeInfo = routeMapper.selectById(routeId);
        if (routeInfo == null) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NOT_EXISTS);
        }
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (!Objects.equals(routeInfo.getCreator(), String.valueOf(loginUserId))) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_LOCK_ONLY_BY_CREATOR);
        }
        routeInfo.setLockFlag(RouteLockFlagEnum.LOCK.getCode());
        routeMapper.updateById(routeInfo);
    }

    @Override
    public void unlockRouteById(Long routeId) {
        RouteDO routeInfo = routeMapper.selectById(routeId);
        if (routeInfo == null) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NOT_EXISTS);
        }
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (!Objects.equals(routeInfo.getCreator(), String.valueOf(loginUserId))) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_UNLOCK_ONLY_BY_CREATOR);
        }
        routeInfo.setLockFlag(RouteLockFlagEnum.UNLOCK.getCode());
        routeMapper.updateById(routeInfo);
    }

    @Override
    public boolean checkRouteNameRepeat(Long routeId, Long deptId, String routeName) {
        Long count = routeMapper.selectCount(
                new LambdaQueryWrapperX<RouteDO>()
                        .eq(RouteDO::getRouteName, routeName)
                        .eq(RouteDO::getDeptId, deptId)
                        .ne(routeId != null, RouteDO::getId, routeId)
        );
        return count > 0;
    }


    @Override
    @DataPermission(enable = false)
    public void addRouteCopyCount(Long routeId) {
        RouteDO routeInfo = routeMapper.selectById(routeId);
        if (routeInfo == null) {
            throw new ServiceException(ErrorCodeConstants.ROUTE_NOT_EXISTS);
        }
        routeInfo.setCopyCount(routeInfo.getCopyCount() + 1);
        routeMapper.updateById(routeInfo);
    }

    @DataPermission(enable = false)
    @Override
    public RouteDO getById(Long routeId) {
        return routeMapper.selectById(routeId);
    }

    @Override
    public PageResult<ExecRouteVO> getExecRouteListByDockSn(DockExecRoutePageParam param) {
        Page<ExecRouteVO> page = new Page<>(param.getPageNo(), param.getPageSize());
        List<ExecRouteVO> list = routeMapper.getExecRouteListByDockSn(page, param.getDockSn());
        page.setRecords(list);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public void deleteRouteByDeptId(Long deptId) {
        List<Long> routeIds = routeMapper.selectList(new LambdaQueryWrapperX<RouteDO>()
                        .eq(RouteDO::getDeptId, deptId)
                        .select(RouteDO::getId))
                .stream()
                .map(RouteDO::getId)
                .toList();
        routeIds.forEach(id -> routeService.delRouteById(id, true));
    }

    private void addOrUpdateRouteInfo(RouteDO routeInfo, RouteBaseInfoDTO routeBaseInfo) {

        // 深拷贝对象，防止影响数据库数据
        RouteBaseInfoDTO cloneRouteBaseInfo = SerializationUtils.clone(routeBaseInfo);

        RouteFileInfo routeFileInfo = this.generateRouteFile(cloneRouteBaseInfo, routeInfo.getRouteType());
        routeInfo.setRouteUrl(routeFileInfo.getUrl());
        routeInfo.setFingerprint(routeFileInfo.getFingerprint());

        List<Long> fileIds = routeInfo.getFileIds();
        fileIds.add(routeFileInfo.getFileId());

        routeMapper.updateById(routeInfo);
    }


    private RouteFileInfo generateRouteFile(RouteBaseInfoDTO routeInfo, Integer routeType) {

        WpmlRouteInfo wpmlRouteInfo = RouteConvertUtils.convertRouteContent(routeInfo, routeType);
        byte[] routeContent = KmzUtils.buildWpmlRouteInfoToBytes(wpmlRouteInfo,routeType);
        // 将航线上传到OSS中
        String routeName = IdGenerator.generateId() + ".kmz";
        String routePath = S3FileDirPrefixEnum.ROUTE_FILE.getCompleteDir(routeName, TenantContextHolder.getTenantId());

        FileUploadVO routeFileDetail = fileService.uploadFile(routeName, routePath, routeContent);
        // 计算md5 值
        String md5Value = DigestUtils.md5Hex(routeContent);
        return RouteFileInfo.builder().url(routeFileDetail.getUrl()).fingerprint(md5Value).fileId(routeFileDetail.getFileId()).build();
    }

    @Override
    @Transactional
    public Long importRouteFromKmz(String kmzUrl, String routeName, Integer routeType) {
        try {
            log.info("[航线导入] 开始导入KMZ文件，URL: {}, 航线名称: {}, 航线类型: {}", kmzUrl, routeName, routeType);

            // 1. 解析KMZ文件，获取完整的WpmlRouteInfo对象
            WpmlRouteInfo wpmlRouteInfo = KmzUtils.loadCompleteKmzToWpmlRouteInfo(kmzUrl);
            if (wpmlRouteInfo == null) {
                throw new ServiceException(ErrorCodeConstants.ROUTE_IMPORT_PARSE_ERROR);
            }

            // 2. 将WpmlRouteInfo转换为RouteBaseInfoDTO
            RouteBaseInfoDTO routeBaseInfo = RouteConvertUtils.convertWpmlToRouteBaseInfo(wpmlRouteInfo, routeType);
            if (routeBaseInfo == null) {
                throw new ServiceException(ErrorCodeConstants.ROUTE_IMPORT_CONVERT_ERROR);
            }

            // 3. 设置导入的航线名称和描述
            routeBaseInfo.setName(routeName);
            routeBaseInfo.setDescription("从KMZ文件导入的航线");

            // 4. 调用现有的保存方法
            RouteSaveDTO routeSaveDTO = BeanUtils.toBean(routeBaseInfo, RouteSaveDTO.class);
            Long routeId = this.routeSave(routeSaveDTO);

            log.info("[航线导入] 导入成功，航线ID: {}", routeId);
            return routeId;

        } catch (Exception e) {
            log.error("[航线导入] 导入失败，KMZ URL: {}, 错误信息: {}", kmzUrl, e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));

            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException(ErrorCodeConstants.ROUTE_IMPORT_FAILED, e.getMessage());
        }
    }
}
