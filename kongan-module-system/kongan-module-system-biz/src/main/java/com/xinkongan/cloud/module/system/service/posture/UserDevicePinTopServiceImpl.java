package com.xinkongan.cloud.module.system.service.posture;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinkongan.cloud.framework.common.enums.WebSocketUserTypeEnum;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.dal.dataobject.posture.UserDevicePinTopDO;
import com.xinkongan.cloud.module.system.dal.mysql.posture.UserDevicePinTopMapper;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * (SystemUserDevicePinTop)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-12 15:42:03
 */
@Service
public class UserDevicePinTopServiceImpl extends ServiceImpl<UserDevicePinTopMapper, UserDevicePinTopDO> implements IUserDevicePinTopService {

    @Resource
    private WebSocketSendApi webSocketSendApi;

    @Override
    public boolean pinTop(String deviceKey) {
        boolean pinTop = isPinTop(deviceKey);
        if (pinTop) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_DEVICE_IS_INSTALLED_ON_TOP);
        }
        boolean insert = baseMapper.insert(new UserDevicePinTopDO().setDeviceKey(deviceKey).setUserId(SecurityFrameworkUtils.getLoginUserId())) > 0;
        // 通知置顶
        notifyTheTopChange();
        return insert;
    }

    @Override
    public boolean isPinTop(String deviceKey) {
        Long count = baseMapper.selectCount(new LambdaQueryWrapperX<UserDevicePinTopDO>()
                .eq(UserDevicePinTopDO::getDeviceKey, deviceKey)
                .eq(UserDevicePinTopDO::getUserId, SecurityFrameworkUtils.getLoginUserId())
        );
        return count > 0;
    }

    @Override
    public boolean unPinTop(String deviceKey) {
        int delete = baseMapper.delete(new LambdaQueryWrapperX<UserDevicePinTopDO>()
                .eq(UserDevicePinTopDO::getDeviceKey, deviceKey)
                .eq(UserDevicePinTopDO::getUserId, SecurityFrameworkUtils.getLoginUserId()));
        if (delete > 0) {
            // 通知置顶
            notifyTheTopChange();
            return true;
        }
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.THE_DEVICE_IS_NOT_INSTALLED_ON_TOP);
    }

    @Override
    public List<String> getPinTopDeviceList() {
        return baseMapper.selectList(new LambdaQueryWrapperX<UserDevicePinTopDO>()
                        .select(UserDevicePinTopDO::getDeviceKey)
                        .eq(UserDevicePinTopDO::getUserId, SecurityFrameworkUtils.getLoginUserId())
                        .orderByDesc(UserDevicePinTopDO::getUpdateTime)
                ).stream()
                .map(UserDevicePinTopDO::getDeviceKey)
                .toList();
    }

    @Override
    public void notifyTheTopChange() {
        List<String> list = getPinTopDeviceList();
        webSocketSendApi.sendByTenantUserSet(
                WebSocketMessageDTO.builder()
                        .userIdSet(Set.of(SecurityFrameworkUtils.getLoginUserId()))
                        .tenantId(TenantContextHolder.getTenantId())
                        .userTypeEnumSet(Set.of(WebSocketUserTypeEnum.WEB))
                        .message(
                                CustomWebSocketMessage.builder()
                                        .bizCode(BizCodeEnum.TOP_DEVICE_UPDATE.getCode())
                                        .data(list)
                                        .build()
                        )
                        .build()
        );
    }
}
