package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 任务排期请求VO
 * <AUTHOR>
 * @Date 2025/3/4 17:11
 */
@Data
public class ScheduleFlyReqVO {

    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @Schema(description = "0巡检 1建模 2接警")
    private List<Integer> scene;

    @Schema(description = "任务状态")
    private List<Integer> status;

    @Schema(description = "任务范围 0内部任务 1外部任务")
    private List<Integer> scope;
}