package com.xinkongan.cloud.module.system.service.material;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.MaterialTopVO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialReferenceDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialReferenceMapper;
import com.xinkongan.cloud.module.system.dto.TaskTopReportDTO;
import com.xinkongan.cloud.module.system.enums.material.MaterialReferenceEnums;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class MaterialReferenceServiceImpl implements IMaterialReferenceService {

    @Resource
    private MaterialReferenceMapper materialReferenceMapper;


    @Override
    public void saveMaterialReference(String referenceKey, MaterialReferenceEnums referType, List<Long> materialIds) {
        if (CollectionUtil.isEmpty(materialIds)) {
            return;
        }
        materialReferenceMapper.delete(
                new LambdaQueryWrapper<MaterialReferenceDO>()
                        .eq(MaterialReferenceDO::getReferenceKey, referenceKey)
                        .eq(MaterialReferenceDO::getType, referType.getType())
                        .in(MaterialReferenceDO::getMaterialId, materialIds)
        );
        List<MaterialReferenceDO> materialReferences = materialIds
                .stream().map(l -> {
                    MaterialReferenceDO materialReferenceInfo = new MaterialReferenceDO();
                    materialReferenceInfo.setMaterialId(l);
                    materialReferenceInfo.setReferenceKey(referenceKey);
                    materialReferenceInfo.setType(referType.getType());
                    return materialReferenceInfo;
                }).toList();
        materialReferenceMapper.insertBatch(materialReferences);
    }

    @Override
    public void deleteMaterialReferenceByReferenceKey(String referenceKey, MaterialReferenceEnums referType) {
        materialReferenceMapper.delete(
                new LambdaQueryWrapper<MaterialReferenceDO>()
                        .eq(MaterialReferenceDO::getReferenceKey, referenceKey)
                        .eq(MaterialReferenceDO::getType, referType.getType())
        );
    }


    @Override
    public void deleteMaterialReferenceByMaterialIds(List<Long> materialIds) {
        if (CollectionUtil.isEmpty(materialIds)) {
            return;
        }
        materialReferenceMapper.delete(
                new LambdaQueryWrapper<MaterialReferenceDO>()
                        .in(MaterialReferenceDO::getMaterialId, materialIds)
        );
    }

    @Override
    public List<Long> getMaterialIdsByReferenceKey(String referenceKey, MaterialReferenceEnums referType) {
        List<MaterialReferenceDO> materialReferenceInfos = materialReferenceMapper.selectList(
                new LambdaQueryWrapper<MaterialReferenceDO>()
                        .eq(MaterialReferenceDO::getReferenceKey, referenceKey)
                        .eq(MaterialReferenceDO::getType, referType.getType())
        );
        return materialReferenceInfos.stream().map(MaterialReferenceDO::getMaterialId).toList();
    }

    @Override
    public List<MaterialTopVO> getMaterialReferenceTop(TaskTopReportDTO taskTopReportDTO) {
        return materialReferenceMapper.getMaterialReferenceTop(taskTopReportDTO.getMaterialType());
    }
}
