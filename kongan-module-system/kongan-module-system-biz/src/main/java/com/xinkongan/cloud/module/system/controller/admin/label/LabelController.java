package com.xinkongan.cloud.module.system.controller.admin.label;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.mybatis.config.IdGenerator;
import com.xinkongan.cloud.module.system.controller.admin.label.dto.*;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelDetailRespVO;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.LabelRespVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialInfoVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteRespVO;
import com.xinkongan.cloud.module.system.enums.label.LabelReferenceEnums;
import com.xinkongan.cloud.module.system.service.label.ILabelReferenceService;
import com.xinkongan.cloud.module.system.service.label.ILabelService;
import com.xinkongan.cloud.module.system.service.material.IMaterialManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@RestController
@Tag(name = "飞控平台 - 标注管理")
@RequestMapping("/system/label")
public class LabelController {

    @Resource
    private ILabelService labelService;

    @Resource
    private IMaterialManageService materialManageService;


    @Resource
    private ILabelReferenceService labelReferenceService;


    @PostMapping(value = "/save")
    @Operation(summary = "标注管理-新增标注")
    @PreAuthorize("@ss.hasPermission('system:label:save')")
    public CommonResult<Long> saveLabel(@Valid @RequestBody LabelSaveDTO labelSaveDTO) {
        Long id = labelService.saveLabel(labelSaveDTO);
        return CommonResult.success(id);
    }


    @GetMapping(value = "/del/{labelId}")
    @Operation(summary = "标注管理-删除标注")
    @PreAuthorize("@ss.hasPermission('system:label:del')")
    public CommonResult<Void> delLabel(@PathVariable Long labelId) {
        labelService.delLabelById(labelId, false);
        return CommonResult.success();
    }

    @GetMapping(value = "/check/{labelId}")
    @Operation(summary = "标注管理-航线关联检查")
    public CommonResult<Boolean> labelRelateCheck(@PathVariable Long labelId) {
        Boolean res = labelReferenceService.checkLabelReference(labelId, LabelReferenceEnums.ROUTE_REFERENCE);
        return CommonResult.success(res);
    }

    @PostMapping(value = "/update")
    @Operation(summary = "标注管理-编辑标注")
    @PreAuthorize("@ss.hasPermission('system:label:update')")
    public CommonResult<Long> updateLabel(@Valid @RequestBody LabelUpdateDTO labelUpdateDTO) {
        Long id = labelService.updateLabelById(labelUpdateDTO);
        return CommonResult.success(id);
    }


    @GetMapping(value = "/get/{labelId}")
    @Operation(summary = "标注管理-根据id查询")
    @PreAuthorize("@ss.hasPermission('system:label:query')")
    public CommonResult<LabelDetailRespVO> getLabelInfo(@PathVariable Long labelId) {
        LabelDetailRespVO labelDetailInfo = labelService.getLabelDetailById(labelId);
        return CommonResult.success(labelDetailInfo);
    }

    @PostMapping(value = "/page")
    @Operation(summary = "标注管理-分页查询")
    @PreAuthorize("@ss.hasPermission('system:label:query')")
    public CommonResult<PageResult<LabelRespVO>> getLabelPage(@Valid @RequestBody LabelSearchDTO search) {
        PageResult<LabelRespVO> result = labelService.getLabelByPage(search);
        return CommonResult.success(result);
    }

    @PostMapping(value = "/getLabelPageByDeptId")
    @Operation(summary = "标注管理-分页查询-根据组织ID")
    @PreAuthorize("@ss.hasPermission('system:label:query')")
    public CommonResult<PageResult<LabelRespVO>> getLabelPageByDeptId(@Valid @RequestBody LabelSearchDTO search) {
        PageResult<LabelRespVO> result = labelService.getLabelPageByDeptId(search);
        return CommonResult.success(result);
    }

    @GetMapping(value = "/list")
    @Operation(summary = "标注管理-关联素材查询")
    @PreAuthorize("@ss.hasPermission('system:label:query')")
    public CommonResult<List<MaterialInfoVO>> materialList(List<Long> materialIds) {
        List<MaterialInfoVO> materialList = materialManageService.getMaterialListByIds(materialIds);
        return CommonResult.success(materialList);
    }

    @GetMapping(value = "/lock/{lableId}")
    @Operation(summary = "标注管理-锁定标注")
    public CommonResult<Void> lockLabel(@PathVariable Long lableId) {
        labelService.lockLabel(lableId);
        return CommonResult.success();
    }

    @GetMapping(value = "/unlock/{lableId}")
    @Operation(summary = "标注管理-取消锁定标注")
    public CommonResult<Void> unLockLabel(@PathVariable Long lableId) {
        labelService.unLockLabel(lableId);
        return CommonResult.success();
    }

    @PostMapping(value = "/route/page")
    @Operation(summary = "标注管理-应用航线分页")
    @PreAuthorize("@ss.hasPermission('system:label:query')")
    public CommonResult<PageResult<RouteRespVO>> getLabelReferenceRoutePage(@Valid @RequestBody LabelReferSearchDTO search) {
        PageResult<RouteRespVO> result = labelReferenceService.getLabelReferenceRoutePage(search);
        return CommonResult.success(result);
    }

    @PostMapping(value = "/random/id")
    @Operation(summary = "生成随机id")
    public CommonResult<Long> generateId() {
        long id = IdGenerator.generateId();
        return CommonResult.success(id);
    }


    @PostMapping(value = "/getPageNumById")
    @Operation(summary = "标注管理-查询标注页码")
    @PreAuthorize("@ss.hasPermission('system:label:query')")
    public CommonResult<Integer> getPageNumById(@Valid @RequestBody LabelSearchByIdDTO search) {
        Integer result = labelService.getPageNumById(search);
        return CommonResult.success(result);
    }

}
