package com.xinkongan.cloud.module.system.framework.operation;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "kongan.operation-sys.config")
public class OperationSysConfig {

    @Schema(description = "访问密钥")
    private String accessKey = "123456";

}
