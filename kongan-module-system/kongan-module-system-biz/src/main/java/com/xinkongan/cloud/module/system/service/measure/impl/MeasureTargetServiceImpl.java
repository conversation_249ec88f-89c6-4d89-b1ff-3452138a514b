package com.xinkongan.cloud.module.system.service.measure.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.xinkongan.cloud.framework.common.constant.BusinessTopicConstant;
import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.mq.core.send.IRocketMQSendService;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.FlyFileDTO;
import com.xinkongan.cloud.module.system.controller.admin.measure.vo.*;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordDO;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordFileDO;
import com.xinkongan.cloud.module.system.dal.dataobject.measure.BaseMeasureTargetDO;
import com.xinkongan.cloud.module.system.dal.mysql.measure.MeasureTargetMapper;
import com.xinkongan.cloud.module.system.dto.MeasureTargetBaseDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileSourceEnum;
import com.xinkongan.cloud.module.system.enums.flyrecord.FileTypeEnum;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordFileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import com.xinkongan.cloud.module.system.service.measure.MeasureTargetService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import com.xinkongan.cloud.module.websocket.enums.ReceivingTypeEnum;
import com.xinkongan.cloud.sdk.geo.utils.LocationUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.MEASURE_TARGET_NOT_EXIST;

/**
 * 激光打点 Service 实现类
 */
@Service
@Slf4j
public class MeasureTargetServiceImpl implements MeasureTargetService {

    @Resource
    private MeasureTargetMapper measureTargetMapper;
    @Resource
    private IFlyRecordService flyRecordService;
    @Resource
    private IFlyRecordFileService flyRecordFileService;
    @Resource
    private LocationUtil locationUtil;
    @Resource
    private WebSocketSendApi webSocketSendApi;
    @Resource
    private IJobService jobService;
    @Resource
    private IRocketMQSendService rocketMQSendService;

    /**
     * 创建激光打点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MeasureTargetBaseVO createMeasureTarget(CreateMeasureTargetReqVO createMeasureTargetReqVO) {
        log.info("【激光打点】创建激光打点, createMeasureTargetReqVO:{}", createMeasureTargetReqVO);
        // 校验当前飞行记录是否存在
        Long flyRecordId = createMeasureTargetReqVO.getFlyRecordId();
        FlyRecordDO flyRecordDO = flyRecordService.getById(flyRecordId);
        if (flyRecordDO == null) {
            throw new ServiceException(ErrorCodeConstants.FLY_RECORD_NOT_FOUND);
        }

        Long flyRecordFileId = this.processFile(createMeasureTargetReqVO, flyRecordDO);

        // 保存激光打点
        BaseMeasureTargetDO baseMeasureTargetDO = BeanUtil.toBean(createMeasureTargetReqVO, BaseMeasureTargetDO.class);
        baseMeasureTargetDO.setJobId(flyRecordDO.getJobId());
        baseMeasureTargetDO.setFlyRecordFileId(flyRecordFileId);
        baseMeasureTargetDO.setThumbnailUrl(createMeasureTargetReqVO.getUrl());
        // 获取当前飞行记录中最大打点的排序值
        Integer maxSort = measureTargetMapper.selectMaxSortByJobId(flyRecordDO.getJobId());
        log.info("【激光打点】当前任务最大排序值 maxSort:{}", maxSort);
        baseMeasureTargetDO.setSort(maxSort + 1);
        measureTargetMapper.insert(baseMeasureTargetDO);
        log.info("【激光打点】创建激光打点结果, baseMeasureTargetDO:{}", baseMeasureTargetDO);
        MeasureTargetBaseVO measureTargetBaseVO = BeanUtil.toBean(baseMeasureTargetDO, MeasureTargetBaseVO.class);
        // 发送截图给前端展示
        sendMeasureTargetFileToWeb(baseMeasureTargetDO);
        // 返回激光打点信息
        return measureTargetBaseVO;
    }

    /**
     * 查询激光打点列表
     */
    @Override
    public List<MeasureTargetBaseVO> listMeasureTarget(ListMeasureTargetReqVO reqVO) {
        log.info("【激光打点】查询当前任务所有激光打点, reqVO:{}", reqVO);
        List<BaseMeasureTargetDO> baseMeasureTargets = measureTargetMapper.selectList(
                new LambdaQueryWrapperX<BaseMeasureTargetDO>()
                        .eqIfPresent(BaseMeasureTargetDO::getFlyRecordId, reqVO.getFlyRecordId())
                        .eqIfPresent(BaseMeasureTargetDO::getJobId, reqVO.getJobId()));
        if (CollectionUtil.isEmpty(baseMeasureTargets)) {
            return List.of();
        }
        return baseMeasureTargets
                .stream()
                .map(b -> BeanUtil.toBean(b, MeasureTargetBaseVO.class))
                .collect(Collectors.toList());
    }

    /**
     * 删除激光打点
     * 注意：
     * 1 删除激光打点需要同时删除关联的截图和异常
     * 2 删除激光打点关联的截图和异常时不删除激光打点数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMeasureTarget(Long id) {
        log.info("【激光打点】删除激光打点, id:{}", id);
        // 查询激光打点是否存在
        BaseMeasureTargetDO baseMeasureTargetDO = measureTargetMapper.selectById(id);
        if (baseMeasureTargetDO == null) {
            throw new ServiceException(MEASURE_TARGET_NOT_EXIST);
        }
        // 删除打点数据和缩略图数据
        measureTargetMapper.deleteById(id);
        flyRecordFileService.deleteById(baseMeasureTargetDO.getFlyRecordFileId());
        // 发送删除激光打点给前端
        sendDeleteMeasureTargetToWeb(baseMeasureTargetDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMeasureTargetBatchByFlyRecordId(Long flyRecordId) {
        if (flyRecordId == null) {
            log.info("【飞行记录删除打点】飞行记录id 为空");
            return;
        }
        List<BaseMeasureTargetDO> baseMeasureTargets = measureTargetMapper.selectList(
                new LambdaQueryWrapperX<BaseMeasureTargetDO>()
                        .eq(BaseMeasureTargetDO::getFlyRecordId, flyRecordId));
        baseMeasureTargets.forEach(b -> {
            try {
                // 删除打点数据和缩略图数据
                measureTargetMapper.deleteById(b.getId());
                flyRecordFileService.deleteById(b.getFlyRecordFileId());
            } catch (Exception e) {
                log.error("[删除打点数据异常]，异常信息为：{}", e.getMessage());
                log.error(ExceptionUtils.getStackTrace(e));
            }
        });
    }

    @Override
    public void deleteMeasureTargetBatchByFlyRecordIds(List<Long> flyRecordIds) {
        if (flyRecordIds == null) {
            log.info("【飞行记录删除打点】飞行记录ids 为空");
            return;
        }
        flyRecordIds.forEach(this::deleteMeasureTargetBatchByFlyRecordId);
    }

    /**
     * 根据飞行记录图片id查询激光打点
     */
    @Override
    public MeasureTargetBaseDTO getMeasureTargetByFlyRecordFileId(Long flyRecordFileId) {
        BaseMeasureTargetDO baseMeasureTargetDO = measureTargetMapper.selectOne(
                new LambdaQueryWrapperX<BaseMeasureTargetDO>()
                        .eq(BaseMeasureTargetDO::getFlyRecordFileId, flyRecordFileId));
        if (baseMeasureTargetDO == null) {
            return null;
        }
        return BeanUtil.toBean(baseMeasureTargetDO, MeasureTargetBaseDTO.class);
    }

    @Override
    public List<MeasureTargetBaseVO> listMeasureTargetBySn(List<String> dockSns) {
        List<MeasureTargetBaseVO> results = new ArrayList<>();
        for (String dockSn : dockSns) {
            // 根据无人机sn获取当前飞行记录id
            JobFlyInfo jobFlyInfo = jobService.getExecTaskCacheByDockSn(dockSn);
            if (jobFlyInfo == null || jobFlyInfo.getFlyRecordId() == null) {
                log.info("【激光打点】当前无人机sn:{} 无飞行记录", dockSn);
                continue;
            }
            List<MeasureTargetBaseVO> measureTargetBaseInfos = listMeasureTarget(ListMeasureTargetReqVO.builder().flyRecordId(jobFlyInfo.getFlyRecordId()).build());
            results.addAll(measureTargetBaseInfos);
        }
        return results;
    }

    @Override
    public Long measureTargetCount(String dockSn) {
        // 根据无人机sn获取当前飞行记录id
        JobFlyInfo jobFlyInfo = jobService.getExecTaskCacheByDockSn(dockSn);
        if (jobFlyInfo == null || jobFlyInfo.getFlyRecordId() == null) {
            log.info("【激光打点】当前无人机sn:{} 无飞行记录", dockSn);
            return 0L;
        }
        return measureTargetMapper.selectCount(
                new LambdaQueryWrapperX<BaseMeasureTargetDO>()
                       .eq(BaseMeasureTargetDO::getFlyRecordId, jobFlyInfo.getFlyRecordId()));
    }

    /**
     * 处理截图文件
     */
    private Long processFile(CreateMeasureTargetReqVO createMeasureTargetReqVO, FlyRecordDO flyRecordDO) {
        log.info("【激光打点】处理截图文件, flyRecordDO:{}", flyRecordDO);
        // 无人机sn
        String droneSn = createMeasureTargetReqVO.getSn();
        String height = String.valueOf(createMeasureTargetReqVO.getHeight());
        // 飞行记录id
        Long flyRecordId = createMeasureTargetReqVO.getFlyRecordId();
        FlyRecordFileDO flyRecordFileDO = new FlyRecordFileDO();

        flyRecordFileDO.setFlyRecordId(flyRecordId);
        flyRecordFileDO.setFileId(createMeasureTargetReqVO.getFileId());
        flyRecordFileDO.setUrl(createMeasureTargetReqVO.getUrl());
        flyRecordFileDO.setName(createMeasureTargetReqVO.getFileName());
        flyRecordFileDO.setFileSource(FileSourceEnum.SCREENSHOT.getCode());
        flyRecordFileDO.setFileType(FileTypeEnum.IMAGE.getCode());
        flyRecordFileDO.setFileSize(createMeasureTargetReqVO.getSize());
        flyRecordFileDO.setLon(String.valueOf(createMeasureTargetReqVO.getLongitude()));
        flyRecordFileDO.setLat(String.valueOf(createMeasureTargetReqVO.getLatitude()));
        flyRecordFileDO.setAbsoluteAltitude(height);
        flyRecordFileDO.setDockSn(flyRecordDO.getDockSn());
        flyRecordFileDO.setActionTime(LocalDateTime.now());

        String address = locationUtil.getLocationByWg84(createMeasureTargetReqVO.getLongitude(), createMeasureTargetReqVO.getLatitude());
        flyRecordFileDO.setAddress(address);
        Long flyRecordFileId = flyRecordFileService.createFileRecord(flyRecordFileDO);
        log.info("【激光打点】截图保存结果 flyRecordFileId:{}", flyRecordFileId);
        return flyRecordFileId;
    }

    /**
     * 发送截图给前端展示
     */
    private void sendMeasureTargetFileToWeb(BaseMeasureTargetDO measureTargetDO) {
        SendMeasureToWebVO sendMeasureToWebVO = new SendMeasureToWebVO();
        sendMeasureToWebVO.setIsAdd(YesNoEnum.YES.getCode());
        // 打点信息
        MeasureTargetBaseVO measureTargetBaseVO = BeanUtil.toBean(measureTargetDO, MeasureTargetBaseVO.class);
        sendMeasureToWebVO.setMeasureTargetBaseVO(measureTargetBaseVO);
        // 发送打点信息
        WebSocketMessageDTO webSocketSendReqDTO = buildWebsocketSendDTO(TenantContextHolder.getTenantId(), SecurityFrameworkUtils.getLoginUserDeptId(),
                BizCodeEnum.MEASURE_TARGET_UPDATE_SEND_TO_VIEW, sendMeasureToWebVO);
        log.info("【激光打点】发送打点给前端, webSocketSendReqDTO:{}", webSocketSendReqDTO);
        webSocketSendApi.sendByTenantDeptSet(webSocketSendReqDTO);
        // 查询截图信息
        FlyRecordFileDO flyRecordFileDO = flyRecordFileService.getById(measureTargetDO.getFlyRecordFileId());
        if (flyRecordFileDO == null) {
            log.error("【激光打点】发送截图给前端展示, 截图信息不存在");
            return;
        }
        FlyFileDTO flyFileDTO = BeanUtil.copyProperties(flyRecordFileDO, FlyFileDTO.class);
        log.info("【激光打点】发送截图给前端展示, webSocketFileDTO:{}", flyFileDTO);
        rocketMQSendService.syncSend(BusinessTopicConstant.FLY_FILE_PUSH, flyFileDTO);
    }

    /**
     * 发送删除激光打点给前端
     */
    private void sendDeleteMeasureTargetToWeb(BaseMeasureTargetDO baseMeasureTargetDO) {
        SendMeasureToWebVO sendMeasureToWebVO = new SendMeasureToWebVO();
        sendMeasureToWebVO.setIsAdd(YesNoEnum.NO.getCode());
        MeasureTargetBaseVO targetBaseVO = BeanUtil.toBean(baseMeasureTargetDO, MeasureTargetBaseVO.class);
        sendMeasureToWebVO.setMeasureTargetBaseVO(targetBaseVO);
        WebSocketMessageDTO webSocketSendReqDTO = buildWebsocketSendDTO(baseMeasureTargetDO.getTenantId(),
                baseMeasureTargetDO.getDeptId(), BizCodeEnum.MEASURE_TARGET_UPDATE_SEND_TO_VIEW, sendMeasureToWebVO);
        log.info("【激光打点】发送删除激光打点, webSocketSendReqDTO:{}", webSocketSendReqDTO);
        webSocketSendApi.sendByTenantDeptSet(webSocketSendReqDTO);
    }

    /**
     * 构建发送给前端的 websocket DTO
     */
    private <T> WebSocketMessageDTO buildWebsocketSendDTO(Long tenantId, Long deptId, BizCodeEnum bizCodeEnum, T data) {
        WebSocketMessageDTO webSocketSendReqDTO = new WebSocketMessageDTO();
        webSocketSendReqDTO.setTenantId(tenantId); // 设置租户
        webSocketSendReqDTO.setDeptIdSet(Collections.singleton(deptId));
        CustomWebSocketMessage message = new CustomWebSocketMessage();
        message.setBizCode(bizCodeEnum.getCode());
        message.setData(data);
        message.setTypeEnum(ReceivingTypeEnum.BY_THIS_DEPT);
        webSocketSendReqDTO.setMessage(message);
        return webSocketSendReqDTO;
    }
}
