package com.xinkongan.cloud.module.system.service.measure;

import com.xinkongan.cloud.module.system.controller.admin.measure.vo.CreateMeasureTargetReqVO;
import com.xinkongan.cloud.module.system.controller.admin.measure.vo.ListMeasureTargetReqVO;
import com.xinkongan.cloud.module.system.controller.admin.measure.vo.MeasureTargetBaseVO;
import com.xinkongan.cloud.module.system.dto.MeasureTargetBaseDTO;

import java.util.List;

public interface MeasureTargetService {

    /**
     * 创建激光打点
     */
    MeasureTargetBaseVO createMeasureTarget(CreateMeasureTargetReqVO createMeasureTargetReqVO);


    /**
     * 查询激光打点列表
     */
    List<MeasureTargetBaseVO> listMeasureTarget(ListMeasureTargetReqVO reqVO);



    /**
     * 删除激光打点
     * 注意：
     * 1 删除激光打点需要同时删除关联的截图和异常
     * 2 删除激光打点关联的截图和异常时不删除激光打点数据
     */
    void deleteMeasureTarget(Long id);


    /**
     * 删除飞行记录时删除的打点数据
     */
    void deleteMeasureTargetBatchByFlyRecordId(Long flyRecordId);


    /**
     * 删除飞行记录删除打点数据
     */
    void deleteMeasureTargetBatchByFlyRecordIds(List<Long> flyRecordIds);


    /**
     * 根据飞行记录图片id查询激光打点
     */
    MeasureTargetBaseDTO getMeasureTargetByFlyRecordFileId(Long flyRecordFileId);


    /**
     * 监控页面查询激光打点列表
     */
    List<MeasureTargetBaseVO> listMeasureTargetBySn(List<String> dockSns);

    /**
     * 监控页面查询激光打点数量
     */
    Long measureTargetCount(String dockSn);
}
