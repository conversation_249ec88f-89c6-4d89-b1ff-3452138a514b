package com.xinkongan.cloud.module.system.controller.admin.dock.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/3
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DockTypeFeature {

    @Schema(description = "图标URL")
    private String logoUrl;

    @Schema(description = "当前机场类型是否支持设置任务精度")
    private Boolean supportedWaylinePrecisionType;

    @Schema(description = "当前机场类型是否存在默认经度")
    private Integer waylinePrecisionType;

    @Schema(description = "是否支持框选变焦")
    private Boolean cameraFrameZoom;

}
