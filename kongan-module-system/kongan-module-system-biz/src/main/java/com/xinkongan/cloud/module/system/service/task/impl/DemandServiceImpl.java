package com.xinkongan.cloud.module.system.service.task.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.common.util.http.HttpUtils;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.job.core.powerjob.scheduler.SchedulerManager;
import com.xinkongan.cloud.framework.lock.service.DistributedLockExecutor;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.framework.mybatis.core.util.MyBatisUtils;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.HistogramVO;
import com.xinkongan.cloud.module.system.controller.admin.label.vo.TabRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.demand.*;
import com.xinkongan.cloud.module.system.dal.dataobject.task.DemandDO;

import com.xinkongan.cloud.module.system.dal.mysql.task.DemandMapper;

import com.xinkongan.cloud.module.system.service.label.TabServiceImpl;
import com.xinkongan.cloud.module.system.service.task.*;

import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DockModeCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.powerjob.worker.log.OmsLogger;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * @Description 需求管理Service实现类
 * <AUTHOR>
 * @Date 2025/8/21 20:23
 */
@Service
@Slf4j
public class DemandServiceImpl implements IDemandService {

    @Resource
    private DemandMapper demandMapper;
    @Resource
    private TabServiceImpl tabService;


    /**
     * 需求分页
     **/
    @Override
    public PageResult<FlyDemandPageRespVO> page(FlyDemandPageReqVO reqVO) {
        log.info("[需求管理] 分页查询 reqVO:{}", JSONUtil.toJsonStr(reqVO));
        // MyBatis Plus 查询
        IPage<FlyDemandPageRespVO> mpPage = MyBatisUtils.buildPage(reqVO);
        IPage<FlyDemandPageRespVO> pageRes = null;
        pageRes = demandMapper.page(mpPage, reqVO);
        if (pageRes.getRecords().isEmpty()) {
            return PageResult.empty();
        }
        return new PageResult<>(pageRes.getRecords(), pageRes.getTotal());
    }

    /**
     * 创建飞行需求
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createDemand(FlyDemandReqVO flyDemandReqVO) {
        log.info("[需求管理] 创建飞行需求 flyDemandReqVO:{}", JSONUtil.toJsonStr(flyDemandReqVO));
        // 1 先保存飞行需求
        DemandDO demandDO = BeanUtil.toBean(flyDemandReqVO, DemandDO.class);
        if (demandDO.getTenantId() == null) {
            demandDO.setTenantId(TenantContextHolder.getTenantId());
        }
        if (demandDO.getDeptId() == null) {
            demandDO.setDeptId(SecurityFrameworkUtils.getLoginUserDeptId());
        }
        demandMapper.insert(demandDO);
        // 2 保存点线面数据和飞行需求id关联
        tabService.saveDemandTabInfos(demandDO.getId(), flyDemandReqVO.getTabSaveInfos());
        //TODO 3 判断入参状态是否为1 已提交  ，是则将数据转为json发送给第三方
        if (demandDO.getStatus() == 1) {
            //TODO 3.1 调用第三方接口
//            String responseBody = HttpUtils.post(URL + "?" + queryString, headers, requestBody);
            demandDO.setSubmitTime(LocalDateTime.now());
            demandMapper.updateById(demandDO);
        }

        return demandDO.getId();
    }

    /**
     * 更新需求
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateDemand(FlyDemandUpdateReqVO flyDemandUpdateReqVO) {
        log.info("[需求管理] 修改需求 FlyDemandUpdateReqVO:{}", JSONUtil.toJsonStr(flyDemandUpdateReqVO));
        // 1 先更新飞行需求
        DemandDO demandDO = BeanUtil.toBean(flyDemandUpdateReqVO, DemandDO.class);
        demandDO.setId(flyDemandUpdateReqVO.getId());
        // 1 先根据需求id删除点线面表数据
        tabService.delTabByDemandId(demandDO.getId());
        // 2 保存点线面数据和飞行需求id关联
        tabService.saveDemandTabInfos(demandDO.getId(), flyDemandUpdateReqVO.getTabSaveInfos());
        // 3 更新需求
        demandMapper.updateById(demandDO);
        //TODO 4 判断入参状态是否为1 已提交  ，是则将数据转为json发送给第三方
        if (demandDO.getStatus() == 1) {
            //TODO 3.1 调用第三方接口

            demandDO.setSubmitTime(LocalDateTime.now());
            demandMapper.updateById(demandDO);
        }
        return true;
    }


    @Override
    public FlyDemandRespVO getById(Long demandId) {
        DemandDO demandDO = demandMapper.selectById(demandId);
        FlyDemandRespVO flyDemandRespVO = BeanUtil.toBean(demandDO, FlyDemandRespVO.class);
        // 查询点线面数据
        List<TabRespVO> tableInfos = tabService.getTabInfosByDemandId(demandId);
        flyDemandRespVO.setTabInfos(tableInfos);
        return flyDemandRespVO;
    }



    /**
     * 删除需求

     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteDemand(Long demandId) {
        // 查询需求
        DemandDO demandDO = demandMapper.selectById(demandId);
        if (demandDO == null) {
            throw exception(DEMAND_NOT_EXISTS);
        }
        // 1 删除需求关联的点线面数据
        tabService.delTabByDemandId(demandId);
        // 2 删除飞行需求
        demandMapper.deleteById(demandId);
        // 此处无需通知第三方
        return Boolean.TRUE;
    }

    @Override
    public Long getDemandCount() {
        return demandMapper.selectCount();
    }

    @Override
    public DemandStatisticCountVO statisticCount() {
        return demandMapper.statisticCount();
    }


//    @Override
//    @TenantIgnore
//    @DataPermission(enable = false)
//    public Long countJobsByRouteId(Long routeId) {
//        return jobMapper.selectCount(JobDO::getRouteId, routeId);
//    }
//
//    @Override
//    public Long countExecJobByDeptIds(Set<Long> deptIds) {
//        if (CollectionUtil.isEmpty(deptIds)) {
//            return 0L;
//        }
//        Long count = jobMapper.selectCount(
//                new LambdaQueryWrapperX<JobDO>()
//                        .eq(JobDO::getStatus, JobStatusTypeEnum.EXECUTING.getCode())
//                        .in(JobDO::getDeptId, deptIds)
//        );
//        return count;
//    }
//
//
//    @Override
//    public List<TaskTopStatisticVO> getTaskTopStatisticInfo(LocalDateTime[] timeRange) {
//        return jobMapper.getTaskTopStatisticInfo(timeRange);
//    }
//
//
//    @Override
//    public Integer getPageNumById(Long id, Integer pageSize) {
//        // 查询当前任务
//        JobDO jobDO = jobMapper.selectById(id);
//        if (jobDO == null) {
//            throw exception(JOB_NOT_EXISTS);
//        }
//        // 不同的场景查询条件不同
//        switch (TaskSceneTypeEnum.find(jobDO.getScene())) {
//            case INSPECTION -> {
//                return inspectionJobService.getPageNumById(jobDO, pageSize);
//            }
//            case ALARM_RESPONSE -> {
//                return alarmJobService.getPageNumById(jobDO, pageSize);
//            }
//            default -> throw exception(JOB_SCENE_NO_IMPLEMENTS);
//        }
//    }
//
//
//
//    @Override
//    public List<PieReqVO> getJobPieStatistic(PieParamsInfo pieParamsInfo) {
//
//        List<PieReqVO> pieResult = new ArrayList<>();
//        // 查询巡检任务数目
//        Long inspectionCount = jobMapper.selectCount(
//                new LambdaQueryWrapperX<JobDO>()
//                        .eq(pieParamsInfo.getDeptId() != null, JobDO::getDeptId, pieParamsInfo.getDeptId())
//                        .eq(pieParamsInfo.getUserId() != null, JobDO::getCreator, pieParamsInfo.getUserId())
//                        .eq(JobDO::getScene, TaskSceneTypeEnum.INSPECTION.getScene())
//        );
//        pieResult.add(PieReqVO.builder().key("智能巡检").count(inspectionCount).build());
//
//        // 查询接警任务数目
//        Long alarmCount = jobMapper.selectCount(
//                new LambdaQueryWrapperX<JobDO>()
//                        .eq(pieParamsInfo.getDeptId() != null, JobDO::getDeptId, pieParamsInfo.getDeptId())
//                        .eq(pieParamsInfo.getUserId() != null, JobDO::getCreator, pieParamsInfo.getUserId())
//                        .eq(JobDO::getScene, TaskSceneTypeEnum.ALARM_RESPONSE.getScene())
//        );
//        pieResult.add(PieReqVO.builder().key("接警侦查").count(alarmCount).build());
//        return pieResult;
//    }
//
//    @Override
//    public List<HistogramVO> getJobHistogramStatistic(HistogramSearchDTO histogramSearchInfo) {
//        Integer type = histogramSearchInfo.getType();
//        Long deptId = histogramSearchInfo.getDeptId();
//
//        // 获取时间范围和日期格式
//        LocalDateTime[] timeRange = getTimeRange(type);
//        String dateFormat = getDateFormat(type);
//
//        // 获取数据库统计结果
//        List<HistogramVO> dbResults = jobMapper.computeHistogramData(deptId, histogramSearchInfo.getUserId(), dateFormat, timeRange);
//        // 生成完整日期序列并填充数据
//        return fillMissingDates(dbResults, timeRange, type);
//    }

    /**
     * 获取对应时间类型的日期格式
     */
    private String getDateFormat(Integer type) {
        return switch (type) {
            case 1 -> "%Y-%m-%d";     // 本周
            case 2 -> "%Y-%m-%d";     // 本月
            case 3 -> "%Y-%m";        // 本年
            default -> throw new IllegalArgumentException("Invalid type: " + type);
        };
    }

    /**
     * 获取时间范围（优化后的版本）
     */
    private LocalDateTime[] getTimeRange(Integer type) {
        LocalDateTime now = LocalDateTime.now();
        return switch (type) {
            case 1 -> { // 本周
                LocalDateTime start = now.with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0);
                yield new LocalDateTime[]{start, start.plusDays(6).withHour(23).withMinute(59).withSecond(59)};
            }
            case 2 -> { // 本月
                LocalDateTime start = now.with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0);
                yield new LocalDateTime[]{start, now.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59)};
            }
            case 3 -> { // 本年
                LocalDateTime start = now.with(TemporalAdjusters.firstDayOfYear()).withHour(0).withMinute(0).withSecond(0);
                yield new LocalDateTime[]{start, now.with(TemporalAdjusters.lastDayOfYear()).withHour(23).withMinute(59).withSecond(59)};
            }
            default -> throw new IllegalArgumentException("Invalid type: " + type);
        };
    }

    /**
     * 填充缺失的日期数据
     */
    private List<HistogramVO> fillMissingDates(List<HistogramVO> dbResults, LocalDateTime[] timeRange, Integer type) {
        Map<String, HistogramVO> resultMap = dbResults.stream().collect(Collectors.toMap(HistogramVO::getTimePeriod, Function.identity()));

        List<HistogramVO> filledResults = new ArrayList<>();
        List<String> timeKeys = this.generateTimeKeys(timeRange, type);
        for (String key : timeKeys) {
            filledResults.add(resultMap.getOrDefault(key, new HistogramVO().setTimePeriod(key).setInspectionCount(0L).setAlarmCount(0L)));
        }
        return filledResults;
    }

    private List<String> generateTimeKeys(LocalDateTime[] timeRange, Integer type) {
        List<String> timeKeys = new ArrayList<>();
        LocalDate startDate = timeRange[0].toLocalDate();
        LocalDate endDate = timeRange[1].toLocalDate();

        switch (type) {
            case 1, 2 -> { // 本周
                DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    timeKeys.add(date.format(dayFormatter));
                }
            }
            case 3 -> { // 本年
                DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusMonths(1)) {
                    timeKeys.add(date.format(monthFormatter));
                }
            }
            default -> throw new IllegalArgumentException("Invalid type: " + type);
        }
        return timeKeys;
    }

//    @Override
//    public Long getJobCountByUserId(Long userId) {
//        Long count = jobMapper.selectCount(
//                new LambdaQueryWrapperX<JobDO>()
//                        .eq(JobDO::getCreator, userId)
//        );
//        return count;
//    }








}