package com.xinkongan.cloud.module.system.service.callback;

import com.xinkongan.cloud.module.system.api.callback.dto.SystemCallbackDTO;
import com.xinkongan.cloud.module.system.enums.callback.SystemCallbackTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 系统回调服务Service
 * <AUTHOR>
 * @Date 2025/5/22 16:38
 */
@Service
public class SystemCallbackServiceImpl implements ISystemCallbackService {

    @Autowired(required = false)
    private List<ICallbackHandle> systemCallbackHandle;

    @Override
    public Object callback(SystemCallbackDTO systemCallbackDTO) {
        SystemCallbackTypeEnum callbackType = systemCallbackDTO.getCallbackType();
        for (ICallbackHandle<?> callbackHandle : systemCallbackHandle) {
            if(callbackHandle.getCallbackType().equals(callbackType)){
                return callbackHandle.handle(systemCallbackDTO.getCallbackParam());
            }
        }
        return null;
    }
}