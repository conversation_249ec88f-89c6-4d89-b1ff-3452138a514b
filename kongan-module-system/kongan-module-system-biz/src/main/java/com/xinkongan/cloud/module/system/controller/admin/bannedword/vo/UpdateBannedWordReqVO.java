package com.xinkongan.cloud.module.system.controller.admin.bannedword.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.ToString;

/**
 * @Description 创建违禁词
 * <AUTHOR>
 * @Date 2024/11/5 10:24
 */
@Schema(description = "管理后台 - 创建违禁词 Request VO")
@Data
@ToString(callSuper = true)
public class UpdateBannedWordReqVO {

    @Schema(description = "违禁词")
    @NotBlank(message = "违禁词id不能为空!")
    private Long id;

    @Schema(description = "违禁词")
    @NotBlank(message = "违禁词不能为空!")
    private String word;
}