package com.xinkongan.cloud.module.system.controller.admin.route.dto;

import com.xinkongan.cloud.framework.kmz.dto.ActionGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointActionInfoDTO {

    // 航点动作信息
    private WayPointActionInfoDTO wayPointActionInfo;

    // 航点信息
    private WayPointDTO point;

    // 需要设置结束的动作组的引用
    private ActionGroup actionGroup;
}
