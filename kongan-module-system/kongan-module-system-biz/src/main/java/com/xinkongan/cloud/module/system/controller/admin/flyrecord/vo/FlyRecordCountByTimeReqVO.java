package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description 根据时间统计飞行记录
 * <AUTHOR>
 * @Date 2025/2/24 10:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlyRecordCountByTimeReqVO {

    @Schema(description = "0本周1本月2本年")
    @NotNull(message = "时间类型不能为空")
    private Integer timeType;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;
}