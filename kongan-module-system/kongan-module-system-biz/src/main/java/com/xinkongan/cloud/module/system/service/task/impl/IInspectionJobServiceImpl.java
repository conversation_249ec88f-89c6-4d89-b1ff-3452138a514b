package com.xinkongan.cloud.module.system.service.task.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.common.enums.YesNoEnum;
import com.xinkongan.cloud.framework.common.util.cron.CronUtils;
import com.xinkongan.cloud.framework.job.core.powerjob.handler.JobParam;
import com.xinkongan.cloud.framework.job.core.powerjob.scheduler.SchedulerManager;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.algorithm.api.exception.ExceptionApi;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceVO;
import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeSaveVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteBaseRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobDetailRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.job.JobDetailStatistics;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobDO;
import com.xinkongan.cloud.module.system.dal.dataobject.task.JobFlyDO;
import com.xinkongan.cloud.module.system.dal.mysql.task.JobMapper;
import com.xinkongan.cloud.module.system.enums.notice.NoticeSendTypeEnum;
import com.xinkongan.cloud.module.system.enums.notice.NoticeTypeEnum;
import com.xinkongan.cloud.module.system.enums.task.DockTaskModeEnum;
import com.xinkongan.cloud.module.system.enums.task.JobStatusTypeEnum;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordFileService;
import com.xinkongan.cloud.module.system.service.flyrecord.IFlyRecordService;
import com.xinkongan.cloud.module.system.service.notice.INoticeService;
import com.xinkongan.cloud.module.system.service.route.IRouteService;
import com.xinkongan.cloud.module.system.service.task.IDockTaskApplyService;
import com.xinkongan.cloud.module.system.service.task.IDockTaskService;
import com.xinkongan.cloud.module.system.service.task.IInspectionJobService;
import com.xinkongan.cloud.module.system.service.task.IJobFlyService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * @Description 任务管理Service实现类
 * <AUTHOR>
 * @Date 2025/1/2 15:23
 */
@Service
@Slf4j
public class IInspectionJobServiceImpl implements IInspectionJobService {

    @Resource
    private JobMapper jobMapper;// 任务Mapper
    @Resource
    private SchedulerManager schedulerManager;// 定时任务管理器
    @Resource
    private DockDeviceService dockDeviceService;// 机场设备Service
    @Resource
    private IRouteService routeService;// 航线Service
    @Resource
    private IJobFlyService jobFlyService;// 任务飞行Service
    @Resource
    private IDockTaskService dockTaskService;// 计划Service
    @Resource
    private WebSocketSendApi webSocketSendApi;// websocket发送API
    @Resource
    private INoticeService noticeService;
    @Resource
    private AdminUserService adminUserService;
    @Resource
    private IDockTaskApplyService dockTaskApplyService;
    @Resource
    private IFlyRecordService flyRecordService;
    @Resource
    private IFlyRecordFileService flyRecordFileService;
    @Resource
    private ExceptionApi exceptionApi;

    /**
     * 创建任务 1
     *
     * <AUTHOR>
     * @date 2025/1/3 9:14
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createJob(JobDO jobDO) {
        log.info("[任务管理] 创建任务 jobDO:{}", JSONUtil.toJsonStr(jobDO));
        jobMapper.insert(jobDO);
        Long id = jobDO.getId();
        // 创建定时任务
        DockTaskModeEnum dockTaskModeEnum = DockTaskModeEnum.find(jobDO.getExecMode());
        if (DockTaskModeEnum.TIMING.equals(dockTaskModeEnum) || DockTaskModeEnum.CYCLE.equals(dockTaskModeEnum)) {
            log.info("[任务管理] 创建定时任务 jobId:{}", id);
            JobParam jobParam = JobParam.builder().id(id).handler(JobServiceJobHandler.HANDLER_JOB_BEAN_NAME).build();
            LocalDateTime execTime = jobDO.getExecTime();
            DateTime date = DateUtil.date(execTime);
            String cron = CronUtils.getCronByDate(date);
            log.info("[任务管理] 创建定时任务 jobParam:{} cron:{}", JSONUtil.toJsonStr(jobParam), cron);
            Long schedulerId = schedulerManager.addJob("定时任务_" + id, jobParam, cron, null, null);
            // 保存定时任务任务管理器的id
            jobMapper.update(Wrappers.lambdaUpdate(JobDO.class)
                    .eq(JobDO::getId, id).set(JobDO::getSchedulerId, schedulerId));
        }
        return id;
    }

    private void sendJobFailNotice(JobDO jobDO) {
        String dockSn = jobDO.getDockSn();
        DockDeviceDO byCacheDeviceSn = dockDeviceService.getByCacheDeviceSn(dockSn);
        String dockName = "";
        if (byCacheDeviceSn != null) {
            dockName = byCacheDeviceSn.getDeviceName();
        }
        // 查询本组织及上级所有账号
        Set<Long> userIds = adminUserService.getDeptAndParentDeptPermissionUserIds(jobDO.getDeptId());
        if (CollUtil.isNotEmpty(userIds)) {
            // 任务失败 发送消息
            String content = String.format("任务执行失败，执行任务：%s，执行机场%s，sn码为:%s，失败原因：%s", jobDO.getName(), dockName, dockSn, jobDO.getReason());
            if (JobStatusTypeEnum.UNFINISHED.equals(JobStatusTypeEnum.find(jobDO.getStatus()))) {
                content = String.format("任务未完成，执行任务：%s，执行机场：%s，sn码为:%s，未完成原因：%s", jobDO.getName(), dockName, dockSn, jobDO.getReason());
            }
            noticeService.noticeToUsers(Set.of(NoticeSendTypeEnum.HOVER), NoticeTypeEnum.JOB_EXEC_FAIL, userIds,
                    NoticeSaveVO.builder().name(jobDO.getName() + "任务执行失败").content(content)
                            .dockSn(byCacheDeviceSn != null ? byCacheDeviceSn.getDeviceSn() : null).dockName(dockName)
                            .jobId(jobDO.getId()).jobName(jobDO.getName()).build());
        }
    }

    /**
     * 修改任务状态
     *
     * <AUTHOR>
     * @date 2025/1/2 15:24
     **/
    @Override
    public void updateJobStatus(Long jobId, JobStatusTypeEnum status) {
        log.info("[任务管理] 修改任务状态 jobId:{}, status:{}", jobId, status);
        // 查询任务
        JobDO jobDO = jobMapper.selectById(jobId);
        log.info("[任务管理] 修改任务状态 jobId:{}, jobDO:{}", jobId, JSONUtil.toJsonStr(jobDO));
        // 任务状态修改校验
        validateUpdateJobStatus(jobDO);
        LocalDateTime endTime = null;
        if (JobStatusTypeEnum.FAILED.equals(status) || JobStatusTypeEnum.EXECUTED.equals(status)) {
            endTime = LocalDateTime.now();
        }
        // 修改任务状态
        jobMapper.update(Wrappers.lambdaUpdate(JobDO.class).eq(JobDO::getId, jobId)
                .set(endTime != null, JobDO::getExecEndTime, endTime).set(JobDO::getStatus, status.getCode()));
        // 已失效、已取消 禁用定时任务
        try {
            if (JobStatusTypeEnum.EXPIRED.equals(status) || JobStatusTypeEnum.CANCELED.equals(status)) {
                schedulerManager.disableJob(jobDO.getSchedulerId());
            }
        } catch (Exception e) {
            log.error("禁用任务失败 jobId:{}", jobId, e);
        }
        // 发送给前端
        jobDO = jobMapper.selectById(jobId);
        sendJobDOToWeb(List.of(jobDO));
        if (status.equals(JobStatusTypeEnum.FAILED) || status.equals(JobStatusTypeEnum.UNFINISHED)) {
            // 发送消息通知
            sendJobFailNotice(jobDO);
        }
    }

    /**
     * 任务详情
     *
     * <AUTHOR>
     * @date 2025/1/14 11:07
     **/
    @Override
    public JobDetailRespVO getByJobId(Long jobId) {
        log.info("[任务管理] 获取任务详情 jobId:{}", jobId);
        JobDetailRespVO jobDetailRespVO = jobMapper.selectByJobId(jobId);
        log.info("[任务管理] 获取任务详情 jobId:{}, jobDO:{}", jobId, JSONUtil.toJsonStr(jobDetailRespVO));
        if (jobDetailRespVO == null) {
            throw exception(JOB_NOT_EXISTS);
        }
        // 查询共享的机场
        if (jobDetailRespVO.getDockSn() != null && jobDetailRespVO.getDockName() == null) {
            DockDeviceVO byDockSn = null;
            try {
                byDockSn = dockDeviceService.getByDockSn(jobDetailRespVO.getDockSn(), jobDetailRespVO.getTenantId());
            } catch (Exception e) {
                log.error("[任务管理] 获取任务详情 查询共享的机场失败 dockSn:{}", jobDetailRespVO.getDockSn(), e);
            }
            if (byDockSn != null) {
                // 设置机场名称
                jobDetailRespVO.setDockName(byDockSn.getDeviceName());
            }
        }
        // 查询分享的航线
        if (jobDetailRespVO.getRouteId() != null && jobDetailRespVO.getRouteName() == null) {
            RouteBaseRespVO routeBaseById = null;
            try {
                routeBaseById = routeService.getRouteBaseById(jobDetailRespVO.getRouteId());
            } catch (Exception e) {
                log.error("[任务管理] 获取任务详情 查询共享的航线失败 routeId:{}", jobDetailRespVO.getRouteId(), e);
            }
            if (routeBaseById != null && Objects.equals(routeBaseById.getShareFlag(), YesNoEnum.YES.getCode())) {
                jobDetailRespVO.setRouteName(routeBaseById.getRouteName());
            } else {
                // 没有权限，航线也没有共享 清空航线id
                jobDetailRespVO.setRouteId(null);
            }
        }
        // 执行时间列表 根据飞行时间中的执行时间查询,根据执行时间倒序排序
        List<JobFlyDO> jobFlyDOS = jobFlyService.listFlyDescExecTimeByJobId(jobId);
        if (CollUtil.isNotEmpty(jobFlyDOS)) {
            List<LocalDateTime> dateTimes = jobFlyDOS.stream().map(JobFlyDO::getExecTime).toList();
            jobDetailRespVO.setExecuteTimeList(dateTimes);
        }
        // 如果执行时间是空，则返回空列表
        if (CollUtil.isEmpty(jobDetailRespVO.getExecuteTimeList())) {
            jobDetailRespVO.setExecuteTimeList(List.of());
        }

        // 任务实际耗时 单位/s 飞行时长相加
        Float sum = flyRecordService.getTimeSumByJobId(jobId);

        // 识别异常数量
        Long exceptionCount = exceptionApi.getExceptionCountByJobId(jobId).getCheckedData();

        // 飞行素材数量
        Long fileCount = flyRecordFileService.countPicByJobId(jobId);

        JobDetailStatistics jobDetailStatistics = new JobDetailStatistics();
        // 飞行次数
        if (CollUtil.isNotEmpty(jobFlyDOS)) {
            jobDetailStatistics.setFlyCount(jobFlyDOS.size());
        }
        // 任务实际耗时 单位/s
        jobDetailStatistics.setFlyTimeCount(sum.intValue());
        // 识别异常数量
        jobDetailStatistics.setExceptionCount(exceptionCount.intValue());
        // 飞行素材数量
        jobDetailStatistics.setFlyMaterialCount(fileCount == null ? 0 : fileCount.intValue());
        jobDetailRespVO.setJobDetailStatistics(jobDetailStatistics);

        return jobDetailRespVO;
    }

    /**
     * 删除任务 若任务状态为“执行中”/“待审批”则无法删除
     *
     * <AUTHOR>
     * @date 2025/1/14 14:19
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteJob(JobDO jobDO) {
        log.info("[任务管理] 删除任务 jobDO:{}", jobDO);
        if (jobDO == null) {
            throw exception(JOB_NOT_EXISTS);
        }
        Long jobId = jobDO.getId();
        Long taskId = jobDO.getTaskId();
        log.info("[任务管理] 删除任务 jobDO:{}", JSONUtil.toJsonStr(jobDO));
        Integer status = jobDO.getStatus();
        JobStatusTypeEnum statusTypeEnum = JobStatusTypeEnum.find(status);
        if (JobStatusTypeEnum.WAIT_APPROVAL.equals(statusTypeEnum)
                || JobStatusTypeEnum.EXECUTING.equals(statusTypeEnum)) {
            throw exception(JOB_CAN_NOT_DELETE);
        }

        // 查询是否有任务在审批中
        Boolean isOnApproval = dockTaskApplyService.isTaskOnApproval(List.of(jobDO.getTaskId()));
        if (isOnApproval) {
            throw exception(JOB_CAN_NOT_DELETE_BATCH);
        }

        // 删除飞行
        jobFlyService.deleteByJobId(jobId);
        // 删除任务
        jobMapper.deleteById(jobId);
        // 删除没有任务的计划
        dockTaskService.deleteNoJobTask(List.of(taskId));
        // 删除任务管理器中的任务
        deleteJobForSchedulerManager(List.of(jobDO));
        return Boolean.TRUE;
    }

    @Override
    public void deleteJobBatch(List<JobDO> inspectionJobDOList) {
        inspectionJobDOList.forEach(jobDO -> {
            JobStatusTypeEnum statusTypeEnum = JobStatusTypeEnum.find(jobDO.getStatus());
            if (JobStatusTypeEnum.WAIT_APPROVAL.equals(statusTypeEnum) || JobStatusTypeEnum.EXECUTING.equals(statusTypeEnum)) {
                throw exception(JOB_CAN_NOT_DELETE_BATCH);
            }
        });

        // 查询是否有任务在审批中
        Boolean isOnApproval = dockTaskApplyService.isTaskOnApproval(inspectionJobDOList.stream().map(JobDO::getTaskId).toList());
        if (isOnApproval) {
            throw exception(JOB_CAN_NOT_DELETE_BATCH);
        }

        List<Long> ids = inspectionJobDOList.stream().map(JobDO::getId).toList();
        List<Long> taskList = inspectionJobDOList.stream().map(JobDO::getTaskId).toList();
        // 删除飞行
        jobFlyService.deleteBatchByJobIds(ids);
        // 删除任务
        jobMapper.deleteByIds(ids);
        // 删除任务管理器中的任务
        deleteJobForSchedulerManager(inspectionJobDOList);
        // 删除没有任务的计划
        dockTaskService.deleteNoJobTask(taskList);
    }

    /**
     * 取消任务 仅“待执行”的任务有“取消执行”的操作 （待审批的计划在机场管理-申请审批中点击“取消申请”进行取消）
     *
     * <AUTHOR>
     * @date 2025/1/14 16:25
     **/
    @Override
    public void cancelJob(JobDO jobDO) {
        log.info("[任务管理] 取消任务 jobDO：{}", JSONUtil.toJsonStr(jobDO));
        if (jobDO == null) {
            throw exception(JOB_NOT_EXISTS);
        }
        // 校验任务状态
        JobStatusTypeEnum statusTypeEnum = JobStatusTypeEnum.find(jobDO.getStatus());
        if (!statusTypeEnum.equals(JobStatusTypeEnum.WAIT_EXECUTION)) {
            throw exception(JOB_CAN_NOT_CANCEL, statusTypeEnum.getDesc());
        }
        // 修改任务状态为已取消
        updateJobStatus(jobDO.getId(), JobStatusTypeEnum.CANCELED);
        // 禁用任务管理器中的定时任务任务
        try {
            if (jobDO.getSchedulerId() != null) {
                schedulerManager.disableJob(jobDO.getSchedulerId());
            }
        } catch (Exception e) {
            log.info("任务管理器 取消任务失败", e);
        }
    }

    /**
     * 根据id取消任务
     *
     * <AUTHOR>
     * @date 2025/1/16 9:48
     **/
    @Override
    public void cancelJobBatch(List<JobDO> jobDOS) {
        // 筛选出自己权限内的所有任务
        if (CollUtil.isEmpty(jobDOS)) {
            throw exception(JOB_NOT_EXISTS);
        }
        List<Long> ids = jobDOS.stream().map(JobDO::getId).toList();

        for (JobDO jobDO : jobDOS) {
            Long schedulerId = jobDO.getSchedulerId();
            schedulerManager.disableJob(schedulerId);
        }
        // 修改任务状态为已取消
        jobMapper.update(Wrappers.<JobDO>lambdaUpdate().in(JobDO::getId, ids).set(JobDO::getStatus, JobStatusTypeEnum.CANCELED.getCode()));
        // 发送websocket给前端
        jobDOS = jobMapper.selectList(Wrappers.<JobDO>lambdaQuery().in(JobDO::getId, ids));
        sendJobDOToWeb(jobDOS);
    }

    @Override
    public Integer getPageNumById(JobDO jobDO, Integer pageSize) {
        LocalDateTime execTime = jobDO.getExecTime();
        List<Integer> sortList = List.of(2, 0, 1, 3, 5, 6, 4, 7);
        Set<Integer> set = new HashSet<>();
        for (Integer i : sortList) {
            if (!Objects.equals(i, jobDO.getStatus())) {
                set.add(sortList.get(i));
            } else {
                break;
            }
        }

        // 查询排在当前任务前的任务总数
        Integer count = jobMapper.selectAfterCount(execTime, jobDO.getScene(), set, jobDO.getStatus());
        log.info("查询任务页码 id:{},execTime:{},count:{}", jobDO.getId(), execTime, count);
        // 计算当前任务的页码
        return(count + pageSize) / pageSize;
    }

    /**
     * 删除任务管理器中的任务
     *
     * <AUTHOR>
     * @date 2025/1/14 16:16
     **/
    private void deleteJobForSchedulerManager(List<JobDO> jobDOS) {
        for (JobDO jobDO : jobDOS) {
            try {
                if (jobDO.getSchedulerId() != null) {
                    schedulerManager.deleteJob(jobDO.getSchedulerId());
                }
            } catch (Exception e) {
                log.info("任务管理器 删除任务失败", e);
            }
        }
    }

    /**
     * 任务状态修改校验
     *
     * <AUTHOR>
     * @date 2025/1/2 15:47
     **/
    private static void validateUpdateJobStatus(JobDO jobDO) {
        if (jobDO == null) {
            throw exception(JOB_NOT_EXISTS);
        }
        JobStatusTypeEnum oldStatusTypeEnum = JobStatusTypeEnum.find(jobDO.getStatus());
        // 校验当前任务状态是否可更改
        switch (oldStatusTypeEnum) {
            case UNKNOWN -> throw exception(JOB_STATUS_NOT_EXISTS);
            case CANCELED, EXECUTED, FAILED, EXPIRED -> throw exception(JOB_STATUS_CAN_NOT_UPDATE);
        }
    }


    /**
     * 发送给前端
     *
     * <AUTHOR>
     * @date 2025/1/21 20:13
     **/
    private void sendJobDOToWeb(List<JobDO> jobDOS) {
        // 发送websocket给前端
        WebSocketMessageDTO<List<JobDO>> webSocketMessageDTO = WebSocketMessageDTO.<List<JobDO>>builder()
                .tenantId(TenantContextHolder.getTenantId())
                .message(CustomWebSocketMessage.<List<JobDO>>builder()
                        .bizCode(BizCodeEnum.JOB_UPDATE.getCode())
                        .data(jobDOS)
                        .build())
                .build();
        log.info("[任务管理] 修改任务状态 webSocketMessageDTO:{}", JSONUtil.toJsonStr(webSocketMessageDTO));
        webSocketSendApi.sendByTenant(webSocketMessageDTO);
    }


}