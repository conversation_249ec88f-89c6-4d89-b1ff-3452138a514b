package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 定期删除飞行记录设置
 * <AUTHOR>
 * @Date 2025/2/17 11:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "定期删除飞行记录ReqVO")
public class ScheduleDeleteSetReqVO {

    @Schema(description = "周期类型 0定期删除1永不删除")
    @NotNull(message = "周期类型不能为空")
    private Integer cycleType;

    @Schema(description = "间隔天数")
    private Integer days;

    @Schema(description = "删除内容 0飞行记录1图片2视频")
    private List<Integer> deleteTypeArr;
}