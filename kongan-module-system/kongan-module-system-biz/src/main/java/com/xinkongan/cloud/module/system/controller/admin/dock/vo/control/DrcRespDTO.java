package com.xinkongan.cloud.module.system.controller.admin.dock.vo.control;

import com.xinkongan.cloud.sdk.dock.mqtt.model.MqttBrokerDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DrcRespDTO {

    @Schema(description = "授权主题")
    private JwtAclDTO jwtAclDTO;

    @Schema(description = "低电量返航未授权打断")
    private Boolean lowChargeReturnNotAuth = false;

    @Schema(description = "需要申请控制权")
    private Boolean needToRequestControl = false;

    @Schema(description = "当前用户名")
    private String currentNickname;

    @Schema(description = "DRC-WS连接")
    private MqttBrokerDTO drcWsConnect;

}
