package com.xinkongan.cloud.module.system.controller.admin.label.dto;

import com.xinkongan.cloud.module.system.dto.FileSaveInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LabelUpdateDTO extends FileSaveInfoDTO {

    @Schema(description = "标注id")
    private Long id;

    @Schema(description = "标注名称")
    private String name;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度")
    private Double height;

    @Schema(description = "地理位置")
    private String address;

    @Schema(description = "标记数据保存")
    private List<TabSaveDTO> tabSaveInfos;

    @Schema(description = "引用素材的id列表")
    private List<Long> materialIds;

    public List<TabSaveDTO> getTabSaveInfos() {
        if (tabSaveInfos == null) {
            tabSaveInfos = new ArrayList<>();
        }
        return tabSaveInfos;
    }

    public List<Long> getMaterialIds() {
        if (materialIds == null) {
            materialIds = new ArrayList<>();
        }
        return materialIds;
    }
}
