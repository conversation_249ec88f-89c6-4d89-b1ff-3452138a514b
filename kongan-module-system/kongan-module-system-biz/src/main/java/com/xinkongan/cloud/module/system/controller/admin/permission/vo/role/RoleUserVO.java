package com.xinkongan.cloud.module.system.controller.admin.permission.vo.role;

import com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RoleUserVO extends UserRespVO {

    @Schema(description = "任务数量")
    private Long taskCount;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
