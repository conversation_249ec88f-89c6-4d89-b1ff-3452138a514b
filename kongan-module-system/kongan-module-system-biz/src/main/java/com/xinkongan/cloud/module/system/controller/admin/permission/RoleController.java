package com.xinkongan.cloud.module.system.controller.admin.permission;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import com.xinkongan.cloud.framework.common.enums.CommonStatusEnum;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.module.system.controller.admin.permission.dto.UserInfoSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.permission.vo.role.*;
import com.xinkongan.cloud.module.system.controller.admin.user.vo.user.UserRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;
import com.xinkongan.cloud.module.system.service.permission.RoleService;
import com.xinkongan.cloud.module.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;
import static java.util.Collections.singleton;


@Validated
@RestController
@Tag(name = "管理后台 - 角色")
@RequestMapping("/system/role")
public class RoleController {

    @Resource
    private RoleService roleService;

    @Resource
    private AdminUserService adminUserService;


    @PostMapping("/create")
    @Operation(summary = "创建角色")
    @PreAuthorize("@ss.hasPermission('system:role:create')")
    public CommonResult<RoleRespVO> createRole(@Valid @RequestBody RoleSaveReqVO createReqVO) {
        Long id = roleService.createRole(createReqVO, null);
        RoleRespVO roleInfo = roleService.getRoleDetailById(id);
        return success(roleInfo);
    }

    @PutMapping("/base/update")
    @Operation(summary = "修改角色基本信息")
    @PreAuthorize("@ss.hasPermission('system:role:base:update')")
    public CommonResult<Boolean> updateRoleBaseInfo(@Valid @RequestBody RoleBaseSaveReqVO roleBaseSaveReq) {
        roleService.updateRoleBaseInfo(roleBaseSaveReq);
        return success(true);
    }

    @PutMapping("/auth/update")
    @Operation(summary = "修改角色权限信息")
    @PreAuthorize("@ss.hasPermission('system:role:auth:update')")
    public CommonResult<Boolean> updateRole(@Valid @RequestBody RoleAuthSaveReqVO roleAuthSaveReqVO) {
        roleService.updateAuthRole(roleAuthSaveReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除角色")
    @Parameter(name = "id", description = "角色编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:role:delete')")
    public CommonResult<Boolean> deleteRole(@RequestParam("id") Long id) {
        roleService.deleteRole(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得角色信息")
    @PreAuthorize("@ss.hasPermission('system:role:query')")
    public CommonResult<RoleRespVO> getRole(@RequestParam("id") Long id) {
        RoleRespVO roleInfo = roleService.getRoleDetailById(id);
        return success(roleInfo);
    }

    @GetMapping(value = {"/simple-list/{deptId}"})
    @Operation(summary = "查询本组织的角色数据", description = "只查询本组织的角色数据")
    @PreAuthorize("@ss.hasAnyPermissions('system:role:query','system:user:update')")
    public CommonResult<List<RoleRespVO>> getSimpleRoleList(@PathVariable Long deptId) {
        List<RoleDO> list = roleService.getRoleListByStatus(singleton(CommonStatusEnum.ENABLE.getStatus()), deptId);
        list.sort(Comparator.comparing(RoleDO::getSort));
        return success(BeanUtils.toBean(list, RoleRespVO.class));
    }

    @PostMapping("/tree")
    @Operation(summary = "获取角色信息-树结构")
    @PreAuthorize("@ss.hasAnyPermissions('system:role:query')")
    public CommonResult<List<BaseTreeNode>> getDeptUserTree(@RequestBody RoleSearchReqVO roleSearchParams) {
        List<BaseTreeNode> roleTreeInfo = roleService.getDeptRoleTreeInfo(roleSearchParams);
        return success(roleTreeInfo);
    }

    @PostMapping("/move")
    @Operation(summary = "角色位置移动")
    @PreAuthorize("@ss.hasAnyPermissions('system:role:move')")
    public CommonResult<Void> moveRoleTreeNodePosition(@RequestBody RoleMoveReqVO roleMoveReqInfo) {
        roleService.roleMove(roleMoveReqInfo);
        return success();
    }

    @PostMapping("/user/list")
    @Operation(summary = "角色人员列表")
    @PreAuthorize("@ss.hasAnyPermissions('system:role:query')")
    public CommonResult<PageResult<RoleUserVO>> roleUserList(@RequestBody UserInfoSearchDTO userInfoSearchInfo) {
        PageResult<RoleUserVO> userRespPageResult = adminUserService.getUserInfoByRoleId(userInfoSearchInfo);
        return success(userRespPageResult);
    }
}
