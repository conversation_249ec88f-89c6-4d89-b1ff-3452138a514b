package com.xinkongan.cloud.module.system.controller.admin.report.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthDataReportVO {

    @Schema(description = "组织总数", example = "1")
    private Long deptCount;

    @Schema(description = "角色总数", example = "2")
    private Long roleCount;

    @Schema(description = "人员总数", example = "3")
    private Long userCount;

    @Schema(description = "角色统计数据")
    private List<RoleStatisticVO> roleStatistic;

    public List<RoleStatisticVO> getRoleStatistic() {
        if (roleStatistic == null) {
            roleStatistic = new ArrayList<>();
        }
        return roleStatistic;
    }
}
