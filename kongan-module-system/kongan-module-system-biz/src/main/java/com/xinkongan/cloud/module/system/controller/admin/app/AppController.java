package com.xinkongan.cloud.module.system.controller.admin.app;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.service.app.AppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;


/**
 * @Description app信息维护controller
 * <AUTHOR>
 * @Date 2025/6/27 17:35
 */
@Tag(name = "系统首页 - app下载")
@RestController
@RequestMapping("/system/app")
public class AppController {

    @Resource
    private AppService appService;

    /**
     * 指挥端获取最新版本app下载地址
     */
    @Operation(summary = "获取最新版本app下载地址")
    @GetMapping(value = "/getLatestAppUrl")
    public CommonResult<String> getLatestAppUrl() {
        String url = appService.getLatestAppUrl();
        return success(url);
    }

}
