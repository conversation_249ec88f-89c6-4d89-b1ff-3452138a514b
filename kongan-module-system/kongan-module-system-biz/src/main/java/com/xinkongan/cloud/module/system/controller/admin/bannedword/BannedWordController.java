package com.xinkongan.cloud.module.system.controller.admin.bannedword;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.BannedWordPagePageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.BannedWordVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.CreateBannedWordReqVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.UpdateBannedWordReqVO;
import com.xinkongan.cloud.module.system.service.bannedword.IBannedWordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.h2.command.dml.Update;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 违禁词管理
 * <AUTHOR>
 * @Date 2024/11/5 10:23
 */
@Tag(name = "管理后台 - 参数配置")
@RestController
@RequestMapping("/system/bannedWord")
@Validated
public class BannedWordController {

    @Resource
    private IBannedWordService bannedWordService;

    @GetMapping("/page")
    @Operation(summary = "获取违禁词分页")
    @PreAuthorize("@ss.hasPermission('system:bannedword:query')")
    public CommonResult<PageResult<BannedWordVO>> getBannedWordPage(@Valid BannedWordPagePageReqVO pageReqVO) {
        PageResult<BannedWordVO> page = bannedWordService.getBannedWordPage(pageReqVO);
        return success(page);
    }

    @PostMapping("/create")
    @Operation(summary = "创建违禁词")
    @PreAuthorize("@ss.hasPermission('system:bannedword:create')")
    public CommonResult<Long> createBannedWord(@Valid @RequestBody CreateBannedWordReqVO createBannedWordReqVO) {
        return success(bannedWordService.createBannedWord(createBannedWordReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "修改违禁词")
    @PreAuthorize("@ss.hasPermission('system:bannedword:update')")
    public CommonResult<Boolean> update(UpdateBannedWordReqVO updateReqVO) {
        return success(bannedWordService.updateBannedWord(updateReqVO));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除违禁词")
    @PreAuthorize("@ss.hasPermission('system:bannedword:delete')")
    public CommonResult<Boolean> deleteBannedWord(@RequestParam("id") Long id) {
        return success(bannedWordService.deleteBannedWord(id));
    }

}