package com.xinkongan.cloud.module.system.controller.admin.measure;


import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.ratelimiter.core.annotation.RateLimiter;
import com.xinkongan.cloud.framework.ratelimiter.core.keyresolver.impl.UserRateLimiterKeyResolver;
import com.xinkongan.cloud.module.system.controller.admin.measure.vo.CreateMeasureTargetReqVO;
import com.xinkongan.cloud.module.system.controller.admin.measure.vo.ListMeasureTargetReqVO;
import com.xinkongan.cloud.module.system.controller.admin.measure.vo.MeasureTargetBaseVO;
import com.xinkongan.cloud.module.system.service.measure.MeasureTargetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.Collections;
import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.MEASURE_TARGET_QUERY_PARAM_CAN_NOT_NULL;

/**
 * 激光打点
 */
@Tag(name = "飞控系统 - 激光打点")
@Validated
@RestController
@RequestMapping("/system/measure")
public class MeasureTargetController {

    @Resource
    private MeasureTargetService measureTargetService;


    @PostMapping("/create")
    @Operation(summary = "激光打点-创建打点")
    @RateLimiter(keyResolver = UserRateLimiterKeyResolver.class, time = 1, count = 1)
    public CommonResult<MeasureTargetBaseVO> createMeasureTarget(@Valid @RequestBody CreateMeasureTargetReqVO createMeasureTargetReqVO) {
        MeasureTargetBaseVO measureTarget = measureTargetService.createMeasureTarget(createMeasureTargetReqVO);
        return success(measureTarget);
    }


    @GetMapping("/listMeasureTargetBySn")
    @Operation(summary = "激光打点-打点列表(sn)", description = "监控页面查询激光打点列表")
    public CommonResult<List<MeasureTargetBaseVO>> listMeasureTargetBySn(@Valid @NotNull @RequestParam("dockSn") String dockSn) {
        List<MeasureTargetBaseVO> measureTargetList = measureTargetService.listMeasureTargetBySn(Collections.singletonList(dockSn));
        return success(measureTargetList);
    }


    @GetMapping("/listMeasureTargetByDockSn")
    @Operation(summary = "激光打点-打点列表(机场sn列表)", description = "监控页面查询激光打点列表")
    public CommonResult<List<MeasureTargetBaseVO>> listMeasureTargetByDockSns(@RequestParam("dockSns") List<String> dockSns) {
        List<MeasureTargetBaseVO> measureTargetList = measureTargetService.listMeasureTargetBySn(dockSns);
        return success(measureTargetList);
    }


    @GetMapping("/listMeasureTarget")
    @Operation(summary = "激光打点-打点列表(param)", description = "查询激光打点列表")
    public CommonResult<List<MeasureTargetBaseVO>> listMeasureTarget(ListMeasureTargetReqVO reqVO) {
        if (reqVO == null) {
            throw new ServiceException(MEASURE_TARGET_QUERY_PARAM_CAN_NOT_NULL);
        }
        List<MeasureTargetBaseVO> measureTargetList = measureTargetService.listMeasureTarget(reqVO);
        return success(measureTargetList);
    }


    @PostMapping("/delete/{id}")
    @Operation(summary = "激光打点-删除打点", description = "根据id删除激光打点")
    @RateLimiter(keyResolver = UserRateLimiterKeyResolver.class, time = 1, count = 5)
    public CommonResult<Void> deleteMeasureTarget(@Valid @NotNull @PathVariable Long id) {
        measureTargetService.deleteMeasureTarget(id);
        return success();
    }

    @GetMapping("/measureTargetCount")
    @Operation(summary = "激光打点-打点数量", description = "监控页面查询激光打点数量")
    public CommonResult<Long> measureTargetCount(@Valid @NotNull @RequestParam("dockSn") String dockSn) {
        Long count = measureTargetService.measureTargetCount(dockSn);
        return success(count);
    }
}
