package com.xinkongan.cloud.module.system.service.upgrade;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.security.core.util.SecurityFrameworkUtils;
import com.xinkongan.cloud.module.system.api.upgrade.dto.UpgradeNoticeDTO;
import com.xinkongan.cloud.module.system.dal.dataobject.upgrade.SystemUpgradeNoticeDO;
import com.xinkongan.cloud.module.system.dal.mysql.upgrade.SystemUpgradeNoticeMapper;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @Description 系统升级Serice实现
 * <AUTHOR>
 * @Date 2025/5/22 15:51
 */
@Slf4j
@Service
public class UpgradeServiceImpl implements IUpgradeService {

    @Resource
    private SystemUpgradeNoticeMapper systemUpgradeNoticeMapper;
    @Resource
    private WebSocketSendApi webSocketSendApi;
    @Resource
    private IRedisCacheService redisCacheService;

    /**
     * 获取升级通知
     */
    @Override
    public UpgradeNoticeDTO getUpgradeNotice() {
        SystemUpgradeNoticeDO systemUpgradeNoticeDO = systemUpgradeNoticeMapper.selectOne(Wrappers.<SystemUpgradeNoticeDO>lambdaQuery()
                .eq(SystemUpgradeNoticeDO::getStatus, 0)
                .orderByDesc(SystemUpgradeNoticeDO::getCreateTime).last("LIMIT 1"));
        log.info("获取升级通知:{}", systemUpgradeNoticeDO);
        if (systemUpgradeNoticeDO != null && !isNotRemind(systemUpgradeNoticeDO.getId())) {
            return BeanUtil.copyProperties(systemUpgradeNoticeDO, UpgradeNoticeDTO.class);
        }
        return null;
    }

    /**
     * 推送系统升级通知
     */
    @Override
    public void pushUpgradeNotice(UpgradeNoticeDTO upgradeNoticeDTO) {
        // 推送给整个系统的人
        log.info("推送系统升级通知:{}", upgradeNoticeDTO);
        webSocketSendApi.sendAll(WebSocketMessageDTO.builder()
                .message(CustomWebSocketMessage.builder()
                        .bizCode(BizCodeEnum.SYSTEM_UPGRADE.getCode())
                        .data(upgradeNoticeDTO)
                        .build())
                .build());
    }

    @Override
    public void notRemind(Long id) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        log.info("不再提醒设置 userId:{} upgradeId:{}", loginUserId, id);
        // 过期时间5天
        redisCacheService.put(String.format(RedisKeyConstants.SYSTEM_UPGRADE_NOTICE_REMIND, loginUserId, id), 1, 5L, TimeUnit.DAYS);
    }

    @Override
    public boolean isNotRemind(Long id) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        boolean hasKey = redisCacheService.hasKey(String.format(RedisKeyConstants.SYSTEM_UPGRADE_NOTICE_REMIND, loginUserId, id));
        log.info("是否提醒 userId:{} upgradeId:{} hasKey:{}", loginUserId, id, hasKey);
        return hasKey;
    }
}