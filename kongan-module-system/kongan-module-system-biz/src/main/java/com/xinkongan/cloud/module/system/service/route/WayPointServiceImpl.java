package com.xinkongan.cloud.module.system.service.route;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteAlgorithmSaveDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteFolderDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.WayPointDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.*;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.WayPointDO;
import com.xinkongan.cloud.module.system.dal.mysql.route.RouteMapper;
import com.xinkongan.cloud.module.system.dal.mysql.route.WayPointMapper;
import com.xinkongan.cloud.module.system.dto.WayPointActionDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WayPointServiceImpl implements IWayPointService {

    @Resource
    private WayPointMapper wayPointMapper;

    @Resource
    private RouteMapper routeMapper;

    @Resource
    private IWayPointActionService wayPointActionService;

    @Resource
    private IRouteAlgorithmService routeAlgorithmService;

    @Resource
    private IWayPointShoutService wayPointShoutService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRouteWayPointInfo(Long routeId, List<RouteFolderDTO> folders) {
        if (CollectionUtil.isEmpty(folders)) {
            return;
        }
        // 算法个数统计
        Set<Long> algorithmIdSet = new HashSet<>();

        for (int i = 0; i < folders.size(); i++) {
            RouteFolderDTO routeFolderInfo = folders.get(i);
            List<WayPointDTO> points = routeFolderInfo.getPoints();
            if (CollectionUtil.isEmpty(points)) {
                continue;
            }
            List<WayPointActionDTO> wayPointActions = new ArrayList<>();
            List<WayPointDO> wayPoints = new ArrayList<>();
            Stack<WayPointActionDTO> multiTakePhoneStack = new Stack<>();
            Stack<WayPointActionDTO> recordStack = new Stack<>();

            // 保存航点和航点动作数据
            for (WayPointDTO point : points) {
                WayPointDO wayPointDO = BeanUtils.toBean(point, WayPointDO.class);
                wayPointDO.setWaylineId(i);
                wayPointDO.setRouteId(routeId);
                wayPointMapper.insert(wayPointDO);
                wayPoints.add(wayPointDO);

                // 航点动作列表
                List<Long> waypointActionList = wayPointDO.getWaypointActionList();
                // 保存航点动作
                List<Long> pointActionListInfo = wayPointActionService.saveWayPointActionInfo(routeId, wayPointDO.getId(),
                        point.getActions(), wayPointActions, multiTakePhoneStack, recordStack);
                waypointActionList.addAll(pointActionListInfo);

                // 保存航点算法信息
                List<Long> algorithmAction = routeAlgorithmService.saveRouteAlgorithmInfo(routeId, wayPointDO.getId(), point.getRouteAlgorithmInfos(), wayPointActions);
                algorithmIdSet.addAll(point.getRouteAlgorithmInfos().stream().map(RouteAlgorithmSaveDTO::getAlgorithmId).toList());
                waypointActionList.addAll(algorithmAction);

                // 保存航点喊话信息
                List<Long> shoutAction = wayPointShoutService.saveWayPointShoutInfo(routeId, wayPointDO.getId(), point.getWayPointShouts(), wayPointActions);
                waypointActionList.addAll(shoutAction);
            }
            // 处理 multiTakePhoneStack 中的数据
            while (!multiTakePhoneStack.isEmpty()) {
                // 统一设置为最后一个航点
                WayPointActionDTO pop = multiTakePhoneStack.pop();
                pop.setEndIndex(points.size() - 1);
            }

            while (!recordStack.isEmpty()) {
                // 统一设置为最后一个航点
                WayPointActionDTO pop = recordStack.pop();
                pop.setEndIndex(points.size() - 1);
            }
            // 提前过滤掉 startIndex 和 endIndex 相等的无效动作
            List<WayPointActionDTO> validWayPointActions = new ArrayList<>();
            for (WayPointActionDTO action : wayPointActions) {
                if (!Objects.equals(action.getStartIndex(), action.getEndIndex())) {
                    validWayPointActions.add(action);
                }
            }
            // 遍历所有的航点数据
            for (WayPointDO wayPoint : wayPoints) {
                int pointIndex = wayPoint.getPointIndex();
                for (WayPointActionDTO wayPointAction : validWayPointActions) {
                    // 如果当前航点的索引小于动作的起始索引，后续动作肯定也不满足条件，提前终止内层循环
                    if (pointIndex < wayPointAction.getStartIndex()) {
                        break;
                    }
                    if (pointIndex < wayPointAction.getEndIndex()) {
                        List<Long> legActionList = wayPoint.getLegActionList();
                        legActionList.add(wayPointAction.getId());
                    }
                }
            }
            // 批量更新航点动作
            wayPointMapper.updateBatch(wayPoints);
        }
        // 更新航线算法个数
        RouteDO routeInfo = routeMapper.selectById(routeId);
        routeInfo.setId(routeId);
        routeInfo.setAlgorithmCount(algorithmIdSet.size());
        routeMapper.updateById(routeInfo);
    }


    @Override
    public List<RouteFolderRespVO> getWayPointInfoByRouteId(Long routeId) {

        List<WayPointDO> wayPoints = wayPointMapper.selectList(WayPointDO::getRouteId, routeId);
        if (CollectionUtil.isEmpty(wayPoints)) {
            return new ArrayList<>();
        }
        List<WayPointActionRespVO> wayPointActionByRoute = wayPointActionService.getWayPointActionByRouteId(routeId);
        Map<Long, List<WayPointActionRespVO>> pointActionMaps = wayPointActionByRoute.stream().collect(Collectors.groupingBy(WayPointActionRespVO::getPointId));
        Map<Long, WayPointActionRespVO> actionMaps = wayPointActionByRoute.stream().collect(Collectors.toMap(WayPointActionRespVO::getId, v -> v));
        Map<Long, List<RouteAlgorithmVO>> routeAlgorithmMaps = routeAlgorithmService.getRouteAlgorithmInfoByRouteId(routeId);
        Map<Long, List<WayPointShoutVO>> wayPointShoutInfoMaps = wayPointShoutService.getWayPointShoutInfoByRouteId(routeId);

        List<RouteFolderRespVO> routeFolders = new ArrayList<>();
        Map<Integer, List<WayPointRespVO>> wayPointFolderMaps = wayPoints
                .stream()
                .map(p -> this.convert(p, pointActionMaps, actionMaps, routeAlgorithmMaps, wayPointShoutInfoMaps))
                .collect(Collectors.groupingBy(WayPointRespVO::getWaylineId));
        for (Map.Entry<Integer, List<WayPointRespVO>> entry : wayPointFolderMaps.entrySet()) {
            // 对航点进行一个排序
            List<WayPointRespVO> value = entry.getValue();
            value.sort(Comparator.comparing(WayPointRespVO::getPointIndex));

            RouteFolderRespVO routeFolderRespVO = new RouteFolderRespVO(entry.getKey(), value);
            routeFolders.add(routeFolderRespVO);
        }
        routeFolders.sort(Comparator.comparing(RouteFolderRespVO::getWaylineId));
        return routeFolders;
    }

    @Override
    public Long getLastIndexByRouteId(Long routeId) {
        return wayPointMapper.selectCount(Wrappers.<WayPointDO>lambdaQuery().eq(WayPointDO::getRouteId, routeId));
    }

    private WayPointRespVO convert(WayPointDO wayPoint,
                                   Map<Long, List<WayPointActionRespVO>> pointActionMaps,
                                   Map<Long, WayPointActionRespVO> actionMaps,
                                   Map<Long, List<RouteAlgorithmVO>> routeAlgorithmMaps,
                                   Map<Long, List<WayPointShoutVO>> wayPointShoutInfoMaps) {
        WayPointRespVO wayPointRespVO = BeanUtils.toBean(wayPoint, WayPointRespVO.class);

        // 查询航点动作信息
        List<WayPointActionRespVO> wayPointActionResp = pointActionMaps.get(wayPointRespVO.getId());
        wayPointRespVO.setActions(wayPointActionResp);

        // 添加航线算法信息
        List<RouteAlgorithmVO> routeAlgorithms = routeAlgorithmMaps.get(wayPointRespVO.getId());
        wayPointRespVO.setRouteAlgorithms(routeAlgorithms);

        // 添加航点喊话信息
        List<WayPointShoutVO> wayPointShouts = wayPointShoutInfoMaps.get(wayPointRespVO.getId());
        wayPointRespVO.setWayPointShouts(wayPointShouts);

        // 处理航段动作
        List<Long> legActionList = wayPoint.getLegActionList();
        if (CollectionUtil.isNotEmpty(legActionList)) {
            List<WayPointActionRespVO> legActionInfos = wayPointRespVO.getLegActions();
            for (Long id : legActionList) {
                WayPointActionRespVO wayPointActionRespInfo = actionMaps.get(id);
                if (wayPointActionRespInfo != null) {
                    legActionInfos.add(wayPointActionRespInfo);
                }
            }
        }
        return wayPointRespVO;
    }

    @Override
    public WayPointRespVO getWayPointInfoByRouteIdAndIndex(Long routeId, Integer index) {
        List<WayPointDO> wayPointInfos = wayPointMapper.selectList(
                new LambdaQueryWrapperX<WayPointDO>()
                        .eq(WayPointDO::getRouteId, routeId)
                        .eq(WayPointDO::getPointIndex, index)
        );
        if (CollectionUtil.isEmpty(wayPointInfos)) {
            throw new RuntimeException(String.format("航点序号错误，routeId:%s,index:%s", routeId, index));
        }
        WayPointDO wayPoint = wayPointInfos.get(0);
        WayPointRespVO wayPointRespInfo = BeanUtils.toBean(wayPoint, WayPointRespVO.class);

        // 补充算法信息
        List<RouteAlgorithmVO> routeAlgorithmInfos = routeAlgorithmService.getRouteAlgorithmInfoByPointId(wayPoint.getId());
        wayPointRespInfo.setRouteAlgorithms(routeAlgorithmInfos);

        // 补充喊话信息
        List<WayPointShoutVO> wayPointShoutInfos = wayPointShoutService.getWayPointShoutInfoByPointId(wayPoint.getId());
        wayPointRespInfo.setWayPointShouts(wayPointShoutInfos);

        return wayPointRespInfo;
    }

    @Override
    public void deletePointByRouteId(Long routeId) {
        // 删除航点信息
        wayPointMapper.delete(WayPointDO::getRouteId, routeId);

        // 移除航点动作信息
        wayPointActionService.deletePointActionByRouteId(routeId);

        // 移除航线的算法信息
        routeAlgorithmService.deleteRouteAlgorithmInfoByRouteIdAndPointId(routeId, null);

        // 移除航线的喊话信息
        wayPointShoutService.deleteWayPointShoutInfoByRouteIdAndPointId(routeId, null);
    }
}
