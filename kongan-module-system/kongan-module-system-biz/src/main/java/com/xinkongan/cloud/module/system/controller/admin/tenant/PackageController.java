package com.xinkongan.cloud.module.system.controller.admin.tenant;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.datapermission.core.annotation.DataPermission;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant.TenantPackageVO;
import com.xinkongan.cloud.module.system.service.tenant.FileStorageVO;
import com.xinkongan.cloud.module.system.service.tenant.TenantPackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.TENANT_STORAGE_FULL;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Tag(name = "管理后台 - 租户套餐")
@RestController
@RequestMapping("/system/tenantPackage")
public class PackageController {

    @Resource
    private TenantPackageService tenantPackageService;

    @DataPermission(enable = false)
    @Operation(summary = "获得租户套餐信息")
    @GetMapping("/get")
    public CommonResult<TenantPackageVO> getPackageInfo() {
        TenantPackageVO packageInfo = tenantPackageService.getPackageInfo();
        return CommonResult.success(packageInfo);
    }

    @DataPermission(enable = false)
    @Operation(summary = "检查存储是否占满")
    @GetMapping("/isStorageFull")
    public CommonResult<Boolean> isStorageFull() {
        Boolean storageFull = tenantPackageService.isStorageFull(TenantContextHolder.getTenantId());
        CommonResult<Boolean> success = CommonResult.success(storageFull);
        if (storageFull) {
            success.setMsg(TENANT_STORAGE_FULL.getMsg());
        }
        return success;
    }

    @DataPermission(enable = false)
    @Operation(summary = "检查剩余存储空间是否能够支持指定文件大小的文件上传 单位:MB")
    @GetMapping("/checkStorageRemainder/{size}")
    public CommonResult<Boolean> checkStorageRemainder(@PathVariable Double size) {
        FileStorageVO fileStorageVO = tenantPackageService.getUseStorageCount(TenantContextHolder.getTenantId());
        return CommonResult.success(fileStorageVO.getRemainderStorageMB() > size);
    }

}
