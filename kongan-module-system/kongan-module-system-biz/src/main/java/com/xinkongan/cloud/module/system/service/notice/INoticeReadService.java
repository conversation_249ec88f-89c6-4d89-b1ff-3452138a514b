package com.xinkongan.cloud.module.system.service.notice;

import com.xinkongan.cloud.module.system.controller.admin.notice.vo.NoticeStatisticsVO;
import com.xinkongan.cloud.module.system.dal.dataobject.notice.NoticeReadDO;

import java.util.List;

/**
 * @Description 已读消息Service
 * <AUTHOR>
 * @Date 2025/3/6 13:51
 */
public interface INoticeReadService {

    /**
     * 保存消息的已读状态
     **/
    Boolean saveNoticeRead(List<NoticeReadDO> noticeReadDOList);


    Boolean markAsRead(Long userId, List<Long> noticeIds);

    void deleteBatchByNoticeIds(List<Long> noticeIds);

    /**
     * 未读消息数量
     **/
    Long unreadCount(Long userId);

    /**
     * 消息统计
     **/
    NoticeStatisticsVO noticeStatistics(Long userId);
}
