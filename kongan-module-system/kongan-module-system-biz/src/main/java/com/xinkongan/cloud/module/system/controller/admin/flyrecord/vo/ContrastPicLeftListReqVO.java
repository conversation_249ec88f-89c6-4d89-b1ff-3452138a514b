package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 对比记录左侧飞行记录图片ReqVO
 * <AUTHOR>
 * @Date 2025/2/20 11:18
 */
@Schema(description = "对比记录左侧飞行记录图片ReqVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContrastPicLeftListReqVO {

    @Schema(description = "飞行记录id")
    @NotNull(message = "飞行记录id不能为空")
    private Long flyRecordId;
}