package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage.DockCoverageVO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage.UpdateDockCoverageReqVO;
import com.xinkongan.cloud.module.system.service.dock.coverage.IDockCoverageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 机场可飞范围Controller
 * <AUTHOR>
 * @Date 2024/12/30 10:10
 */
@Validated
@RestController
@Tag(name = "管理后台 - 机场可飞范围")
@RequestMapping("/system/dockCoverage")
public class DockCoverageController {

    @Resource
    private IDockCoverageService dockCoverageService;

    /**
     * 查询机场可飞范围
     *
     * <AUTHOR>
     * @date 2024/12/30 10:28
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasAnyPermissions('system:dock:coverage:query','system:route:query','system:route:page')")
    @Operation(summary = "查询机场可飞范围", description = "根据机场sn查询机场可飞范围")
    public CommonResult<DockCoverageVO> getDockCoverage(@RequestParam("dockSn") String dockSn) {
        return success(dockCoverageService.getByDockSn(dockSn));
    }

    /**
     * 修改
     *
     * <AUTHOR>
     * @date 2024/12/30 10:27
     **/
    @PostMapping("/updateById")
    @PreAuthorize("@ss.hasPermission('system:dock:coverage:edit')")
    @Operation(summary = "修改机场可飞范围", description = "根据id修改机场可飞范围")
    public CommonResult<Boolean> createDockCoverage(@Validated @RequestBody UpdateDockCoverageReqVO updateDockCoverageReqVO) {
        return success(dockCoverageService.updateById(updateDockCoverageReqVO));
    }

}