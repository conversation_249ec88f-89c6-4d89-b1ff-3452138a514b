package com.xinkongan.cloud.module.system.controller.admin.psdk.vo;

import com.xinkongan.cloud.module.system.dal.dataobject.psdk.VoiceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2023/6/7 16:08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VoiceSendToDockVO extends VoiceDO {

    @Schema(description = "声音大小")
    @NotNull(message = "播放音量不能为空")
    private Integer voice;

    @Schema(description = "语速")
    private String speed;

    @Schema(description = "播放模式（0：单次  1：循环）")
    @NotNull(message = "播放模式不能为空")
    private Integer mode;

    @Schema(description = "机场sn")
    @NotNull(message = "机场sn不能为空")
    private String dockSn;
}
