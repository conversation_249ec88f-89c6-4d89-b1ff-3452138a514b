package com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant;

import com.xinkongan.cloud.module.system.service.tenant.FileStorageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenantPackageVO {

    @Schema(description = "存储空间")
    private FileStorageVO fileStorageVO;

    @Schema(description = "设备数量")
    private DeviceNumberVO deviceNumberVO;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "是否即将到期")
    private Boolean expireStatus;

}
