package com.xinkongan.cloud.module.system.service.bannedword.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinkongan.cloud.framework.bannedword.core.WordTrie;
import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.framework.mybatis.core.util.MyBatisUtils;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.BannedWordPagePageReqVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.BannedWordVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.CreateBannedWordReqVO;
import com.xinkongan.cloud.module.system.controller.admin.bannedword.vo.UpdateBannedWordReqVO;
import com.xinkongan.cloud.module.system.dal.dataobject.bannedword.BannedWordDO;
import com.xinkongan.cloud.module.system.dal.mysql.bannedword.BannedWordMapper;
import com.xinkongan.cloud.module.system.service.bannedword.IBannedWordService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xinkongan.cloud.module.system.enums.ErrorCodeConstants.BANNED_WORD_SAVE_ERROR;

/**
 * @Description 违禁词校验ServiceImpl
 * <AUTHOR>
 * @Date 2024/11/5 9:23
 */
@Service
@Slf4j
public class IBannedWordServiceImpl implements IBannedWordService {

    @Resource
    private BannedWordMapper bannedWordMapper;
    /**
     * 当前系统的违禁词Trie树
     * <AUTHOR>
     * @date 2024/11/5 9:56
     **/
    private static WordTrie wordTrie = new WordTrie();


    @PostConstruct
    @Override
    public synchronized void loadBannedWords() {
        WordTrie tempTire = new WordTrie();
        log.info("【违禁词】开始初始化违禁词Tire树...");
        // 查询所有违禁词
        List<BannedWordDO> bannedWordDOS = bannedWordMapper.selectList();
        if(CollUtil.isNotEmpty(bannedWordDOS)) {
            // 构建违禁词树
            bannedWordDOS.stream().map(BannedWordDO::getWord).distinct().forEach(tempTire::insert);
            // 替换缓存
            wordTrie = tempTire;
            log.info("【违禁词】初始化违禁词Tire树完成,违禁词数量:{}", bannedWordDOS.size());
        }
    }

    /**
     * 校验文本中是否包含违禁词，并返回违禁词
     * <AUTHOR>
     * @date 2024/11/5 10:11
     **/
    @Override
    public String getBannedWord(String text) {
        log.info("【违禁词】文本是否包含违禁词text:{}", text);
        return wordTrie.containsAndReturnBannedWord(text);
    }

    /**
     * 创建违禁词
     * <AUTHOR>
     * @date 2024/11/5 10:26
     **/
    @Override
    public Long createBannedWord(CreateBannedWordReqVO createBannedWordReqVO) {
        BannedWordDO bannedWordDO = BeanUtil.toBean(createBannedWordReqVO, BannedWordDO.class);
        int insert = bannedWordMapper.insert(bannedWordDO);
        if(insert <= 0 ) throw exception(BANNED_WORD_SAVE_ERROR);
        return bannedWordDO.getId();
    }

    /**
     * 违禁词分页
     * <AUTHOR>
     * @date 2024/11/5 10:35
     **/
    @Override
    public PageResult<BannedWordVO> getBannedWordPage(BannedWordPagePageReqVO pageReqVO) {
        IPage<BannedWordVO> mpPage = MyBatisUtils.buildPage(pageReqVO);
        IPage<BannedWordVO> page = bannedWordMapper.selectPage(mpPage,pageReqVO);
        if(CollUtil.isNotEmpty(page.getRecords())) {
            return PageResult.empty();
        }
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 修改违禁词
     * <AUTHOR>
     * @date 2024/11/5 10:53
     **/
    @Override
    public Boolean updateBannedWord(UpdateBannedWordReqVO updateReqVO) {
        log.info("【违禁词】修改违禁词:{}", updateReqVO);
        // 修改违禁词
        BannedWordDO bannedWordDO = BeanUtil.toBean(updateReqVO, BannedWordDO.class);
        bannedWordMapper.updateById(bannedWordDO);
        // 重新构建违禁词Tire树
        this.loadBannedWords();
        return Boolean.TRUE;
    }

    /**
     * 删除违禁词
     * <AUTHOR>
     * @date 2024/11/5 10:59
     **/
    @Override
    public Boolean deleteBannedWord(Long id) {
        log.info("【违禁词】删除违禁词：id:{}", id);
        // 删除违禁词
        bannedWordMapper.deleteById(id);
        // 重新构建违禁词Tire树
        this.loadBannedWords();
        return Boolean.TRUE;
    }


}