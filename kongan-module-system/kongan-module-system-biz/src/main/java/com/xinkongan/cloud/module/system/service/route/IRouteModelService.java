package com.xinkongan.cloud.module.system.service.route;

import com.xinkongan.cloud.module.system.controller.admin.route.dto.*;


public interface IRouteModelService {


    /**
     * 新增航线建模参数信息
     *
     * @param modelRouteDTO 航线建模参数
     */
    void saveRouteModelInfo(ModelRouteDTO modelRouteDTO);

    /**
     * 根据航线id查询建模参数详情
     *
     * @param routeId 航线id
     * @return 航线详情
     */
    ModelRouteDTO getRouteModelInfoByRouteId(Long routeId);


    /**
     * 根据航线id修改航线建模参数信息
     *
     * @param modelRouteDTO 航线建模参数
     */
    void updateRouteModelInfo(ModelRouteDTO modelRouteDTO);


    /**
     * 删除航线建模参数信息
     *
     * @param routeId 航线id
     */
    void delRouteModelInfoByRouteId(Long routeId);

}
