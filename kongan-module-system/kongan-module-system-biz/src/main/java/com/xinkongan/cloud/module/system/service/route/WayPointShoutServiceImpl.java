package com.xinkongan.cloud.module.system.service.route;

import cn.hutool.core.collection.CollectionUtil;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteAlgorithmVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointShoutVO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteAlgorithmDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.WayPointShoutDO;
import com.xinkongan.cloud.module.system.dal.mysql.route.WayPointShoutMapper;
import com.xinkongan.cloud.module.system.dto.WayPointActionDTO;
import com.xinkongan.cloud.module.system.dto.WayPointShoutDTO;
import com.xinkongan.cloud.module.system.enums.route.WayPointActionTransTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WayPointShoutServiceImpl implements IWayPointShoutService {

    @Resource
    private WayPointShoutMapper wayPointShoutMapper;


    @Override
    public List<Long> saveWayPointShoutInfo(Long routeId, Long pointId, List<WayPointShoutDTO> wayPointShouts, List<WayPointActionDTO> wayPointActions) {
        if (CollectionUtil.isEmpty(wayPointShouts)) {
            return new ArrayList<>();
        }
        List<WayPointShoutDO> wayPointShoutInfos = wayPointShouts.stream().map(t -> this.convert(routeId, pointId, t)).toList();
        wayPointShoutMapper.insertBatch(wayPointShoutInfos);

        List<Long> wayPointActionIds = new ArrayList<>();
        for (WayPointShoutDO wayPointShoutInfo : wayPointShoutInfos) {
            wayPointActionIds.add(wayPointShoutInfo.getId());
            if (Objects.equals(wayPointShoutInfo.getType(), 1)) {
                WayPointActionDTO wayPointActionInfo = new WayPointActionDTO();
                wayPointActionInfo.setId(wayPointShoutInfo.getId());
                wayPointActionInfo.setPointId(pointId);
                wayPointActionInfo.setStartIndex(wayPointShoutInfo.getStartIndex());
                wayPointActionInfo.setEndIndex(wayPointShoutInfo.getEndIndex());
                wayPointActionInfo.setType(WayPointActionTransTypeEnum.legShoutActionType.getCode());
                wayPointActions.add(wayPointActionInfo);
            }
        }
        return wayPointActionIds;
    }

    @Override
    public void deleteWayPointShoutInfoByRouteIdAndPointId(Long routeId, Long pointId) {
        wayPointShoutMapper.delete(
                new LambdaQueryWrapperX<WayPointShoutDO>()
                        .eq(WayPointShoutDO::getRouteId, routeId)
                        .eq(pointId != null, WayPointShoutDO::getPointId, pointId)
        );
    }

    @Override
    public List<WayPointShoutVO> getWayPointShoutInfoByPointId(Long id) {
        List<WayPointShoutDO> wayPointShoutInfos = wayPointShoutMapper.selectList(
                new LambdaQueryWrapperX<WayPointShoutDO>()
                        .eq(WayPointShoutDO::getPointId, id)
        );
        return wayPointShoutInfos.stream().map(this::convert).toList();
    }

    @Override
    public Map<Long, List<WayPointShoutVO>> getWayPointShoutInfoByRouteId(Long routeId) {
        List<WayPointShoutDO> wayPointShoutInfos = wayPointShoutMapper.selectList(
                new LambdaQueryWrapperX<WayPointShoutDO>()
                        .eq(WayPointShoutDO::getRouteId, routeId)
        );

        if (CollectionUtil.isEmpty(wayPointShoutInfos)) {
            return Map.of();
        }
        return wayPointShoutInfos.stream()
                .map(this::convert)
                .collect(
                        Collectors.groupingBy(WayPointShoutVO::getPointId)
                );
    }

    private WayPointShoutVO convert(WayPointShoutDO wayPointShoutDO) {
        WayPointShoutVO wayPointShoutVO = BeanUtils.toBean(wayPointShoutDO, WayPointShoutVO.class);
        return wayPointShoutVO;
    }

    private WayPointShoutDO convert(Long routeId, Long pointId, WayPointShoutDTO wayPointShout) {
        WayPointShoutDO wayPointShoutDO = BeanUtils.toBean(wayPointShout, WayPointShoutDO.class);
        wayPointShoutDO.setRouteId(routeId);
        wayPointShoutDO.setPointId(pointId);

        return wayPointShoutDO;
    }
}
