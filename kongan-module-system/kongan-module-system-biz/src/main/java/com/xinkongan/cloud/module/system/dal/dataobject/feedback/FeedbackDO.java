package com.xinkongan.cloud.module.system.dal.dataobject.feedback;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xinkongan.cloud.framework.mybatis.core.dataobject.DeptBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

/**
 * @Description 问题反馈表
 * <AUTHOR>
 * @Date 2025/05/26 10:08
 */
@TableName("system_question_feedback")
@KeySequence("system_question_feedback_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class FeedbackDO extends DeptBaseDO {

    /**
     * 主键id,采用雪花算法生成id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 反馈类型
     */
    private Integer type;

    /**
     * 反馈建议
     */
    private String feedbackAdvice;

    /**
     * 文件id
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> fileIds;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 处理意见
     */
    private String handleOpinion;

    /**
     * 处理状态
     */
    private Integer status;

}
