package com.xinkongan.cloud.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "管理后台 - 用户移动参数")
public class UserMoveReqVO {

    @Schema(description = "移动标志：up（上移），down（下移），top（置顶），bottom（置底）", example = "up")
    private String move;

    @Schema(description = "用户id", example = "103")
    private Long userId;

}
