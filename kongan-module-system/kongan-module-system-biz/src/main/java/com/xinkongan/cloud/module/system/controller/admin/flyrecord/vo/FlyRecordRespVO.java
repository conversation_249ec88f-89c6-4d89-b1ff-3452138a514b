package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class FlyRecordRespVO {

    @Schema(description = "飞行记录id")
    private Long id;

    @Schema(description = "记录名称")
    private String name;

    @Schema(description = "起飞时间")
    private LocalDateTime takeOffTime;

    @Schema(description = "降落时间")
    private LocalDateTime landTime;

    @Schema(description = "计划id")
    private Long taskId;

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "飞行id")
    private Long flyId;

    @Schema(description = "执行机场sn")
    private String dockSn;

    @Schema(description = "无人机sn")
    private String droneSn;

    @Schema(description = "航线id")
    private Long routeId;

    @Schema(description = "该飞行架次当前已上传媒体数量")
    private Integer uploadedFileCount;

    @Schema(description = "该飞行架次拍摄媒体总数量")
    private Integer expectedFileCount;

    @Schema(description = "重要程度0普通1重要")
    private Integer importantLevel;

    @Schema(description = "飞行里程/m")
    private Float flightMileage;

    @Schema(description = "飞行时长/s")
    private Float flightDuration;

    @Schema(description = "起飞点纬度")
    private Double takeOffLat;

    @Schema(description = "起飞点经度")
    private Double takeOffLon;

    @Schema(description = "最终坐标点纬度")
    private Double lastLat;

    @Schema(description = "最终坐标点经度")
    private Double lastLon;

    @Schema(description = "场景0巡检任务1警情任务2联合行动3建模任务4临时任务")
    private Integer sceneType;

    @Schema(description = "飞行类型0机场1无人机")
    private Integer flightType;

    @Schema(description = "飞手id")
    private Long userId;

    @Schema(description = "飞行记录是否已结束 1是0否")
    private Integer endFlag;

    @Schema(description = "关联文件表id")
    private Long fileId;

    @Schema(description = "关联osd文件的url")
    private String osdUrl;

    @Schema(description = "scene")
    private Integer scene;

    @Schema(description = "媒体数目总计")
    private Long mediaCount;

}
