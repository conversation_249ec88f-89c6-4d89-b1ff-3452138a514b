package com.xinkongan.cloud.module.system.controller.admin.permission.vo.role;

import com.xinkongan.cloud.framework.bannedword.core.annotation.BannedWords;
import com.mzt.logapi.starter.annotation.DiffLogField;
import com.xinkongan.cloud.framework.common.validation.InEnum;
import com.xinkongan.cloud.module.system.enums.permission.DataScopeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Collections;
import java.util.Set;

@Schema(description = "管理后台 - 角色创建/更新 Request VO")
@Data
public class RoleSaveReqVO {

    @Schema(description = "角色编号", example = "1")
    private Long id;

    @Schema(description = "角色名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "管理员")
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过 30 个字符")
    @DiffLogField(name = "角色名称")
    @BannedWords(message = "角色名称不能包含违禁词：{value}")
    private String name;

    @DiffLogField(name = "角色标志")
    @Size(max = 100, message = "角色标志长度不能超过 100 个字符")
    @Schema(description = "角色标志", requiredMode = Schema.RequiredMode.REQUIRED, example = "ADMIN")
    private String code;

    @DiffLogField(name = "显示顺序")
    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Integer sort;

    @DiffLogField(name = "状态")
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    @DiffLogField(name = "备注")
    @Schema(description = "备注", example = "我是一个角色")
    @Size(max = 500, message = "备注长度不能超过 500 个字符")
    private String remark;

    @NotNull(message = "数据范围不能为空")
    @InEnum(value = DataScopeEnum.class, message = "数据范围必须是 {value}")
    @Schema(description = "数据范围，参见 DataScopeEnum 枚举类", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer dataScope;

    @Schema(description = "菜单编号列表", example = "1,3,5")
    private Set<Long> menuIds = Collections.emptySet(); // 兜底

    @Schema(description = "租户id", example = "1")
    private Long tenantId;

    @Schema(description = "组织id", example = "2")
    private Long deptId;

    @Schema(description = "用户id", example = "2")
    private Long userId;
}
