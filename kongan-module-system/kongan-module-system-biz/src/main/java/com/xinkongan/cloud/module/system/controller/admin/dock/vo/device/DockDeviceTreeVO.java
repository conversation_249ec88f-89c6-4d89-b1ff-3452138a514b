package com.xinkongan.cloud.module.system.controller.admin.dock.vo.device;

import com.xinkongan.cloud.framework.common.core.BaseTreeNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DockDeviceTreeVO {

    private List<BaseTreeNode> interior;
    private List<BaseTreeNode> without;

}
