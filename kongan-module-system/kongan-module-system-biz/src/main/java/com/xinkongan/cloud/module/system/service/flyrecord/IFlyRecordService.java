package com.xinkongan.cloud.module.system.service.flyrecord;

import com.xinkongan.cloud.framework.common.pojo.PageResult;
import com.xinkongan.cloud.module.system.api.flyrecord.dto.DroneFlyPoint;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.dto.FlyRecordSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.dto.HistogramSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo.*;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordDO;
import tech.powerjob.worker.log.OmsLogger;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @Description 飞行记录Service
 * <AUTHOR>
 * @Date 2025/2/10 14:17
 */
public interface IFlyRecordService {

    /**
     * 保存飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/10 14:18
     **/
    Long save(FlyRecordDO flyRecordDO);

    /**
     * 修改飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/10 17:24
     **/
    Boolean updateById(FlyRecordDO flyRecordDO);

    /**
     * 结束机场飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/11 10:17
     **/
    void endDockFlyRecord(String dockSn, Long flyRecordId);

    /**
     * 根据飞行id获取飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/12 15:26
     **/
    FlyRecordDO getByFlyId(Long flyId);

    /**
     * 飞行记录分页
     *
     * <AUTHOR>
     * @date 2025/2/13 11:20
     **/
    PageResult<FlyRecordPageRespVO> page(FlyRecordPageReqVO reqVO);

    /**
     * 飞行记录详情
     *
     * <AUTHOR>
     * @date 2025/2/13 14:52
     **/
    FlyRecordDetailRespVO detailById(Long id);

    /**
     * 飞行记录分享
     *
     * <AUTHOR>
     * @date 2025/2/13 16:00
     **/
    void share(FlyRecordShareVO flyRecordShareVO);

    /**
     * 查询飞行记录分享的组织
     *
     * <AUTHOR>
     * @date 2025/2/19 9:30
     **/
    FlyRecordShareVO getFlyRecordShareDept(Long flyRecordId);

    /**
     * 飞行记录实时轨迹
     *
     * <AUTHOR>
     * @date 2025/2/13 18:52
     **/
    List<DroneFlyPoint> getDroneFlyPoints(Long flyRecordId);

    /**
     * 根据id获取飞行记录
     * 注意: 只有有登录信息才能调用此接口
     *
     * <AUTHOR>
     * @date 2025/2/14 10:29
     **/
    FlyRecordDO getById(Long flyRecordId);

    /**
     * 更新飞行记录重要性
     *
     * <AUTHOR>
     * @date 2025/2/14 11:04
     **/
    Boolean updateImportant(UpdateImportantReqVO reqVO);

    /**
     * 删除飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/17 11:29
     **/
    Boolean deleteById(Long id);

    /**
     * 删除不重要的飞行记录
     *
     * <AUTHOR>
     * @date 2025/2/17 10:40
     **/
    void deleteUnImportantFlyRecord(Long deptId, LocalDateTime endTime, OmsLogger omsLogger);

    /**
     * 无需数据权限的查询
     **/
    FlyRecordDO getByIdNoPermission(Long id);

    /**
     * 飞行记录统计
     **/
    FlyRecordCountRespVO getFlyRecordCount();

    /**
     * 根据时间统计飞行记录
     **/
    List<FlyRecordCountByTimeRespVO> getFlyRecordCountByTime(FlyRecordCountByTimeReqVO reqVO);

    /**
     * 检查当前用户是否有该飞行记录的权限
     **/
    Boolean hasFlyRecordPermission(Long flyRecordId, Long tenantId, Set<Long> dataPermissionIds);

    /**
     * 根据id获取页码
     **/
    Integer getPageNumById(Long id, Integer pageSize);

    PageResult<RelateFlyRecordPageRespVO> relateFlyRecordPage(RelateFlyRecordPageReqVO reqVO);

    /**
     * 忽略数据权限查询飞行记录
     **/
    FlyRecordDO getByIdIgnorePermission(Long id);

    /**
     * 根据任务id获取飞行记录飞行时长总和
     **/
    Float getTimeSumByJobId(Long jobId);


    /**
     * 根据部门id获取统计数据
     * @param deptId 部门id
     * @return 统计数据
     */
    FlyRecordStatisticDataVO getStatisticDataByDeptId(Long deptId);

    /**
     * 分页查询飞行记录
     * @param searchParams 查询参数
     * @return 分页结果
     */
    PageResult<FlyRecordRespVO> getFlyRecordPage(FlyRecordSearchDTO searchParams);

    List<HistogramRespVO> getHistogramData(HistogramSearchDTO searchParams);

    /**
     * 根据组织ID删除飞行记录
     *
     * @param deptId 组织ID
     */
    void deleteFlyRecordByDeptId(Long deptId);
}