package com.xinkongan.cloud.module.system.controller.admin.liveshare;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.LiveShareReqVO;
import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.ShareBaseVO;
import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.WatchLiveVO;
import com.xinkongan.cloud.module.system.service.liveshare.ILiveShareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Tag(name = "直播分享")
@Slf4j
@RestController
@RequestMapping(value = "/system/liveShare")
public class LiveShareController {

    @Resource
    private ILiveShareService liveShareService;

    @Operation(summary = "直播分享")
    @PostMapping(value = "/share")
    public CommonResult<String> liveShare(@RequestBody LiveShareReqVO reqVO) {
        String key = liveShareService.liveShare(reqVO);
        return CommonResult.success(key);
    }

    @Operation(summary = "获取直播分享信息")
    @PermitAll
    @TenantIgnore
    @GetMapping("/get")
    public CommonResult<ShareBaseVO> get(@RequestParam String shareKey) {
        ShareBaseVO shareInfo = liveShareService.getShareInfo(shareKey);
        return CommonResult.success(shareInfo);
    }

    @Operation(summary = "通过密码获取观看直播信息")
    @PermitAll
    @TenantIgnore
    @GetMapping("/getWatchLiveByPassword")
    public CommonResult<WatchLiveVO> getWatchLiveByPassword(@RequestParam String shareKey, @RequestParam String password) {
        WatchLiveVO watchLiveVO = liveShareService.getShareInfoByPassword(shareKey, password);
        return CommonResult.success(watchLiveVO);
    }

}
