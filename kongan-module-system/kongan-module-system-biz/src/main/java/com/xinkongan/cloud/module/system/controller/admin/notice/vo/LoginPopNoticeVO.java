package com.xinkongan.cloud.module.system.controller.admin.notice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 登录后弹窗消息
 * <AUTHOR>
 * @Date 2025/3/10 16:10
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "登录后弹窗消息")
@Builder
public class LoginPopNoticeVO {

    @Schema(description = "最新未读消息")
    private NoticeVO newestNotice;

    @Schema(description = "弹窗消息列表")
    private List<NoticeVO> popNoticeList;
}