package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 飞行记录分享
 * <AUTHOR>
 * @Date 2025/2/13 15:59
 */
@Schema(description = "飞行记录分享VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlyRecordShareVO {

    @Schema(description = "飞行记录id")
    @NotNull
    private Long flyRecordId;

    @Schema(description = "分享的组织列表")
    private List<Long> deptIds;


    public List<Long> getDeptIds() {
        if (deptIds == null) {
            return new ArrayList<>();
        }
        return deptIds;
    }
}