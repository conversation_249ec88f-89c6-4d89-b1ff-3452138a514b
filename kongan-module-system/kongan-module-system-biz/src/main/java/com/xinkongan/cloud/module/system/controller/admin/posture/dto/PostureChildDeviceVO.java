package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DeviceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PostureChildDeviceVO {

    @Schema(description = "设备SN")
    private String deviceSn;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备类型")
    private Integer deviceType;

}
