package com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz;

import com.xinkongan.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xinkongan.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 禁飞区分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "管理后台 - 禁飞区分页查询请求VO")
public class NfzPageReqVO extends PageParam {

    @Schema(description = "禁飞区名称", example = "测试禁飞区")
    private String name;

    @Schema(description = "状态：true表示启用，false表示禁用", example = "true")
    private Boolean status;

    @Schema(description = "创建时间开始")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime beginCreateTime;

    @Schema(description = "创建时间结束")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreateTime;

    @Schema(description = "搜索关键字（名称模糊查询）", example = "禁飞区")
    private String searchKey;
}
