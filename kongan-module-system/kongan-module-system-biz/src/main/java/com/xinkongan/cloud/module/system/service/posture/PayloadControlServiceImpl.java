package com.xinkongan.cloud.module.system.service.posture;

import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.common.exception.util.ServiceExceptionUtil;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.framework.tenant.core.util.TenantUtils;
import com.xinkongan.cloud.module.system.api.dock.dto.payload.CameraScreenDragDTO;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.device.DockDeviceCameraVO;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.control.DronePayloadParam;
import com.xinkongan.cloud.module.system.controller.admin.posture.dto.control.PayloadCommandsParam;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.DockDeviceDO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.FlyIncidentTypeEnum;
import com.xinkongan.cloud.module.system.enums.PayloadCommandsEnum;
import com.xinkongan.cloud.module.system.service.dock.dockdevice.DockDeviceService;
import com.xinkongan.cloud.module.system.service.task.IJobFlyProgressService;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.OsdCamera;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.OsdDockDrone;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.CameraModeEnum;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesPublish;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicReference;

import static com.xinkongan.cloud.framework.redis.enums.RedisKeyExpireEnums.OSD_DOCK_DRONE;

/**
 * <AUTHOR>
 * @date 2025/2/19
 */
@Slf4j
@Service
public class PayloadControlServiceImpl implements IPayloadControlService {

    @Resource
    private ServicesPublish servicesPublish;

    @Resource
    private IJobFlyProgressService progressService;

    @Resource
    private IRedisCacheService redisCacheService;// 缓存Service
    @Resource
    private DockDeviceService dockDeviceService;

    @Override
    public void cameraScreenDrag(CameraScreenDragDTO dto) {
        servicesPublish.publishNoReply(dto.getDockSn(), PayloadCommandsEnum.CAMERA_SCREEN_DRAG.getCmd(), dto);
    }

    @Override
    public Boolean simpleControlByDroneSn(String droneSn, PayloadCommandsEnum commandsEnum) {
        AtomicReference<DockDeviceDO> atomicReference = new AtomicReference<>();
        TenantUtils.executeIgnore(() -> atomicReference.set(dockDeviceService.getByDBDeviceSn(droneSn)));
        TenantContextHolder.setTenantId(atomicReference.get().getTenantId());
        DockDeviceDO dockDeviceDO = dockDeviceService.getByChildSn(droneSn);
        if (dockDeviceDO == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.DEVICE_DOCK_NOT_EXISTS);
        }
        // 查询设备相机信息
        DockDeviceCameraVO cameraPayloadInfo = dockDeviceService.getDeviceCamera(droneSn);
        PayloadCommandsParam param = new PayloadCommandsParam();
        DronePayloadParam dronePayloadParam = DronePayloadParam.builder()
                .payloadIndex(cameraPayloadInfo.getLivePayloadIndex())
                .build();
        param.setDockSn(dockDeviceDO.getDeviceSn());
        param.setCmd(commandsEnum);
        param.setData(dronePayloadParam);
        if (commandsEnum.equals(PayloadCommandsEnum.CAMERA_RECORDING_START)) {
            String droneKey = OSD_DOCK_DRONE.getKey(droneSn);
            OsdDockDrone droneOsd = redisCacheService.get(droneKey, OsdDockDrone.class);
            if (!droneOsd.getCameras().get(0).getCameraMode().equals(CameraModeEnum.VIDEO)) {
                // 设置拍照模式为录像模式
                PayloadCommandsParam payloadCommandsParam = new PayloadCommandsParam();
                payloadCommandsParam.setDockSn(dockDeviceDO.getDeviceSn());
                payloadCommandsParam.setCmd(PayloadCommandsEnum.CAMERA_MODE_SWitCH);
                payloadCommandsParam.setData(DronePayloadParam.builder()
                        .payloadIndex(cameraPayloadInfo.getLivePayloadIndex())
                        .cameraMode(CameraModeEnum.VIDEO)
                        .build());
                control(payloadCommandsParam);
            }
        }
        return control(param);
    }

    public Boolean control(PayloadCommandsParam param) {
        Boolean valid = param.valid();
        if (!valid) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PAYLOAD_CONTROL_PARAMETERS_ARE_INVALID);
        }
        // 记录飞行记录关键动作 全景图拍照、视频开始/停止录制
        processFlyAction(param);
        CommonTopicResponse<ServicesReplyData> response = servicesPublish.publish(param.getDockSn(), param.getCmd().getCmd(), param.getData());
        return response.getData().getResult().isSuccess();
    }

    /**
     * 记录飞行记录关键动作 全景图拍照、视频开始/停止录制
     *
     * @param param 负载控制参数
     */
    private void processFlyAction(PayloadCommandsParam param) {
        try {
            String dockSn = param.getDockSn();
            String payloadIndex = param.getData().getPayloadIndex();
            // 拍照
            if (PayloadCommandsEnum.CAMERA_PHOTO_TAKE.equals(param.getCmd())) {
                // 获取机场
                DockDeviceDO byCacheDeviceSn = dockDeviceService.getByCacheDeviceSn(dockSn);
                if (byCacheDeviceSn == null) {
                    log.warn("机场未找到: param {}", JSONUtil.toJsonStr(param));
                    return;
                }
                String droneSn = byCacheDeviceSn.getChildSn();
                // 获取无人机当前模式是否为全景拍照模式
                String osdDroneKey = String.format(RedisKeyConstants.DOCK_OSD_DRONE, dockSn, droneSn);
                OsdDockDrone osdDockDrone = redisCacheService.get(osdDroneKey, OsdDockDrone.class);
                if (osdDockDrone == null) {
                    log.warn("无人机osd未找到: osdDroneKey {}", osdDroneKey);
                    return;
                }
                for (OsdCamera camera : osdDockDrone.getCameras()) {
                    log.info("相机信息:{}", JSONUtil.toJsonStr(camera));
                    // 如果当前相机模式为全景拍照
                    if (camera.getPayloadIndex().toString().equals(payloadIndex) && camera.getCameraMode().equals(CameraModeEnum.PANORAMA)) {
                        // 记录全景拍照动作
                        progressService.saveDetail(dockSn, FlyIncidentTypeEnum.PANO_TAKE, null, null);
                    }
                }
            } else if (PayloadCommandsEnum.CAMERA_RECORDING_START.equals(param.getCmd()) || PayloadCommandsEnum.CAMERA_RECORDING_STOP.equals(param.getCmd())) {
                // 视频开始/停止录制
                FlyIncidentTypeEnum liveRecordType = PayloadCommandsEnum.CAMERA_RECORDING_START.equals(param.getCmd()) ? FlyIncidentTypeEnum.LIVE_RECORD_START : FlyIncidentTypeEnum.LIVE_RECORD_OVER;
                progressService.saveDetail(dockSn, liveRecordType, null, null);
            }
        } catch (Exception e) {
            log.error("保存关键动作失败", e);
        }
    }

}
