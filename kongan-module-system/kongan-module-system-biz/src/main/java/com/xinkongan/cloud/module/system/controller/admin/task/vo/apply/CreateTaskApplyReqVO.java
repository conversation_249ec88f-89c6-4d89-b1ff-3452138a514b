package com.xinkongan.cloud.module.system.controller.admin.task.vo.apply;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.coverage.DockCoverageVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteFolderRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Description 创建机场任务申请
 * <AUTHOR>
 * @Date 2024/12/23 18:42
 */
@Data
@Schema(description = "创建机场任务申请")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateTaskApplyReqVO {

    // 机场sn
    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "机场名称")
    private String dockName;

    // 申请人id
    @Schema(description = "申请人id")
    private Long userId;

    // 申请人名称
    @Schema(description = "申请人名称")
    private String userNickname;

    // 申请人手机号
    @Schema(description = "申请人手机号")
    private String userPhoneNumber;

    // 申请人所属组织id
    @Schema(description = "申请人所属组织id")
    private Long userDeptId;

    // 申请人组织名称
    @Schema(description = "申请人组织名称")
    private String userDeptName;

    // 申请原因
    @Schema(description = "申请原因")
    private String reason;

    // 任务id
    @Schema(description = "任务id")
    private Long taskId;

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "任务紧急程度 1紧急0普通")
    private Integer taskUrgency;

    // 任务名称
    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "任务类型  1接警任务、2临时任务、3巡检任务、4建模任务")
    private Integer scene;

    /**
     * 计划模式 0立即执行1定时任务2循环任务
     */
    private Integer taskExecMode;

    /**
     * 循环模式 0按天循环1按周循环2按月循环
     *
     * <AUTHOR>
     * @date 2025/1/13 10:49
     **/
    private Integer taskCycleMode;


    // 任务航线id
    @Schema(description = "任务航线id")
    private Long taskRouteId;

    // 任务航线名称
    @Schema(description = "任务航线名称")
    private String taskRouteName;

    @Schema(description = "任务航线缩略图url")
    private String routeThumbnail;

    @Schema(description = "任务航点信息")
    private List<RouteFolderRespVO> taskRoutePointInfo;

    // 任务预计执行时间
    @Schema(description = "任务预计执行时间(s)")
    private Integer taskExpectedExecutionTime;

    // 任务预计执行里程
    @Schema(description = "任务预计执行里程(km)")
    private Float taskExpectedExecutionMileage;

    // 执行次数
    @Schema(description = "执行次数")
    private Integer taskCount;

    // 任务周期-开始时间
    @Schema(description = "任务周期-开始时间")
    private LocalDateTime taskStartTime;

    // 任务周期-结束时间
    @Schema(description = "任务周期-结束时间")
    private LocalDateTime taskEndTime;

    @Schema(description = "申请初次执行日期")
    private LocalDateTime taskFlyTime;

    @Schema(description = "执行时间列表")
    private List<LocalDateTime> taskFlyTimeList;

    @Schema(description = "任务描述")
    private String taskDescription;

    @Schema(description = "航线任务精度 0:GPS 1高精度RTK")
    private Integer waylinePrecisionType;

    @Schema(description = "是否自动断点续飞 0否 1是")
    private Integer autoBreakPoint;

    // 审批人id
    @Schema(description = "审批人id")
    private Long approveId;

    // 审批人名称
    @Schema(description = "审批人名称")
    private String approveNickname;

    // 审批时间
    @Schema(description = "审批时间")
    private Date approveTime;

    // 审批人组织id
    @Schema(description = "审批人组织id")
    private Long approveDeptId;

    // 审批人手机号
    @Schema(description = "审批人手机号")
    private String approvePhoneNumber;

    @Schema(description = "申请人租户id")
    private Long tenantId;

    @Schema(description = "警情id")
    private Long alarmId;

    @Schema(description = "警情点经度")
    private Double alarmLongitude;

    @Schema(description = "警情点纬度")
    private Double alarmLatitude;

    @Schema(description = "机场可飞范围")
    private DockCoverageVO dockCoverage;
}



