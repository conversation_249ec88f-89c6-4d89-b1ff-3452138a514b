package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
public class DeviceRankVO {

    @Schema(description = "设备序列号")
    private String deviceSn;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "飞行里程-单位：m")
    private Double mileage;

    @Schema(description = "飞行时间-单位：s")
    private Long flightTime;

    @Schema(description = "飞行次数-单位：次")
    private Long number;

}
