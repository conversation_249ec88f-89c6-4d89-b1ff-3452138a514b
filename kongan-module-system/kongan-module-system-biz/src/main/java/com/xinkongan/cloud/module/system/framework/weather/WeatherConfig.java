package com.xinkongan.cloud.module.system.framework.weather;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "kongan.weather")
public class WeatherConfig {

    @Schema(description = "开启天气查询")
    private Boolean enable = Boolean.TRUE;

    @Schema(description = "天气查询接口地址,调用3天天气即可")
    private String url = "https://devapi.qweather.com/v7/weather/3d";

    @Schema(description = "调用Token")
    private String token = "b6246c41ca2a42d0b1df418f786a65c6";

    public String buildUrl(Double lon, Double lat) {
        return url + "?location=" + lon + "," + lat;
    }

    public String buildToken() {
        return token;
    }
}
