package com.xinkongan.cloud.module.system.service.flyrecord;

import cn.hutool.json.JSONUtil;
import com.xinkongan.cloud.framework.common.constant.BusinessTopicConstant;
import com.xinkongan.cloud.framework.mq.core.consume.BaseRocketMQListener;
import com.xinkongan.cloud.module.algorithm.dto.AlgorithmCreatePushDTO;
import com.xinkongan.cloud.module.system.api.dock.dto.realtime.JobFlyInfo;
import com.xinkongan.cloud.module.system.dal.dataobject.flyrecord.FlyRecordDO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 飞行产生的图片推送
 * <AUTHOR>
 * @Date 2025/4/17 19:12
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = BusinessTopicConstant.ALGORITHM_TASK_CREATE_SUCCESS_PUSH,
        consumerGroup = "fly-record-save-algorithm-consumer-group",
        messageModel = MessageModel.CLUSTERING)
public class FlyRecordSaveAlgorithmHistoryHandler extends BaseRocketMQListener<AlgorithmCreatePushDTO> {

    @Resource
    private IFlyRecordService flyRecordService;

    @Override
    public void handlerMessage(AlgorithmCreatePushDTO createPushDTO) {
        log.info("收到算法创建成功消息：{}", JSONUtil.toJsonStr(createPushDTO));
        Long algorithmId = createPushDTO.getAlgorithmId();
        JobFlyInfo jobFlyInfo = createPushDTO.getJobFlyInfo();
        if (jobFlyInfo != null) {
            // 根据飞行查询飞行记录
            FlyRecordDO flyRecordDO = flyRecordService.getByFlyId(jobFlyInfo.getFlyId());
            if (flyRecordDO == null) {
                log.warn("未找到对应的飞行记录，flyId：{}", jobFlyInfo.getFlyId());
                return;
            }
            // 保存算法历史
            List<Long> algorithmIds = flyRecordDO.getAlgorithmIds();
            if (algorithmIds == null) {
                algorithmIds = new ArrayList<>();
            }
            // 如果不存在，添加到列表中
            if (!algorithmIds.contains(algorithmId)) {
                algorithmIds.add(algorithmId);
                flyRecordDO.setAlgorithmIds(algorithmIds);
                flyRecordService.updateById(flyRecordDO);
            }
        }
    }
}