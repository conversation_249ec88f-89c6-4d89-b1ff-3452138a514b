package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DockModeCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PostureDeviceVO {

    @Schema(description = "设备Key")
    private String deviceKey;

    @Schema(description = "设备id")
    private Long deviceId;

    @Schema(description = "设备SN")
    private String deviceSn;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "任务名称")
    private String JobName;

    @Schema(description = "所属组织")
    private String deptName;

    @Schema(description = "共享状态")
    private Integer shareStatus;

    @Schema(description = "是否是外部机场 1是0否")
    private Integer without;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "子设备SN")
    private String childSn;

    @Schema(description = "设备类型")
    private Integer deviceType;

    @Schema(description = "子设备")
    private PostureChildDeviceVO childDeviceVO;

    @Schema(description = "排序时间")
    private LocalDateTime sortTime;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度")
    private Double height;

    @Schema(description = "机场状态")
    private DockModeCodeEnum dockModeCode;

}

