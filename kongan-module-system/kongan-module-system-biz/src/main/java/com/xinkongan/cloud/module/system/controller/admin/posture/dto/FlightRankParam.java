package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import com.xinkongan.cloud.module.system.controller.admin.posture.PostureDateParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
public class FlightRankParam extends PostureDateParam {

    @Schema(description = "统计类型：1:时长,2:里程,3飞行次数")
    private Integer type;

}
