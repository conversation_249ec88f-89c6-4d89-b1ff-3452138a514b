package com.xinkongan.cloud.module.system.controller.admin.dock.vo.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApproveReqDTO {

    @Schema(description = "机场SN")
    private String dockSn;

    @Schema(description = "审批结果")
    private Boolean flag;

    @Schema(description = "用户ID")
    private Long authUserId;

    @Schema(description = "用户昵称")
    private String nickname;

}
