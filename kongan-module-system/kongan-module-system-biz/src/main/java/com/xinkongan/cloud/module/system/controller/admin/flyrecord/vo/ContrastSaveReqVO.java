package com.xinkongan.cloud.module.system.controller.admin.flyrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description 保存对比记录
 * <AUTHOR>
 * @Date 2025/2/19 15:22
 */
@Data
@Schema(description = "对比记录保存ReqVO")
public class ContrastSaveReqVO {

    @Schema(description = "对比记录名称")
    private String name;

    @Schema(description = "左图飞行记录文件id")
    private Long leftFileId;

    @Schema(description = "左图飞行记录id")
    private Long leftFlyRecordId;

    @Schema(description = "左图异常id")
    private Long leftExceptionId;

    @Schema(description = "右图飞行记录文件id")
    private Long rightFileId;

    @Schema(description = "右图飞行记录id")
    private Long rightFlyRecordId;

    @Schema(description = "右图异常id")
    private Long rightExceptionId;

    @Schema(description = "右图组织id")
    private Long rightDeptId;

    @Schema(description = "异常图片url")
    private String thumbnail;

    @Schema(description = "异常图片大小")
    private Integer size;

    @Schema(description = "异常图片路径")
    private String path;

    @Schema(description = "异常图片高度")
    private Integer height;

    @Schema(description = "异常图片宽度")
    private Integer width;

}