package com.xinkongan.cloud.module.system.service.material;

import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.module.system.dto.MaterialParseDTO;
import com.xinkongan.cloud.module.system.framework.material.SystemMaterialConfig;
import com.xinkongan.cloud.sdk.gis.service.IStoreService;
import com.xinkongan.cloud.sdk.gis.service.IWorkspaceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 二维素材gis瓦片的解析
 */

@Slf4j
@Service
public class TwoGisDimenParseServiceImpl extends DefaultParseHandler {

    @Resource
    private SystemMaterialConfig systemMaterialConfig;

    @Resource
    private IStoreService storeService;

    @Resource
    private IWorkspaceService workspaceService;

    @Override
    public void doHandler(MaterialParseDTO material) {
        SystemMaterialConfig.GisProperties gisProperties = systemMaterialConfig.getGisProperties();
        if (gisProperties == null) {
            throw new RuntimeException("未配置GIS相关配置");
        }
        String downloadUrl = gisProperties.getDownloadUrl();
        String downloadCallbackUrl = gisProperties.getDownloadCallbackUrl();
        String path = gisProperties.getPath();
        if (downloadUrl == null || downloadCallbackUrl == null || path == null) {
            throw new RuntimeException("未配置GIS相关配置");
        }
        log.info("【二维素材解析】,素材信息为：{}", JSONObject.toJSONString(material));

        // 1. 创建工作空间
        String workspace = "w_" + material.getDeptId();
        Boolean createWorkspace = workspaceService.createWorkspace(workspace);
        if (!createWorkspace) {
            throw new RuntimeException("geoserver 创建工作空间失败");
        }
        // 2. 发送请求下载 tif 数据
        storeService.uploadFileToDataDir(workspace, material.getUrl(), String.valueOf(material.getId()));
    }
}
