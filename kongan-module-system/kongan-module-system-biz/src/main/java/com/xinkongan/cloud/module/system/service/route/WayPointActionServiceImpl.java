package com.xinkongan.cloud.module.system.service.route;

import cn.hutool.core.collection.CollectionUtil;
import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.WayPointActionInfoDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteAlgorithmVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointActionRespVO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.WayPointShoutVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask.DynamicActionRespVO;
import com.xinkongan.cloud.module.system.controller.admin.task.vo.docktask.RouteDynamicStateRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteAlgorithmDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.WayPointActionDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.WayPointDO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.WayPointShoutDO;
import com.xinkongan.cloud.module.system.dal.mysql.route.RouteAlgorithmMapper;
import com.xinkongan.cloud.module.system.dal.mysql.route.WayPointActionMapper;
import com.xinkongan.cloud.module.system.dal.mysql.route.WayPointMapper;
import com.xinkongan.cloud.module.system.dal.mysql.route.WayPointShoutMapper;
import com.xinkongan.cloud.module.system.dto.WayPointActionDTO;
import com.xinkongan.cloud.module.system.enums.route.WayPointActionEnum;
import com.xinkongan.cloud.module.system.enums.route.WayPointActionTransTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Stack;

@Slf4j
@Service
public class WayPointActionServiceImpl implements IWayPointActionService {

    @Resource
    private WayPointActionMapper wayPointActionMapper;

    @Resource
    private WayPointMapper wayPointMapper;

    @Resource
    private RouteAlgorithmMapper routeAlgorithmMapper;

    @Resource
    private WayPointShoutMapper wayPointShoutMapper;

    @Override
    public List<Long> saveWayPointActionInfo(Long routeId, Long waypointId,
                                             List<WayPointActionInfoDTO> actionInfos,
                                             List<WayPointActionDTO> wayPointActions,
                                             Stack<WayPointActionDTO> multiTakePhoneStack,
                                             Stack<WayPointActionDTO> recordStack) {
        if (CollectionUtil.isEmpty(actionInfos)) {
            return new ArrayList<>();
        }
        WayPointDO wayPointDO = wayPointMapper.selectById(waypointId);
        // 航点动作列表
        List<Long> waypointActionIds = new ArrayList<>();
        // 保存航点动作信息
        for (WayPointActionInfoDTO actionInfo : actionInfos) {
            WayPointActionDO wayPointActionDO = BeanUtils.toBean(actionInfo, WayPointActionDO.class);
            wayPointActionDO.setPointId(waypointId);
            wayPointActionDO.setRouteId(routeId);
            wayPointActionMapper.insert(wayPointActionDO);
            waypointActionIds.add(wayPointActionDO.getId());

            // 以下类型分别会产生动作组
            WayPointActionEnum wayPointActionEnum = WayPointActionEnum.getWayPointActionEnumByType(actionInfo.getType());
            switch (Objects.requireNonNull(wayPointActionEnum)) {
                case multipleTiming, multipleDistance -> {
                    // 先根据栈数据判断是否需要添加该动作
                    if (!multiTakePhoneStack.isEmpty()) {
                        WayPointActionDTO pointActionInfoDTO = multiTakePhoneStack.peek();
                        if (WayPointActionTransTypeEnum.isMultipleAction(pointActionInfoDTO.getType())) {
                            // 说明之前有设置过该动作，则后面设置失效
                            continue;
                        }
                    }
                    WayPointActionDTO pointActionInfoDTO = new WayPointActionDTO();
                    pointActionInfoDTO.setId(wayPointActionDO.getId());
                    pointActionInfoDTO.setType(actionInfo.getType());
                    pointActionInfoDTO.setPointId(waypointId);
                    pointActionInfoDTO.setStartIndex(wayPointDO.getPointIndex());
                    wayPointActions.add(pointActionInfoDTO);
                    multiTakePhoneStack.push(pointActionInfoDTO);
                }
                case multiplePhotoOver -> {
                    while (!multiTakePhoneStack.isEmpty()) {
                        WayPointActionDTO pop = multiTakePhoneStack.pop();
                        pop.setEndIndex(wayPointDO.getPointIndex());
                    }
                }
                case startRecord -> {
                    // 开启录制，结束录制需要单独处理
                    if (!recordStack.isEmpty()) {
                        // 说明之前有设置过该动作，则后面设置失效
                        continue;
                    }
                    WayPointActionDTO pointActionInfoDTO = new WayPointActionDTO();
                    pointActionInfoDTO.setId(wayPointActionDO.getId());
                    pointActionInfoDTO.setType(actionInfo.getType());
                    pointActionInfoDTO.setPointId(waypointId);
                    pointActionInfoDTO.setStartIndex(wayPointDO.getPointIndex());
                    wayPointActions.add(pointActionInfoDTO);
                    recordStack.push(pointActionInfoDTO);
                }
                case stopRecord -> {
                    if (recordStack.isEmpty()) {
                        continue;
                    }
                    WayPointActionDTO pop = recordStack.pop();
                    pop.setEndIndex(wayPointDO.getPointIndex());
                }
            }
        }
        return waypointActionIds;
    }

    @Override
    public List<WayPointActionRespVO> getWayPointActionByRouteId(Long routeId) {
        List<WayPointActionDO> wayPointActions = wayPointActionMapper.selectList(WayPointActionDO::getRouteId, routeId);
        return wayPointActions.stream().map(this::convert).toList();
    }

    @Override
    public List<WayPointActionRespVO> getWayPointActionByRouteIdAndPointIndex(Long routeId, Integer pointIndex) {
        List<WayPointDO> wayPointLists = wayPointMapper.selectList(
                new LambdaQueryWrapperX<WayPointDO>()
                        .eq(WayPointDO::getRouteId, routeId)
                        .eq(WayPointDO::getPointIndex, pointIndex)
        );
        if (CollectionUtil.isEmpty(wayPointLists)) {
            return new ArrayList<>();
        }
        WayPointDO wayPointDO = wayPointLists.get(0);
        List<Long> waypointActionList = wayPointDO.getWaypointActionList();
        if (CollectionUtil.isEmpty(waypointActionList)) {
            return new ArrayList<>();
        }
        List<Object> waypointActionInfos = new ArrayList<>();

        // 查询航点动作信息
        List<WayPointActionDO> wayPointActions = wayPointActionMapper.selectList(
                new LambdaQueryWrapperX<WayPointActionDO>()
                        .in(WayPointActionDO::getId, waypointActionList)
        );
        waypointActionInfos.addAll(wayPointActions);
        // 查询算法动作
        List<RouteAlgorithmDO> algorithmActionList = routeAlgorithmMapper.selectList(
                new LambdaQueryWrapperX<RouteAlgorithmDO>()
                        .eq(RouteAlgorithmDO::getRouteId, routeId)
                        .in(RouteAlgorithmDO::getId, waypointActionList)
        );
        waypointActionInfos.addAll(algorithmActionList);
        // 查询喊话动作
        List<WayPointShoutDO> shoutActionList = wayPointShoutMapper.selectList(
                new LambdaQueryWrapperX<WayPointShoutDO>()
                        .eq(WayPointShoutDO::getRouteId, routeId)
                        .in(WayPointShoutDO::getId, waypointActionList)
        );
        waypointActionInfos.addAll(shoutActionList);
        return waypointActionInfos.stream().map(this::convert).toList();
    }


    @Override
    public List<WayPointActionRespVO> getWayLegActionByRouteIdAndPointIndex(Long routeId, Integer pointIndex) {
        List<WayPointDO> wayPointLists = wayPointMapper.selectList(
                new LambdaQueryWrapperX<WayPointDO>()
                        .eq(WayPointDO::getRouteId, routeId)
                        .eq(WayPointDO::getPointIndex, pointIndex)
        );
        if (CollectionUtil.isEmpty(wayPointLists)) {
            return new ArrayList<>();
        }
        WayPointDO wayPointDO = wayPointLists.get(0);
        List<Long> waypointLegActionList = wayPointDO.getLegActionList();
        if (CollectionUtil.isEmpty(waypointLegActionList)) {
            return new ArrayList<>();
        }
        List<Object> waypointActionInfos = new ArrayList<>();

        // 查询航点动作信息
        List<WayPointActionDO> wayPointActions = wayPointActionMapper.selectList(
                new LambdaQueryWrapperX<WayPointActionDO>()
                        .in(WayPointActionDO::getId, waypointLegActionList)
        );
        waypointActionInfos.addAll(wayPointActions);

        // 查询算法动作
        List<RouteAlgorithmDO> algorithmActionList = routeAlgorithmMapper.selectList(
                new LambdaQueryWrapperX<RouteAlgorithmDO>()
                        .eq(RouteAlgorithmDO::getRouteId, routeId)
                        .in(RouteAlgorithmDO::getId, waypointLegActionList)
        );
        waypointActionInfos.addAll(algorithmActionList);

        // 查询喊话动作
        List<WayPointShoutDO> shoutActionList = wayPointShoutMapper.selectList(
                new LambdaQueryWrapperX<WayPointShoutDO>()
                        .eq(WayPointShoutDO::getRouteId, routeId)
                        .in(WayPointShoutDO::getId, waypointLegActionList)
        );
        waypointActionInfos.addAll(shoutActionList);

        return waypointActionInfos.stream().map(this::convert).toList();
    }

    @Override
    public RouteDynamicStateRespVO getDynamicActionByRouteIdAndPointIndex(Long routeId, Integer pointIndex) {
        RouteDynamicStateRespVO respVO = new RouteDynamicStateRespVO();
        respVO.setCurrentWaypoint(pointIndex);
        List<WayPointActionRespVO> pointActions = getWayPointActionByRouteIdAndPointIndex(routeId, pointIndex - 1);
        if (!pointActions.isEmpty()) {
            List<DynamicActionRespVO> list = pointActions.stream()
                    .map(pointAction -> BeanUtils.toBean(pointAction, DynamicActionRespVO.class))
                    .toList();
            respVO.setPointActions(list);
        }
        List<WayPointActionRespVO> legActions = getWayLegActionByRouteIdAndPointIndex(routeId, pointIndex - 1);
        if (!legActions.isEmpty()) {
            List<DynamicActionRespVO> list = legActions.stream()
                    .map(legAction -> BeanUtils.toBean(legAction, DynamicActionRespVO.class))
                    .toList();
            respVO.setLegActions(list);
        }
        // 查询是否存在下一个航点
        List<WayPointDO> wayPointDOS = wayPointMapper.selectList(
                new LambdaQueryWrapperX<WayPointDO>()
                        .eq(WayPointDO::getRouteId, routeId)
                        .eq(WayPointDO::getPointIndex, pointIndex)
        );
        respVO.setLastWaypoint(CollectionUtil.isEmpty(wayPointDOS));
        return respVO;
    }

    private WayPointActionRespVO convert(Object wayPointActionInfo) {
        WayPointActionRespVO wayPointActionRespInfo = null;

        if (wayPointActionInfo instanceof WayPointActionDO) {
            wayPointActionRespInfo = BeanUtils.toBean(wayPointActionInfo, WayPointActionRespVO.class);

        } else if (wayPointActionInfo instanceof RouteAlgorithmDO routeAlgorithmAction) {
            wayPointActionRespInfo = new WayPointActionRespVO();

            wayPointActionRespInfo.setId(routeAlgorithmAction.getId());
            wayPointActionRespInfo.setType(WayPointActionTransTypeEnum.algorihtmActionType.getCode());
            wayPointActionRespInfo.setPointId(routeAlgorithmAction.getPointId());
            wayPointActionRespInfo.setRouteId(routeAlgorithmAction.getRouteId());

            // 具体算法的信息
            RouteAlgorithmVO routeAlgorithmInfo = routeAlgorithmMapper.getRouteAlgorithmInfoById(routeAlgorithmAction.getId());
            wayPointActionRespInfo.setRouteAlgorithmInfo(routeAlgorithmInfo);

        } else if (wayPointActionInfo instanceof WayPointShoutDO shoutAction) {

            wayPointActionRespInfo = new WayPointActionRespVO();
            wayPointActionRespInfo.setId(shoutAction.getId());
            wayPointActionRespInfo.setType(WayPointActionTransTypeEnum.legShoutActionType.getCode());
            wayPointActionRespInfo.setPointId(shoutAction.getPointId());
            wayPointActionRespInfo.setRouteId(shoutAction.getRouteId());

            WayPointShoutDO wayPointShoutInfo = wayPointShoutMapper.selectById(shoutAction.getId());
            if (wayPointShoutInfo != null) {
                WayPointShoutVO wayPointShoutVO = BeanUtils.toBean(wayPointShoutInfo, WayPointShoutVO.class);
                wayPointActionRespInfo.setWayPointShoutInfo(wayPointShoutVO);
            }
        }
        return wayPointActionRespInfo;
    }


    private WayPointActionRespVO convert(WayPointActionDO wayPointActionInfo) {
        return BeanUtils.toBean(wayPointActionInfo, WayPointActionRespVO.class);
    }


    @Override
    public void deletePointActionByRouteId(Long routeId) {
        wayPointActionMapper.delete(WayPointActionDO::getRouteId, routeId);
    }
}
