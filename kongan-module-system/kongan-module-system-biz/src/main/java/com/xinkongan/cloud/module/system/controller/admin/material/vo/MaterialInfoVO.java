package com.xinkongan.cloud.module.system.controller.admin.material.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinkongan.cloud.framework.datapermission.core.plugins.ShareResultVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class MaterialInfoVO extends ShareResultVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "素材名称")
    private String name;

    @Schema(description = "素材文件地址，上传原文件地址")
    private String url;

    @Schema(description = "素材文件存储路径，上传原文件存储路径")
    private String objectKey;

    @Schema(description = "页面卡片展示缩略图地址")
    private String jpgUrl;

    @Schema(description = "素材类型")
    private Integer type;

    @Schema(description = "素材解析状态")
    private String status;

    @Schema(description = "解析进度")
    private Integer process;

    @Schema(description = "素材原始文件的大小")
    private Long size;

    @Schema(description = "左上经度")
    private Double leftUpLon;

    @Schema(description = "左上维度")
    private Double leftUpLat;

    @Schema(description = "右下经度")
    private Double rightDownLon;

    @Schema(description = "右下纬度")
    private Double rightDownLat;

    @Schema(description = "中心点经度")
    private Double centerLon;

    @Schema(description = "中心点纬度")
    private Double centerLat;

    @Schema(description = "备用字段")
    private String extend;

    @Schema(description = "全景图地址")
    private String address;

    @Schema(description = "创建人姓名")
    private String creatorName;

    @Schema(description = "全景图高度")
    private Double height;

    @Schema(description = "全景图拍摄偏航角")
    private String gimbalYamDegree;

    @Schema(description = "是否已在大屏加载(1是 0否)")
    private Integer isLoaded;

    @Schema(description = "清晰度")
    private Integer clarityLevel;

    @Schema(description = "数据源名称")
    private String storeName;

    @Schema(description = "工作空间名称")
    private String workspaceName;

    @Schema(description = "图层名称")
    private String layerName;

    @Schema(description = "瓦片的图片格式 image/jpeg image/png")
    private String format;

    @Schema(description = "最小层级 0-21")
    private Integer minimumLevel;

    @Schema(description = "最大层级 0-21")
    private Integer maximumLevel;

    @Schema(description = "缓存占用大小")
    private Double gwcSize;

    @Schema(description = "是否为建模素材 0 否 1 是")
    private Integer isModel;

    @Schema(description = "瓦片访问的url")
    private String tileUrl;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "组织名称")
    private String deptName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2024-12-31 14:50:00")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间", example = "2024-12-31 14:50:00")
    private Date updateTime;

    @Schema(description = "加载状态 1已加载 0未加载")
    private Integer loadingStatus = 0;// 默认未加载
}
