package com.xinkongan.cloud.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "管理后台 - 租户注册 Request VO")
public class TenantRegisterReqVO {

    @NotBlank(message = "公司名称不能为空")
    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "安徽省空安信息技术有限公司")
    private String company;

    @Schema(description = "所属行业：关联字典表主键", example = "1419771575730176")
    private Long industry;

    @NotBlank(message = "所属城市不能为空")
    @Schema(description = "所属城市：安徽省/合肥市/长丰县")
    private String city;

    @NotBlank(message = "管理员姓名不能为空")
    @Schema(description = "租户管理员姓名")
    private String nickName;

    @NotBlank(message = "管理员用户名不能为空")
    @Schema(description = "租户管理员用户名")
    private String username;

    @NotBlank(message = "管理员密码不能为空")
    @Schema(description = "租户管理员密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private String password;

    @NotBlank(message = "管理员手机号不能为空")
    @Schema(description = "租户管理员手机号")
    private String mobile;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "表单提交验证码")
    private String verificationCode;
}
