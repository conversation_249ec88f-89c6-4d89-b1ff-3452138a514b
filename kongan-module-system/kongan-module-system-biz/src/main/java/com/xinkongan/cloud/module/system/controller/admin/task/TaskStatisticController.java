package com.xinkongan.cloud.module.system.controller.admin.task;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.dept.dto.HistogramSearchDTO;
import com.xinkongan.cloud.module.system.controller.admin.dept.vo.dept.HistogramVO;
import com.xinkongan.cloud.module.system.service.task.IDockTaskApplyService;
import com.xinkongan.cloud.module.system.service.task.IJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 任务统计
 * <AUTHOR>
 * @Date 2025/5/8 16:45
 */
@Validated
@RestController
@Tag(name = "飞控平台 - 任务统计")
@RequestMapping("/system/task/statistic")
public class TaskStatisticController {

    @Resource
    private IJobService jobService;
    @Resource
    private IDockTaskApplyService dockTaskApplyService;

    @GetMapping("/waitApproval")
    @Operation(summary = "待审批任务数量")
    public CommonResult<Long> waitApprovalCount() {
        Long count = dockTaskApplyService.getWaitApprovalCount();
        return CommonResult.success(count);
    }

    @GetMapping(value = "/histogram")
    @Operation(summary = "任务列表-柱状图数据")
    public CommonResult<List<HistogramVO>> histogramStatistic(HistogramSearchDTO histogramSearchInfo) {
        List<HistogramVO> result = jobService.getJobHistogramStatistic(histogramSearchInfo);
        return CommonResult.success(result);
    }
}