package com.xinkongan.cloud.module.system.controller.admin.task.vo.job;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 任务排期返回信息
 * <AUTHOR>
 * @Date 2025/3/4 16:38
 */
@Data
@Schema(description = "任务排期返回信息")
public class ScheduleFlyInfo {

    @Schema(description = "飞行id")
    private Long flyId;

    @Schema(description = "飞行预计结束时间")
    private LocalDateTime predictEndTime;

    @Schema(description = "飞行记录id")
    private Long flyRecordId;

    @Schema(description = "飞行记录名称")
    private String flyRecordName;

    @Schema(description = "起飞时间")
    private LocalDateTime takeOffTime;

    @Schema(description = "降落时间")
    private LocalDateTime landTime;

    @Schema(description = "任务id")
    private Long jobId;

    @Schema(description = "任务名称")
    private String jobName;

    @Schema(description = "任务精度")
    private Integer waylinePrecisionType;

    @Schema(description = "是否自动断点")
    private Integer autoBreakPoint;

    @Schema(description = "任务描述")
    private String description;

    @Schema(description = "航线id")
    private Long routeId;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "任务所属组织")
    private Long deptId;

    @Schema(description = "任务所属组织名称")
    private String deptName;

    @Schema(description = "任务执行时间")
    private LocalDateTime execTime;

    @Schema(description = "任务预计耗时")
    private Integer predictTime;

    @Schema(description = "任务范围 0内部任务 1外部任务")
    private Integer scope;

    @Schema(description = "任务场景")
    private Integer scene;


}