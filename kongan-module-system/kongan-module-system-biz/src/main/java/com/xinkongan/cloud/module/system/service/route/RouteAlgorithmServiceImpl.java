package com.xinkongan.cloud.module.system.service.route;

import com.xinkongan.cloud.framework.common.util.object.BeanUtils;
import com.xinkongan.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xinkongan.cloud.module.system.controller.admin.route.dto.RouteAlgorithmSaveDTO;
import com.xinkongan.cloud.module.system.controller.admin.route.vo.RouteAlgorithmVO;
import com.xinkongan.cloud.module.system.dal.dataobject.route.RouteAlgorithmDO;
import com.xinkongan.cloud.module.system.dal.mysql.route.RouteAlgorithmMapper;
import com.xinkongan.cloud.module.system.dto.WayPointActionDTO;
import com.xinkongan.cloud.module.system.enums.route.WayPointActionTransTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RouteAlgorithmServiceImpl implements IRouteAlgorithmService {

    @Resource
    private RouteAlgorithmMapper algorithmMapper;


    @Override
    public void deleteRouteAlgorithmInfoByRouteIdAndPointId(Long routeId, Long pointId) {
        algorithmMapper.delete(
                new LambdaQueryWrapperX<RouteAlgorithmDO>()
                        .eq(RouteAlgorithmDO::getRouteId, routeId)
                        .eq(pointId != null, RouteAlgorithmDO::getPointId, pointId)
        );
    }

    @Override
    public List<Long> saveRouteAlgorithmInfo(Long routeId, Long pointId, List<RouteAlgorithmSaveDTO> routeAlgorithmInfos, List<WayPointActionDTO> wayPointActions) {
        if (routeAlgorithmInfos == null || routeAlgorithmInfos.isEmpty()) {
            return new ArrayList<>();
        }
        List<RouteAlgorithmDO> routeAlgorithms = routeAlgorithmInfos.stream().map(t -> this.convert(routeId, pointId, t)).toList();
        algorithmMapper.insertBatch(routeAlgorithms);

        List<Long> algorithmActionIds = new ArrayList<>();

        // 补充航点动作信息
        for (RouteAlgorithmDO routeAlgorithm : routeAlgorithms) {
            algorithmActionIds.add(routeAlgorithm.getId());
            if (Objects.equals(routeAlgorithm.getType(), 1)) {
                WayPointActionDTO wayPointActionInfo = new WayPointActionDTO();
                wayPointActionInfo.setId(routeAlgorithm.getId());
                wayPointActionInfo.setPointId(pointId);
                wayPointActionInfo.setStartIndex(routeAlgorithm.getStartIndex());
                wayPointActionInfo.setEndIndex(routeAlgorithm.getEndIndex());
                wayPointActionInfo.setType(WayPointActionTransTypeEnum.algorihtmActionType.getCode());
                wayPointActions.add(wayPointActionInfo);
            }
        }
        return algorithmActionIds;
    }

    @Override
    public Map<Long, List<RouteAlgorithmVO>> getRouteAlgorithmInfoByRouteId(Long routeId) {
        List<RouteAlgorithmVO> routeAlgorithmInfo = algorithmMapper.getRouteAlgorithmInfoByRouteId(routeId);

        if (routeAlgorithmInfo == null || routeAlgorithmInfo.isEmpty()) {
            return Map.of();
        }
        return routeAlgorithmInfo.stream()
                .collect(
                        Collectors.groupingBy(RouteAlgorithmVO::getPointId)
                );
    }

    @Override
    public List<RouteAlgorithmVO> getRouteAlgorithmInfoByPointId(Long pointId) {
        return algorithmMapper.getRouteAlgorithmInfoByPointId(pointId);
    }

    @Override
    public Boolean isAlgorithmExampleUsed(Long id) {
        return algorithmMapper.selectCount(new LambdaQueryWrapperX<RouteAlgorithmDO>().eq(RouteAlgorithmDO::getExampleId, id)) > 0;
    }

    private RouteAlgorithmDO convert(Long routeId, Long pointId, RouteAlgorithmSaveDTO routeAlgorithmInfo) {
        RouteAlgorithmDO routeAlgorithmDO = BeanUtils.toBean(routeAlgorithmInfo, RouteAlgorithmDO.class);
        routeAlgorithmDO.setRouteId(routeId);
        routeAlgorithmDO.setPointId(pointId);
        return routeAlgorithmDO;
    }
}
