package com.xinkongan.cloud.module.system.controller.admin.report;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.LabelStatisticVO;
import com.xinkongan.cloud.module.system.controller.admin.report.vo.LabelTopVO;
import com.xinkongan.cloud.module.system.service.label.ILabelReferenceService;
import com.xinkongan.cloud.module.system.service.label.ILabelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RestController
@Tag(name = "数据报表-标注管理")
@RequestMapping("/system/report/label")
public class LabelReportController {


    @Resource
    private ILabelService labelService;


    @Resource
    private ILabelReferenceService labelReferenceService;


    @GetMapping(value = "/statistic")
    @Operation(summary = "数据报表-基础数据")
    @PreAuthorize("@ss.hasPermission('system:label:statistic')")
    public CommonResult<LabelStatisticVO> authReport() {
        LabelStatisticVO labelStatistic = labelService.getLabelStatistic();
        return CommonResult.success(labelStatistic);
    }


    @GetMapping(value = "/top")
    @Operation(summary = "数据报表-top排行榜")
    @PreAuthorize("@ss.hasPermission('system:label:statistic')")
    public CommonResult<List<LabelTopVO>> getLabelTop() {
        List<LabelTopVO> labelTops =  labelReferenceService.getLabelReferenceTop();
        return CommonResult.success(labelTops);
    }
}
