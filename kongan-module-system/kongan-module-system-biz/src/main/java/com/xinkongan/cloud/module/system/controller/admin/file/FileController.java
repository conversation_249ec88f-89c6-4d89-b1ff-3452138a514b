package com.xinkongan.cloud.module.system.controller.admin.file;


import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.api.file.dto.file.StsCredentialsDTO;
import com.xinkongan.cloud.module.system.controller.admin.file.vo.file.FileCreateReqVO;
import com.xinkongan.cloud.module.system.enums.file.S3FileDirPrefixEnum;
import com.xinkongan.cloud.module.system.service.file.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

@Slf4j
@Validated
@Tag(name = "管理后台 - 文件存储")
@RestController
@RequestMapping("/system/file")
public class FileController {

    @Resource
    private FileService fileService;


    @PostMapping("/create")
    @Operation(summary = "创建文件", description = "模式二：前端上传文件：配合 presigned-url 接口，记录上传了上传的文件")
    public CommonResult<Long> createFile(@Valid @RequestBody FileCreateReqVO createReqVO) {
        return success(fileService.createFile(createReqVO));
    }


    @GetMapping("/get/sts")
    @Operation(summary = "获取文件上传的sts")
    public CommonResult<StsCredentialsDTO> getSts(@RequestParam S3FileDirPrefixEnum fileDirPrefixEnum) {
        StsCredentialsDTO credentialsInfo = fileService.getSTS(TenantContextHolder.getTenantId(), fileDirPrefixEnum);
        return CommonResult.success(credentialsInfo);
    }

    /**
     * 素材解析端获取STS进行素材上传
     * 该接口只用于素材解析端
     * 进行单独的鉴权
     *
     * @param dirPrefix
     * @return
     */
    @PermitAll
    @TenantIgnore
    @GetMapping("/material/server/get/sts")
    @Operation(summary = "素材解析获取sts")
    public CommonResult<StsCredentialsDTO> getStsToMaterialServer(@RequestParam(value = "dirPrefix") String dirPrefix,
                                                                  @RequestParam(value = "tenantId") Long tenantId) {
        // 这里需要鉴权

        S3FileDirPrefixEnum dirPrefixEnum = S3FileDirPrefixEnum.getDirEnumByStr(dirPrefix);
        return success(fileService.getSTS(tenantId, dirPrefixEnum));
    }

}
