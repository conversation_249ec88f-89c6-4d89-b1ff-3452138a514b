package com.xinkongan.cloud.module.system.controller.admin.live;

import com.alibaba.fastjson.JSONObject;
import com.xinkongan.cloud.framework.common.util.cache.RedisKeyConstants;
import com.xinkongan.cloud.framework.redis.core.IRedisCacheService;
import com.xinkongan.cloud.framework.tenant.core.aop.TenantIgnore;
import com.xinkongan.cloud.framework.tenant.core.context.TenantContextHolder;
import com.xinkongan.cloud.module.system.api.live.dto.AgoraCallbackParamDTO;
import com.xinkongan.cloud.module.system.enums.live.agora.ProductTypeEnum;
import com.xinkongan.cloud.module.system.service.live.callback.IAgoraCallbackService;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.util.List;

/**
 * @Description 声网直播回调
 * <AUTHOR>
 * @Date 2025/2/18 11:27
 */
@Slf4j
@RestController
@RequestMapping(value = "/system/live/agora")
public class AgoraWebhookCallback implements Serializable {

    @Resource
    private List<IAgoraCallbackService> agoraCallbackServiceList;
    @Autowired
    private IRedisCacheService redisCacheService;

    /**
     * 处理声网回调
     *
     * @return
     */
    @PostMapping(value = "/callback")
    @PermitAll
    @TenantIgnore
    public void agoraCallback(@RequestBody AgoraCallbackParamDTO agoraCallbackParam, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("声网直播回调，回调参数为：{}", JSONObject.toJSONString(agoraCallbackParam));
        String key = String.format(RedisKeyConstants.AGORA_CALLBACK_ID, agoraCallbackParam.getEventType() + ":" + agoraCallbackParam.getSid()+agoraCallbackParam.getNoticeId());
        boolean hasKey = redisCacheService.hasKey(key);
        if (hasKey) {
            log.info("回调已处理，sid:{}", agoraCallbackParam.getSid());
        } else {
            redisCacheService.put(key, 1, 60L);
            Integer productId = agoraCallbackParam.getProductId();
            ProductTypeEnum productTypeEnum = ProductTypeEnum.find(productId);
            if (productTypeEnum == null) {
                log.info("[声网回调] 未找到对应的业务类型，productId:{}", productId);
            } else {
                // 根据对应的业务找到对应的业务处理回调
                for (IAgoraCallbackService agoraCallbackService : agoraCallbackServiceList) {
                    if (productTypeEnum.equals(agoraCallbackService.getProductType())) {
                        agoraCallbackService.handlerCallback(agoraCallbackParam);
                    }
                }
            }
            request.setCharacterEncoding("UTF-8");
            OutputStream out = response.getOutputStream();
            PrintWriter pw = new PrintWriter(new BufferedWriter(new OutputStreamWriter(out)));
            pw.print(" {\"code\":0}");
            pw.flush();
            pw.close();
            TenantContextHolder.clear();
        }
    }
}