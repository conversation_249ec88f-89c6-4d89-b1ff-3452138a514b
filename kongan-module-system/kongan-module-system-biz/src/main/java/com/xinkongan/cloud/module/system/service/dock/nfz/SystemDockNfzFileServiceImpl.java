package com.xinkongan.cloud.module.system.service.dock.nfz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz.NfzFileRespVO;
import com.xinkongan.cloud.module.system.convert.dock.NfzConvert;
import com.xinkongan.cloud.module.system.dal.dataobject.dock.SystemDockNfzFileDO;
import com.xinkongan.cloud.module.system.dal.mysql.dock.SystemDockNfzFileMapper;
import com.xinkongan.cloud.module.system.service.file.FileService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统禁飞区文件Service实现类
 */
@Slf4j
@Service
public class SystemDockNfzFileServiceImpl implements ISystemDockNfzFileService {

    @Resource
    private SystemDockNfzFileMapper nfzFileMapper;

    @Resource
    private FileService fileService;

    @Override
    public List<NfzFileRespVO> getNfzFilesByTenantId(Long tenantId) {
        LambdaQueryWrapper<SystemDockNfzFileDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemDockNfzFileDO::getTenantId, tenantId);
        queryWrapper.orderByDesc(SystemDockNfzFileDO::getCreateTime);

        List<SystemDockNfzFileDO> nfzFileDOList = nfzFileMapper.selectList(queryWrapper);
        if (nfzFileDOList.isEmpty()) {
            log.info("根据租户ID获取禁飞区文件，租户ID: {}，未找到相关文件", tenantId);
            return List.of();
        }
        return NfzConvert.INSTANCE.convertFileToRespVOList(nfzFileDOList);
    }

    @Override
    public int deleteNfzFilesByTenantId(Long tenantId) {
        LambdaQueryWrapper<SystemDockNfzFileDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemDockNfzFileDO::getTenantId, tenantId);

        // 先查询要删除的文件记录，获取fileId用于删除OSS文件
        List<SystemDockNfzFileDO> filesToDelete = nfzFileMapper.selectList(queryWrapper);

        if (filesToDelete.isEmpty()) {
            log.info("根据租户ID删除禁飞区文件，租户ID: {}，没有找到要删除的文件", tenantId);
            return 0;
        }

        // 提取所有fileId用于批量删除OSS文件
        List<Long> fileIds = filesToDelete.stream()
                .map(SystemDockNfzFileDO::getFileId)
                .filter(fileId -> fileId != null)
                .toList();

        // 删除OSS中的文件
        if (!fileIds.isEmpty()) {
            try {
                fileService.deleteBatchIds(fileIds);
                log.info("成功删除OSS文件，文件ID列表: {}", fileIds);
            } catch (Exception e) {
                log.error("删除OSS文件失败，文件ID列表: {}, 错误: {}", fileIds, e.getMessage(), e);
                // 继续执行数据库删除，避免数据不一致
            }
        }

        // 删除数据库记录
        int deletedCount = nfzFileMapper.delete(queryWrapper);
        log.info("根据租户ID删除禁飞区文件，租户ID: {}，删除数量: {}，同时删除OSS文件数量: {}",
                tenantId, deletedCount, fileIds.size());
        return deletedCount;
    }

    @Override
    public int deleteNfzFilesByNfzId(Long nfzId) {
        // 根据业务逻辑，一个租户的所有禁飞区对应一个文件
        // 删除单个禁飞区时不应该删除文件，只有在同步时才重新生成文件
        // 这里保持原有逻辑，不删除文件
        log.info("根据禁飞区ID删除关联文件，禁飞区ID: {}，根据业务逻辑不删除文件", nfzId);
        return 0;
    }

    @Override
    public Long saveNfzFile(String name, String fileUrl, String checksum, Integer size, Long fileId, Long tenantId) {
        SystemDockNfzFileDO nfzFileDO = SystemDockNfzFileDO.builder()
                .name(name)
                .fileUrl(fileUrl)
                .checksum(checksum)
                .size(size)
                .fileId(fileId)
                .build();

        // 手动设置父类属性tenantId
        nfzFileDO.setTenantId(tenantId);

        nfzFileMapper.insert(nfzFileDO);
        log.info("保存禁飞区文件成功，文件ID: {}, 文件名: {}", nfzFileDO.getId(), name);
        return nfzFileDO.getId();
    }

    @Override
    public boolean existsByFileName(String fileName, Long tenantId) {

        return nfzFileMapper.exists(new LambdaQueryWrapper<SystemDockNfzFileDO>()
               .eq(SystemDockNfzFileDO::getTenantId, tenantId)
               .eq(SystemDockNfzFileDO::getName, fileName));
    }
}
