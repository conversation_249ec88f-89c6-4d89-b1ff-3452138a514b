package com.xinkongan.cloud.module.system.service.material;

import cn.hutool.core.collection.CollectionUtil;
import com.xinkongan.cloud.framework.common.exception.ServiceException;
import com.xinkongan.cloud.framework.env.config.SystemEnv;
import com.xinkongan.cloud.module.system.controller.admin.material.dto.ThreeModelUnzipCallbackDTO;
import com.xinkongan.cloud.module.system.dal.dataobject.material.MaterialDO;
import com.xinkongan.cloud.module.system.dal.mysql.material.MaterialMapper;
import com.xinkongan.cloud.module.system.dto.MaterialParseDTO;
import com.xinkongan.cloud.module.system.dto.MaterialParseSuccessNotifyDTO;
import com.xinkongan.cloud.module.system.enums.ErrorCodeConstants;
import com.xinkongan.cloud.module.system.enums.file.S3FileDirPrefixEnum;
import com.xinkongan.cloud.module.system.enums.material.MaterialParseStatus;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ThreeDModelMaterialParseHandler extends DefaultParseHandler implements ThreeDModelCallbackDealService {

    @Resource
    private SystemEnv systemEnv;

    @Resource
    private MaterialMapper materialMapper;

    @Override
    public void doHandler(MaterialParseDTO materialParse) {
        log.info("[处理三维素材解析任务]，参数为：{}", materialParse);
        MaterialDO material = materialMapper.selectById(materialParse.getId());
        if (material == null) {
            throw new ServiceException(ErrorCodeConstants.MATERIAL_ERROR_NOT_FOUND);
        }
        if (systemEnv.isMinio()) {
            // minio环境
            this.dealMinioThreeMaterialFileUrl(material);
            return;
        }
        material.setProcess(50);
        materialMapper.updateById(material);
        this.sendMaterialParseProcessNotify(material.getId(), 50);
    }

    /**
     * minio环境下处理三维素材的文件url
     */
    private void dealMinioThreeMaterialFileUrl(MaterialDO material) {

        String fileUrl = material.getUrl();
        String[] fields = fileUrl.split("\\/\\/");
        if (fields.length != 2) {
            throw new ServiceException(ErrorCodeConstants.THREE_DIEMEN_URL_ERROR);
        }
        String[] fieldss = fields[1].split("\\/");
        // 拼装json文件的访问路径
        String fileName = fieldss[fieldss.length - 1].replace(".zip", "");
        StringBuilder url = new StringBuilder(fields[0] + "//")
                .append(fieldss[0])
                .append("/")
                .append(fieldss[1])
                .append("/")
                .append(S3FileDirPrefixEnum.THREE_DIMENSION.getOriDir())
                .append("/")
                .append(fileName)
                //.append("/")
                //.append(material.getName().replace(".zip",""))
                .append("/tileset.json");
        material.setTileUrl(url.toString());
        material.setStatus(MaterialParseStatus.SUCCESS.getCode());
        materialMapper.updateById(material);
    }

    /**
     * aliyun环境下处理三维素材的文件url
     */
    private void dealAliyunThreeMaterialFileUrl(MaterialDO material) {
        String url = material.getUrl().replace(".zip", "/tileset.json");
        url = url.replace(S3FileDirPrefixEnum.THREE_DIMENSION.getOriDir(), S3FileDirPrefixEnum.THREE_DIMENSION_UNZIP.getOriDir());
        material.setTileUrl(url);
        material.setStatus(MaterialParseStatus.PARSING.getCode());
        materialMapper.updateById(material);
    }


    @Override
    public void dealThreeDModelUnzipCallback(ThreeModelUnzipCallbackDTO threeModelUnzipCallbackParams) {
        List<MaterialDO> materialInfos = materialMapper.selectList(MaterialDO::getObjectKey, threeModelUnzipCallbackParams.getObjectName());
        if (CollectionUtil.isEmpty(materialInfos)) {
            log.error("[未查询到相关素材信息]");
            return;
        }
        // 更新解析状态，并发送通知
        MaterialDO materialDO = materialInfos.get(0);
        if (systemEnv.isAliOss()) {
            // aliyun环境
            this.dealAliyunThreeMaterialFileUrl(materialDO);
        }
        materialDO.setStatus(MaterialParseStatus.SUCCESS.getCode());
        materialDO.setProcess(100);
        materialMapper.updateById(materialDO);

        MaterialParseSuccessNotifyDTO payload = MaterialParseSuccessNotifyDTO.builder()
                .tileUrl(materialDO.getTileUrl())
                .thumbnailUrl(materialDO.getJpgUrl())
                .materialInfo(materialDO)
                .build();

        // 发送通知
        this.sendMaterialParseProcessNotify(materialDO.getId(), 100);
        this.sendMaterialParseStatusNotify(materialDO.getId(), MaterialParseStatus.SUCCESS, payload);
    }
}
