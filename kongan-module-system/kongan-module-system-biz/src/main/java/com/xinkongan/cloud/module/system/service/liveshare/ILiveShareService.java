package com.xinkongan.cloud.module.system.service.liveshare;

import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.LiveShareReqVO;
import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.ShareBaseVO;
import com.xinkongan.cloud.module.system.controller.admin.liveshare.vo.WatchLiveVO;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
public interface ILiveShareService {

    /**
     * 直播分享
     *
     * @param reqVO 直播分享参数
     * @return 分享链接Key
     */
    String liveShare(LiveShareReqVO reqVO);

    /**
     * 获取直播分享信息
     *
     * @param shareKey 分享直播的唯一key
     * @return 直播分享信息
     */
    ShareBaseVO getShareInfo(String shareKey);

    /**
     * 通过密码获取观看直播信息
     *
     * @param shareKey 分享直播的唯一key
     * @param password 直播分享密码
     * @return 直播分享信息
     */
    WatchLiveVO getShareInfoByPassword(String shareKey, String password);

    /**
     * 删除分享
     *
     * @param dockSn 机场SN
     * @return 删除结果
     */
    int deleteShare(String dockSn);

}
