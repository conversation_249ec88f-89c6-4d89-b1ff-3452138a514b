package com.xinkongan.cloud.module.system.controller.admin.notice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Description 消息批量删除ReqVO
 * <AUTHOR>
 * @Date 2025/3/10 15:09
 */
@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "批量删除消息 ReqVO")
public class NoticeDeleteBatchReqVO {

    @Schema(description = "消息id列表")
    @NotEmpty(message = "消息ids不能为空")
    private List<Long> ids;
}