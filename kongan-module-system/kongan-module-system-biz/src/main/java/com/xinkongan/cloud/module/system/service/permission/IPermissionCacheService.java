package com.xinkongan.cloud.module.system.service.permission;


import com.xinkongan.cloud.module.system.controller.admin.auth.vo.AuthPermissionInfoRespVO;
import com.xinkongan.cloud.module.system.controller.admin.permission.vo.role.RoleRespVO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.MenuDO;
import com.xinkongan.cloud.module.system.dal.dataobject.permission.RoleDO;

import java.util.List;
import java.util.Set;

public interface IPermissionCacheService {

    /**
     * 根据用户id缓存中查询用户角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleDO> getUserRoleDosById(Long userId);


    /**
     * 根据用户ID 缓存中查询用户角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleRespVO> getUserRoleRespById(Long userId);


    /**
     * 根据用户id 查询该用户具有的角色id 集合
     *
     * @param userId 用户id
     * @return 角色id 集合
     */
    Set<Long> getRoleIdsByUserId(Long userId);


    /**
     * 根据用户id 查询用户具有的菜单列表
     *
     * @param userId 用户id
     * @return 菜单列表
     */
    List<AuthPermissionInfoRespVO.MenuVO> getUserMenusByUserId(Long userId);


    /**
     * 根据用户id 查询用户具有的菜单列表
     *
     * @param userId 用户id
     * @return 菜单列表
     */
    List<MenuDO> getUserMenuInfosByUserId(Long userId);


    /**
     * 获取用户的权限
     *
     * @param userId 用户id
     * @return 权限标识符列表
     */
    Set<String> getUserPermission(Long userId);


    /**
     * 获取用户的角色权限
     *
     * @param userId 用户id
     * @return 角色权限标识符
     */
    Set<String> getUserRolePermission(Long userId);
}
