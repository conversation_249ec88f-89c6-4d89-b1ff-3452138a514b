package com.xinkongan.cloud.module.system.controller.admin.posture.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

/**
 * @Description 一键起飞-起飞请求VO
 * <AUTHOR>
 * @Date 2024/10/10 11:43
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OneKeyTakeoffTakeoffReqVO {

    @Schema(description = "一键起飞机场sn")
    @NotNull(message = "机场sn不能为空")
    private String dockSn;

    @Range(min = -180, max = 180, message = "经度范围为 -180至180度")
    @NotNull(message = "经度不能为空")
    @Schema(description = "经度")
    private Double targetLongitude;

    @Range(min = -90, max = 90, message = "纬度范围为 -90至90度")
    @NotNull(message = "纬度不能为空")
    @Schema(description = "纬度")
    private Double targetLatitude;

    @Range(min = 2, max = 1500, message = "高度范围为 2-1500 m")
    @NotNull(message = "高度不能为空")
    @Schema(description = "高度（椭球高）2-1500m")
    private Double targetHeight;
}