package com.xinkongan.cloud.module.system.controller.admin.dock;

import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.system.controller.admin.dock.vo.nfz.NfzFileRespVO;
import com.xinkongan.cloud.module.system.service.dock.nfz.ISystemDockNfzFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 禁飞区文件管理Controller
 */
@Slf4j
@Validated
@RestController
@Tag(name = "管理后台 - 禁飞区文件管理")
@RequestMapping("/system/dock/nfz-file")
public class SystemDockNfzFileController {

    @Resource
    private ISystemDockNfzFileService nfzFileService;

    @GetMapping("/list/{tenantId}")
    @Operation(summary = "根据租户查询禁飞区文件列表")
    @Parameter(name = "tenantId", description = "租户ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('system:dock:nfz:file:query')")
    public CommonResult<List<NfzFileRespVO>> getNfzFilesByTenantId(@PathVariable("tenantId") Long tenantId) {
        List<NfzFileRespVO> nfzFileList = nfzFileService.getNfzFilesByTenantId(tenantId);
        return success(nfzFileList);
    }

    @DeleteMapping("/delete/tenant/{tenantId}")
    @Operation(summary = "根据租户删除禁飞区文件")
    @Parameter(name = "tenantId", description = "租户ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('system:dock:nfz:file:delete')")
    public CommonResult<Integer> deleteNfzFilesByTenantId(@PathVariable("tenantId") Long tenantId) {
        int deletedCount = nfzFileService.deleteNfzFilesByTenantId(tenantId);
        return success(deletedCount);
    }
}
