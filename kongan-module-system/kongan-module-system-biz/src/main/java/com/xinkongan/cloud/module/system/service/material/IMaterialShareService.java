package com.xinkongan.cloud.module.system.service.material;

import com.xinkongan.cloud.module.system.controller.admin.material.dto.MaterialPublicShareDTO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialShareResultVO;
import com.xinkongan.cloud.module.system.controller.admin.material.vo.MaterialViewShareVO;

public interface IMaterialShareService {


    MaterialViewShareVO createMaterialShareLink(MaterialPublicShareDTO materialPublicShare);


    /**
     * 判断该分享链接是否是公开分享
     *
     * @param shareKey
     * @return 判断结果
     */
    MaterialShareResultVO isPublic(Long shareKey);


    /**
     * 根据分享key获取分享信息
     *
     * @param shareKey
     * @return
     */
    MaterialViewShareVO getShareInfoByShareKey(Long shareKey);
}
