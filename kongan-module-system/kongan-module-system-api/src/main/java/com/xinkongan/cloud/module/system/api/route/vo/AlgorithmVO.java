package com.xinkongan.cloud.module.system.api.route.vo;

import com.xinkongan.cloud.framework.common.dto.GeoArea;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AlgorithmVO {

    @Schema(description = "算法id")
    private Long algorithmId;

    @Schema(description = "实例id")
    private Long exampleId;

    @Schema(description = "地理区域信息")
    private List<GeoArea> areas;

    public List<GeoArea> getAreas() {
        if (areas == null) {
            areas = new ArrayList<>();
        }
        return areas;
    }
}
