package com.xinkongan.cloud.module.system.api.material;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description 修改素材加载状态
 * <AUTHOR>
 * @Date 2025/5/12 11:37
 */
@Schema(description = "修改素材加载状态")
@Data
public class MaterialLoadingStatusDTO {

    @Schema(description = "素材ID")
    private Long materialId;

    @Schema(description = "加载状态 1已加载 0未加载")
    private Integer loadingStatus;
}