package com.xinkongan.cloud.module.system.enums.dict;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DictType {

    // 注册页面，行业字典信息
    INDUSTRY("industry", 1),

    // 系统默认字典配置
    DEFAULT_DICT_CONFIG("default_dict_config", 2),

    // 配置算法实例页面，算法实例处置方式信息
    WAY("way", 6),

    // 运营端-运营系统-设备管理-设备型号-设备类型
    MANAGE_DEVICE_TYPE("manage_device_type", 7),

    // 运营端-运营系统-租户管理-租户删除相关表配置
    MANAGE_TENANT_DELETE_TABLE("manage_tenant_delete_table", 8),

    // 指挥端查询协同端app标识
    MANAGE_APP_CODE("manage_app_code", 10),

    // 算法发现告警的处置动作
    HANDLING_ACTION("handling", 11),
    DEVICE_FULL("device_full", 9);

    private String dict;

    private Integer type;

    public static DictType findByType(Integer type) {
        for (DictType value : DictType.values()) {
            if (Objects.equals(value.getType(), type)) {
                return value;
            }
        }
        return null;
    }
}
