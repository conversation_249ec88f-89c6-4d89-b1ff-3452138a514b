package com.xinkongan.cloud.module.system.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 断点类型枚举
 * <AUTHOR>
 * @Date 2025/01/20
 */
@AllArgsConstructor
@Getter
public enum BreakpointTypeEnum {

    /**
     * 主动断点 - 用户主动触发的断点
     */
    ACTIVE(1, "主动断点"),
    
    /**
     * 被动断点 - 系统自动触发的断点（如电量不足）
     */
    PASSIVE(2, "被动断点"),
    
    /**
     * 异常断点 - 异常情况导致的断点（如设备故障）
     */
    EXCEPTION(3, "异常断点"),
    
    /**
     * 未知
     */
    UNKNOWN(-1, "未知");
    
    private final Integer code;
    private final String desc;
    
    public static BreakpointTypeEnum find(Integer code) {
        for (BreakpointTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return UNKNOWN;
    }
}
