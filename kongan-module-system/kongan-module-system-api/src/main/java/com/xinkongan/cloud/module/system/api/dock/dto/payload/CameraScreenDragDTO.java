package com.xinkongan.cloud.module.system.api.dock.dto.payload;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CameraScreenDragDTO {

    @NotNull
    @Schema(description = "机场SN")
    private String dockSn;

    @Pattern(regexp = "\\d+-\\d+-\\d+")
    @NotNull
    @Schema(description = "负载索引")
    private String payloadIndex;

    /**
     * true: 锁定偏航角，偏航角与无人机一起旋转。
     * false: 只有云台转动，但无人机不转动。
     */
    @Schema(description = "锁定偏航角")
    private Boolean locked;

    @Schema(description = "pitchSpeed")
    private Double pitchSpeed;

    /**
     * 仅当locked为false时有效。
     */
    @Schema(description = "仅当locked为false时有效。")
    private Double yawSpeed;

}
