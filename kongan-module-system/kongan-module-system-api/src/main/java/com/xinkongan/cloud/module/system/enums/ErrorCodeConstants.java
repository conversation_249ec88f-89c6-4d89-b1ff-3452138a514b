package com.xinkongan.cloud.module.system.enums;

import com.xinkongan.cloud.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== AUTH 模块 1-002-000-000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1_002_000_000, "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1_002_000_001, "登录失败，账号被禁用");
    ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode(1_002_000_004, "验证码不正确，原因：{}");
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(1_002_000_005, "未绑定账号，需要进行绑定");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1_002_000_007, "手机号不存在");
    ErrorCode AUTH_REGISTER_CAPTCHA_CODE_ERROR = new ErrorCode(1_002_000_008, "验证码不正确，原因：{}");
    ErrorCode AUTH_TENANT_IS_AUDIT_ERROR = new ErrorCode(1_002_000_009, "当前租户正在审核中");
    ErrorCode AUTH_TENANT_IS_DISABLE_ERROR = new ErrorCode(1_002_000_010, "当前租户已被禁用");
    ErrorCode RSA_PUBLIC_ERROR = new ErrorCode(1_002_000_011, "RSA 解密错误，请检查公钥是否正确");
    ErrorCode AUTH_TENANT_AUDIT_NOT_PASS_ERROR = new ErrorCode(1_002_000_011, "当前租户审核未通过");
    ErrorCode ACCESS_KEY_ERROR = new ErrorCode(1_002_000_012, "访问key不一致，不允许访问");
    ErrorCode TENANT_NAME_HAS_EXIST = new ErrorCode(1_002_000_013, "租户名称已经存在");
    // ========== 菜单模块 1-002-001-000 ==========
    ErrorCode MENU_NAME_DUPLICATE = new ErrorCode(1_002_001_000, "已经存在该名字的菜单");
    ErrorCode MENU_PARENT_NOT_EXISTS = new ErrorCode(1_002_001_001, "父菜单不存在");
    ErrorCode MENU_PARENT_ERROR = new ErrorCode(1_002_001_002, "不能设置自己为父菜单");
    ErrorCode MENU_NOT_EXISTS = new ErrorCode(1_002_001_003, "菜单不存在");
    ErrorCode MENU_EXISTS_CHILDREN = new ErrorCode(1_002_001_004, "存在子菜单，无法删除");
    ErrorCode MENU_PARENT_NOT_DIR_OR_MENU = new ErrorCode(1_002_001_005, "父菜单的类型必须是目录或者菜单");

    // ========== 角色模块 1-002-002-000 ==========
    ErrorCode ROLE_NOT_EXISTS = new ErrorCode(1_002_002_000, "角色不存在");
    ErrorCode ROLE_NAME_DUPLICATE = new ErrorCode(1_002_002_001, "已经存在名为【{}】的角色");
    ErrorCode ROLE_CODE_DUPLICATE = new ErrorCode(1_002_002_002, "已经存在标识为【{}】的角色");
    ErrorCode ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE = new ErrorCode(1_002_002_003, "不能操作类型为系统内置的角色");
    ErrorCode ROLE_IS_DISABLE = new ErrorCode(1_002_002_004, "名字为【{}】的角色已被禁用");
    ErrorCode ROLE_ADMIN_CODE_ERROR = new ErrorCode(1_002_002_005, "标识【{}】不能使用");
    ErrorCode SYSTEM_ADMIN_ROLE_NOT_DEL_UPDATE = new ErrorCode(1_002_002_006, "租户管理员角色不能被删除更改");
    ErrorCode ROLE_HAS_BIND_USERS = new ErrorCode(1_002_002_007, "当前角色已绑定账号，无法删除");

    // ========== 用户模块 1-002-003-000 ==========
    ErrorCode USER_USERNAME_EXISTS = new ErrorCode(1_002_003_000, "用户账号已经存在");
    ErrorCode USER_MOBILE_EXISTS = new ErrorCode(1_002_003_001, "手机号已经存在");
    ErrorCode USER_EMAIL_EXISTS = new ErrorCode(1_002_003_002, "邮箱已经存在");
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_002_003_003, "用户不存在");
    ErrorCode USER_IMPORT_LIST_IS_EMPTY = new ErrorCode(1_002_003_004, "导入用户数据不能为空！");
    ErrorCode USER_PASSWORD_FAILED = new ErrorCode(1_002_003_005, "用户密码校验失败");
    ErrorCode USER_IS_DISABLE = new ErrorCode(1_002_003_006, "名字为【{}】的用户已被禁用");
    ErrorCode USER_COUNT_MAX = new ErrorCode(1_002_003_008, "创建用户失败，原因：超过租户最大租户配额({})！");
    ErrorCode USER_IMPORT_INIT_PASSWORD = new ErrorCode(1_002_003_009, "初始密码不能为空");
    ErrorCode NOT_DEL_ONE_SELE_ACCOUNT = new ErrorCode(1_002_003_010, "不能删除自己");

    // ========== 部门模块 1-002-004-000 ==========
    ErrorCode DEPT_NAME_DUPLICATE = new ErrorCode(1_002_004_000, "已经存在该名字的组织");
    ErrorCode DEPT_PARENT_NOT_EXITS = new ErrorCode(1_002_004_001, "父级组织不存在");
    ErrorCode DEPT_NOT_FOUND = new ErrorCode(1_002_004_002, "当前组织不存在");
    ErrorCode DEPT_EXITS_CHILDREN = new ErrorCode(1_002_004_003, "存在子组织，无法删除");
    ErrorCode DEPT_PARENT_ERROR = new ErrorCode(1_002_004_004, "不能设置自己为父组织");
    ErrorCode DEPT_NOT_ENABLE = new ErrorCode(1_002_004_006, "组织({})不处于开启状态，不允许选择");
    ErrorCode DEPT_PARENT_IS_CHILD = new ErrorCode(1_002_004_007, "不能设置自己的子组织为父组织");
    ErrorCode DEPT_LEVEL_EXCEED_MAX_LEVEL = new ErrorCode(1_002_004_008, "组织层级已经达到最高，不能继续添加");
    ErrorCode DEPT_MOVE_COMMAND_ERROR = new ErrorCode(1_002_004_009, "移动操作指令错误");
    ErrorCode DEPT_NAME_NOT_EMPTY = new ErrorCode(1_002_004_010, "组织名称不能为空");
    ErrorCode DEPT_AND_CHILD_DEPT_EXIST_JOB_EXEC = new ErrorCode(1_002_004_011, "当前组织及其子组织存在任务正在执行中");
    ErrorCode DEPT_AND_CHILD_DEPT_EXIST_DEVICE_EXEC = new ErrorCode(1_002_004_012, "当前组织及其子组织存在机场设备正在执行中");
    ErrorCode THE_ORGANIZATION_LIST_IS_NOT_VALID = new ErrorCode(1_002_004_013, "组织列表不合法");

    // ========== 岗位模块 1-002-005-000 ==========
    ErrorCode POST_NOT_FOUND = new ErrorCode(1_002_005_000, "当前岗位不存在");
    ErrorCode POST_NOT_ENABLE = new ErrorCode(1_002_005_001, "岗位({}) 不处于开启状态，不允许选择");
    ErrorCode POST_NAME_DUPLICATE = new ErrorCode(1_002_005_002, "已经存在该名字的岗位");
    ErrorCode POST_CODE_DUPLICATE = new ErrorCode(1_002_005_003, "已经存在该标识的岗位");

    // ========== 违禁词 1-002-006-000 ==========
    ErrorCode BANNED_WORD_SAVE_ERROR = new ErrorCode(1_002_006_001, "违禁词保存失败");

    // ========== 字典数据 1-002-007-000 ==========
    ErrorCode DICT_DATA_NOT_EXISTS = new ErrorCode(1_002_007_001, "当前字典数据不存在");
    ErrorCode DICT_DATA_NOT_ENABLE = new ErrorCode(1_002_007_002, "字典数据({})不处于开启状态，不允许选择");
    ErrorCode DICT_DATA_VALUE_DUPLICATE = new ErrorCode(1_002_007_003, "已经存在该值的字典数据");
    ErrorCode DICT_TYPE_ERROR = new ErrorCode(1_002_007_004, "字典类型错误，请检查字典类型");
    ErrorCode DICT_TYPE_REPEAT_ERROR = new ErrorCode(1_002_007_005, "字典名称重复，该类型已存在该字典");
    ErrorCode DICT_CODE_NOT_EMPTY_ERROR = new ErrorCode(1_002_007_006, "字典编码不能为空");
    ErrorCode DICT_CODE_ILLEGAL_ERROR = new ErrorCode(1_002_007_007, "字典编码不能合法");

    // ========== 通知公告 1-002-008-000 ==========
    ErrorCode NOTICE_NOT_FOUND = new ErrorCode(1_002_008_001, "当前通知公告不存在");
    ErrorCode NOTICE_SAVE_FAIL = new ErrorCode(1_002_008_002, "保存失败");

    // ========== 短信渠道 1-002-011-000 ==========
    ErrorCode SMS_CHANNEL_NOT_EXISTS = new ErrorCode(1_002_011_000, "短信渠道不存在");
    ErrorCode SMS_CHANNEL_DISABLE = new ErrorCode(1_002_011_001, "短信渠道不处于开启状态，不允许选择");
    ErrorCode SMS_CHANNEL_HAS_CHILDREN = new ErrorCode(1_002_011_002, "无法删除，该短信渠道还有短信模板");

    // ========== 短信模板 1-002-012-000 ==========
    ErrorCode SMS_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_012_000, "短信模板不存在");
    ErrorCode SMS_TEMPLATE_CODE_DUPLICATE = new ErrorCode(1_002_012_001, "已经存在编码为【{}】的短信模板");
    ErrorCode SMS_TEMPLATE_API_ERROR = new ErrorCode(1_002_012_002, "短信 API 模板调用失败，原因是：{}");
    ErrorCode SMS_TEMPLATE_API_AUDIT_CHECKING = new ErrorCode(1_002_012_003, "短信 API 模版无法使用，原因：审批中");
    ErrorCode SMS_TEMPLATE_API_AUDIT_FAIL = new ErrorCode(1_002_012_004, "短信 API 模版无法使用，原因：审批不通过，{}");
    ErrorCode SMS_TEMPLATE_API_NOT_FOUND = new ErrorCode(1_002_012_005, "短信 API 模版无法使用，原因：模版不存在");

    // ========== 短信发送 1-002-013-000 ==========
    ErrorCode SMS_SEND_MOBILE_NOT_EXISTS = new ErrorCode(1_002_013_000, "手机号不存在");
    ErrorCode SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS = new ErrorCode(1_002_013_001, "模板参数({})缺失");
    ErrorCode SMS_SEND_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_013_002, "短信模板不存在");

    // ========== 短信验证码 1-002-014-000 ==========
    ErrorCode SMS_CODE_NOT_FOUND = new ErrorCode(1_002_014_000, "验证码不存在");
    ErrorCode SMS_CODE_EXPIRED = new ErrorCode(1_002_014_001, "验证码已过期");
    ErrorCode SMS_CODE_USED = new ErrorCode(1_002_014_002, "验证码已使用");
    ErrorCode SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY = new ErrorCode(1_002_014_004, "超过每日短信发送数量");
    ErrorCode SMS_CODE_SEND_TOO_FAST = new ErrorCode(1_002_014_005, "短信发送过于频繁");

    // ========== 租户信息 1-002-015-000 ==========
    ErrorCode TENANT_NOT_EXISTS = new ErrorCode(1_002_015_000, "租户不存在");
    ErrorCode TENANT_DISABLE = new ErrorCode(1_002_015_001, "名字为【{}】的租户已被禁用");
    ErrorCode TENANT_EXPIRE = new ErrorCode(1_002_015_002, "您的组织已过期，请联系管理员续费！");
    ErrorCode TENANT_CAN_NOT_UPDATE_SYSTEM = new ErrorCode(1_002_015_003, "系统租户不能进行修改、删除等操作！");
    ErrorCode TENANT_NAME_DUPLICATE = new ErrorCode(1_002_015_004, "名字为【{}】的租户已存在");
    ErrorCode TENANT_WEBSITE_DUPLICATE = new ErrorCode(1_002_015_005, "域名为【{}】的租户已存在");
    ErrorCode TENANT_CONTEXT_ERROR = new ErrorCode(1_002_015_007, "租户上下文为空，请补充租户线程上下文信息");
    ErrorCode TENANTS_ARE_NOT_AVAILABLE = new ErrorCode(1_002_015_007, "租户不可用，请联系运营人员");

    // ========== 租户套餐 1-002-016-000 ==========
    ErrorCode TENANT_PACKAGE_NOT_EXISTS = new ErrorCode(1_002_016_000, "租户套餐不存在");
    ErrorCode TENANT_PACKAGE_USED = new ErrorCode(1_002_016_001, "租户正在使用该套餐，请给租户重新设置套餐后再尝试删除");
    ErrorCode TENANT_PACKAGE_DISABLE = new ErrorCode(1_002_016_002, "名字为【{}】的租户套餐已被禁用");
    ErrorCode TENANT_STORAGE_FULL = new ErrorCode(1_002_016_003, "操作失败，当前组织存储空间不足，请联系管理员进行扩容");
    ErrorCode DEVICE_LIMIT_EXCEEDED = new ErrorCode(1_002_016_004, "操作失败，当前组织存储空间不足，请联系管理员进行扩容");

    // ========== 社交用户 1-002-018-000 ==========
    ErrorCode SOCIAL_USER_AUTH_FAILURE = new ErrorCode(1_002_018_000, "社交授权失败，原因是：{}");
    ErrorCode SOCIAL_USER_NOT_FOUND = new ErrorCode(1_002_018_001, "社交授权失败，找不到对应的用户");

    ErrorCode SOCIAL_CLIENT_WEIXIN_MINI_APP_PHONE_CODE_ERROR = new ErrorCode(1_002_018_200, "获得手机号失败");
    ErrorCode SOCIAL_CLIENT_WEIXIN_MINI_APP_QRCODE_ERROR = new ErrorCode(1_002_018_201, "获得小程序码失败");
    ErrorCode SOCIAL_CLIENT_WEIXIN_MINI_APP_SUBSCRIBE_TEMPLATE_ERROR = new ErrorCode(1_002_018_202, "获得小程序订阅消息模版失败");
    ErrorCode SOCIAL_CLIENT_WEIXIN_MINI_APP_SUBSCRIBE_MESSAGE_ERROR = new ErrorCode(1_002_018_203, "发送小程序订阅消息失败");
    ErrorCode SOCIAL_CLIENT_NOT_EXISTS = new ErrorCode(1_002_018_210, "社交客户端不存在");
    ErrorCode SOCIAL_CLIENT_UNIQUE = new ErrorCode(1_002_018_211, "社交客户端已存在配置");


    // ========== OAuth2 客户端 1-002-020-000 =========
    ErrorCode OAUTH2_CLIENT_NOT_EXISTS = new ErrorCode(1_002_020_000, "OAuth2 客户端不存在");
    ErrorCode OAUTH2_CLIENT_EXISTS = new ErrorCode(1_002_020_001, "OAuth2 客户端编号已存在");
    ErrorCode OAUTH2_CLIENT_DISABLE = new ErrorCode(1_002_020_002, "OAuth2 客户端已禁用");
    ErrorCode OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS = new ErrorCode(1_002_020_003, "不支持该授权类型");
    ErrorCode OAUTH2_CLIENT_SCOPE_OVER = new ErrorCode(1_002_020_004, "授权范围过大");
    ErrorCode OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH = new ErrorCode(1_002_020_005, "无效 redirect_uri: {}");
    ErrorCode OAUTH2_CLIENT_CLIENT_SECRET_ERROR = new ErrorCode(1_002_020_006, "无效 client_secret: {}");

    // ========== OAuth2 授权 1-002-021-000 =========
    ErrorCode OAUTH2_GRANT_CLIENT_ID_MISMATCH = new ErrorCode(1_002_021_000, "client_id 不匹配");
    ErrorCode OAUTH2_GRANT_REDIRECT_URI_MISMATCH = new ErrorCode(1_002_021_001, "redirect_uri 不匹配");
    ErrorCode OAUTH2_GRANT_STATE_MISMATCH = new ErrorCode(1_002_021_002, "state 不匹配");

    // ========== OAuth2 授权 1-002-022-000 =========
    ErrorCode OAUTH2_CODE_NOT_EXISTS = new ErrorCode(1_002_022_000, "code 不存在");
    ErrorCode OAUTH2_CODE_EXPIRE = new ErrorCode(1_002_022_001, "code 已过期");

    // ========== 邮箱账号 1-002-023-000 ==========
    ErrorCode MAIL_ACCOUNT_NOT_EXISTS = new ErrorCode(1_002_023_000, "邮箱账号不存在");
    ErrorCode MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS = new ErrorCode(1_002_023_001, "无法删除，该邮箱账号还有邮件模板");

    // ========== 邮件模版 1-002-024-000 ==========
    ErrorCode MAIL_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_024_000, "邮件模版不存在");
    ErrorCode MAIL_TEMPLATE_CODE_EXISTS = new ErrorCode(1_002_024_001, "邮件模版 code({}) 已存在");

    // ========== 邮件发送 1-002-025-000 ==========
    ErrorCode MAIL_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(1_002_025_000, "模板参数({})缺失");
    ErrorCode MAIL_SEND_MAIL_NOT_EXISTS = new ErrorCode(1_002_025_001, "邮箱不存在");

    // ========== API 错误日志 1_002_026_000 ==========
    ErrorCode API_ERROR_LOG_NOT_FOUND = new ErrorCode(1_002_026_000, "API 错误日志不存在");
    ErrorCode API_ERROR_LOG_PROCESSED = new ErrorCode(1_002_026_001, "API 错误日志已处理");

    // ========= 文件相关 1-002-027-000 =================
    ErrorCode FILE_PATH_EXISTS = new ErrorCode(1_002_027_000, "文件路径已存在");
    ErrorCode FILE_NOT_EXISTS = new ErrorCode(1_002_027_001, "文件不存在");
    ErrorCode FILE_IS_EMPTY = new ErrorCode(1_002_027_002, "文件为空");
    ErrorCode FILE_CONFIG_NOT_EXISTS = new ErrorCode(1_002_027_003, "文件配置不存在");
    ErrorCode FILE_CONFIG_DELETE_FAIL_MASTER = new ErrorCode(1_002_027_004, "该文件配置不允许删除，原因：它是主配置，删除会导致无法上传文件");
    ErrorCode FILE_NAME_NOT_EMPTY = new ErrorCode(1_002_027_005, "文件name不能为空");
    ErrorCode FILE_PATH_NOT_EMPTY = new ErrorCode(1_002_027_006, "文件path不能为空");
    ErrorCode FILE_URL_NOT_EMPTY = new ErrorCode(1_002_027_007, "文件url不能为空");
    ErrorCode FILE_URL_NOT_MATCH = new ErrorCode(1_002_027_008, "文件解析失败");
    ErrorCode FILE_STS_NOT_EXISTS = new ErrorCode(1_002_027_009, "文件上传凭证不能为空");

    ErrorCode FILE_DELETE_PARAM_NULL = new ErrorCode(1_002_027_009, "文件模块，调用参数为空");
    ErrorCode FILE_DEL_OVERSTEPPING_AUTHORITY = new ErrorCode(1_002_027_010, "文件模块，越权无法删除");
    ErrorCode FILE_DELETE_FAILURE = new ErrorCode(1_002_027_011, "文件模块，文件删除失败");

    // ========== 参数配置 1-002-028-000 ==========
    ErrorCode CONFIG_NOT_EXISTS = new ErrorCode(1_002_028_001, "参数配置不存在");
    ErrorCode CONFIG_KEY_DUPLICATE = new ErrorCode(1_002_028_002, "参数配置 key 重复");
    ErrorCode CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE = new ErrorCode(1_002_028_003, "不能删除类型为系统内置的参数配置");
    ErrorCode CONFIG_GET_VALUE_ERROR_IF_VISIBLE = new ErrorCode(1_002_028_004, "获取参数配置失败，原因：不允许获取不可见配置");
    ErrorCode CONFIG_ERROR = new ErrorCode(1_002_028_005, "配置错误[{}]");

    // ========== 参数配置 1-002-029-000 ==========
    ErrorCode WEBSOCKET_AUTHENTICATION_FAILURE = new ErrorCode(1_002_029_001, "WebSocket认证失败");

    // ========== 组织相关 1-002-030-000 ==========
    ErrorCode DEPT_BINDING_CODE_BUILD_FAILED = new ErrorCode(1_002_030_001, "组织绑定码生成失败");

    // ========== 设备相关 1-002-031-000 ==========
    ErrorCode THE_DEVICE_CONNECTION_INFORMATION_DOES_NOT_EXIST = new ErrorCode(1_002_031_001, "设备连接信息不存在");
    ErrorCode DEVICE_DOCK_NOT_EXISTS = new ErrorCode(1_002_031_002, "机场不存在");
    ErrorCode DEVICE_SHARE_NOT_ENABLED = new ErrorCode(1_002_031_003, "该机场未开启共享");
    ErrorCode UNCLASSIFIED_MESSAGES = new ErrorCode(1_002_031_004, "未分类的消息，无法处理");
    ErrorCode DOCK_COVERAGE_NOT_EXISTS = new ErrorCode(1_002_031_005, "未找到机场可飞范围");
    ErrorCode DOCK_COVERAGE_DICT_NOT_EXISTS = new ErrorCode(1_002_031_006, "未找到机场可飞范围默认字典值");
    ErrorCode DOCK_COVERAGE_RADIUS_ERROR = new ErrorCode(1_002_031_007, "半径范围错误[{}]");
    ErrorCode DOCK_CONFIG_NOT_EXISTS = new ErrorCode(1_002_031_008, "未找到机场设置");
    ErrorCode DOCK_CONFIG_DICT_NOT_EXISTS = new ErrorCode(1_002_031_009, "未找到机场设置默认字典值");
    ErrorCode DOCK_CONFIG_WORKING_CAN_NOT_CLOSE = new ErrorCode(1_002_031_010, "该机场任务作业中，请勿关闭共享");
    ErrorCode DOCK_BIND_FAILED = new ErrorCode(1_002_031_010, "机场绑定失败");
    ErrorCode UNPROCESSED_DEVICE_TYPE = new ErrorCode(1_002_031_011, "未处理的设备类型");
    ErrorCode LIVE_BROADCAST_NOT_ENABLED = new ErrorCode(1_002_031_012, "直播未开启");
    ErrorCode DEVICE_NOT_EXISTS = new ErrorCode(1_002_031_013, "设备不存在");
    ErrorCode CAMERA_NOT_EXISTS = new ErrorCode(1_002_031_014, "相机不存在");
    ErrorCode UNKNOWN_DEVICE = new ErrorCode(1_002_031_015, "未知设备");
    ErrorCode THE_DEVICE_IS_INSTALLED_ON_TOP = new ErrorCode(1_002_031_016, "设备已置顶");
    ErrorCode THE_DEVICE_IS_NOT_INSTALLED_ON_TOP = new ErrorCode(1_002_031_017, "设备未置顶");
    ErrorCode NO_DEVICE_CONTROL_PERMISSION = new ErrorCode(1_002_031_018, "无设备控制权限");
    ErrorCode NO_LIVE_CONTROL_PERMISSION = new ErrorCode(1_002_031_018, "无直播控制权限");
    ErrorCode PAYLOAD_CONTROL_PARAMETERS_ARE_INVALID = new ErrorCode(1_002_031_019, "负载控制参数不合法");
    ErrorCode ROUTE_SUSPENSION_FAILURE = new ErrorCode(1_002_031_020, "航线暂停失败");
    ErrorCode ROUTE_RECOVER_FAILURE = new ErrorCode(1_002_031_021, "航线恢复失败");
    ErrorCode REMOTE_CONTROL_FAILURE = new ErrorCode(1_002_031_022, "远程控制失败");
    ErrorCode DOCK_STATUS_NOT_SUPPORT_DEBUGGING = new ErrorCode(1_002_031_023, "机场状态不支持调试");
    ErrorCode LIVE_SHARING_FAILURE = new ErrorCode(1_002_031_024, "直播分享失败");
    ErrorCode LIVE_SHARE_DOES_NOT_EXIST = new ErrorCode(1_002_031_025, "直播分享不存在");
    ErrorCode LIVE_SHARE_PASSWORD_IS_INCORRECT = new ErrorCode(1_002_031_026, "查看直播密码错误");
    ErrorCode DOCK_NOT_IDLE = new ErrorCode(1_002_031_027, "当前机场处于非空闲状态");
    // ========== 任务相关 1-002-032-000 ==========
    ErrorCode APPLY_NOT_EXISTS = new ErrorCode(1_002_032_000, "申请不存在");
    ErrorCode TASK_APPLY_STATUS_NOT_PENDING = new ErrorCode(1_002_032_001, "操作失败，该任务状态已变更");
    ErrorCode JOB_NOT_EXISTS = new ErrorCode(1_002_032_002, "任务不存在");
    ErrorCode JOB_STATUS_NOT_EXISTS = new ErrorCode(1_002_032_003, "任务状态不存在");
    ErrorCode JOB_STATUS_CAN_NOT_UPDATE = new ErrorCode(1_002_032_004, "当前任务状态无法修改");
    ErrorCode TASK_MODE_UNKNOWN = new ErrorCode(1_002_032_005, "未知的任务模式");
    ErrorCode TASK_PARAMS_ERROR = new ErrorCode(1_002_032_006, "任务参数错误[{}]");
    ErrorCode TASK_CREATE_ERROR = new ErrorCode(1_002_032_007, "任务创建失败[{}]");
    ErrorCode JOB_CAN_NOT_DELETE = new ErrorCode(1_002_032_008, "任务正在执行中或正在审批中，无法删除");
    ErrorCode JOB_CAN_NOT_DELETE_BATCH = new ErrorCode(1_002_032_009, "当前有任务正在执行中或正在审批中，无法删除");
    ErrorCode JOB_CAN_NOT_CANCEL = new ErrorCode(1_002_032_010, "当前任务状态无法取消执行,[{}]");
    ErrorCode JOB_CAN_NOT_FINISH = new ErrorCode(1_002_032_011, "当前任务状态无法结束任务,[{}]");
    ErrorCode JOB_NOT_EXECUTION = new ErrorCode(1_002_032_012, "任务不在待执行状态，无法执行定时任务");
    ErrorCode TASK_EXEC_TIME_BEFORE_NOW_ERROR = new ErrorCode(1_002_032_013, "下发失败，执行时间不能早于当前时间");
    ErrorCode THE_STATUS_DOES_NOT_SUPPORT_TAKEOFF = new ErrorCode(1_002_032_014, "当前状态不支持起飞");
    ErrorCode NO_TASK = new ErrorCode(1_002_032_015, "无法获取任务信息，请检查任务状态");
    ErrorCode ALREADY_REQUESTED = new ErrorCode(1_002_032_016, "您已申请远程控制，请耐心等待");
    ErrorCode JOB_SCENE_NO_IMPLEMENTS = new ErrorCode(1_002_032_017, "当前任务场景未实现");
    ErrorCode JOB_FLY_ALREADY_HAVE = new ErrorCode(1_002_032_018, "当前存在为处理完的任务信息");
    ErrorCode JOB_FLY_NOT_EXISTS = new ErrorCode(1_002_032_019, "飞行任务不存在");

    // ========== 断点续飞相关 1-002-032-020 ==========
    ErrorCode BREAKPOINT_NOT_EXISTS = new ErrorCode(1_002_032_020, "断点信息不存在");
    ErrorCode BREAKPOINT_ALREADY_RESUMED = new ErrorCode(1_002_032_021, "断点已续飞，无法重复操作");
    ErrorCode BREAKPOINT_RESUME_FAILED = new ErrorCode(1_002_032_022, "断点续飞任务创建失败");
    ErrorCode BREAKPOINT_INVALID_STATUS = new ErrorCode(1_002_032_023, "断点状态无效，无法进行续飞操作");
    ErrorCode BREAKPOINT_CREATE_FAILED = new ErrorCode(1_002_032_024, "断点信息创建失败");


    // ========== 航线相关 1-002-033-000 ==========

    ErrorCode ROUTE_NOT_EXISTS = new ErrorCode(1_002_033_000, "航线不存在");
    ErrorCode ROUTE_NAME_HAS_REPEAT = new ErrorCode(1_002_033_001, "航线名称已重复");
    ErrorCode ROUTE_IS_LOCK_STATUS = new ErrorCode(1_002_033_002, "当前航线处于锁定状态，不允许编辑");
    ErrorCode ROUTE_DURATION_IS_NULL = new ErrorCode(1_002_033_003, "航线预计时长为空");
    ErrorCode ROUTE_LOCK_ONLY_BY_CREATOR = new ErrorCode(1_002_033_004, "航线仅支持被创建人锁定");
    ErrorCode ROUTE_UNLOCK_ONLY_BY_CREATOR = new ErrorCode(1_002_033_005, "航线仅支持被创建人解锁");
    ErrorCode ROUTE_HAS_BY_JOB_BIND_NOT_DEL = new ErrorCode(1_002_033_006, "当前航线已关联任务，无法删除，如需删除，请先删除关联的任务");
    ErrorCode NO_FLIGHT_ROUTE_EXECUTION_AUTHORITY = new ErrorCode(1_002_033_007, "无当前任务航线执行权限");
    ErrorCode ROUTE_IMPORT_FAILED = new ErrorCode(1_002_033_008, "航线导入失败：{}");
    ErrorCode ROUTE_IMPORT_PARSE_ERROR = new ErrorCode(1_002_033_009, "KMZ文件解析失败，请检查文件格式是否正确");
    ErrorCode ROUTE_IMPORT_CONVERT_ERROR = new ErrorCode(1_002_033_010, "航线数据转换失败，请检查文件内容是否完整");


    // ========== 天气相关 1-002-034-000 ==========

    ErrorCode WEATHER_SERVER_ERROR = new ErrorCode(1_002_034_000, "天气服务异常");

    // ========== 安全检查相关 1-002-035-000 ==========
    ErrorCode TASK_HAS_EXCEED_COVERAGE = new ErrorCode(1_002_035_001, "当前下发航线存在航点超过设备可飞范围");
    ErrorCode TASK_PREDICT_DISTANCE_EXCEED = new ErrorCode(1_002_035_002, "预计航线里程过长");
    ErrorCode TASK_PREDICT_TIME_EXCEED = new ErrorCode(1_002_035_003, "预计飞行时长过长，任务可能需要多次执行");


    // ========== 素材解析相关 1-002-035-000 ==========
    ErrorCode MATERIAL_ERROR_NOT_FOUND = new ErrorCode(1_002_035_000, "素材不存在，id错误");

    ErrorCode SYSTEM_ERROR_PARAM_NULL = new ErrorCode(1_002_035_001, "素材模块异常，参数为空");

    ErrorCode MATERIAL_IS_NULL = new ErrorCode(1_002_035_002, "该素材不存在");

    // ========== 禁飞区相关 1-002-036-000 ==========
    ErrorCode NFZ_NOT_EXISTS = new ErrorCode(1_002_036_000, "禁飞区不存在");
    ErrorCode NFZ_NAME_EXISTS = new ErrorCode(1_002_036_001, "禁飞区名称已存在");
    ErrorCode NFZ_FILE_NOT_EXISTS = new ErrorCode(1_002_036_002, "禁飞区文件不存在");
    ErrorCode NFZ_SYNC_FAILED = new ErrorCode(1_002_036_003, "禁飞区同步到机场失败");

    // ========== 限飞区相关 1-002-037-000 ==========
    ErrorCode FLIGHT_RESTRICTION_ZONE_NOT_EXISTS = new ErrorCode(1_002_037_000, "限飞区不存在");
    ErrorCode FLIGHT_RESTRICTION_ZONE_INVALID_SHAPE_TYPE = new ErrorCode(1_002_037_001, "无效的几何形状类型");
    ErrorCode FLIGHT_RESTRICTION_ZONE_INVALID_ZONE_TYPE = new ErrorCode(1_002_037_002, "无效的区域类型");
    ErrorCode FLIGHT_RESTRICTION_ZONE_INVALID_HEIGHT_RANGE = new ErrorCode(1_002_037_003, "最小高度不能大于等于最大高度");
    ErrorCode FLIGHT_RESTRICTION_ZONE_INVALID_POLYGON_DATA = new ErrorCode(1_002_037_004, "无效的多边形数据格式");
    ErrorCode FLIGHT_RESTRICTION_ZONE_MISSING_POLYGON_DATA = new ErrorCode(1_002_037_005, "多边体类型必须提供多边形数据");
    ErrorCode FLIGHT_RESTRICTION_ZONE_MISSING_CIRCLE_DATA = new ErrorCode(1_002_037_006, "圆柱体类型必须提供圆心坐标和半径");
    ErrorCode FLIGHT_RESTRICTION_ZONE_INVALID_COORDINATES = new ErrorCode(1_002_037_007, "无效的坐标数据");
    ErrorCode FLIGHT_RESTRICTION_ZONE_INVALID_RADIUS = new ErrorCode(1_002_037_008, "半径必须大于0");
    ErrorCode FLIGHT_RESTRICTION_ZONE_INVALID_AREA = new ErrorCode(1_002_037_009, "面积必须大于0");

    ErrorCode THREE_DIEMEN_URL_ERROR = new ErrorCode(1_002_035_003, "素材信息格式不正确，无法切分");
    ErrorCode MATERIAL_CONTRAST_RECORD_ERROR_NOT_FOUND = new ErrorCode(1_002_035_004, "id错误，素材对比记录不存在");
    ErrorCode MATERIAL_SHARE_RECORD_NOT_FOUND = new ErrorCode(1_002_035_005, "素材分享记录不存在，请检查分享url");
    ErrorCode MATERIAL_SHARE_PASSWORD_ERROR = new ErrorCode(1_002_035_006, "素材分享秘钥错误");
    ErrorCode MATERIAL_SHARE_PASSWORD_EMPTY = new ErrorCode(1_002_035_007, "素材分享密码不能为空");
    ErrorCode MATERIAL_SHARE_EXPIRE_PASS = new ErrorCode(1_002_035_008, "素材分享链接已过期");

    // ========== 飞行记录相关 1-002-037-000 ==========

    ErrorCode FLY_RECORD_NOT_FOUND = new ErrorCode(1_002_037_000, "飞行记录不存在");
    ErrorCode FLY_RECORD_PARAM_ERROR = new ErrorCode(1_002_037_001, "飞行记录参数错误,[{}]");
    ErrorCode FLY_RECORD_FILE_NOT_EXISTS = new ErrorCode(1_002_037_002, "飞行记录文件不存在");
    ErrorCode FLY_RECORD_DELETE_TYPE_ERROR = new ErrorCode(1_002_037_003, "删除内容错误");
    ErrorCode FLY_RECORD_DELETE_PARAM_ERROR = new ErrorCode(1_002_037_004, "参数类型错误,[{}]");


    // ========== 负载控制相关1-002-038-000 ==========
    ErrorCode FILE_NOT_EXIST = new ErrorCode(1_002_038_000, "截图文件不存在");
    ErrorCode MEASURE_TARGET_NOT_EXIST = new ErrorCode(1_002_038_001, "激光打点不存在");
    ErrorCode MEASURE_TARGET_DELETE_FAILED = new ErrorCode(1_002_038_002, "激光打点删除失败");
    ErrorCode MEASURE_TARGET_QUERY_PARAM_CAN_NOT_NULL = new ErrorCode(1_002_038_003, "激光打点查询条件不能为空");
    ErrorCode PSDK_PLAY_MODE_SET_ERROR = new ErrorCode(1_002_038_004, "喊话器播放模式设置失败");
    ErrorCode SPEAKER_PLAY_VOLUME_SET_ERROR = new ErrorCode(1_002_038_005, "喊话器音量设置失败");
    ErrorCode SPEAKER_TTS_PLAY_ERROR = new ErrorCode(1_002_038_006, "播放tts文本失败");
    ErrorCode SPEAKER_AUDIO_PLAY_START_ERROR = new ErrorCode(1_002_038_007, "音频播放失败");
    ErrorCode SPEAKER_REPLAY_ERROR = new ErrorCode(1_002_038_008, "重新播放出现异常");
    ErrorCode SPEAKER_PLAY_STOP_ERROR = new ErrorCode(1_002_038_009, "停止播放出现异常");
    ErrorCode SAVE_VOICE_NAME_EXISTS = new ErrorCode(1_002_038_010, "名称已存在，请重新编辑名称");
    ErrorCode VOICE_NOT_EXISTS = new ErrorCode(1_002_038_011, "喊话器语音不存在");
    ErrorCode VOICE_SIZE_OVERFLOW = new ErrorCode(1_002_038_012, "喊话文件超过200MB");
    ErrorCode VOICE_MP3_TO_PCM_ERROR = new ErrorCode(1_002_038_013, "喊话文件转化异常，请重新上传");
    ErrorCode SPEAKER_IS_PLAYING = new ErrorCode(1_002_038_014, "当前喊话器正在喊话中");
    ErrorCode SPEAKER_IS_NULL = new ErrorCode(1_002_038_015, "喊话器不存在");
    ErrorCode SPEAKER_IS_NOT_PLAY = new ErrorCode(1_002_038_016, "当前喊话器不在空闲中");


    // ========== 标注相关1-002-039-000 ==========
    ErrorCode LABEL_ID_ERROR = new ErrorCode(1_002_039_000, "id错误，标注不存在");
    ErrorCode LABEL_LOCK_ONLY_BY_CREATOR = new ErrorCode(1_002_039_001, "仅支持被创建人锁定及解锁");
    ErrorCode LABEL_HAS_REFERENCE = new ErrorCode(1_002_039_002, "当前标注已关联航线，无法删除，如需删除，请先删除关联的航线");
    ErrorCode TAB_ID_ERROR = new ErrorCode(1_002_039_003, "区域id错误，不能查询到区域信息");

    // ========== 接警相关1-002-040-000 ==========
    ErrorCode CREATE_ALARM_ERROR = new ErrorCode(1_002_040_000, "警情点创建失败");
    ErrorCode ALARM_CONFIG_EXISTS = new ErrorCode(1_002_040_001, "警情场景已存在");
    ErrorCode ALARM_CONFIG_NOT_EXISTS = new ErrorCode(1_002_040_002, "警情场景不存在");
    ErrorCode ALARM_CONFIG_NOT_EXISTS_USE_DEFAULT = new ErrorCode(1_002_040_002, "该场景已被删除，已使用默认场景");
    ErrorCode ALARM_CONFIG_DEFAULT_CAN_NOT_DELETE = new ErrorCode(1_002_040_003, "删除失败，默认接警配置无法删除，请先取消默认设置");
    ErrorCode ALARM_NOT_EXISTS = new ErrorCode(1_002_040_004, "警情点不存在");
    ErrorCode ALARM_JOB_NOT_EXISTS = new ErrorCode(1_002_040_005, "警情任务不存在");
    ErrorCode DOCK_ALARM_CONFIG_NOT_EXISTS = new ErrorCode(1_002_040_006, "未找到机场接警配置");
    ErrorCode DOCK_ALARM_ALREADY_EXPIRED = new ErrorCode(1_002_040_007, "警情任务已失效");
    ErrorCode ALARM_CONFIG_ALGORITHM_RADIUS_NOT_EXISTS = new ErrorCode(1_002_040_008, "警情点半径不存在");

    // ========== 接警相关1-002-041-000 ==========
    ErrorCode ALGORITHM_DOES_NOT_EXIST = new ErrorCode(1_002_041_000, "算法不存在");
    ErrorCode ALGORITHM_DISPOSAL_DOES_NOT_EXIST = new ErrorCode(1_002_041_001, "算法实例不存在");
    ErrorCode DISPOSAL_UNABLE_TO_DELETE = new ErrorCode(1_002_041_002, "该实例已被航线关联，无法删除");


    // ========== 机场日志 =====================
    ErrorCode AT_LEAST_ONE_LOG_IS_BEING_UPLOADED = new ErrorCode(1_002_042_001, "存在正在上传的日志");
    ErrorCode DEVICE_LOG_NOT_EXISTS = new ErrorCode(1_002_042_002, "当前日志不存在");

    // ========== 限飞区高度调整相关错误码 1-002-043-000 ==========
    ErrorCode FLIGHT_RESTRICTION_ADJUSTMENT_FAILED = new ErrorCode(1_002_043_001, "限飞区高度调整失败");
    ErrorCode FLIGHT_RESTRICTION_CONFLICT_DETECTED = new ErrorCode(1_002_043_002, "检测到限飞区冲突");
    ErrorCode FLIGHT_RESTRICTION_GEOMETRY_CALCULATION_ERROR = new ErrorCode(1_002_043_003, "限飞区几何计算错误");

}
