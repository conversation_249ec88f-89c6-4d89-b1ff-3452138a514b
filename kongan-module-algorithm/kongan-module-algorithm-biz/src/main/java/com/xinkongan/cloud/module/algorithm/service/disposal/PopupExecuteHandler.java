package com.xinkongan.cloud.module.algorithm.service.disposal;

import cn.hutool.core.bean.BeanUtil;
import com.xinkongan.cloud.framework.common.constant.BusinessTopicConstant;
import com.xinkongan.cloud.framework.mq.core.consume.BaseRocketMQListener;
import com.xinkongan.cloud.module.algorithm.enums.DisposalActionTypeEnum;
import com.xinkongan.cloud.module.algorithm.vo.DetectResultEventVO;
import com.xinkongan.cloud.module.algorithm.vo.disposal.Popup;
import com.xinkongan.cloud.module.websocket.api.WebSocketSendApi;
import com.xinkongan.cloud.module.websocket.dto.CustomWebSocketMessage;
import com.xinkongan.cloud.module.websocket.dto.WebSocketMessageDTO;
import com.xinkongan.cloud.module.websocket.enums.BizCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 告警处置-弹窗
 *
 * <AUTHOR>
 * @date 2025/4/10
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = BusinessTopicConstant.ALGORITHM_THRESHOLD_ALARM,
        consumerGroup = "popup-disposal-execute-consumer-group",
        messageModel = MessageModel.CLUSTERING)
public class PopupExecuteHandler extends BaseRocketMQListener<DetectResultEventVO> {

    @Resource
    private WebSocketSendApi webSocketSendApi;

    @Override
    public void handlerMessage(DetectResultEventVO detectResultEventVO) {
        Map<String, Object> context = detectResultEventVO.getDisposalActionDO().getContext();
        List<Long> userIds = detectResultEventVO.getDisposalActionDO().getUserIds();
        if (context.containsKey(DisposalActionTypeEnum.POPUP.getCode())) {
            log.info("弹窗处置");
            Popup popup = BeanUtil.toBean(context.get(DisposalActionTypeEnum.POPUP.getCode()), Popup.class);
            // 发送消息
            webSocketSendApi.sendByTenantUserSet(
                    WebSocketMessageDTO.builder()
                            .tenantId(detectResultEventVO.getDisposalActionDO().getTenantId())
                            .userIdSet(new HashSet<>(userIds.stream().filter(Objects::nonNull).collect(Collectors.toSet())))
                            .message(CustomWebSocketMessage.builder()
                                    .bizCode(BizCodeEnum.ALGORITHMIC_ALARM_POPUP.getCode())
                                    .data(popup)
                                    .build())
                            .build()
            );
        }

    }
}
