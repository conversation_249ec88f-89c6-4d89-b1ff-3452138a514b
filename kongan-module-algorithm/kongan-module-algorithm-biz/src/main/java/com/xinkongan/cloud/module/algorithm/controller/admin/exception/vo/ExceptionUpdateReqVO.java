package com.xinkongan.cloud.module.algorithm.controller.admin.exception.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 修改异常
 * <AUTHOR>
 * @Date 2025/4/7 15:28
 */
@Data
@Schema(description = "修改异常")
@AllArgsConstructor
@NoArgsConstructor
public class ExceptionUpdateReqVO {

    @Schema(description = "异常id")
    @NotNull(message = "异常id不能为空")
    private Long id;

    @Schema(description = "异常名称")
    private String name;

    @Schema(description = "异常状态 1真实 2误报")
    private Integer status;
}