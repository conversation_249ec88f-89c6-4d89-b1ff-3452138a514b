package com.xinkongan.cloud.module.algorithm.controller.admin.exception.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 标注异常分页
 * <AUTHOR>
 * @Date 2025/4/7 11:25
 */
@Data
@Schema(description = "标注异常分页")
public class ExceptionPageRespVO {

    @Schema(description = "异常id")
    private Long id;

    @Schema(description = "异常图片")
    private String url;

    @Schema(description = "异常名称")
    private String name;

    @Schema(description = "异常类型")
    private String type;

    @Schema(description = "告警地址")
    private String address;

    @Schema(description = "异常状态 -1未确定 1真实 2误报")
    private Integer status;

    @Schema(description = "关联任务")
    private Long jobId;

    @Schema(description = "关联任务名称")
    private String jobName;

    @Schema(description = "所属组织id")
    private Long deptId;

    @Schema(description = "所属组织名称")
    private String deptName;

    @Schema(description = "纬度")
    private String lat;

    @Schema(description = "经度")
    private String lon;

    @Schema(description = "异常创建时间")
    private LocalDateTime actionTime;

    @Schema(description = "异常事件列表")
    private List<ExceptionEventVO> exceptionEventVOList;

    @Schema(description = "异常上报人id")
    private Long reportUserId;

    @Schema(description = "异常上报人名称")
    private String reportUserName;
}