package com.xinkongan.cloud.module.algorithm.api;

import cn.hutool.core.collection.CollUtil;
import com.xinkongan.cloud.framework.common.pojo.CommonResult;
import com.xinkongan.cloud.module.algorithm.dal.dataobject.algorithm.AlgorithmDO;
import com.xinkongan.cloud.module.algorithm.dto.AlgorithmExampleDTO;
import com.xinkongan.cloud.module.algorithm.dto.AlgorithmIdNameVO;
import com.xinkongan.cloud.module.algorithm.service.algorithm.IAlgorithmDisposalService;
import com.xinkongan.cloud.module.algorithm.service.algorithm.IAlgorithmService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import static com.xinkongan.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description 算法api
 * <AUTHOR>
 * @Date 2025/4/22 15:12
 */
@Slf4j
@RestController
public class AlgorithmApiImpl implements AlgorithmApi {

    @Resource
    private IAlgorithmService algorithmService;
    @Resource
    private IAlgorithmDisposalService algorithmDisposalService;

    @Override
    public CommonResult<List<AlgorithmIdNameVO>> getAlgorithmList(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return success(List.of());
        }
        List<AlgorithmDO> algorithmByIds = algorithmService.getAlgorithmByIds(ids);
        if (CollUtil.isEmpty(algorithmByIds)) {
            return success(List.of());
        }
        return success(algorithmByIds.stream().map(algorithmDO -> {
            AlgorithmIdNameVO algorithmIdNameVO = new AlgorithmIdNameVO();
            algorithmIdNameVO.setId(algorithmDO.getId());
            algorithmIdNameVO.setName(algorithmDO.getName());
            return algorithmIdNameVO;
        }).toList());
    }

    @Override
    public CommonResult<Map<Long, AlgorithmExampleDTO>> getAlgorithmExampleMapByExampleIds(List<Long> ids) {
        Map<Long, AlgorithmExampleDTO> map = algorithmDisposalService.getAlgorithmExampleMapByExampleIds(ids);
        return success(map);
    }
}