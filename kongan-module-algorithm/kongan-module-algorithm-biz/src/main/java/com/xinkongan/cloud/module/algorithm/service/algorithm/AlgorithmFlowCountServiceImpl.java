package com.xinkongan.cloud.module.algorithm.service.algorithm;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xinkongan.cloud.module.algorithm.dal.dataobject.algorithm.AlgorithmFlowCountDO;
import com.xinkongan.cloud.module.algorithm.dal.mysql.algorithm.AlgorithmFlowCountMapper;
import com.xinkongan.cloud.module.algorithm.dto.FlowSegmentCountDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 算法流程计数Service实现类
 */
@Slf4j
@Service
public class AlgorithmFlowCountServiceImpl implements IAlgorithmFlowCountService {

    @Resource
    private IAlgorithmTotalCountCacheService algorithmTotalCountCacheService;
    @Resource
    private AlgorithmFlowCountMapper algorithmFlowCountMapper;

    @Override
    public void flushCountToDB(Long tenantId, Long flyId) {
        log.info("流量算法分段累计计数落库 tenantId={}, flyId={}", tenantId, flyId);
        List<FlowSegmentCountDTO> allCumulativeCount = algorithmTotalCountCacheService.getAllRealCount(flyId);
        if (CollUtil.isNotEmpty(allCumulativeCount)) {
            // 根据oneKey分组
            Map<String, List<FlowSegmentCountDTO>> groupBy = allCumulativeCount.stream().collect(Collectors.groupingBy(FlowSegmentCountDTO::getOneKey));
            for (Map.Entry<String, List<FlowSegmentCountDTO>> entry : groupBy.entrySet()) {
                String oneKey = entry.getKey();
                List<FlowSegmentCountDTO> flowSegmentCountDTOS = entry.getValue();
                AlgorithmFlowCountDO flowCountDO = AlgorithmFlowCountDO.builder()
                        .oneKey(oneKey)
                        .flyId(flyId)
                        .algorithmId(flowSegmentCountDTOS.get(0).getAlgorithmId())
                        .countList(flowSegmentCountDTOS)
                        .build();
                flowCountDO.setTenantId(tenantId);
                algorithmFlowCountMapper.insert(flowCountDO);
            }
        } else {
            log.info("当前flyId={}无分段累计计数数据", flyId);
        }
    }

    @Override
    public List<FlowSegmentCountDTO> listCountByFlyId(Long flyId) {
        log.info("根据flyId查询算法分段累计计数 flyId={}", flyId);
        List<AlgorithmFlowCountDO> algorithmFlowCountDOS = algorithmFlowCountMapper.selectList(Wrappers.<AlgorithmFlowCountDO>lambdaQuery()
                .eq(AlgorithmFlowCountDO::getFlyId, flyId));
        if (CollUtil.isNotEmpty(algorithmFlowCountDOS)) {
            return algorithmFlowCountDOS.stream().map(AlgorithmFlowCountDO::getCountList).flatMap(List::stream).collect(Collectors.toList());
        } else {
            log.info("当前flyId={}无算法分段累计计数数据", flyId);
            return List.of();
        }
    }
}
