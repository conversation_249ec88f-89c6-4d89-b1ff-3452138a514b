package com.xinkongan.cloud.module.algorithm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmAlarmText {

    @Schema(description = "算法名称")
    private String algorithmName;

    @Schema(description = "时间戳")
    private Long timestamp;

    @Schema(description = "无人机Sn")
    private String droneSn;

}
