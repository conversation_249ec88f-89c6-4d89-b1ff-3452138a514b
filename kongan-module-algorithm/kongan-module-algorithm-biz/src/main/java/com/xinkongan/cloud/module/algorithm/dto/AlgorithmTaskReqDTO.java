package com.xinkongan.cloud.module.algorithm.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AlgorithmTaskReqDTO {

    @Schema(description = "机场sn")
    private String dockSn;

    @Schema(description = "无人机Sn")
    private String droneSn;

    @Schema(description = "算法任务选项")
    private List<AlgorithmOptionDTO> algorithmTaskOptions;

    public List<AlgorithmOptionDTO> getAlgorithmTaskOptions() {
        if (algorithmTaskOptions == null) {
            algorithmTaskOptions = new ArrayList<>();
        }
        return algorithmTaskOptions;
    }
}
