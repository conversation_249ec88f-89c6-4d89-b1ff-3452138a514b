<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinkongan.cloud.module.algorithm.dal.mysql.algorithm.AlgorithmDisposalMapper">

    <resultMap id="AlgorithmDisposalResultMap"
               type="com.xinkongan.cloud.module.algorithm.controller.admin.vo.AlgorithmDisposalRespVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="algorithmId" column="algorithm_id"/>
        <result property="disposalActionId" column="disposal_action_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserName" column="createUserName"/>

        <!-- JSON 字段 -->
        <result property="instanceParameters" column="instance_parameters"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="way" column="way"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="context" column="context"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="userIds" column="userIds"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="alarmInterval" column="alarmInterval"/>
    </resultMap>


    <select id="getAlgorithmListPage" resultMap="AlgorithmDisposalResultMap">
        SELECT ad.id,
               ad.name,
               ad.algorithm_id,
               ad.disposal_action_id,
               ad.create_time,
               ad.creator,
               ad.instance_parameters,
               ada.user_ids       as userIds,
               ada.way            as way,
               ada.context        as context,
               ada.alarm_interval as alarmInterval,
               su.nickname        as createUserName
        FROM `system_algorithm_disposal` ad
                 LEFT JOIN system_algorithm_disposal_action ada ON ad.disposal_action_id = ada.id
                 LEFT JOIN system_users su ON su.id = ad.creator
        WHERE ad.algorithm_id = #{param.algorithmId}
          AND ad.dept_id = #{deptId}
          AND ad.id_init = 0
          AND ad.deleted = 0
    </select>
    <select id="selectAlgorithmExampleByExampleIds"
            resultType="com.xinkongan.cloud.module.algorithm.dto.AlgorithmExampleDTO">
        SELECT ad.algorithm_id as algorithmId,
               a.name         as algorithmName,
               ad.id as exampleId,
               ad.name as exampleName
        FROM system_algorithm_disposal ad
        INNER JOIN system_algorithm a ON ad.algorithm_id = a.id AND a.deleted = 0
        WHERE ad.deleted = 0 AND ad.id IN
              <foreach collection="ids" item="id" open="(" separator="," close=")">
                  #{id}
              </foreach>
    </select>
</mapper>