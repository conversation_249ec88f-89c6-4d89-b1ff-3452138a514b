package com.xinkongan.cloud.module.algorithm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LandOnMessageDTO {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "飞行id")
    private Long flyId;

    @Schema(description = "飞行记录id")
    private Long flyRecordId;

    @Schema(description = "无人机序列号")
    private String droneSn;

    @Schema(description = "机场序列号")
    private String dockSn;
}
