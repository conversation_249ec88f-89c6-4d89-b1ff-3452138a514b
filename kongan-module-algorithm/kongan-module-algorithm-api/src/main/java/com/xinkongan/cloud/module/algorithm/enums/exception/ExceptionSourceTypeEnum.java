package com.xinkongan.cloud.module.algorithm.enums.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 来源 ALGORITHM 算法标注, APP_REPORT app上报, MANUAL_MARK 人工标注
 * <AUTHOR>
 * @Date 2025/4/1 16:49
 */
@AllArgsConstructor
@Getter
public enum ExceptionSourceTypeEnum {

    /**
     * 算法标注
     */
    ALGORITHM("ALGORITHM", "算法标注"),

    /**
     * app上报
     */
    APP_REPORT("APP_REPORT", "app上报"),
    /**
     * 人工标注
     */
    MANUAL_MARK("MANUAL_MARK", "人工标注");
    private final String code;
    private final String desc;

    public static ExceptionSourceTypeEnum find(String code) {
        if (code == null) {
            return null;
        }
        for (ExceptionSourceTypeEnum e : ExceptionSourceTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

}
