package com.xinkongan.cloud.sdk.gis.dto.gwc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 构建缓存任务发送请求dto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeedRequestDTO implements Serializable {

    // 缓存的图层名称
    private String name;

    // 要缓存的Srs
    private String srs;

    // 要缓存的开始的层级
    private Integer zoomStart;

    // 要缓存的结束的层级
    private Integer zoomStop;

    // 缓存的格式
    private String format;

    // 缓存的类型
    private String type;

    // 缓存的线程数
    private Integer threadCount;
}
