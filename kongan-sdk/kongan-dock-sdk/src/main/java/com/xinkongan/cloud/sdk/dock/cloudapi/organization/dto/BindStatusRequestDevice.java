package com.xinkongan.cloud.sdk.dock.cloudapi.organization.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.1
 * @date 2022/6/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BindStatusRequestDevice {

    @NotNull
    private String sn;

    @NotNull
    @JsonProperty("is_device_bind_organization")
    private Boolean deviceBindOrganization;

    @NotNull
    private String organizationId;

    @NotNull
    private String organizationName;

    @NotNull
    private String deviceCallsign;

}
