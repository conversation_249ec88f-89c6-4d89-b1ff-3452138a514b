package com.xinkongan.cloud.sdk.dock.cloudapi.wayline.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.enums.ExitingRTHActionEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.enums.ExitingRTHReasonEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceExitHomingNotify {

    private ExitingRTHActionEnum action;

    private String sn;

    private ExitingRTHReasonEnum reason;

}
