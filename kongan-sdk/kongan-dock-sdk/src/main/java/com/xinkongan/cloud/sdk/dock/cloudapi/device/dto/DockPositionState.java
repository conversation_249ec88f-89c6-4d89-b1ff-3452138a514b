package com.xinkongan.cloud.sdk.dock.cloudapi.device.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.PositionFixedEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 0.3
 * @date 2022/1/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DockPositionState implements Serializable {

    @JsonProperty("is_calibration")
    private Boolean calibration;

    private Integer gpsNumber;

    private PositionFixedEnum isFixed;

    private Integer quality;

    private Integer rtkNumber;

}
