package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.enums.FlightAreaSyncReasonEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.enums.FlightAreaSyncStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlightAreasSyncProgress {

    private String dockSn;

    /**
     * Synchronize state
     */
    private FlightAreaSyncStatusEnum status;

    /**
     * Return Code
     */
    private FlightAreaSyncReasonEnum reason;

    /**
     * Custom flight area file
     */
    private FlightAreaFile file;

}
