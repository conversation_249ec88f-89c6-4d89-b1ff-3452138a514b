package com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.request.AirportOrganizationGetRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.requests.TopicRequestsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.messaging.MessageHeaders;

/**
 * @Description AirportOrganizationGetSource
 * <AUTHOR>
 * @Date 2024/11/7 16:39
 */
@Data
@AllArgsConstructor
public class AirportOrganizationGetSource extends BaseSource {

    private TopicRequestsRequest<AirportOrganizationGetRequest> request;

    private MessageHeaders headers;

}