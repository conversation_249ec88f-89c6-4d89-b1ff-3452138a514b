package com.xinkongan.cloud.sdk.dock.cloudapi.organization.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.api.AirportOrganizationGetEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source.AirportOrganizationGetSource;

/**
 * @Description AirportOrganizationGetEvent
 * <AUTHOR>
 * @Date 2024/11/7 16:40
 */
public class AirportOrganizationGetEvent extends BaseEvent<AirportOrganizationGetSource> {

    public AirportOrganizationGetEvent(AirportOrganizationGetSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return AirportOrganizationGetEventHandler.class;
    }
}