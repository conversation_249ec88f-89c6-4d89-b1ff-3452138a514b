package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.api;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.LiveStartPushDTO;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.enums.LiveStreamMethodEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.request.LiveCameraChangeRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.request.LiveLensChangeRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.request.LiveSetQualityRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.request.LiveStopPushRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesPublish;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/7 16:00
 */
@Service
public class DockCloudApiLiveStreamService {

    @Resource
    private ServicesPublish servicesPublish;

    private static final long DEFAULT_TIMEOUT = 20_000;

    /**
     * Start livestreaming
     *
     * @return services_reply
     */
    public CommonTopicResponse<ServicesReplyData<String>> liveStartPush(String dockSn, LiveStartPushDTO liveStartPushDTO) {
        return servicesPublish.publish(
                new TypeReference<String>() {
                },
                dockSn,
                LiveStreamMethodEnum.LIVE_START_PUSH.getMethod(),
                liveStartPushDTO,
                DEFAULT_TIMEOUT);
    }

    /**
     * Stop livestreaming
     *
     * @param request data
     * @return services_reply
     */
    public CommonTopicResponse<ServicesReplyData> liveStopPush(String dockSn, LiveStopPushRequest request) {
        return servicesPublish.publish(
                dockSn,
                LiveStreamMethodEnum.LIVE_STOP_PUSH.getMethod(),
                request,
                DEFAULT_TIMEOUT);
    }

    /**
     * Set livestream quality
     *
     * @param request data
     * @return services_reply
     */
    public CommonTopicResponse<ServicesReplyData> liveSetQuality(String dockSn, LiveSetQualityRequest request) {
        return servicesPublish.publish(
                dockSn,
                LiveStreamMethodEnum.LIVE_SET_QUALITY.getMethod(),
                request,
                DEFAULT_TIMEOUT);
    }

    /**
     * Set livestream lens
     *
     * @param request data
     * @return services_reply
     */
    public CommonTopicResponse<ServicesReplyData> liveLensChange(String dockSn, LiveLensChangeRequest request) {
        return servicesPublish.publish(
                dockSn,
                LiveStreamMethodEnum.LIVE_LENS_CHANGE.getMethod(),
                request,
                DEFAULT_TIMEOUT);
    }

    public CommonTopicResponse<ServicesReplyData> liveCameraChange(String dockSn, LiveCameraChangeRequest request) {
        return servicesPublish.publish(
                dockSn,
                LiveStreamMethodEnum.LIVE_CAMERA_CHANGE.getMethod(),
                request,
                DEFAULT_TIMEOUT);
    }

}