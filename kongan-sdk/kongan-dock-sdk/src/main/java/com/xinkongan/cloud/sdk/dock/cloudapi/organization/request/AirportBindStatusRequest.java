package com.xinkongan.cloud.sdk.dock.cloudapi.organization.request;

import com.xinkongan.cloud.sdk.dock.cloudapi.organization.dto.BindStatusResponseDevice;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AirportBindStatusRequest {

    private List<BindStatusResponseDevice> devices;

}
