package com.xinkongan.cloud.sdk.dock.cloudapi.log.request;

import com.xinkongan.cloud.sdk.dock.cloudapi.log.dto.FileUploadStartParam;
import com.xinkongan.cloud.sdk.dock.cloudapi.storage.CredentialsToken;
import com.xinkongan.cloud.sdk.dock.cloudapi.storage.StsCredentialsResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.2
 * @date 2022/9/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileUploadStartRequest {

    private String dockSn;

    @NotNull
    private String bucket;

    @NotNull
    @Valid
    private CredentialsToken credentials;

    @NotNull
    private String endpoint;

    @NotNull
    private String fileStoreDir;

    @NotEmpty
    private String provider;

    @NotNull
    @Valid
    private FileUploadStartParam params;

    @NotNull
    private String region;

    public FileUploadStartRequest(StsCredentialsResponse sts) {
        this.bucket = sts.getBucket();
        long expire = sts.getCredentials().getExpire();
        sts.getCredentials().setExpire(System.currentTimeMillis() + (expire - 60) * 1000);
        this.credentials = sts.getCredentials();
        this.endpoint = sts.getEndpoint();
        this.fileStoreDir = sts.getObjectKeyPrefix();
        this.provider = sts.getProvider();
        this.region = sts.getRegion();
    }

}
