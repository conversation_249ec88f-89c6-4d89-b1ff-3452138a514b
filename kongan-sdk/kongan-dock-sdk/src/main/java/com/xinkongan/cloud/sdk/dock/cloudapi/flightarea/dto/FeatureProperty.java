package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.enums.GeometrySubTypeEnum;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.9
 * @date 2023/11/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeatureProperty {

    @JsonProperty("subType")
    private GeometrySubTypeEnum subType;

    @Min(10)
    private Float radius = 0f;

    private Boolean enable;

}
