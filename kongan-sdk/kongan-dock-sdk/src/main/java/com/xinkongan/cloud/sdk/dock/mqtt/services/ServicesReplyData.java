package com.xinkongan.cloud.sdk.dock.mqtt.services;

import com.xinkongan.cloud.sdk.dock.common.DockErrorCode;

/**
 * <AUTHOR>
 * @version 0.1
 * @date 2021/11/22
 */
public class ServicesReplyData<T> {

    private DockErrorCode result;

    private T output;

    public ServicesReplyData() {
    }

    @Override
    public String toString() {
        return "DrcUpData{" +
                "result=" + result +
                ", output=" + output +
                '}';
    }

    public DockErrorCode getResult() {
        return result;
    }

    public ServicesReplyData<T> setResult(DockErrorCode result) {
        this.result = result;
        return this;
    }

    public T getOutput() {
        return output;
    }

    public ServicesReplyData<T> setOutput(T output) {
        this.output = output;
        return this;
    }
}