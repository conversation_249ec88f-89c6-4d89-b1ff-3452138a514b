package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.9
 * @date 2023/11/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlightAreaJson {

    private final String type = "FeatureCollection";

    @NotNull
    private List<FlightAreaFeature> features;

}
