package com.xinkongan.cloud.sdk.dock.cloudapi.organization.api;

import com.xinkongan.cloud.framework.event.core.EventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source.AirportOrganizationGetSource;

/**
 * @Description AirportOrganizationGetEventHandler
 * <AUTHOR>
 * @Date 2024/11/7 16:39
 */
public interface AirportOrganizationGetEventHandler extends EventHandler<AirportOrganizationGetSource> {
}
