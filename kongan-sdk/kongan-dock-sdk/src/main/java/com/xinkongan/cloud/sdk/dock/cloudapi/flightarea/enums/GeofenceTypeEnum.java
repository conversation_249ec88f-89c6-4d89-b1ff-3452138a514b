package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.xinkongan.cloud.sdk.dock.exception.CloudSDKException;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.9
 * @date 2023/11/21
 */
public enum GeofenceTypeEnum {

    DFENCE("dfence"),

    NFZ("nfz"),

    ;

    private final String type;

    GeofenceTypeEnum(String type) {
        this.type = type;
    }

    @JsonValue
    public String getType() {
        return type;
    }

    @JsonCreator
    public static GeofenceTypeEnum find(String type) {
        return Arrays.stream(values()).filter(typeEnum -> typeEnum.type.equals(type)).findAny()
                .orElseThrow(() -> new CloudSDKException(GeofenceTypeEnum.class, type));
    }
}
