package com.xinkongan.cloud.sdk.dock.cloudapi.property.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.PayloadIndex;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThermalIsothermLowerLimitSet {

    @NotNull
    @Valid
    private PayloadIndex payloadIndex;

    @NotNull
    private Integer thermalIsothermLowerLimit;

}
