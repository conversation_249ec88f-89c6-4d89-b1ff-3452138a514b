package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.xinkongan.cloud.sdk.dock.exception.CloudSDKException;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/25
 */
public enum LensChangeVideoTypeEnum {

    ZOOM("zoom"),

    WIDE("wide"),

    IR("ir");

    private final String type;

    LensChangeVideoTypeEnum(String type) {
        this.type = type;
    }

    @JsonValue
    public String getType() {
        return type;
    }

    @JsonCreator
    public static LensChangeVideoTypeEnum find(String videoType) {
        return Arrays.stream(values()).filter(typeEnum -> typeEnum.type.equals(videoType)).findAny()
                .orElseThrow(() -> new CloudSDKException(LensChangeVideoTypeEnum.class, videoType));
    }
}
