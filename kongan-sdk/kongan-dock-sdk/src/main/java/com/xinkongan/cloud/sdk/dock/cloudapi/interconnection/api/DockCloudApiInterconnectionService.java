package com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.api;

import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.enums.InterconnectionMethodEnum;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.request.CustomDataTransmissionToEsdkRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.request.CustomDataTransmissionToPsdkRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.config.context.DeviceContext;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesPublish;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import jakarta.annotation.Resource;

/**
 * @Description DockCloudApiInterconnectionService
 * <AUTHOR>
 * @Date 2024/11/7 15:55
 */
public class DockCloudApiInterconnectionService {

    @Resource
    private DeviceContext deviceContext;

    @Resource
    private ServicesPublish servicesPublish;

    /**
     * cloud-custom data transmit to esdk
     *
     * @return services_reply
     */
    public CommonTopicResponse<ServicesReplyData> customDataTransmissionToEsdk(CustomDataTransmissionToEsdkRequest request) {
        return servicesPublish.publish(
                deviceContext.getDockSn(),
                InterconnectionMethodEnum.CUSTOM_DATA_TRANSMISSION_TO_ESDK.getMethod(),
                request);
    }


    /**
     * cloud-custom data transmit to psdk
     *
     * @return services_reply
     */
    public CommonTopicResponse<ServicesReplyData> customDataTransmissionToPsdk(CustomDataTransmissionToPsdkRequest request) {
        return servicesPublish.publish(
                deviceContext.getDockSn(),
                InterconnectionMethodEnum.CUSTOM_DATA_TRANSMISSION_TO_PSDK.getMethod(),
                request);
    }
}