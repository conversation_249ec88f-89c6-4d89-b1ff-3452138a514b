package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LivestreamRtmpUrl implements ILivestreamUrl {

    @NotNull
    private String url;

    @Override
    public LivestreamRtmpUrl clone() {
        try {
            return (LivestreamRtmpUrl) super.clone();
        } catch (CloneNotSupportedException e) {
            return new LivestreamRtmpUrl().setUrl(url);
        }
    }

}
