package com.xinkongan.cloud.sdk.dock.cloudapi.device.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.api.DockDroneCurrentCommanderFlightModeEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.event.source.DockDroneCurrentCommanderFlightModeSource;

/**
 * @Description DockDroneCurrentCommanderFlightModeEvent
 * <AUTHOR>
 * @Date 2024/11/7 15:26
 */
public class DockDroneCurrentCommanderFlightModeEvent extends BaseEvent<DockDroneCurrentCommanderFlightModeSource> {

    public DockDroneCurrentCommanderFlightModeEvent(DockDroneCurrentCommanderFlightModeSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return DockDroneCurrentCommanderFlightModeEventHandler.class;
    }
}