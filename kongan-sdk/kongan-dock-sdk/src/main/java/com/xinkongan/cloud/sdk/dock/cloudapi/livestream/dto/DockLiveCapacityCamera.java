package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.PayloadIndex;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 0.1
 * @date 2021/11/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DockLiveCapacityCamera {

    /**
     * Total number of video streams that can be used for livestreaming
     * Total number of video streams that the camera can live stream
     */
    private Integer availableVideoNumber;

    /**
     * Maximum number of video streams that the camera can live stream at the same time.
     */
    private Integer coexistVideoNumberMax;

    /**
     * Camera index, composed of product type enumeration and gimbal index.
     */
    private PayloadIndex cameraIndex;

    private List<DockLiveCapacityVideo> videoList;

}