package com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.request.AirportBindStatusRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.requests.TopicRequestsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.messaging.MessageHeaders;

/**
 * @Description AirportBindStatusSource
 * <AUTHOR>
 * @Date 2024/11/7 16:37
 */
@Data
@AllArgsConstructor
public class AirportBindStatusSource extends BaseSource {

    private TopicRequestsRequest<AirportBindStatusRequest> request;

    private MessageHeaders headers;

}