package com.xinkongan.cloud.sdk.dock.cloudapi.organization.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.api.AirportOrganizationBindEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source.AirportOrganizationBindSource;

/**
 * @Description AirportOrganizationBindEvent
 * <AUTHOR>
 * @Date 2024/11/7 16:41
 */
public class AirportOrganizationBindEvent extends BaseEvent<AirportOrganizationBindSource> {

    public AirportOrganizationBindEvent(AirportOrganizationBindSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return AirportOrganizationBindEventHandler.class;
    }
}