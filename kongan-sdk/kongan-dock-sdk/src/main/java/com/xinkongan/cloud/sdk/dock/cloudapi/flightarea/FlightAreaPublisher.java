package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea;

import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto.FlightAreasDroneLocation;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto.FlightAreasSyncProgress;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.FlightAreasDroneLocationEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.FlightAreasGetEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.FlightAreasSyncProgressEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source.FlightAreasDroneLocationSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source.FlightAreasGetSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source.FlightAreasSyncProgressSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.request.FlightAreasGetRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.events.TopicEventsRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.model.ChannelName;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.model.MqttReply;
import com.xinkongan.cloud.sdk.dock.mqtt.requests.TopicRequestsRequest;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationContext;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/16
 */
@Service
public class FlightAreaPublisher {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * Progress of custom flight area file synchronize from the Cloud to the Device. Used for further defining flight area.
     *
     * @param request data
     * @param headers The headers for a {@link Message}.
     * @return events_reply
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_EVENTS_FLIGHT_AREAS_SYNC_PROGRESS, outputChannel = ChannelName.OUTBOUND_EVENTS)
    public CommonTopicResponse<MqttReply> flightAreasSyncProgress(TopicEventsRequest<FlightAreasSyncProgress> request, MessageHeaders headers) {
        applicationContext.publishEvent(new FlightAreasSyncProgressEvent(new FlightAreasSyncProgressSource(request)));
        return CommonTopicResponse.reply(request);
    }

    /**
     * Push warning information
     *
     * @param request data
     * @param headers The headers for a {@link Message}.
     * @return events_reply
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_EVENTS_FLIGHT_AREAS_DRONE_LOCATION, outputChannel = ChannelName.OUTBOUND_EVENTS)
    public CommonTopicResponse<MqttReply> flightAreasDroneLocation(TopicEventsRequest<FlightAreasDroneLocation> request, MessageHeaders headers) {
        applicationContext.publishEvent(new FlightAreasDroneLocationEvent(new FlightAreasDroneLocationSource(request)));
        return CommonTopicResponse.reply(request);
    }

    /**
     * Get custom flight area file
     *
     * @param request data
     * @param headers The headers for a {@link Message}.
     * @return requests_reply
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_REQUESTS_FLIGHT_AREAS_GET)
    public void flightAreasGet(TopicRequestsRequest<FlightAreasGetRequest> request, MessageHeaders headers) {
        applicationContext.publishEvent(new FlightAreasGetEvent(new FlightAreasGetSource(request, headers)));
    }
}
