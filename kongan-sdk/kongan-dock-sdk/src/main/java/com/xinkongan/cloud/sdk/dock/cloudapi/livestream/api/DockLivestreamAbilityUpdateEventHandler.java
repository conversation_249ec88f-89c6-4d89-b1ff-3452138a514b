package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.api;

import com.xinkongan.cloud.framework.event.core.EventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.event.source.DockLivestreamAbilityUpdateSource;

/**
 * @Description DockLivestreamAbilityUpdateEventHandler
 * <AUTHOR>
 * @Date 2024/11/7 16:02
 */
public interface DockLivestreamAbilityUpdateEventHandler extends EventHandler<DockLivestreamAbilityUpdateSource> {
}
