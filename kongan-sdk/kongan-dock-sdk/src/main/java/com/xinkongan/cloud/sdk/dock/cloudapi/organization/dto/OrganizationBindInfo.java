package com.xinkongan.cloud.sdk.dock.cloudapi.organization.dto;

import com.xinkongan.cloud.framework.common.exception.IDockErrorInfo;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.1
 * @date 2022/6/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationBindInfo {

    @NotNull
    private String sn;

    @NotNull
    private Integer errCode;

    private static int SUCCESS = 0;

    public static OrganizationBindInfo success(String sn) {
        return OrganizationBindInfo.builder().sn(sn).errCode(SUCCESS).build();
    }

    public static OrganizationBindInfo error(String sn, IDockErrorInfo errCode) {
        return OrganizationBindInfo.builder().sn(sn).errCode(errCode.getCode()).build();
    }

}
