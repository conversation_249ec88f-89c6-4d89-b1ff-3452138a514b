package com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.api.CustomDataTransmissionFromEsdkEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event.source.CustomDataTransmissionFromEsdkSource;

/**
 * @Description CustomDataTransmissionFromEsdkEvent
 * <AUTHOR>
 * @Date 2024/11/7 15:56
 */
public class CustomDataTransmissionFromEsdkEvent extends BaseEvent<CustomDataTransmissionFromEsdkSource> {

    public CustomDataTransmissionFromEsdkEvent(CustomDataTransmissionFromEsdkSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return CustomDataTransmissionFromEsdkEventHandler.class;
    }
}