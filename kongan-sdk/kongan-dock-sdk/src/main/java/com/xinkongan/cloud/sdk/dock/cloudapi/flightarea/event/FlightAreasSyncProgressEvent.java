package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.api.FlightAreasSyncProgressEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source.FlightAreasSyncProgressSource;

/**
 * @Description FlightAreasSyncProgressEvent
 * <AUTHOR>
 * @Date 2024/11/7 15:45
 */
public class FlightAreasSyncProgressEvent extends BaseEvent<FlightAreasSyncProgressSource> {

    public FlightAreasSyncProgressEvent(FlightAreasSyncProgressSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return FlightAreasSyncProgressEventHandler.class;
    }
}