package com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomDataTransmissionToPsdkRequest {

    /**
     * Data content
     * length: Less than 256
     */
    @NotNull
    @Length(max = 256)
    private String value;

}
