package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.api;

import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.enums.FlightAreaMethodEnum;
import com.xinkongan.cloud.sdk.dock.mqtt.config.context.DeviceContext;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesPublish;
import com.xinkongan.cloud.sdk.dock.mqtt.services.ServicesReplyData;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Description DockCloudApiFlightAreaService
 * <AUTHOR>
 * @Date 2024/11/7 15:42
 */
@Service
public class DockCloudApiFlightAreaService {

    @Resource
    private DeviceContext deviceContext;

    @Resource
    private ServicesPublish servicesPublish;

    /**
     * Update command
     *
     * @return services_reply
     */
    public CommonTopicResponse<ServicesReplyData> flightAreasUpdate() {
        return servicesPublish.publish(
                deviceContext.getDockSn(),
                FlightAreaMethodEnum.FLIGHT_AREAS_UPDATE.getMethod());
    }

    /**
     * Update command with specific dock SN
     *
     * @param dockSn 机场SN
     * @return services_reply
     */
    public CommonTopicResponse<ServicesReplyData> flightAreasUpdate(String dockSn) {
        return servicesPublish.publish(
                dockSn,
                FlightAreaMethodEnum.FLIGHT_AREAS_UPDATE.getMethod());
    }
}