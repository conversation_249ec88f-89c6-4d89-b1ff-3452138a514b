package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto.FlightAreasSyncProgress;
import com.xinkongan.cloud.sdk.dock.mqtt.events.TopicEventsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description FlightAreasSyncProgressSource
 * <AUTHOR>
 * @Date 2024/11/7 15:44
 */
@Data
@AllArgsConstructor
public class FlightAreasSyncProgressSource extends BaseSource {

    private TopicEventsRequest<FlightAreasSyncProgress> request;
}
