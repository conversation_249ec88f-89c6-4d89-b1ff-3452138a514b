package com.xinkongan.cloud.sdk.dock.cloudapi.wayline.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.enums.BreakpointStateEnum;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightTaskBreakPoint {

    /**
     * Breakpoint index
     */
    @NotNull
    @Min(0)
    private Integer index;

    /**
     * Breakpoint state
     */
    @NotNull
    private BreakpointStateEnum state;

    /**
     * Current wayline segment process
     */
    @NotNull
    @Min(0)
    @Max(1)
    private Float progress;

    /**
     * Wayline ID
     */
    @NotNull
    private Integer waylineId;

}