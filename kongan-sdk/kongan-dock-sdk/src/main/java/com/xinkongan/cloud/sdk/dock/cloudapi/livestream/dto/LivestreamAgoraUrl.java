package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.net.URLEncoder;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LivestreamAgoraUrl implements ILivestreamUrl {

    @NotNull
    private String channel;

    @NotNull
    private String sn;

    @NotNull
    private String token;

    @NotNull
    private Integer uid;


    @Override
    public String toString() {
        return "channel=" + channel +
                "&sn=" + sn +
                "&token=" + URLEncoder.encode(token, Charset.defaultCharset()) +
                "&uid=" + uid;
    }

    @Override
    public LivestreamAgoraUrl clone() {
        try {
            return (LivestreamAgoraUrl) super.clone();
        } catch (CloneNotSupportedException e) {
            return new LivestreamAgoraUrl().setSn(sn).setToken(token).setChannel(channel).setUid(uid);
        }
    }

}
