package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto.FlightAreasDroneLocation;
import com.xinkongan.cloud.sdk.dock.mqtt.events.TopicEventsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description FlightAreasDroneLocationSource
 * <AUTHOR>
 * @Date 2024/11/7 15:47
 */
@Data
@AllArgsConstructor
public class FlightAreasDroneLocationSource extends BaseSource {

    private TopicEventsRequest<FlightAreasDroneLocation> request;
}