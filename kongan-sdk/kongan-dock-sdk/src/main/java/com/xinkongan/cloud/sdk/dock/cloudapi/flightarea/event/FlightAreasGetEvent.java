package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.api.FlightAreasGetEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source.FlightAreasGetSource;

/**
 * @Description FlightAreasGetEvent
 * <AUTHOR>
 * @Date 2024/11/7 15:49
 */
public class FlightAreasGetEvent extends BaseEvent<FlightAreasGetSource> {

    public FlightAreasGetEvent(FlightAreasGetSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return FlightAreasGetEventHandler.class;
    }
}