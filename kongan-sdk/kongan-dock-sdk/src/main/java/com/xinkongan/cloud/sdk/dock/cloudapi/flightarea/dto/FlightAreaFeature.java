package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.enums.GeofenceTypeEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.9
 * @date 2023/11/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlightAreaFeature {

    @Pattern(regexp = "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$")
    @NotNull
    private String id;

    private final String type = "Feature";

    @NotNull
    private GeofenceTypeEnum geofenceType;

    @NotNull
    @Valid
    private FlightAreaGeometry geometry;

    @NotNull
    private FeatureProperty properties;

}
