package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.enums.GeometryTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.9
 * @date 2023/11/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlightAreaPolygonGeometry extends FlightAreaGeometry {

    private final GeometryTypeEnum type = GeometryTypeEnum.POLYGON;

    private Double[][][] coordinates;

}
