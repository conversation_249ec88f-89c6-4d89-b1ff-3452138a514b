package com.xinkongan.cloud.sdk.dock.cloudapi.livestream;

import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto.DockLivestreamAbilityUpdate;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.event.DockLivestreamAbilityUpdateEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.event.source.DockLivestreamAbilityUpdateSource;
import com.xinkongan.cloud.sdk.dock.mqtt.model.ChannelName;
import com.xinkongan.cloud.sdk.dock.mqtt.state.TopicStateRequest;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationContext;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/5/19
 */
@Service
public class LivestreamPublisher {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * Livestream ability update for dock
     *
     * @param request data
     * @param headers The headers for a {@link Message}.
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_STATE_DOCK_LIVESTREAM_ABILITY_UPDATE)
    public void dockLivestreamAbilityUpdate(TopicStateRequest<DockLivestreamAbilityUpdate> request, MessageHeaders headers) {
        applicationContext.publishEvent(new DockLivestreamAbilityUpdateEvent(new DockLivestreamAbilityUpdateSource(request)));
    }

}
