package com.xinkongan.cloud.sdk.dock.cloudapi.wayline.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.wayline.enums.SimulateSwitchEnum;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/8/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimulateMission {

    @NotNull
    private SimulateSwitchEnum isEnable;

    @NotNull
    @Min(-90)
    @Max(90)
    private Float latitude;

    @NotNull
    @Min(-180)
    @Max(180)
    private Float longitude;

}
