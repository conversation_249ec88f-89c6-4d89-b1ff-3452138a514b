package com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.dto.CustomDataTransmissionFromEsdk;
import com.xinkongan.cloud.sdk.dock.mqtt.events.TopicEventsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description CustomDataTransmissionFromEsdkSource
 * <AUTHOR>
 * @Date 2024/11/7 15:55
 */
@Data
@AllArgsConstructor
public class CustomDataTransmissionFromEsdkSource extends BaseSource {

    private TopicEventsRequest<CustomDataTransmissionFromEsdk> request;
}