package com.xinkongan.cloud.sdk.dock.cloudapi.control.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DroneControlRequest {

    @NotNull
    private Long seq;

    @Min(-17)
    @Max(17)
    private Float x;

    @Min(-17)
    @Max(17)
    private Float y;

    @Min(-4)
    @Max(5)
    private Float h;

    @Min(-90)
    @Max(90)
    private Float w;

    @Min(2)
    @Max(10)
    private Integer freq;

    @Min(100)
    @Max(1000)
    private Integer delayTime;

}
