package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 0.1
 * @date 2021/11/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DockLiveCapacityDevice {

    /**
     * Device serial number
     */
    private String sn;

    /**
     * Total number of video streams that can be used for livestreaming
     * Total number of video streams used for livestreaming that belongs to devices.
     */
    private Integer availableVideoNumber;

    /**
     * Maximum number of video streams that can be used for livestreaming at the same time
     */
    private Integer coexistVideoNumberMax;

    /**
     * Camera list on the device
     */
    private List<DockLiveCapacityCamera> cameraList;

}