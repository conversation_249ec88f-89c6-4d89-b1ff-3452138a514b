package com.xinkongan.cloud.sdk.dock.cloudapi.organization;

import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.AirportBindStatusEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.AirportOrganizationBindEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.AirportOrganizationGetEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source.AirportBindStatusSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source.AirportOrganizationBindSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source.AirportOrganizationGetSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.request.AirportBindStatusRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.request.AirportOrganizationBindRequest;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.request.AirportOrganizationGetRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.model.ChannelName;
import com.xinkongan.cloud.sdk.dock.mqtt.requests.TopicRequestsRequest;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationContext;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

/**
 * 组织绑定相关
 *
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/30
 */
@Service
public class OrganizationPublisher {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 获取组织绑定信息
     *
     * @param request data
     * @param headers 消息请求头 {@link Message}.
     * @return events_reply
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_REQUESTS_AIRPORT_BIND_STATUS)
    public void airportBindStatus(TopicRequestsRequest<AirportBindStatusRequest> request, MessageHeaders headers) {
        applicationContext.publishEvent(new AirportBindStatusEvent(new AirportBindStatusSource(request, headers)));
    }

    /**
     * 搜索设备绑定的组织信息
     *
     * @param request data
     * @param headers The headers for a {@link Message}.
     * @return events_reply
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_REQUESTS_AIRPORT_ORGANIZATION_GET)
    public void airportOrganizationGet(TopicRequestsRequest<AirportOrganizationGetRequest> request, MessageHeaders headers) {
        applicationContext.publishEvent(new AirportOrganizationGetEvent(new AirportOrganizationGetSource(request, headers)));
    }

    /**
     * 设备与组织绑定
     *
     * @param request data
     * @param headers The headers for a {@link Message}.
     * @return events_reply
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_REQUESTS_AIRPORT_ORGANIZATION_BIND)
    public void airportOrganizationBind(TopicRequestsRequest<AirportOrganizationBindRequest> request, MessageHeaders headers) {
        applicationContext.publishEvent(new AirportOrganizationBindEvent(new AirportOrganizationBindSource(request, headers)));
    }
}
