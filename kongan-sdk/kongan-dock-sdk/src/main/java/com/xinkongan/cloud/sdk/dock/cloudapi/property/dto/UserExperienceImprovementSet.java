package com.xinkongan.cloud.sdk.dock.cloudapi.property.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.UserExperienceImprovementEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserExperienceImprovementSet {

    @NotNull
    @JsonProperty("user_experience_improvement")
    private UserExperienceImprovementEnum userExperienceImprovement;

}
