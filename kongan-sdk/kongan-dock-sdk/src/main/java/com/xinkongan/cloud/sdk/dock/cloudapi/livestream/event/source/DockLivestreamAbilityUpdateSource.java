package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto.DockLivestreamAbilityUpdate;
import com.xinkongan.cloud.sdk.dock.mqtt.state.TopicStateRequest;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description DockLivestreamAbilityUpdateSource
 * <AUTHOR>
 * @Date 2024/11/7 16:01
 */
@Data
@AllArgsConstructor
public class DockLivestreamAbilityUpdateSource extends BaseSource {

    private TopicStateRequest<DockLivestreamAbilityUpdate> request;
}