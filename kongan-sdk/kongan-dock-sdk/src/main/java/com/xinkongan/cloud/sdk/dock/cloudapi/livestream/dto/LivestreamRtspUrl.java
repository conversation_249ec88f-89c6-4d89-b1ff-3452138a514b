package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LivestreamRtspUrl implements ILivestreamUrl {

    @NotNull
    private String username;

    @NotNull
    private String password;

    @NotNull
    private Integer port;


    @Override
    public LivestreamRtspUrl clone() {
        try {
            return (LivestreamRtspUrl) super.clone();
        } catch (CloneNotSupportedException e) {
            return new LivestreamRtspUrl().setUsername(username).setPassword(password).setPort(port);
        }
    }

}
