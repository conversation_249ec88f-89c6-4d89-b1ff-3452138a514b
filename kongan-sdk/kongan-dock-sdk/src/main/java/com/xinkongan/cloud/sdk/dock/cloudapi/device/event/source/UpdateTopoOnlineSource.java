package com.xinkongan.cloud.sdk.dock.cloudapi.device.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.UpdateTopo;
import com.xinkongan.cloud.sdk.dock.mqtt.status.TopicStatusRequest;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description UpdateTopoOnlineSource
 * <AUTHOR>
 * @Date 2024/11/7 14:55
 */
@Data
@AllArgsConstructor
public class UpdateTopoOnlineSource extends BaseSource {

    private TopicStatusRequest<UpdateTopo> request;
}