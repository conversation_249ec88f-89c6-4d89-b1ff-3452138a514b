package com.xinkongan.cloud.sdk.dock.cloudapi.device.enums;

import com.xinkongan.cloud.sdk.dock.exception.CloudSDKException;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/20
 */
public enum SimTypeEnum {

    UNKNOWN(0),

    ORDINARY(1),

    THREE_NETWORK_MODES(2),

    ;

    private final int type;

    SimTypeEnum(int type) {
        this.type = type;
    }

    @JsonValue
    public int getType() {
        return type;
    }

    @JsonCreator
    public static SimTypeEnum find(int type) {
        return Arrays.stream(values()).filter(typeEnum -> typeEnum.type == type).findAny()
            .orElseThrow(() -> new CloudSDKException(SimTypeEnum.class, type));
    }

}
