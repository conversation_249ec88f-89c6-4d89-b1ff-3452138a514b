package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.response;

import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto.FlightAreaGetFile;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlightAreasGetResponse {

    /**
     * File list
     */
    @NotNull
    private List<@Valid FlightAreaGetFile> files;

}
