package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.enums.VideoTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 0.1
 * @date 2021/11/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DockLiveCapacityVideo {

    private String videoIndex;

    private VideoTypeEnum videoType;

    private List<VideoTypeEnum> switchableVideoTypes;

}