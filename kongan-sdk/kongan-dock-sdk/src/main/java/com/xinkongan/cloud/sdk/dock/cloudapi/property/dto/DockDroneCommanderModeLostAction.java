package com.xinkongan.cloud.sdk.dock.cloudapi.property.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xinkongan.cloud.sdk.dock.cloudapi.control.enums.CommanderModeLostActionEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DockDroneCommanderModeLostAction {

    @JsonProperty("commander_mode_lost_action")
    @NotNull
    private CommanderModeLostActionEnum commanderModeLostAction;

}
