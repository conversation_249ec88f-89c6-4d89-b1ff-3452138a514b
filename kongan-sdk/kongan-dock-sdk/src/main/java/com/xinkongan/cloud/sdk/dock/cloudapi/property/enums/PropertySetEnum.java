package com.xinkongan.cloud.sdk.dock.cloudapi.property.enums;

import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.DockSilentMode;
import com.xinkongan.cloud.sdk.dock.cloudapi.property.dto.*;
import com.xinkongan.cloud.sdk.dock.exception.CloudSDKException;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.3
 * @date 2022/10/27
 */
@Getter
public enum PropertySetEnum {

    NIGHT_LIGHTS_STATE("night_lights_state", NightLightsStateSet.class),

    HEIGHT_LIMIT("height_limit", HeightLimitSet.class),

    DISTANCE_LIMIT_STATUS("distance_limit_status", DistanceLimitStatusSet.class),

    OBSTACLE_AVOIDANCE("obstacle_avoidance", ObstacleAvoidanceSet.class),

    RTH_ALTITUDE("rth_altitude", RthAltitudeSet.class),

    OUT_OF_CONTROL_ACTION("rc_lost_action", RcLostActionSet.class),

    EXIT_WAYLINE_WHEN_RC_LOST("exit_wayline_when_rc_lost", ExitWaylineWhenRcLostSet.class),

    THERMAL_CURRENT_PALETTE_STYLE("thermal_current_palette_style", ThermalCurrentPaletteStyleSet.class),

    THERMAL_GAIN_MODE("thermal_gain_mode", ThermalGainModeSet.class),

    THERMAL_ISOTHERM_STATE("thermal_isotherm_state", ThermalIsothermStateSet.class),

    THERMAL_ISOTHERM_UPPER_LIMIT("thermal_isotherm_upper_limit", ThermalIsothermUpperLimitSet.class),

    THERMAL_ISOTHERM_LOWER_LIMIT("thermal_isotherm_lower_limit", ThermalIsothermLowerLimitSet.class),

    RTH_MODE("rth_mode", DockDroneRthMode.class),

    USER_EXPERIENCE_IMPROVEMENT("user_experience_improvement", UserExperienceImprovementSet.class),

    COMMANDER_MODE_LOST_ACTION("commander_mode_lost_action", DockDroneCommanderModeLostAction.class),

    COMMANDER_FLIGHT_HEIGHT("commander_flight_height", DockDroneCommanderFlightHeight.class),

    OFFLINE_MAP_ENABLE("offline_map_enable", DockDroneOfflineMapEnable.class),

    SILENT_MODE("silent_mode", DockSilentMode.class),

    ;

    private final String property;

    private final Class<?> clazz;

    PropertySetEnum(String property, Class<?> clazz) {
        this.property = property;
        this.clazz = clazz;
    }

    public static PropertySetEnum find(String property) {
        return Arrays.stream(values()).filter(propertyEnum -> propertyEnum.property.equals(property)).findAny()
                .orElseThrow(() -> new CloudSDKException(PropertySetEnum.class, property));
    }
}
