package com.xinkongan.cloud.sdk.dock.cloudapi.organization.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.api.AirportBindStatusEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source.AirportBindStatusSource;

/**
 * @Description AirportBindStatusEvent
 * <AUTHOR>
 * @Date 2024/11/7 16:38
 */
public class AirportBindStatusEvent extends BaseEvent<AirportBindStatusSource> {

    public AirportBindStatusEvent(AirportBindStatusSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return AirportBindStatusEventHandler.class;
    }
}