package com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.api.CustomDataTransmissionFromPsdkEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event.source.CustomDataTransmissionFromPsdkSource;

/**
 * @Description CustomDataTransmissionFromPsdkEvent
 * <AUTHOR>
 * @Date 2024/11/7 15:58
 */
public class CustomDataTransmissionFromPsdkEvent extends BaseEvent<CustomDataTransmissionFromPsdkSource> {

    public CustomDataTransmissionFromPsdkEvent(CustomDataTransmissionFromPsdkSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return CustomDataTransmissionFromPsdkEventHandler.class;
    }
}