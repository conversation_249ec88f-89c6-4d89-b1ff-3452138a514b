package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.api.FlightAreasDroneLocationEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source.FlightAreasDroneLocationSource;

/**
 * @Description FlightAreasDroneLocationEvent
 * <AUTHOR>
 * @Date 2024/11/7 15:47
 */
public class FlightAreasDroneLocationEvent extends BaseEvent<FlightAreasDroneLocationSource> {

    public FlightAreasDroneLocationEvent(FlightAreasDroneLocationSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return FlightAreasDroneLocationEventHandler.class;
    }
}