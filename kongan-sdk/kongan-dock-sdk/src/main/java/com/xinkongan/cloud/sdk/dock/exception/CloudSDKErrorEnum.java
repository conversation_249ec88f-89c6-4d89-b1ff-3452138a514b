package com.xinkongan.cloud.sdk.dock.exception;

import com.xinkongan.cloud.framework.common.exception.IDockErrorInfo;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/5/23
 */
public enum CloudSDKErrorEnum implements IDockErrorInfo {

    NOT_REGISTERED(210001, "设备未注册。"),

    INVALID_PARAMETER(210002, "无效的参数。"),

    DEVICE_TYPE_NOT_SUPPORT(210003, "当前设备类型不支持此功能。"),

    DEVICE_VERSION_NOT_SUPPORT(210004, "当前设备版本不支持此功能。"),

    DEVICE_PROPERTY_NOT_SUPPORT(210005, "当前设备不支持此特性。"),

    ORGANIZATION_DOES_NOT_EXIST(210006, "该组织不存在。"),

    THE_BINDING_CODE_CANNOT_BE_EMPTY(210007, "绑定码不能为空。"),

    DEVICE_BINDING_FAILED(210008, "绑定设备失败。"),

    MQTT_PUBLISH_ABNORMAL(211001, "发送mqtt消息异常。处理步骤"),

    WEBSOCKET_PUBLISH_ABNORMAL(212001, "webSocket消息发送异常。处理步骤"),

    WRONG_DATA(220001, "数据超过限制。"),

    UNKNOWN(299999, "SDK 未知错误"),
    ;

    private final int code;

    private final String message;

    CloudSDKErrorEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
