package com.xinkongan.cloud.sdk.dock.cloudapi.map.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.map.request.OfflineMapGetRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.requests.TopicRequestsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.messaging.MessageHeaders;

/**
 * @Description OfflineMapGetSource
 * <AUTHOR>
 * @Date 2024/11/7 16:24
 */
@Data
@AllArgsConstructor
public class OfflineMapGetSource extends BaseSource {

    private TopicRequestsRequest<OfflineMapGetRequest> request;

    private MessageHeaders headers;

}