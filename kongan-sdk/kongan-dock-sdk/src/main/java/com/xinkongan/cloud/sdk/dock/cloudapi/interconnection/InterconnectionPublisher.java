package com.xinkongan.cloud.sdk.dock.cloudapi.interconnection;

import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.dto.CustomDataTransmissionFromEsdk;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event.CustomDataTransmissionFromEsdkEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event.source.CustomDataTransmissionFromEsdkSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event.CustomDataTransmissionFromPsdkEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.interconnection.event.source.CustomDataTransmissionFromPsdkSource;
import com.xinkongan.cloud.sdk.dock.mqtt.events.TopicEventsRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.model.ChannelName;
import com.xinkongan.cloud.sdk.dock.mqtt.model.CommonTopicResponse;
import com.xinkongan.cloud.sdk.dock.mqtt.model.MqttReply;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationContext;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/16
 */
@Service
public class InterconnectionPublisher {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * cloud-custom data transmit from esdk
     *
     * @param request data
     * @param headers The headers for a {@link Message}.
     * @return events_reply
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_EVENTS_CUSTOM_DATA_TRANSMISSION_FROM_ESDK, outputChannel = ChannelName.OUTBOUND_EVENTS)
    public CommonTopicResponse<MqttReply> customDataTransmissionFromEsdk(TopicEventsRequest<CustomDataTransmissionFromEsdk> request, MessageHeaders headers) {
        applicationContext.publishEvent(new CustomDataTransmissionFromEsdkEvent(new CustomDataTransmissionFromEsdkSource(request)));
        return CommonTopicResponse.reply(request);
    }



    /**
     * cloud-custom data transmit from psdk
     *
     * @param request data
     * @param headers The headers for a {@link Message}.
     * @return events_reply
     */
    @ServiceActivator(inputChannel = ChannelName.INBOUND_EVENTS_CUSTOM_DATA_TRANSMISSION_FROM_PSDK, outputChannel = ChannelName.OUTBOUND_EVENTS)
    public CommonTopicResponse<MqttReply> customDataTransmissionFromPsdk(TopicEventsRequest<CustomDataTransmissionFromEsdk> request, MessageHeaders headers) {
        applicationContext.publishEvent(new CustomDataTransmissionFromPsdkEvent(new CustomDataTransmissionFromPsdkSource(request)));
        return CommonTopicResponse.reply(request);
    }
}
