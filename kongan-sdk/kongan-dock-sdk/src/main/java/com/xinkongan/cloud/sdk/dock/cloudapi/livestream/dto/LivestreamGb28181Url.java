package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LivestreamGb28181Url implements ILivestreamUrl {

    @NotNull
    private String serverIP;

    @NotNull
    private Integer serverPort;

    @NotNull
    private String serverID;

    @NotNull
    private String agentID;

    @NotNull
    private String agentPassword;

    @NotNull
    private Integer localPort;

    @NotNull
    private String channel;

    @Override
    public LivestreamGb28181Url clone() {
        try {
            return (LivestreamGb28181Url) super.clone();
        } catch (CloneNotSupportedException e) {
            return new LivestreamGb28181Url()
                    .setServerIP(serverIP)
                    .setServerPort(serverPort)
                    .setServerID(serverID)
                    .setAgentID(agentID)
                    .setAgentPassword(agentPassword)
                    .setLocalPort(localPort)
                    .setChannel(channel);
        }
    }
}
