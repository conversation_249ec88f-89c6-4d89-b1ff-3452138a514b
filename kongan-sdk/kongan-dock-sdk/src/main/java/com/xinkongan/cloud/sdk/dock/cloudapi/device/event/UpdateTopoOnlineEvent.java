package com.xinkongan.cloud.sdk.dock.cloudapi.device.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.api.UpdateTopoOnlineEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.event.source.UpdateTopoOnlineSource;

/**
 * @Description UpdateTopoOnlineEvent
 * <AUTHOR>
 * @Date 2024/11/7 14:56
 */
public class UpdateTopoOnlineEvent extends BaseEvent<UpdateTopoOnlineSource> {

    public UpdateTopoOnlineEvent(UpdateTopoOnlineSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return UpdateTopoOnlineEventHandler.class;
    }
}