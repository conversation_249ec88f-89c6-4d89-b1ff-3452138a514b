package com.xinkongan.cloud.sdk.dock.cloudapi.debug.enums;

import com.xinkongan.cloud.sdk.dock.exception.CloudSDKException;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.2
 * @date 2022/8/17
 */
public enum RemoteDebugStatusEnum {

    SENT("sent", false),

    IN_PROGRESS("in_progress", false),

    OK("ok", true),

    PAUSED("paused", false),

    REJECTED("rejected", true),

    FAILED("failed", true),

    CANCELED("canceled", true),

    TIMEOUT("timeout", true);

    private final String status;

    @Getter
    private final boolean end;

    RemoteDebugStatusEnum(String status, boolean end) {
        this.status = status;
        this.end = end;
    }

    @JsonValue
    public String getStatus() {
        return status;
    }

    @JsonCreator
    public static RemoteDebugStatusEnum find(String status) {
        return Arrays.stream(values()).filter(statusEnum -> statusEnum.status.equals(status)).findAny()
                .orElseThrow(() -> new CloudSDKException(RemoteDebugStatusEnum.class, status));
    }
}


