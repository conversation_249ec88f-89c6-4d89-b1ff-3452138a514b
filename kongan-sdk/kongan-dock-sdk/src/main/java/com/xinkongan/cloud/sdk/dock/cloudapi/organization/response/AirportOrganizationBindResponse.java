package com.xinkongan.cloud.sdk.dock.cloudapi.organization.response;

import com.xinkongan.cloud.sdk.dock.cloudapi.organization.dto.OrganizationBindInfo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AirportOrganizationBindResponse {

    @NotNull
    @Size(min = 1, max = 2)
    private List<@Valid OrganizationBindInfo> errInfos;

}
