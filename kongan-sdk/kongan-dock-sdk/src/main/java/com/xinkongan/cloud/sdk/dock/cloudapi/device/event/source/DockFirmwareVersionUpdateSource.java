package com.xinkongan.cloud.sdk.dock.cloudapi.device.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.dto.DockFirmwareVersion;
import com.xinkongan.cloud.sdk.dock.mqtt.state.TopicStateRequest;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description DockFirmwareVersionUpdateSource
 * <AUTHOR>
 * @Date 2024/11/7 15:00
 */
@Data
@AllArgsConstructor
public class DockFirmwareVersionUpdateSource extends BaseSource {

    private TopicStateRequest<DockFirmwareVersion> request;
}