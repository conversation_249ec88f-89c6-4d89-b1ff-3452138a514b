package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.api.DockLivestreamAbilityUpdateEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.livestream.event.source.DockLivestreamAbilityUpdateSource;

/**
 * @Description DockLivestreamAbilityUpdateEvent
 * <AUTHOR>
 * @Date 2024/11/7 16:11
 */
public class DockLivestreamAbilityUpdateEvent extends BaseEvent<DockLivestreamAbilityUpdateSource> {

    public DockLivestreamAbilityUpdateEvent(DockLivestreamAbilityUpdateSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return DockLivestreamAbilityUpdateEventHandler.class;
    }
}