package com.xinkongan.cloud.sdk.dock.cloudapi.organization.dto;

import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.DeviceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.1
 * @date 2022/6/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationBindDevice {

    private String deviceBindingCode;

    private String organizationId;

    private String deviceCallsign;

    private String sn;

    private DeviceEnum deviceModelKey;

}
