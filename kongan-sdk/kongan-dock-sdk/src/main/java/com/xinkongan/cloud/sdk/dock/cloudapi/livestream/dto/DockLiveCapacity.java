package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/5/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DockLiveCapacity {

    /**
     * Total number of video streams available for livestreaming.
     * Indicates the total number of all available live video streams owned by the aircraft or device.
     */
    private Integer availableVideoNumber;

    /**
     * Maximum total number of video streams that can be lived stream simultaneously.
     */
    private Integer coexistVideoNumberMax;

    /**
     * Device live streaming capability list
     */
    private List<DockLiveCapacityDevice> deviceList;

}
