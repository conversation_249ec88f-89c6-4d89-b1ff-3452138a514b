package com.xinkongan.cloud.sdk.dock.cloudapi.wayline.enums;

import com.xinkongan.cloud.sdk.dock.exception.CloudSDKException;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/23
 */
public enum ActionTypeEnum {

    SPOT_CHECK(1),

    ;

    private final int type;

    ActionTypeEnum(int type) {
        this.type = type;
    }

    @JsonValue
    public int getType() {
        return type;
    }

    @JsonCreator
    public static ActionTypeEnum find(int type) {
        return Arrays.stream(values()).filter(typeEnum -> typeEnum.type == type).findAny()
            .orElseThrow(() -> new CloudSDKException(ActionTypeEnum.class, type));
    }

}
