package com.xinkongan.cloud.sdk.dock.cloudapi.device.event;

import com.xinkongan.cloud.framework.event.core.BaseEvent;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.api.DockThermalSupportedPaletteStyleEventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.event.source.DockThermalSupportedPaletteStyleSource;

/**
 * @Description DockThermalSupportedPaletteStyleEvent
 * <AUTHOR>
 * @Date 2024/11/7 15:17
 */
public class DockThermalSupportedPaletteStyleEvent extends BaseEvent<DockThermalSupportedPaletteStyleSource> {

    public DockThermalSupportedPaletteStyleEvent(DockThermalSupportedPaletteStyleSource source) {
        super(source);
    }

    @Override
    public Class<?> handlerClass() {
        return DockThermalSupportedPaletteStyleEventHandler.class;
    }
}