package com.xinkongan.cloud.sdk.dock.cloudapi.livestream.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/5/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LiveSetQualityRequest {

    /**
     * The format is #{uav_sn}/#{camera_id}/#{video_index},
     * drone serial number/payload and mounted location enumeration value/payload lens numbering
     */
    @NotNull
    private String videoId;

    @NotNull
    private Integer videoQuality;

}
