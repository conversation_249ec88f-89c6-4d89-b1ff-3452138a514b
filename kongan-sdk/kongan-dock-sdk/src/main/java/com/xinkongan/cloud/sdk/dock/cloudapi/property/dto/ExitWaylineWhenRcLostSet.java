package com.xinkongan.cloud.sdk.dock.cloudapi.property.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.enums.ExitWaylineWhenRcLostEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.4
 * @date 2023/3/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExitWaylineWhenRcLostSet {

    @NotNull
    @JsonProperty("exit_wayline_when_rc_lost")
    private ExitWaylineWhenRcLostEnum exitWaylineWhenRcLost;

}
