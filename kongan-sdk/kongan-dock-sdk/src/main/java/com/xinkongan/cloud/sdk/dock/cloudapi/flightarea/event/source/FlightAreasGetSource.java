package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.request.FlightAreasGetRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.requests.TopicRequestsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.messaging.MessageHeaders;

/**
 * @Description FlightAreasGetSource
 * <AUTHOR>
 * @Date 2024/11/7 15:48
 */
@Data
@AllArgsConstructor
public class FlightAreasGetSource extends BaseSource {

    private TopicRequestsRequest<FlightAreasGetRequest> request;

    private MessageHeaders headers;

}