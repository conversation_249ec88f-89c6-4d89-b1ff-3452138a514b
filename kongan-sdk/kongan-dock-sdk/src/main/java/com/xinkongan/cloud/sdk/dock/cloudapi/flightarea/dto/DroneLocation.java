package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DroneLocation {

    /**
     * Region unique ID
     */
    private String areaId;

    /**
     * Distance to the custom flight area boundary
     */
    private Float areaDistance;

    /**
     * Whether in custom flight area
     */
    @JsonProperty("is_in_area")
    private Boolean inArea;

}
