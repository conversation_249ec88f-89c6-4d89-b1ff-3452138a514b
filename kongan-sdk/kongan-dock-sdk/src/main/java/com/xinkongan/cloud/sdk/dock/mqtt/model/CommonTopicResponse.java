package com.xinkongan.cloud.sdk.dock.mqtt.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Unified Topic response format
 *
 * <AUTHOR>
 * @version 0.1
 * @date 2021/11/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonTopicResponse<T> {

    /**
     * The command is sent and the response is matched by the tid and bid fields in the message,
     * and the reply should keep the tid and bid the same.
     */
    protected String tid;

    protected String bid;

    protected T data;

    protected String gateway;

    protected Long timestamp;

    protected String method;

    protected Integer needReply;

    public static CommonTopicResponse<MqttReply> reply(CommonTopicRequest request) {
        CommonTopicResponse<MqttReply> response = new CommonTopicResponse<>();
        response.setTid(request.getTid());
        response.setBid(request.getBid());
        response.setGateway(request.getGateway());
        response.setTimestamp(System.currentTimeMillis());
        response.setData(MqttReply.success());
        response.setMethod(request.getMethod());
        response.setNeedReply(request.getNeedReply());
        return response;
    }

    public static <T> CommonTopicResponse<T> reply(CommonTopicRequest request, T data) {
        CommonTopicResponse<T> response = new CommonTopicResponse<>();
        response.setTid(request.getTid());
        response.setBid(request.getBid());
        response.setGateway(request.getGateway());
        response.setTimestamp(System.currentTimeMillis());
        response.setData(data);
        response.setMethod(request.getMethod());
        response.setNeedReply(request.getNeedReply());
        return response;
    }

    public static <T> CommonTopicResponse<MqttReply<T>> replySuccess(CommonTopicRequest request, T data) {
        MqttReply<T> reply = MqttReply.success(data);
        return reply(request, reply);
    }

}