package com.xinkongan.cloud.sdk.dock.cloudapi.organization.event.source;

import com.xinkongan.cloud.framework.event.core.BaseSource;
import com.xinkongan.cloud.sdk.dock.cloudapi.organization.request.AirportOrganizationBindRequest;
import com.xinkongan.cloud.sdk.dock.mqtt.requests.TopicRequestsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.messaging.MessageHeaders;

/**
 * @Description AirportOrganizationBindSource
 * <AUTHOR>
 * @Date 2024/11/7 16:40
 */
@Data
@AllArgsConstructor
public class AirportOrganizationBindSource extends BaseSource {

    private TopicRequestsRequest<AirportOrganizationBindRequest> request;

    private MessageHeaders headers;

}