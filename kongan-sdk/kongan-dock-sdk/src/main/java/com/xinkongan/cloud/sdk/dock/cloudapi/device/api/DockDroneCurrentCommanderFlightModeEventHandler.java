package com.xinkongan.cloud.sdk.dock.cloudapi.device.api;

import com.xinkongan.cloud.framework.event.core.EventHandler;
import com.xinkongan.cloud.sdk.dock.cloudapi.device.event.source.DockDroneCurrentCommanderFlightModeSource;

/**
 * @Description DockDroneCurrentCommanderFlightModeEventHandler
 * <AUTHOR>
 * @Date 2024/11/7 15:26
 */
public interface DockDroneCurrentCommanderFlightModeEventHandler extends EventHandler<DockDroneCurrentCommanderFlightModeSource> {
}
