package com.xinkongan.cloud.sdk.dock.cloudapi.flightarea.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/10/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlightAreaGetFile {

    /**
     * File name
     */
    @NotNull
    @Pattern(regexp = "^geofence_[A-Za-z0-9]{32}.json$")
    private String name;

    /**
     * File URL
     */
    @NotNull
    private String url;

    /**
     * File SHA256 signature
     */
    @NotNull
    private String checksum;

    /**
     * File size
     */
    @NotNull
    private Integer size;

}
